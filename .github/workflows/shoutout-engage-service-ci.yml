name: 🚀 ShoutOUT Engage Service CI

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'shoutout_engage_core_service/**'
      - '.github/workflows/shoutout-engage-service-ci.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'shoutout_engage_core_service/**'
      - '.github/workflows/shoutout-engage-service-ci.yml'

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./shoutout_engage_core_service

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: './shoutout_engage_core_service/package-lock.json'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Type check
      run: npm run type-check
    
    - name: Run tests
      run: npm run test:ci
      
    - name: Build
      run: npm run build