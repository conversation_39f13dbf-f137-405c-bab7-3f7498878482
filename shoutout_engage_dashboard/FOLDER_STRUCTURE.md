# ShoutOut Engage Frontend Folder Structure

This document provides a comprehensive guide to the folder structure of the ShoutOut Engage frontend application. It outlines the organization of files and directories, explaining the purpose of each and how different types of files are categorized.

## Root Directory Structure

The root directory contains configuration files and the main source code:

```
shoutout_engage_dashboard/
├── src/                  # Main source code
├── node_modules/         # Dependencies (generated by npm/yarn)
├── .env                  # Environment variables
├── .env.example          # Example environment variables
├── index.html            # HTML entry point
├── package.json          # Project dependencies and scripts
├── package-lock.json     # Locked dependencies
├── postcss.config.js     # PostCSS configuration
├── tailwind.config.js    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
├── tsconfig.node.json    # TypeScript configuration for Node
└── vite.config.ts        # Vite build tool configuration
```

## Source Code Structure (`src/`)

The `src/` directory contains all the application source code, organized into several subdirectories:

```
src/
├── components/           # Reusable UI components
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and libraries
├── pages/                # Page components for different routes
├── store/                # State management
├── styles/               # CSS and styling
├── types/                # TypeScript type definitions
├── App.tsx               # Main application component
└── main.tsx              # Application entry point
```

### Components (`src/components/`)

The `components/` directory contains reusable UI components organized by feature or functionality:

```
components/
├── auth/                 # Authentication-related components
│   ├── login-form.tsx
│   ├── signup-form.tsx
│   ├── verify-email-form.tsx
│   └── verify-mobile-form.tsx
├── ui/                   # Generic UI components
│   ├── button.tsx
│   ├── input.tsx
│   └── ...
└── theme-provider.tsx    # Theme management component
```

#### UI Components (`src/components/ui/`)

The `ui/` directory contains generic, reusable UI components that form the building blocks of the application interface. These components are designed to be highly reusable and follow a consistent design system.

#### Authentication Components (`src/components/auth/`)

The `auth/` directory contains components specific to authentication functionality, such as login forms, signup forms, and verification forms.

### Pages (`src/pages/`)

The `pages/` directory contains components that represent entire pages or views in the application, organized by feature or route:

```
pages/
├── auth/                 # Authentication pages
│   ├── login.tsx
│   ├── signup.tsx
│   ├── verify-email.tsx
│   └── verify-mobile.tsx
└── dashboard/            # Dashboard pages
    └── index.tsx
```

### Library and Utilities (`src/lib/`)

The `lib/` directory contains utility functions, API clients, and other helper code:

```
lib/
├── supabase.ts           # Supabase client configuration
├── utils.ts              # General utility functions
└── validations/          # Form validation schemas
```

### State Management (`src/store/`)

The `store/` directory contains state management logic using Zustand:

```
store/
└── auth-store.ts         # Authentication state management
```

### Styles (`src/styles/`)

The `styles/` directory contains global CSS and styling configuration:

```
styles/
└── globals.css           # Global CSS styles and Tailwind directives
```

### Types (`src/types/`)

The `types/` directory contains TypeScript type definitions:

```
types/
└── supabase.ts           # Supabase database schema types
```

### Hooks (`src/hooks/`)

The `hooks/` directory is set up for custom React hooks, which allow for reusing stateful logic across components.

## Naming Conventions

The project follows these naming conventions:

1. **Files and Directories**: Use kebab-case for file and directory names (e.g., `auth-store.ts`, `verify-email.tsx`)
2. **Components**: Use PascalCase for component names (e.g., `Button`, `LoginForm`)
3. **TypeScript Interfaces and Types**: Use PascalCase (e.g., `User`, `AuthState`)
4. **Functions and Variables**: Use camelCase (e.g., `handleSubmit`, `isLoading`)

## File Organization Patterns

### Component Structure

Components are typically organized as follows:

1. **Imports**: External libraries first, then internal modules
2. **Type Definitions**: Component props and other types
3. **Component Definition**: The main component function
4. **Helper Functions**: Local utility functions used by the component
5. **Exports**: Default or named exports

### Feature-Based Organization

The codebase is primarily organized by feature (auth, dashboard, etc.) rather than by technical role (components, hooks, etc.). This makes it easier to locate all files related to a specific feature.

## Best Practices

When adding new files to the project, follow these guidelines:

1. **Place files in the appropriate directory** based on their purpose and functionality
2. **Follow the established naming conventions** for consistency
3. **Create new subdirectories** when introducing new features or categories of components
4. **Keep components focused and reusable** by separating concerns and avoiding large, monolithic files
5. **Use relative imports** for files within the same feature and absolute imports (with `@/` prefix) for files across different features

## Conclusion

This folder structure is designed to promote organization, maintainability, and scalability. By following consistent patterns and conventions, the codebase remains clean and easy to navigate as it grows.