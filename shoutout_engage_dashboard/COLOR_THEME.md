# ShoutOut Engage Color Theme Documentation

This document provides a comprehensive guide to the color theme system used in ShoutOut Engage. It outlines the color palette, how colors are applied in both light and dark modes, and how to use these colors in your components.

## Color Palette

ShoutOut Engage uses a purple-based color scheme with complementary colors. Here's the main color palette:

| Color Name | Light Mode (HSL) | Light Mode (Hex) | Dark Mode (HSL) | Dark Mode (Hex) |
|------------|------------------|------------------|-----------------|-----------------|
| Primary    | 246 79% 73%      | #8F87F1          | 246 79% 73%     | #8F87F1         |
| Secondary  | 270 98% 78%      | #C68EFD          | 270 98% 78%     | #C68EFD         |
| Warning    | 290 79% 80%      | #E9A5F1          | 290 79% 80%     | #E9A5F1         |
| Light      | 340 95% 91%      | #FED2E2          | 340 95% 91%     | #FED2E2         |
| Background | 246 79% 97%      | #F5F4FE          | 246 30% 15%     | #1E1B2D         |
| Dark       | 246 30% 40%      | #4A4373          | 246 30% 40%     | #4A4373         |

### Additional Semantic Colors

These colors are derived from the main palette for specific purposes:

| Color Name | Purpose | Value Source |
|------------|---------|--------------|
| Success    | Success states and indicators | Same as Primary |
| Info       | Informational elements | Same as Secondary |
| Danger     | Error states and destructive actions | Custom value |
| Accent     | Highlighting and emphasis | Same as Warning |
| Ring       | Focus indicators | Same as Primary |

## Theme Structure

The color theme is implemented using CSS variables in a light/dark mode system:

### Light Mode

The light mode uses a very light purple background (#F5F4FE) with dark text for high readability.

```css
:root {
  --background: #F5F4FE; /* Very light purple background */
  --foreground: #0F1729;
  --primary: #8F87F1; /* Purple */
  --secondary: #C68EFD; /* Light purple */
  --warning: #E9A5F1; /* Pink-purple */
  --light: #FED2E2; /* Light pink */
  --border: #F5F4FE; /* Match with background color */
  /* ... other variables ... */
}
```

### Dark Mode

The dark mode uses a deep purple background (#1E1B2D) with light text for comfortable viewing in low-light environments.

```css
.dark {
  --background: #1E1B2D; /* Dark purple background */
  --foreground: #F8FAFC;
  --primary: #8F87F1; /* Purple */
  --secondary: #C68EFD; /* Light purple */
  --warning: #E9A5F1; /* Pink-purple */
  --light: #FED2E2; /* Light pink */
  --border: #1E1B2D; /* Match with background color */
  /* ... other variables ... */
}
```

## Using the Theme Colors

### In Tailwind CSS

The theme colors are available through Tailwind CSS classes:

```jsx
// Primary button
<Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
  Primary Button
</Button>

// Secondary button
<Button className="bg-secondary hover:bg-secondary/90 text-secondary-foreground">
  Secondary Button
</Button>

// Background with gradient
<div className="bg-gradient-to-br from-primary/10 to-secondary/10">
  Gradient Background
</div>
```

### Common UI Patterns

#### Buttons

```jsx
// Primary action button
<Button className="bg-primary hover:bg-primary/90">
  Primary Action
</Button>

// Outline button
<Button variant="outline" className="border-primary text-primary hover:bg-primary/10">
  Outline Button
</Button>
```

#### Form Elements

```jsx
// Input with focus styles
<Input className="border-input focus:border-primary focus:ring-primary" />
```

#### Cards and Containers

```jsx
// Card with white background
<div className="rounded-lg border bg-white p-4 shadow-sm">
  Card Content
</div>

// Page background with gradient
<div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10">
  Page Content
</div>
```

## Theme Implementation Details

The theme is implemented using:

1. **CSS Variables**: Defined in `globals.css` using HEX color codes for readability
2. **Tailwind Config**: Extended in `tailwind.config.js` to make the variables available as Tailwind classes
3. **Theme Provider**: A React context in `theme-provider.tsx` that manages theme switching between light and dark modes

## Best Practices

1. **Use semantic color names** rather than specific color values in your components
2. **Maintain contrast ratios** for accessibility (WCAG AA compliance)
3. **Use opacity variants** (e.g., `primary/10` for 10% opacity) for subtle effects
4. **Ensure color consistency** across similar UI elements
5. **Test in both light and dark modes** to ensure good visibility in all conditions

## Theme Switching

Users can switch between light and dark themes using the `ThemeProvider` component:

```jsx
import { useTheme } from "@/components/theme-provider";

function ThemeSwitcher() {
  const { theme, setTheme } = useTheme();

  return (
    <Button onClick={() => setTheme(theme === "light" ? "dark" : "light")}>
      Toggle Theme
    </Button>
  );
}
```
