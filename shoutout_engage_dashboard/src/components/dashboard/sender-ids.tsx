import React from "react";
import { Button } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";

interface SenderID {
  id: number;
  name: string;
  type: string;
  status: string;
}

interface SenderIDsProps {
  senderIds: SenderID[];
  onViewSenderIds: () => void;
}

export function SenderIDs({ senderIds, onViewSenderIds }: SenderIDsProps) {
  return (
    <div className="rounded-lg border bg-white p-3 sm:p-4 shadow-sm">
      <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-gray-800 flex items-center">
        <MessageSquare className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-primary" />
        Sender IDs
      </h2>
      <div className="space-y-2 sm:space-y-3">
        {senderIds.map(sender => (
          <div key={sender.id} className="flex justify-between items-center py-1 sm:py-2 border-b last:border-0">
            <div>
              <span className="font-medium text-sm sm:text-base">{sender.name}</span>
              <span className="text-xs text-gray-500 block">{sender.type}</span>
            </div>
            <span className={`text-xs sm:text-sm px-2 py-1 rounded-full ${
              sender.status === "Verified" 
                ? "bg-green-100 text-green-800" 
                : "bg-yellow-100 text-yellow-800"
            }`}>
              {sender.status}
            </span>
          </div>
        ))}
        <Button 
          variant="outline" 
          className="w-full mt-2 h-9 sm:h-10 text-xs sm:text-sm" 
          onClick={onViewSenderIds}
        >
          View Sender IDs
        </Button>
      </div>
    </div>
  );
}
