import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CreditCard } from "lucide-react";

interface CreditBalanceProps {
  emailCredits: number;
  smsCredits: number;
  plan: string;
  onBuyCredits: () => void;
}

export function CreditBalance({
  emailCredits,
  smsCredits,
  plan,
  onBuyCredits
}: CreditBalanceProps) {
  return (
    <div className="rounded-lg border bg-white p-3 sm:p-4 shadow-sm">
      <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-gray-800 flex items-center">
        <CreditCard className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-primary" />
        Credit Balance
      </h2>
      <div className="space-y-3 sm:space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-sm sm:text-base text-gray-600">Total Email Credits</span>
          <span className="font-semibold text-base sm:text-lg">{emailCredits.toLocaleString()}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm sm:text-base text-gray-600">Total SMS Credits</span>
          <span className="font-semibold text-base sm:text-lg">{smsCredits.toLocaleString()}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm sm:text-base text-gray-600">Current Plan</span>
          <span className="font-semibold text-sm sm:text-base">{plan}</span>
        </div>
        <Button 
          className="w-full h-9 sm:h-10 text-xs sm:text-sm" 
          onClick={onBuyCredits}
        >
          Buy Credits
        </Button>
      </div>
    </div>
  );
}
