import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BarChart3, ChevronDown } from "lucide-react";
import { 
  Line<PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer 
} from "recharts";

interface CampaignDataPoint {
  date: string;
  campaigns: number;
  contacts: number;
}

interface CampaignsChartProps {
  data: CampaignDataPoint[];
  timeframe: string;
  onTimeframeChange: (timeframe: string) => void;
}

export function CampaignsChart({ 
  data, 
  timeframe, 
  onTimeframeChange 
}: CampaignsChartProps) {
  const [showTimeframeMenu, setShowTimeframeMenu] = useState(false);

  const toggleTimeframeMenu = () => {
    setShowTimeframeMenu(!showTimeframeMenu);
  };

  const selectTimeframe = (selected: string) => {
    onTimeframeChange(selected);
    setShowTimeframeMenu(false);
  };

  // Map timeframe to display text
  const timeframeText = {
    "7days": "Last 7 days",
    "14days": "Last 14 days",
    "28days": "Last 28 days",
    "90days": "Last 90 days"
  };

  return (
    <div className="rounded-lg border bg-white p-3 sm:p-4 shadow-sm mt-4 sm:mt-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 sm:mb-4 gap-3 sm:gap-0">
        <h2 className="text-base sm:text-lg font-semibold text-gray-800 flex items-center">
          <BarChart3 className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-primary" />
          Campaigns Performance
        </h2>

        {/* Mobile dropdown for timeframe selection */}
        <div className="sm:hidden relative">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full flex justify-between items-center text-xs"
            onClick={toggleTimeframeMenu}
          >
            {timeframeText[timeframe as keyof typeof timeframeText]}
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>

          {showTimeframeMenu && (
            <div className="absolute z-10 mt-1 w-full bg-white rounded-md shadow-lg border">
              {Object.entries(timeframeText).map(([key, text]) => (
                <button
                  key={key}
                  className={`w-full text-left px-4 py-2 text-xs ${
                    timeframe === key ? 'bg-primary/10 text-primary' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => selectTimeframe(key)}
                >
                  {text}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Desktop buttons for timeframe selection */}
        <div className="hidden sm:flex space-x-2">
          <Button 
            variant={timeframe === "7days" ? "default" : "outline"} 
            size="sm"
            className="text-xs"
            onClick={() => onTimeframeChange("7days")}
          >
            Last 7 days
          </Button>
          <Button 
            variant={timeframe === "14days" ? "default" : "outline"} 
            size="sm"
            className="text-xs"
            onClick={() => onTimeframeChange("14days")}
          >
            Last 14 days
          </Button>
          <Button 
            variant={timeframe === "28days" ? "default" : "outline"} 
            size="sm"
            className="text-xs"
            onClick={() => onTimeframeChange("28days")}
          >
            Last 28 days
          </Button>
          <Button 
            variant={timeframe === "90days" ? "default" : "outline"} 
            size="sm"
            className="text-xs"
            onClick={() => onTimeframeChange("90days")}
          >
            Last 90 days
          </Button>
        </div>
      </div>

      <div className="h-60 sm:h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ 
              top: 5, 
              right: 10, 
              left: 0, 
              bottom: 5 
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tickFormatter={(date) => {
                const d = new Date(date);
                return window.innerWidth < 640 
                  ? d.getDate().toString() // Just day number on mobile
                  : d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
              }}
              tick={{ fontSize: window.innerWidth < 640 ? 10 : 12 }}
            />
            <YAxis 
              yAxisId="left" 
              tick={{ fontSize: window.innerWidth < 640 ? 10 : 12 }}
              width={window.innerWidth < 640 ? 25 : 35}
            />
            <YAxis 
              yAxisId="right" 
              orientation="right" 
              tick={{ fontSize: window.innerWidth < 640 ? 10 : 12 }}
              width={window.innerWidth < 640 ? 25 : 35}
            />
            <Tooltip />
            <Legend 
              wrapperStyle={{ 
                fontSize: window.innerWidth < 640 ? 10 : 12,
                paddingTop: 10
              }} 
            />
            <Line
              yAxisId="left"
              type="monotone"
              dataKey="campaigns"
              name="Campaigns"
              stroke="#3b82f6"
              activeDot={{ r: window.innerWidth < 640 ? 6 : 8 }}
              strokeWidth={window.innerWidth < 640 ? 1.5 : 2}
            />
            <Line
              yAxisId="right"
              type="monotone"
              dataKey="contacts"
              name="Contacts Reached"
              stroke="#10b981"
              activeDot={{ r: window.innerWidth < 640 ? 6 : 8 }}
              strokeWidth={window.innerWidth < 640 ? 1.5 : 2}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
