import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import logoSvg from "@/assets/images/shoutout-logo.svg";
import { useAuthStore } from "@/store/auth-store";
import { ChevronDown, User, Settings, LogOut } from "lucide-react";

interface HeaderProps {
  onLogout: () => Promise<void>;
}

export function Header({ onLogout }: HeaderProps) {
  const { user, profile } = useAuthStore();
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const toggleProfile = () => {
    setIsProfileOpen(!isProfileOpen);
  };

  return (
    <header className="bg-white border-b shadow-sm sticky top-0 z-10">
      <div className="container mx-auto py-4 px-4 sm:px-6">
        <div className="flex flex-wrap justify-between items-center">
          {/* Logo and Title */}
          <div className="flex items-center">
            <img src={logoSvg} alt="ShoutOut Engage Logo" className="h-8 mr-3" />
            <h1 className="text-xl sm:text-2xl font-bold">ShoutOUT Engage</h1>
          </div>

          {/* Credits and User Info - Responsive Layout */}
          <div className="flex flex-wrap items-center mt-4 sm:mt-0 w-full sm:w-auto justify-between sm:justify-end sm:space-x-4">
            {/* Credits Section - Stack on mobile, row on larger screens */}
            <div className="flex space-x-4 sm:space-x-6 order-1 sm:order-none">
              <div className="text-sm">
                <span className="block text-gray-500 text-xs sm:text-sm">SMS Credits</span>
                <span className="font-semibold">5,000</span>
              </div>
              <div className="text-sm">
                <span className="block text-gray-500 text-xs sm:text-sm">Email Credits</span>
                <span className="font-semibold">10,000</span>
              </div>
            </div>

            {/* Buy Credits Button */}
            <Button 
              variant="outline" 
              className="border-primary text-primary hover:bg-primary/10 text-xs sm:text-sm px-2 sm:px-4 h-8 sm:h-10 order-2 sm:order-none"
            >
              Buy Credits
            </Button>

            {/* User Profile Dropdown */}
            <div className="relative order-3 sm:order-none">
              <Button 
                variant="ghost" 
                className="flex items-center text-xs sm:text-sm h-8 sm:h-10 px-2 sm:px-4"
                onClick={toggleProfile}
              >
                <User className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="mr-1 sm:mr-2 max-w-[80px] sm:max-w-none truncate">
                  {profile?.first_name || user?.email}
                </span>
                <ChevronDown className="h-4 w-4" />
              </Button>

              {/* Dropdown Menu */}
              <div 
                className={`${
                  isProfileOpen ? 'block' : 'hidden'
                } absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10`}
              >
                <a href="#" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </a>
                <a 
                  href="#" 
                  onClick={(e) => {
                    e.preventDefault();
                    onLogout();
                  }} 
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
