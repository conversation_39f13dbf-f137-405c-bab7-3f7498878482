import React, { useState } from "react";
import { useAuthStore } from "@/store/auth-store";
import { Sidebar } from "@/components/sidebar";
import { Header } from "@/components/dashboard/header";
import { Onboarding } from "@/components/dashboard/onboarding";
import { CreditBalance } from "@/components/dashboard/credit-balance";
import { ContactsOverview } from "@/components/dashboard/contacts-overview";
import { SenderIDs } from "@/components/dashboard/sender-ids";
import { CampaignsChart } from "@/components/dashboard/campaigns-chart";
import { RecentCampaigns } from "@/components/dashboard/recent-campaigns";

// Mock data for demonstration
const mockCampaignData = [
  { date: "2023-06-01", campaigns: 5, contacts: 120 },
  { date: "2023-06-02", campaigns: 3, contacts: 80 },
  { date: "2023-06-03", campaigns: 7, contacts: 200 },
  { date: "2023-06-04", campaigns: 4, contacts: 150 },
  { date: "2023-06-05", campaigns: 6, contacts: 180 },
  { date: "2023-06-06", campaigns: 8, contacts: 220 },
  { date: "2023-06-07", campaigns: 5, contacts: 170 },
];

const mockRecentCampaigns = [
  { id: 1, name: "Summer Promotion", date: "2023-06-05", status: "Completed", sent: 1200, delivered: 1150 },
  { id: 2, name: "New Product Launch", date: "2023-06-03", status: "Completed", sent: 850, delivered: 830 },
  { id: 3, name: "Customer Survey", date: "2023-06-01", status: "Completed", sent: 500, delivered: 480 },
];

const mockSenderIds = [
  { id: 1, name: "ShoutOUT", type: "SMS", status: "Verified" },
  { id: 2, name: "Marketing", type: "SMS", status: "Pending" },
  { id: 3, name: "Support", type: "SMS", status: "Verified" },
];

export default function Dashboard() {
  const { user, profile, logout } = useAuthStore();
  const [chartTimeframe, setChartTimeframe] = useState("7days");

  const handleLogout = async () => {
    await logout();
  };

  const handleVerifyPhone = () => {
    // Implement phone verification logic
    console.log("Verify phone number");
  };

  const handleChangeNumber = () => {
    // Implement change phone number logic
    console.log("Change phone number");
  };

  const handleBuyCredits = () => {
    // Implement buy credits logic
    console.log("Buy credits");
  };

  const handleViewContacts = () => {
    // Implement view contacts logic
    console.log("View contacts");
  };

  const handleViewSenderIds = () => {
    // Implement view sender IDs logic
    console.log("View sender IDs");
  };

  const handleViewAllCampaigns = () => {
    // Implement view all campaigns logic
    console.log("View all campaigns");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 pb-8">
      {/* Top Navigation */}
      <Header onLogout={handleLogout} />

      <div className="flex">
        {/* Sidebar */}
        <Sidebar />
        
        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Onboarding Section - Only visible if phone not verified */}
          <Onboarding 
            mobileNumber={profile?.mobile_number}
            isMobileVerified={profile?.is_mobile_verified}
            onVerifyPhone={handleVerifyPhone}
            onChangeNumber={handleChangeNumber}
          />

          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Credit Balance */}
            <CreditBalance 
              emailCredits={10000}
              smsCredits={5000}
              plan="Business"
              onBuyCredits={handleBuyCredits}
            />

            {/* Contacts Overview */}
            <ContactsOverview 
              totalContacts={2500}
              emailContacts={2200}
              mobileContacts={1800}
              onViewContacts={handleViewContacts}
            />

            {/* Sender IDs */}
            <SenderIDs 
              senderIds={mockSenderIds}
              onViewSenderIds={handleViewSenderIds}
            />
          </div>

          {/* Campaigns Chart */}
          <CampaignsChart 
            data={mockCampaignData}
            timeframe={chartTimeframe}
            onTimeframeChange={setChartTimeframe}
          />

          {/* Recent Campaigns */}
          <RecentCampaigns 
            campaigns={mockRecentCampaigns}
            onViewAll={handleViewAllCampaigns}
          />
        </div>
      </div>
    </div>
  );
}