import React from "react";
import { Button } from "@/components/ui/button";
import { Users } from "lucide-react";

interface ContactsOverviewProps {
  totalContacts: number;
  emailContacts: number;
  mobileContacts: number;
  onViewContacts: () => void;
}

export function ContactsOverview({
  totalContacts,
  emailContacts,
  mobileContacts,
  onViewContacts
}: ContactsOverviewProps) {
  return (
    <div className="rounded-lg border bg-white p-3 sm:p-4 shadow-sm">
      <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-gray-800 flex items-center">
        <Users className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-primary" />
        Contacts Overview
      </h2>
      <div className="space-y-3 sm:space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-sm sm:text-base text-gray-600">Total Contacts</span>
          <span className="font-semibold text-base sm:text-lg">{totalContacts.toLocaleString()}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm sm:text-base text-gray-600">Email Contacts</span>
          <span className="font-semibold text-base sm:text-lg">{emailContacts.toLocaleString()}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm sm:text-base text-gray-600">Mobile Numbers</span>
          <span className="font-semibold text-base sm:text-lg">{mobileContacts.toLocaleString()}</span>
        </div>
        <Button 
          variant="outline" 
          className="w-full h-9 sm:h-10 text-xs sm:text-sm" 
          onClick={onViewContacts}
        >
          View Contacts
        </Button>
      </div>
    </div>
  );
}
