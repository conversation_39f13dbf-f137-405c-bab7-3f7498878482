import React from "react";
import { Button } from "@/components/ui/button";

interface OnboardingProps {
  mobileNumber: string | null | undefined;
  isMobileVerified: boolean | null | undefined;
  onVerifyPhone: () => void;
  onChangeNumber: () => void;
}

export function Onboarding({
  mobileNumber,
  isMobileVerified,
  onVerifyPhone,
  onChangeNumber
}: OnboardingProps) {
  if (isMobileVerified) return null;

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-6">
        {/* Verification Information */}
        <div className="flex-1">
          <h2 className="text-base sm:text-lg font-semibold text-blue-800 mb-2">Verify Your Phone Number</h2>
          <p className="text-sm text-blue-600 mb-2">Please verify your phone number to unlock all features.</p>
          <p className="text-xs sm:text-sm text-blue-700 mb-4">Current number: {mobileNumber || "Not set"}</p>
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={onVerifyPhone} 
              className="bg-blue-600 hover:bg-blue-700 text-xs sm:text-sm h-9"
            >
              Verify Now
            </Button>
            <Button 
              onClick={onChangeNumber} 
              variant="outline" 
              className="border-blue-600 text-blue-600 hover:bg-blue-50 text-xs sm:text-sm h-9"
            >
              Change Number
            </Button>
          </div>
        </div>

        {/* Quick Start Checklist */}
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm md:min-w-[220px]">
          <h3 className="font-semibold text-gray-700 mb-2 sm:mb-3 text-sm sm:text-base">Quick Start Checklist</h3>
          <ul className="space-y-1 sm:space-y-2">
            <li className="flex items-center">
              <span className="w-4 h-4 sm:w-5 sm:h-5 bg-gray-200 rounded-full flex items-center justify-center mr-2 text-xs sm:text-sm">
                {isMobileVerified ? "✓" : "1"}
              </span>
              <span className={`text-xs sm:text-sm ${isMobileVerified ? "line-through text-gray-400" : ""}`}>
                Verify Phone Number
              </span>
            </li>
            <li className="flex items-center">
              <span className="w-4 h-4 sm:w-5 sm:h-5 bg-gray-200 rounded-full flex items-center justify-center mr-2 text-xs sm:text-sm">2</span>
              <span className="text-xs sm:text-sm">Create a Contact</span>
            </li>
            <li className="flex items-center">
              <span className="w-4 h-4 sm:w-5 sm:h-5 bg-gray-200 rounded-full flex items-center justify-center mr-2 text-xs sm:text-sm">3</span>
              <span className="text-xs sm:text-sm">Launch a Campaign</span>
            </li>
            <li className="flex items-center">
              <span className="w-4 h-4 sm:w-5 sm:h-5 bg-gray-200 rounded-full flex items-center justify-center mr-2 text-xs sm:text-sm">4</span>
              <span className="text-xs sm:text-sm">Go Live</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
