import React from "react";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from "@/components/ui/table";

interface Campaign {
  id: number;
  name: string;
  date: string;
  status: string;
  sent: number;
  delivered: number;
}

interface RecentCampaignsProps {
  campaigns: Campaign[];
  onViewAll: () => void;
}

export function RecentCampaigns({ campaigns, onViewAll }: RecentCampaignsProps) {
  return (
    <div className="rounded-lg border bg-white p-3 sm:p-4 shadow-sm mt-4 sm:mt-6">
      <div className="flex justify-between items-center mb-3 sm:mb-4">
        <h2 className="text-base sm:text-lg font-semibold text-gray-800 flex items-center">
          <Send className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-primary" />
          Recent Campaigns
        </h2>
        <Button 
          variant="outline" 
          size="sm" 
          className="h-8 sm:h-9 text-xs sm:text-sm px-2 sm:px-3"
          onClick={onViewAll}
        >
          View All
        </Button>
      </div>

      {/* Mobile card view */}
      <div className="sm:hidden space-y-3">
        {campaigns.map((campaign) => (
          <div key={campaign.id} className="border rounded-md p-3">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium text-blue-600 text-sm">{campaign.name}</h3>
              <span className="px-2 py-1 text-xs leading-none font-semibold rounded-full bg-green-100 text-green-800">
                {campaign.status}
              </span>
            </div>
            <div className="text-xs text-gray-500 mb-2">
              {new Date(campaign.date).toLocaleDateString()}
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">Sent:</span>
                <span className="ml-1 font-medium">{campaign.sent.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-500">Delivered:</span>
                <span className="ml-1 font-medium">{campaign.delivered.toLocaleString()}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop table view */}
      <div className="hidden sm:block overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Sent</TableHead>
              <TableHead>Delivered</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {campaigns.map((campaign) => (
              <TableRow key={campaign.id}>
                <TableCell className="font-medium text-blue-600 text-sm">
                  {campaign.name}
                </TableCell>
                <TableCell className="text-gray-500 text-sm">
                  {new Date(campaign.date).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <span className="px-2 py-1 inline-flex text-xs leading-none font-semibold rounded-full bg-green-100 text-green-800">
                    {campaign.status}
                  </span>
                </TableCell>
                <TableCell className="text-gray-500 text-sm">
                  {campaign.sent.toLocaleString()}
                </TableCell>
                <TableCell className="text-gray-500 text-sm">
                  {campaign.delivered.toLocaleString()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
