import { Navigate, Outlet } from "react-router-dom";
import { useAuthStore } from "@/store/auth-store";

interface ProtectedRouteProps {
  redirectTo?: string;
}

export function ProtectedRoute({ redirectTo = "/login" }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuthStore();

  // If still loading, show nothing (AuthProvider will show loading spinner)
  if (isLoading) {
    return null;
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to={redirectTo} replace />;
  }

  // If authenticated, render the child routes
  return <Outlet />;
}
