import {create} from 'zustand';
import {persist} from 'zustand/middleware';
import {supabase, getCurrentUser, getSession} from '@/lib/supabase';
import {Database} from '@/types/supabase';

type Profile = Database['public']['Tables']['profiles']['Row'];
type Organization = Database['public']['Tables']['organizations']['Row'];

interface AuthState {
    user: any | null;
    session: any | null;
    profile: Profile | null;
    organization: Organization | null;
    isLoading: boolean;
    isAuthenticated: boolean;

    // Initialize auth state
    initialize: () => Promise<void>;

    // Authentication methods
    login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
    signup: (
        email: string,
        password: string,
        display_name?: string
    ) => Promise<{ success: boolean; error?: string }>;
    logout: () => Promise<void>;


    // Profile methods
    fetchProfile: () => Promise<void>;
    updateProfile: (data: Partial<Profile>) => Promise<{ success: boolean; error?: string }>;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set, get) => ({
            user: null,
            session: null,
            profile: null,
            organization: null,
            isLoading: true,
            isAuthenticated: false,

            initialize: async () => {
                set({isLoading: true});

                try {
                    // Get session
                    const session = await getSession();

                    if (session) {
                        const user = await getCurrentUser();

                        if (user) {
                            set({
                                user,
                                session,
                                isAuthenticated: true
                            });

                            // Fetch user profile
                            await get().fetchProfile();
                        }
                    }
                } catch (error) {
                    console.error('Error initializing auth:', error);
                } finally {
                    set({isLoading: false});
                }
            },

            login: async (email, password) => {
                try {
                    const {data, error} = await supabase.auth.signInWithPassword({
                        email,
                        password,
                    });

                    if (error) throw error;

                    set({
                        user: data.user,
                        session: data.session,
                        isAuthenticated: true
                    });

                    // Fetch user profile
                    await get().fetchProfile();

                    return {success: true};
                } catch (error: any) {
                    console.error('Login error:', error.message);
                    return {success: false, error: error.message};
                }
            },

            signup: async (email, password, display_name) => {
                try {
                    // Use Supabase Auth's built-in signup with email confirmation
                    const { error } = await supabase.auth.signUp({
                        email,
                        password,
                        options: {
                            emailRedirectTo: `${window.location.origin}/auth/callback`,
                            data: { display_name }
                        }
                    });

                    if (error) throw error;

                    // User created but needs to verify email
                    return { 
                        success: true, 
                        message: "Account created. Please verify your email." 
                    };
                } catch (error: any) {
                    console.error('Signup error:', error.message);
                    return {success: false, error: error.message};
                }
            },

            logout: async () => {
                await supabase.auth.signOut();
                set({
                    user: null,
                    session: null,
                    profile: null,
                    organization: null,
                    isAuthenticated: false
                });
            },


            fetchProfile: async () => {
                try {
                    const {data: profile, error: profileError} = await supabase
                        .from('profiles')
                        .select('*')
                        .eq('user_id', get().user?.id)
                        .single();

                    if (profileError) throw profileError;

                    if (profile) {
                        // Fetch organization details
                        const {data: organization, error: orgError} = await supabase
                            .from('organizations')
                            .select('*')
                            .eq('uuid', profile.organization_uuid)
                            .single();

                        if (orgError) throw orgError;

                        set({profile, organization});
                    }
                } catch (error) {
                    console.error('Error fetching profile:', error);
                }
            },

            updateProfile: async (data) => {
                try {
                    const {error} = await supabase
                        .from('profiles')
                        .update(data)
                        .eq('user_id', get().user?.id);

                    if (error) throw error;

                    // Refresh profile
                    await get().fetchProfile();

                    return {success: true};
                } catch (error: any) {
                    console.error('Profile update error:', error.message);
                    return {success: false, error: error.message};
                }
            },
        }),
        {
            name: 'auth-storage',
            partialize: (state) => ({
                user: state.user,
                session: state.session,
                profile: state.profile,
                organization: state.organization,
                isAuthenticated: state.isAuthenticated
            }),
        }
    )
);
