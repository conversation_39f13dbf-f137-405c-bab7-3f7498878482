import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { SignupForm } from "@/components/auth/signup-form";
import { useAuthStore } from "@/store/auth-store";
import logoSvg from "@/assets/images/shoutout-logo.svg";

export default function SignupPage() {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuthStore();

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, isLoading, navigate]);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center bg-gradient-to-br from-primary/10 to-secondary/10">
      <div className="mx-auto w-full max-w-[900px] overflow-hidden rounded-xl bg-white shadow-xl">
        <div className="flex flex-col md:flex-row">
          {/* Left side - Signup Form */}
          <div className="w-full p-8 md:w-1/2">
            <div className="mb-6 flex flex-col space-y-2">
              <h1 className="text-2xl font-semibold tracking-tight">Create an account</h1>
              <p className="text-sm text-muted-foreground">
                Sign up to get started with ShoutOut Engage
              </p>
            </div>
            <SignupForm />
          </div>

          {/* Right side - Logo and Description */}
          <div className="hidden w-full bg-gradient-to-br from-primary to-secondary p-8 text-white md:block md:w-1/2">
            <div className="flex h-full flex-col items-center justify-center space-y-6">
              <div className="text-center">
                <img src={logoSvg} alt="ShoutOut Engage Logo" className="mx-auto mb-4" />
                <p className="mt-2 text-xl">Customer Engagement Platform</p>
              </div>
              <div className="mt-8 max-w-md text-center">
                <p className="text-light">
                  Join thousands of businesses that use ShoutOut Engage to connect with their customers, build relationships, and drive growth.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
