import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/store/auth-store';

export default function AuthCallback() {
  const navigate = useNavigate();
  const fetchProfile = useAuthStore((state) => state.fetchProfile);
  
  useEffect(() => {
    // Handle the auth callback
    const handleAuthCallback = async () => {
      // Get the URL hash (Supabase puts auth info in the hash)
      const hash = window.location.hash;
      
      if (hash) {
        // Process the hash with Supabase
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error processing auth callback:', error.message);
          navigate('/login?error=Unable to verify your email. Please try again.');
          return;
        }
        
        if (data.session) {
          // Session exists, user is authenticated
          // Fetch the user profile to get the organization info
          await fetchProfile();
          
          // Redirect to dashboard
          navigate('/dashboard');
        } else {
          // No session, redirect to login
          navigate('/login?message=Email verified. Please log in.');
        }
      } else {
        // No hash, redirect to login
        navigate('/login');
      }
    };
    
    handleAuthCallback();
  }, [navigate, fetchProfile]);
  
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Verifying your email...</h1>
        <p className="mt-2 text-gray-600">Please wait while we verify your email address.</p>
      </div>
    </div>
  );
}