import { useAuthStore } from "@/store/auth-store";
import { But<PERSON> } from "@/components/ui/button";
import logoSvg from "@/assets/images/shoutout-logo.svg";

export default function DashboardPage() {
  const { user, profile, organization, logout } = useAuthStore();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 pb-8">
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <img src={logoSvg} alt="ShoutOut Engage Logo" className="h-8 mr-3" />
            <h1 className="text-3xl font-bold">Dashboard</h1>
          </div>
          <Button onClick={handleLogout} variant="outline" className="border-primary text-primary hover:bg-primary/10">
            Logout
          </Button>
        </div>

        <div className="grid gap-6">
          <div className="rounded-lg border bg-white p-4 shadow-sm">
            <h2 className="text-xl font-semibold mb-4 text-primary">User Information</h2>
            <div className="space-y-2">
              <p><strong>Email:</strong> {user?.email}</p>
              <p><strong>Name:</strong> {profile?.first_name} {profile?.last_name}</p>
              <p><strong>Role:</strong> {profile?.user_role}</p>
              <p><strong>Email Verified:</strong> {profile?.is_email_verified ? "Yes" : "No"}</p>
              <p><strong>Mobile Verified:</strong> {profile?.is_mobile_verified ? "Yes" : "No"}</p>
            </div>
          </div>

          <div className="rounded-lg border bg-white p-4 shadow-sm">
            <h2 className="text-xl font-semibold mb-4 text-primary">Organization Information</h2>
            <div className="space-y-2">
              <p><strong>Name:</strong> {organization?.organization_name}</p>
              <p><strong>Status:</strong> {organization?.status}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
