// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("PRISMA_DATABASE_URL")
  directUrl = env("PRISMA_DIRECT_URL")
}

// Enums
enum organization_status {
  active
  inactive
  suspended
}

enum user_type {
  user
  admin
  super_admin
}

// Users model (matches Supabase auth.users)
model users {
  uid             String    @id @default(uuid()) @db.Uuid
  display_name    String?   @db.Text
  email           String    @db.Text
  phone           String?   @db.Text
  provider        String?   @db.Text
  provider_type   String?   @db.Text
  created_at      DateTime  @default(now()) @db.Timestamptz(6)
  last_sign_in_at DateTime? @db.Timestamptz(6)

  // Relations
  profiles profiles[] // 1:N relation with profiles

  @@map("users")
}

// Organizations model
model organizations {
  uuid              String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  organization_name String              @db.Text
  email             String?             @db.Text
  phone_number      String?             @db.Text
  status            organization_status @default(inactive)
  created_at        DateTime            @default(now()) @db.Timestamptz(6)
  updated_at        DateTime            @default(now()) @db.Timestamptz(6)

  // Relations
  profiles profiles[] // 1:N relation with profiles

  @@index([organization_name], map: "idx_organizations_name")
  @@index([status], map: "idx_organizations_status")
  @@map("organizations")
}

// Profiles model
model profiles {
  uuid               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id            String    @unique @db.Uuid
  organization_uuid  String?   @db.Uuid
  first_name         String?   @db.Text
  last_name          String?   @db.Text
  user_type          user_type @default(user)
  email              String?   @db.Text
  phone_number       String?   @db.Text
  is_email_verified  Boolean   @default(false)
  is_mobile_verified Boolean   @default(false)
  user_status        String    @default("inactive")
  created_at         DateTime  @default(now()) @db.Timestamptz(6)
  updated_at         DateTime  @default(now()) @db.Timestamptz(6)

  // Relations - Fixed the reference field
  user         users          @relation(fields: [user_id], references: [uid], onDelete: Cascade)
  organization organizations? @relation(fields: [organization_uuid], references: [uuid], onDelete: SetNull)

  @@index([user_id], map: "idx_profiles_user_id")
  @@index([organization_uuid], map: "idx_profiles_organization_uuid")
  @@index([email], map: "idx_profiles_email")
  @@index([user_status], map: "idx_profiles_user_status")
  @@map("profiles")
}
