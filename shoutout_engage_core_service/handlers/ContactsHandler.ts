import { Request, Response } from 'express';
import { ObjectId } from 'mongodb';
import { ContactDAO } from '../lib/db/dao/ContactDAO';
import { ContactStatus, CreateContactData, ContactCreateRequest, ContactResponse, ContactsQueryParams, PaginatedContactsResponse } from '../types/contact.types';
import { logger } from '../lib/logger';
import config from '../lib/config';
import { AuthenticatedRequest } from '../lib/middlewares/supabase.authorizer.middleware';
import {CSVService} from "../services/csv.service";
import {CSVHeaderExtractionResponse} from "../types/file.types";
import {ERROR_CODES} from "../lib/constant/file.constants";
import {SchemaExtractor} from "../lib/utils/schemaExtractor";
import { ContactBulkInsertService } from '../services/contact.bulk.insert.service';
import {
  DatabaseError,
  OrganizationError,
  NotFoundError,
  BadRequestError,
  ValidationError,
  InternalError,
  DuplicateError,
} from '../lib/errors/error.type';
import {OrganizationDAO} from "../lib/db/dao/prisma/OrganizationDAO";
import { ErrorContext } from '../lib/utils/errorContext';


const log = logger(config.logger);

/**
 * Handler class for contact-related business logic
 */
export class ContactsHandler {
  /**
   * Extract headers from a CSV or Excel file
   * @param req Express request with file from multer
   * @param res Express response
   * @returns Promise with extracted headers and contact schema
   */
  public static async extractCSVHeaders(req: Request & { file?: Express.Multer.File }, res: Response): Promise<void> {
    // Create error context for enhanced error logging
    const context = new ErrorContext(req, {
      fileName: req.file?.originalname
    });

    // Get the uploaded file from multer middleware
    const file = req.file;

    if (!file) {
      throw new BadRequestError(
        'No file uploaded',
        'FILE_MISSING',
        [{ field: 'file', message: 'File is required for header extraction' }]
      ).withContext(context.toLogObject());
    }

    try {
      // Validate file structure
      const validationResult = await CSVService.validateFileStructure(file);

      if (!validationResult.isValid) {
        // Determine appropriate error code
        let errorCode = ERROR_CODES.PROCESSING_ERROR;

        if (validationResult.errors.some(e => e.includes('too many rows'))) {
          errorCode = ERROR_CODES.TOO_MANY_ROWS;
        } else if (validationResult.errors.some(e => e.includes('multiple sheets'))) {
          errorCode = ERROR_CODES.MULTIPLE_SHEETS;
        } else if (validationResult.errors.some(e => e.includes('no data or headers'))) {
          errorCode = ERROR_CODES.NO_HEADERS;
        }

        throw new ValidationError(
          validationResult.errors.join('. '),
          errorCode,
          validationResult.errors.map(error => ({
            field: 'file',
            message: error
          }))
        ).withContext(context.toLogObject());
      }

      // Extract headers from the file
      const csvHeaders = await CSVService.extractHeaders(file);

      // Get contact schema fields
      const contactSchema = SchemaExtractor.getContactFields();

      // Return successful response with headers and schema
      res.status(200).json({
        success: true,
        data: {
          csvHeaders,
          contactSchema,
          fileInfo: {
            name: file.originalname,
            size: file.size,
            rowCount: validationResult.rowCount || 0
          }
        }
      } as CSVHeaderExtractionResponse);

    } catch (error) {
      // If it's already an AppError, add context and rethrow
      if (error instanceof ValidationError || error instanceof BadRequestError) {
        throw error.withContext(context.toLogObject());
      }

      // Transform unexpected errors to InternalError
      throw new InternalError(
        error instanceof Error ? error.message : 'Error processing file',
        ERROR_CODES.PROCESSING_ERROR,
        false
      ).withContext(context.toLogObject());
    }
  }

  /**
   * Create a new contact
   * @param req - Express request object with authenticated user and organization context
   * @param res - Express response object
   */
  static async createContact(req: AuthenticatedRequest, res: Response): Promise<void> {
    // Create error context for enhanced error logging
    const context = new ErrorContext(req);

    log.info('Creating contact for user:', { 
      userId: req.userId, 
      organizationId: req.organizationId 
    });

    // Organization ID should be available from middleware
    if (!req.organizationId) {
      throw new OrganizationError('Organization context is required', 'ORG_001')
        .withContext(context.toLogObject());
    }

    // Extract and validate request data
    const contactData = req.body as ContactCreateRequest;

    try {
      // Convert tag_id strings to ObjectIds if tags are provided
      const processedTags = contactData.tags?.map(tag => ({
        tag_id: new ObjectId(tag.tag_id),
        tag_name: tag.tag_name
      })) || [];

      // Prepare data for DAO
      const createData: CreateContactData = {
        org_id: req.organizationId,
        created_by: req.userId!,
        name: contactData.name,
        email: contactData.email,
        phone: contactData.phone,
        country: contactData.country,
        country_code: contactData.country_code,
        avatar_url: contactData.avatar_url,
        tags: processedTags,
        additional_fields: contactData.additional_fields || {},
        status: contactData.status || ContactStatus.ACTIVE
      };

      // Create contact via DAO
      const createdContact = await ContactDAO.createContact(createData);

      // Format response using DAO utility method
      const response: ContactResponse = ContactDAO.formatContactResponse(createdContact);

      log.info('Contact created successfully:', { 
        contactId: createdContact._id, 
        orgId: req.organizationId, 
        userId: req.userId 
      });

      res.status(201).json(response);

    } catch (error) {
      // Add context to any errors and let them bubble up to the error middleware
      if (error instanceof DatabaseError || error instanceof ValidationError || error instanceof BadRequestError || error instanceof DuplicateError) {
        throw error.withContext(context.toLogObject());
      }

      // Transform unexpected errors to DatabaseError
      throw new DatabaseError(
        'Failed to create contact',
        'DB_001'
      ).withContext(context.toLogObject());
    }
  }



  /**
   * Get contacts with pagination, filtering, sorting, and search capabilities
   * @param req - Express request object with authenticated user and organization context
   * @param res - Express response object
   */
  static async getContacts(req: AuthenticatedRequest, res: Response): Promise<void> {
    // Create error context for enhanced error logging
    const context = new ErrorContext(req);

    log.info('Retrieving contacts for user:', { 
      userId: req.userId, 
      organizationId: req.organizationId 
    });

    // Extract and validate organization ID
    if (!req.organizationId) {
      throw new OrganizationError('Organization context is required', 'ORG_001')
        .withContext(context.toLogObject());
    }

    // Process query parameters
    // Note: The validation middleware has already validated these parameters
    const params: ContactsQueryParams = {
      page: Number(req.query.page),
      page_size: Number(req.query.page_size),
      sort_by: req.query.sort_by as string || 'created_at',
      sort_direction: (req.query.sort_direction as 'asc' | 'desc') || 'desc',
      search: req.query.search as string,
      contactFilterQuery: req.query.contactFilterQuery as string
    };

    // Log the processed parameters for debugging
    log.debug('Processed query parameters:', params);

    try {
      // Call ContactDAO.getContacts with organization ID and processed parameters
      const { contacts, totalCount } = await ContactDAO.getContacts(
        req.organizationId,
        params
      );

      // Format contacts using existing formatContactsResponse method
      const formattedContacts = ContactDAO.formatContactsResponse(contacts);
      
      // Calculate pagination metadata
      const page = params.page || 1;
      const pageSize = params.page_size || 20;
      const totalPages = Math.ceil(totalCount / pageSize);
      
      // Build response with pagination metadata
      const response: PaginatedContactsResponse = {
        data: formattedContacts,
        pagination: {
          total_count: totalCount,
          page,
          page_size: pageSize,
          total_pages: totalPages,
          has_next_page: page < totalPages,
          has_prev_page: page > 1
        }
      };
      
      // Return 200 response with data and pagination
      log.info(`Successfully retrieved ${formattedContacts.length} contacts for organization: ${req.organizationId}`);
      res.status(200).json(response);
      
    } catch (dbError) {
      log.error('Database error while retrieving contacts:', dbError);
      
      // Throw a DatabaseError to be handled by the centralized error handler
      throw new DatabaseError('Failed to retrieve contacts', 'DB_001')
        .withContext(context.toLogObject());
    }
  }

  /**
   * Get a contact by ID
   * @param req - Express request object with authenticated user and organization context
   * @param res - Express response object
   */
  static async getContactById(req: AuthenticatedRequest, res: Response): Promise<void> {
    const contactId = req.params.id;
    
    // Create error context for enhanced error logging
    const context = new ErrorContext(req);
    
    log.info('Retrieving contact by ID:', { 
      contactId,
      userId: req.userId, 
      organizationId: req.organizationId 
    });

    // Extract and validate organization ID
    if (!req.organizationId) {
      throw new OrganizationError('Organization context is required', 'ORG_001')
        .withContext(context.toLogObject());
    }

    try {
      // Call ContactDAO.getContactById with organization ID and contact ID
      const contact = await ContactDAO.getContactById(
        req.organizationId,
        contactId
      );

      // Format contact using existing formatContactResponse method
      const formattedContact = ContactDAO.formatContactResponse(contact);
      
      // Return 200 response with contact data
      log.info(`Successfully retrieved contact with ID: ${contactId} for organization: ${req.organizationId}`);
      res.status(200).json(formattedContact);
      
    } catch (dbError: unknown) {
      // If the error is already an AppError (NotFoundError, BadRequestError), add context and rethrow
      if (dbError instanceof NotFoundError || dbError instanceof BadRequestError) {
        throw dbError.withContext(context.toLogObject());
      }
      
      log.error('Database error while retrieving contact:', dbError);
      
      // Throw a DatabaseError to be handled by the centralized error handler
      throw new DatabaseError('Failed to retrieve contact', 'DB_001')
        .withContext(context.toLogObject());
    }
  }

  /**
   * Handle bulk insert of contacts from CSV file
   * @param req - Express request object with authenticated user, organization context, and uploaded file
   * @param res - Express response object
   */
  static async bulkInsertContacts(req: AuthenticatedRequest & { file?: Express.Multer.File }, res: Response): Promise<void> {
    // Create error context for enhanced error logging
    const context = new ErrorContext(req, {
      fileName: req.file?.originalname
    });

    log.info('Starting bulk insert contacts for user:', { 
      userId: req.userId, 
      organizationId: req.organizationId 
    });

    // Validate organization context
    if (!req.organizationId) {
      throw new OrganizationError('Organization context is required', 'ORG_001')
        .withContext(context.toLogObject());
    }

    // Validate user ID
    if (!req.userId) {
      throw new BadRequestError(
        'User context is required',
        'AUTH_001',
        [{ field: 'user', message: 'User authentication is required' }]
      ).withContext(context.toLogObject());
    }

    // Get uploaded file (validated by middleware)
    const file = req.file;
    if (!file) {
      throw new BadRequestError(
        'No file uploaded',
        'FILE_MISSING',
        [{ field: 'file', message: 'CSV file is required for bulk insert' }]
      ).withContext(context.toLogObject());
    }

    try {
      //get organization email from organizationId
      const organization = await OrganizationDAO.getOrganizationByUuid(req.organizationId);
      const organizationEmail = organization?.email;

      if(!organizationEmail){
        throw new BadRequestError(
          'Organization email is required',
          'ORG_EMAIL_MISSING',
          [{ field: 'organization', message: 'Organization email is required for notifications' }]
        ).withContext(context.toLogObject());
      }

      // Get header mapping - it's validated and pre-parsed by middleware
      if (!req.body.headerMapping) {
        throw new BadRequestError(
          'Header mapping is required for bulk insert operations',
          'HEADER_MAPPING_REQUIRED',
          [{ field: 'headerMapping', message: 'Header mapping is required to map CSV headers to contact attributes' }]
        ).withContext(context.toLogObject());
      }

      const headerMapping: { [key: string]: string } = req.body.headerMapping;
      log.info('Header mapping received from validation:', { headerMapping });

      log.info('Processing bulk insert request:', {
        fileName: file.originalname,
        fileSize: file.size,
        orgId: req.organizationId,
        userId: req.userId,
        hasHeaderMapping: !!headerMapping
      });

      // Create bulk insert job using the service
      const { jobId } = await ContactBulkInsertService.createBulkInsertJob(
        file,
        req.organizationId,
        req.userId,
        headerMapping,
        organizationEmail
      );

      log.info('Bulk insert job created successfully:', {
        jobId,
        fileName: file.originalname,
        orgId: req.organizationId,
        userId: req.userId
      });

      // Return 202 Accepted with job information
      res.status(202).json({
        success: true,
        message: 'Bulk insert job created successfully',
        data: {
          jobId,
          status: 'PENDING',
          message: 'Your bulk insert job has been queued for processing. You will receive an email notification when it completes.'
        }
      });

    } catch (serviceError: any) {
      log.error('Error creating bulk insert job:', serviceError);

      // Add context to service errors and let them bubble up
      if (serviceError instanceof ValidationError || serviceError instanceof BadRequestError || serviceError instanceof DatabaseError) {
        throw serviceError.withContext(context.toLogObject());
      }

      // Transform unexpected errors
      throw new InternalError(
        serviceError instanceof Error ? serviceError.message : 'Bulk insert job creation failed',
        'BULK_INSERT_001',
        false
      ).withContext(context.toLogObject());
    }
  }

  /**
   * Get bulk insert job status and progress information
   * @param req - Express request object with authenticated user, organization context, and job ID
   * @param res - Express response object
   */
  static async getJobStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    const jobId = req.params.jobId;
    
    // Create error context for enhanced error logging
    const context = new ErrorContext(req, {
      jobId
    });
    
    log.info('Getting job status for user:', { 
      jobId,
      userId: req.userId, 
      organizationId: req.organizationId 
    });

    // Validate organization context
    if (!req.organizationId) {
      throw new OrganizationError('Organization context is required', 'ORG_001')
        .withContext(context.toLogObject());
    }

    // Validate user ID
    if (!req.userId) {
      throw new BadRequestError(
        'User context is required',
        'AUTH_001',
        [{ field: 'user', message: 'User authentication is required' }]
      ).withContext(context.toLogObject());
    }

    try {
      // Get job status using the service
      const jobStatus = await ContactBulkInsertService.getJobStatus(jobId);

      // Verify job belongs to the user's organization
      if (jobStatus.orgId !== req.organizationId) {
        throw new NotFoundError(
          `Job with ID ${jobId} not found`,
          'JOB_001',
          [{ field: 'jobId', message: 'Job not found or access denied' }]
        ).withContext(context.toLogObject());
      }

      log.info('Job status retrieved successfully:', {
        jobId,
        status: jobStatus.status,
        orgId: req.organizationId,
        userId: req.userId
      });

      // Return 200 response with job status
      res.status(200).json({
        success: true,
        data: jobStatus
      });

    } catch (serviceError: any) {
      log.error('Error getting job status:', serviceError);

      // Add context to service errors and let them bubble up
      if (serviceError instanceof NotFoundError || serviceError instanceof DatabaseError || serviceError instanceof BadRequestError) {
        throw serviceError.withContext(context.toLogObject());
      }

      // Transform unexpected errors
      throw new InternalError(
        serviceError instanceof Error ? serviceError.message : 'Failed to get job status',
        'JOB_001',
        false
      ).withContext(context.toLogObject());
    }
  }


}