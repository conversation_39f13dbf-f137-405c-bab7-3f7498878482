import express, { Request, Response, NextFunction } from 'express';

const router = express.Router();

/* GET home page. */
router.get('/', function(req: Request, res: Response, next: NextFunction) {
    res.json({
        message: 'Welcome to Shoutute Engage Core API',
        title: 'Shoutute Engage Core API',
        version: '1.0.0',
        endpoints: {
            health: '/api/engagecoreservice/health',
            docs: '/api/engagecoreservice/docs',
            contacts: '/api/engagecoreservice/contacts'
        }
    });
});

export default router;