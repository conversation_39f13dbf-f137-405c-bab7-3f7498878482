import express, {Response, NextFunction } from "express";
import { supabaseAuthMiddleware, AuthenticatedRequest } from "../lib/middlewares/supabase.authorizer.middleware";
import { ContactsValidator } from "../validators/ContactsValidator";
import { <PERSON>s<PERSON>and<PERSON> } from "../handlers/ContactsHandler";
import { FileValidator } from "../validators/FileValidator";

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     BulkInsertJobResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: Whether the request was successful
 *           example: true
 *         message:
 *           type: string
 *           description: Human-readable message about the operation
 *           example: "Bulk insert job created successfully"
 *         data:
 *           type: object
 *           properties:
 *             jobId:
 *               type: string
 *               description: Unique identifier for the bulk insert job
 *               example: "job_1234567890abcdef"
 *             status:
 *               type: string
 *               enum: [PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED]
 *               description: Current status of the job
 *               example: "PENDING"
 *             message:
 *               type: string
 *               description: Additional information about the job
 *               example: "Your bulk insert job has been queued for processing. You will receive an email notification when it completes."
 *       required:
 *         - success
 *         - message
 *         - data
 *     
 *     JobStatusResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: Whether the request was successful
 *           example: true
 *         data:
 *           type: object
 *           properties:
 *             jobId:
 *               type: string
 *               description: Unique identifier of the job
 *               example: "job_1234567890abcdef"
 *             status:
 *               type: string
 *               enum: [PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED]
 *               description: Current status of the job
 *               example: "PROCESSING"
 *             jobType:
 *               type: string
 *               enum: [CONTACT_BULK_INSERT, EMAIL]
 *               description: Type of job
 *               example: "CONTACT_BULK_INSERT"
 *             orgId:
 *               type: string
 *               description: Organization identifier
 *               example: "org_123456789"
 *             createdBy:
 *               type: string
 *               description: User ID who created the job
 *               example: "user_123456789"
 *             progress:
 *               $ref: '#/components/schemas/JobProgress'
 *             errors:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/JobError'
 *               description: Array of errors encountered during processing
 *             result:
 *               $ref: '#/components/schemas/BulkInsertResult'
 *             createdAt:
 *               type: string
 *               format: date-time
 *               description: When the job was created
 *               example: "2023-12-01T10:00:00.000Z"
 *             updatedAt:
 *               type: string
 *               format: date-time
 *               description: When the job was last updated
 *               example: "2023-12-01T10:30:00.000Z"
 *             startedAt:
 *               type: string
 *               format: date-time
 *               description: When the job started processing
 *               example: "2023-12-01T10:05:00.000Z"
 *             completedAt:
 *               type: string
 *               format: date-time
 *               description: When the job completed (success or failure)
 *               example: "2023-12-01T10:35:00.000Z"
 *       required:
 *         - success
 *         - data
 *     
 *     JobProgress:
 *       type: object
 *       properties:
 *         totalItems:
 *           type: integer
 *           description: Total number of items to process
 *           example: 1000
 *         processedItems:
 *           type: integer
 *           description: Number of items processed so far
 *           example: 750
 *         successfulItems:
 *           type: integer
 *           description: Number of items processed successfully
 *           example: 720
 *         failedItems:
 *           type: integer
 *           description: Number of items that failed processing
 *           example: 30
 *         percentage:
 *           type: number
 *           description: Progress percentage (0-100)
 *           example: 75.0
 *         currentStep:
 *           type: string
 *           description: Current processing step
 *           example: "Processing contacts"
 *         estimatedCompletion:
 *           type: string
 *           format: date-time
 *           description: Estimated completion time
 *           example: "2023-12-01T11:30:00.000Z"
 *     
 *     JobError:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [VALIDATION, DUPLICATE, SYSTEM, DATABASE, NETWORK, TIMEOUT]
 *           description: Type of error
 *           example: "VALIDATION"
 *         message:
 *           type: string
 *           description: Error message
 *           example: "Invalid email format"
 *         errorCode:
 *           type: string
 *           description: Application-specific error code
 *           example: "VALIDATION_001"
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: When the error occurred
 *           example: "2023-12-01T10:15:00.000Z"
 *         rowNumber:
 *           type: integer
 *           description: Row number where error occurred (for CSV processing)
 *           example: 25
 *         details:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 description: Field name that caused the error
 *                 example: "email"
 *               message:
 *                 type: string
 *                 description: Specific field error message
 *                 example: "Email must be a valid email address"
 *           description: Detailed error information for specific fields
 *       required:
 *         - type
 *         - message
 *         - errorCode
 *         - timestamp
 *     
 *     BulkInsertResult:
 *       type: object
 *       properties:
 *         totalRows:
 *           type: integer
 *           description: Total number of rows processed
 *           example: 1000
 *         successfulRows:
 *           type: integer
 *           description: Number of rows processed successfully
 *           example: 950
 *         failedRows:
 *           type: integer
 *           description: Number of rows that failed processing
 *           example: 50
 *         processingTime:
 *           type: integer
 *           description: Total processing time in milliseconds
 *           example: 1800000
 *         errorReportPath:
 *           type: string
 *           description: Path to error report file (if errors occurred)
 *           example: "/tmp/error-report-job_1234567890abcdef.csv"
 *         errors:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ContactProcessingError'
 *           description: Array of contact processing errors
 *       description: Final processing results (available when job is completed)
 *     
 *     ContactProcessingError:
 *       type: object
 *       properties:
 *         rowNumber:
 *           type: integer
 *           description: Row number in the CSV file where error occurred
 *           example: 25
 *         contactData:
 *           type: object
 *           description: Original contact data from the CSV row
 *           example:
 *             name: "John Doe"
 *             email: "invalid-email"
 *             phone: "+1234567890"
 *         error:
 *           $ref: '#/components/schemas/JobError'
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: When the error occurred
 *           example: "2023-12-01T10:15:00.000Z"
 *       required:
 *         - rowNumber
 *         - contactData
 *         - error
 *         - timestamp
 *     
 *     PaginatedContactsResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ContactResponse'
 *           description: Array of contact objects
 *         pagination:
 *           type: object
 *           properties:
 *             total_count:
 *               type: integer
 *               description: Total number of contacts matching the query
 *               example: 42
 *             page:
 *               type: integer
 *               description: Current page number
 *               example: 1
 *             page_size:
 *               type: integer
 *               description: Number of contacts per page
 *               example: 20
 *             total_pages:
 *               type: integer
 *               description: Total number of pages
 *               example: 3
 *             has_next_page:
 *               type: boolean
 *               description: Whether there is a next page
 *               example: true
 *             has_prev_page:
 *               type: boolean
 *               description: Whether there is a previous page
 *               example: false
 *           required:
 *             - total_count
 *             - page
 *             - page_size
 *             - total_pages
 *             - has_next_page
 *             - has_prev_page
 *       required:
 *         - data
 *         - pagination
 *     
 *     ContactTag:
 *       type: object
 *       required:
 *         - tag_id
 *         - tag_name
 *       properties:
 *         tag_id:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *           description: MongoDB ObjectId of the tag
 *           example: "507f1f77bcf86cd799439011"
 *         tag_name:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: Name of the tag
 *           example: "VIP Customer"
 *     
 *     ContactCreateRequest:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - phone
 *       properties:
 *         name:
 *           type: string
 *           minLength: 1
 *           maxLength: 255
 *           description: Full name of the contact
 *           example: "John Doe"
 *         email:
 *           type: string
 *           format: email
 *           description: Email address of the contact
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           pattern: '^[+]?[0-9\s\-\(\)]+$'
 *           minLength: 7
 *           maxLength: 20
 *           description: Phone number of the contact
 *           example: "******-123-4567"
 *         country:
 *           type: string
 *           maxLength: 100
 *           description: Country name
 *           example: "United States"
 *         country_code:
 *           type: string
 *           pattern: '^[A-Z]{2}$'
 *           description: ISO 3166-1 alpha-2 country code
 *           example: "US"
 *         avatar_url:
 *           type: string
 *           format: uri
 *           description: URL to the contact's avatar image
 *           example: "https://example.com/avatar.jpg"
 *         tags:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ContactTag'
 *           description: Array of tags associated with the contact
 *         additional_fields:
 *           type: object
 *           additionalProperties:
 *             oneOf:
 *               - type: string
 *               - type: number
 *               - type: boolean
 *           description: Additional custom fields for the contact
 *           example:
 *             company: "Acme Corp"
 *             age: 30
 *             is_premium: true
 *         status:
 *           type: string
 *           enum: [active, archived, deleted, forgotten]
 *           default: active
 *           description: Status of the contact
 *     
 *     ContactResponse:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier of the contact
 *           example: "507f1f77bcf86cd799439011"
 *         org_id:
 *           type: string
 *           description: Organization identifier
 *           example: "org_123456789"
 *         created_by:
 *           type: string
 *           description: User ID who created the contact
 *           example: "user_123456789"
 *         name:
 *           type: string
 *           description: Full name of the contact
 *           example: "John Doe"
 *         email:
 *           type: string
 *           format: email
 *           description: Email address of the contact
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           description: Phone number of the contact
 *           example: "******-123-4567"
 *         country:
 *           type: string
 *           description: Country name
 *           example: "United States"
 *         country_code:
 *           type: string
 *           description: ISO 3166-1 alpha-2 country code
 *           example: "US"
 *         avatar_url:
 *           type: string
 *           format: uri
 *           description: URL to the contact's avatar image
 *           example: "https://example.com/avatar.jpg"
 *         tags:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ContactTag'
 *           description: Array of tags associated with the contact
 *         additional_fields:
 *           type: object
 *           additionalProperties:
 *             oneOf:
 *               - type: string
 *               - type: number
 *               - type: boolean
 *           description: Additional custom fields for the contact
 *         status:
 *           type: string
 *           enum: [active, archived, deleted, forgotten]
 *           description: Status of the contact
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the contact was created
 *           example: "2023-12-01T10:30:00.000Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the contact was last updated
 *           example: "2023-12-01T10:30:00.000Z"
 *     
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Human-readable error message
 *           example: "Validation failed"
 *         errorCode:
 *           type: string
 *           description: Application-specific error code
 *           example: "VALIDATION_ERROR"
 *         details:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 description: Field name that failed validation
 *                 example: "email"
 *               message:
 *                 type: string
 *                 description: Specific validation error message
 *                 example: "Email must be a valid email address"
 *           description: Detailed validation errors (for validation failures)
 *     
 *     ErrorCodes:
 *       type: object
 *       description: |
 *         Comprehensive list of error codes used throughout the contacts API.
 *         Error codes are categorized by functionality for easier identification and handling.
 *         
 *         **Authentication Errors:**
 *         - AUTH_001: Invalid authentication token
 *         - AUTH_002: Authentication token expired  
 *         - AUTH_003: Invalid or expired authentication token
 *         - UNAUTHORIZED_001: Unauthorized access
 *         - FORBIDDEN_001: Forbidden access
 *         
 *         **Contact Operation Errors:**
 *         - CONTACT_001: Contact with email already exists in organization
 *         - CONTACT_002: Invalid contact ID format
 *         - CONTACT_003: Contact not found in organization
 *         
 *         **File Validation Errors:**
 *         - FILE_001 (INVALID_EXTENSION): Invalid file extension
 *         - FILE_002 (FILE_TOO_LARGE): File size exceeds maximum limit
 *         - FILE_003 (TOO_MANY_ROWS): File contains too many rows
 *         - FILE_004 (MULTIPLE_SHEETS): Multiple sheets not supported
 *         - FILE_005 (NO_HEADERS): Missing required headers
 *         - FILE_006 (PROCESSING_ERROR): File processing error
 *         
 *         **Job Management Errors:**
 *         - JOB_001: Job not found or access denied
 *         - JOB_002: Job creation failed
 *         - JOB_003: Job processing failed
 *         - JOB_004: Job update failed
 *         - JOB_005: Job cancellation failed
 *         
 *         **Bulk Insert Errors:**
 *         - BULK_INSERT_001: Bulk insert operation failed
 *         - BULK_INSERT_002: Bulk insert file processing error
 *         - BULK_INSERT_003: Bulk insert validation error
 *         
 *         **Email Operation Errors:**
 *         - EMAIL_QUEUE_001: Email queue operation failed
 *         - EMAIL_QUEUE_002: Email send failed
 *         - EMAIL_QUEUE_003: Email template error
 *         
 *         **Validation Errors:**
 *         - VALIDATION_001: General validation failed
 *         - VALIDATION_ERROR: Request validation failed
 *         
 *         **System Errors:**
 *         - DB_001: Database operation failed
 *         - INTERNAL_001: Internal server error
 *         - ORG_001: Organization context is required
 *         - ORG_002: Organization-related error
 *         - DUPLICATE_001: Resource already exists
 *         - NOT_FOUND_001: Resource not found
 *         - BAD_REQUEST_001: Bad request
 */

/**
 * @swagger
 * /contacts:
 *   get:
 *     summary: Get contacts with pagination, filtering, and search
 *     description: |
 *       Retrieves a paginated list of contacts with advanced filtering capabilities.
 *       Supports MongoDB query-based filtering, powerful search functionality for email, mobile number, and name fields, and flexible sorting options.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     parameters:
 *       # Pagination Parameters (Required)
 *       - in: query
 *         name: page
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination (required)
 *       - in: query
 *         name: page_size
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of contacts per page (required, max 100)
 *       
 *       # Sorting Parameters
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           enum: [name, email, phone, created_at, updated_at]
 *           default: created_at
 *         description: Field to sort contacts by
 *       - in: query
 *         name: sort_direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort direction (ascending or descending)
 *       
 *       # Search Parameter
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           minLength: 1
 *         description: |
 *           Search term for name, email, or phone number.
 *           Performs case-insensitive search with name matches prioritized.
 *           Supports partial matching for email and phone.
 *       
 *       # MongoDB Query for Advanced Filtering
 *       - in: query
 *         name: contactFilterQuery
 *         schema:
 *           type: string
 *         description: |
 *           JSON string of MongoDB query for advanced filtering.
 *           Supports all MongoDB query operators and complex nested conditions.
 *           
 *           Example structure:
 *           ```json
 *           {
 *             "$and": [
 *               {
 *                 "firstName": {
 *                   "$regex": "^Stev"
 *                 }
 *               },
 *               {
 *                 "lastName": {
 *                   "$in": ["Vai", "Vaughan"]
 *                 }
 *               },
 *               {
 *                 "age": {
 *                   "$gt": "28"
 *                 }
 *               },
 *               {
 *                 "$or": [
 *                   {
 *                     "isMusician": true
 *                   },
 *                   {
 *                     "instrument": "Guitar"
 *                   }
 *                 ]
 *               },
 *               {
 *                 "$eq": ["$groupedField1", "$groupedField4"]
 *               },
 *               {
 *                 "birthdate": {
 *                   "$gte": "1954-10-03",
 *                   "$lte": "1960-06-06"
 *                 }
 *               }
 *             ]
 *           }
 *           ```
 *           
 *           Commonly used MongoDB operators:
 *           - $eq: Equals
 *           - $ne: Not equal
 *           - $gt: Greater than
 *           - $lt: Less than
 *           - $gte: Greater than or equal
 *           - $lte: Less than or equal
 *           - $in: Value is in array
 *           - $nin: Value is not in array
 *           - $regex: Regular expression match
 *           - $exists: Field exists check
 *           - $and: Logical AND
 *           - $or: Logical OR
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedContactsResponse'
 *             examples:
 *               basic_pagination:
 *                 summary: Basic pagination example
 *                 value:
 *                   data:
 *                     - _id: "507f1f77bcf86cd799439013"
 *                       org_id: "org_123456789"
 *                       created_by: "user_123456789"
 *                       name: "John Doe"
 *                       email: "<EMAIL>"
 *                       phone: "******-123-4567"
 *                       country: "United States"
 *                       country_code: "US"
 *                       avatar_url: "https://example.com/avatar.jpg"
 *                       tags:
 *                         - tag_id: "507f1f77bcf86cd799439011"
 *                           tag_name: "VIP Customer"
 *                       additional_fields:
 *                         company: "Acme Corp"
 *                         age: 30
 *                         is_premium: true
 *                       status: "active"
 *                       created_at: "2023-12-01T10:30:00.000Z"
 *                       updated_at: "2023-12-01T10:30:00.000Z"
 *                     - _id: "507f1f77bcf86cd799439014"
 *                       org_id: "org_123456789"
 *                       created_by: "user_123456789"
 *                       name: "Jane Smith"
 *                       email: "<EMAIL>"
 *                       phone: "******-987-6543"
 *                       status: "active"
 *                       tags: []
 *                       additional_fields: {}
 *                       created_at: "2023-12-02T14:45:00.000Z"
 *                       updated_at: "2023-12-02T14:45:00.000Z"
 *                   pagination:
 *                     total_count: 42
 *                     page: 1
 *                     page_size: 20
 *                     total_pages: 3
 *                     has_next_page: true
 *                     has_prev_page: false
 *               filtered_search:
 *                 summary: Filtered search example
 *                 value:
 *                   data:
 *                     - _id: "507f1f77bcf86cd799439013"
 *                       org_id: "org_123456789"
 *                       created_by: "user_123456789"
 *                       name: "John Doe"
 *                       email: "<EMAIL>"
 *                       phone: "******-123-4567"
 *                       country: "United States"
 *                       country_code: "US"
 *                       tags:
 *                         - tag_id: "507f1f77bcf86cd799439011"
 *                           tag_name: "VIP Customer"
 *                       additional_fields:
 *                         company: "Acme Corp"
 *                       status: "active"
 *                       created_at: "2023-12-01T10:30:00.000Z"
 *                       updated_at: "2023-12-01T10:30:00.000Z"
 *                   pagination:
 *                     total_count: 1
 *                     page: 1
 *                     page_size: 20
 *                     total_pages: 1
 *                     has_next_page: false
 *                     has_prev_page: false
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               invalid_pagination:
 *                 summary: Invalid pagination parameters
 *                 value:
 *                   error: "Invalid query parameters"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "page"
 *                       message: "Page must be at least 1"
 *                     - field: "page_size"
 *                       message: "Page size cannot exceed 100"
 *               invalid_filter:
 *                 summary: Invalid filter JSON
 *                 value:
 *                   error: "Invalid query parameters"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "filter"
 *                       message: "Filter must be a valid JSON string"
 *               invalid_react_query:
 *                 summary: Invalid react-query parameters
 *                 value:
 *                   error: "Invalid query parameters"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "filterField"
 *                       message: "Filter field is required when filter value is provided"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal server error"
 *               errorCode: "INTERNAL_001"
 */
router.get('/', supabaseAuthMiddleware, ContactsValidator.validateGetContactsMiddleware,
    async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.getContacts(req, res);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /contacts:
 *   post:
 *     summary: Create a new contact
 *     description: Creates a new contact within the authenticated user's organization. The contact will be associated with the user's organization automatically based on their authentication context.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ContactCreateRequest'
 *           examples:
 *             basic_contact:
 *               summary: Basic contact with required fields only
 *               value:
 *                 name: "Jane Smith"
 *                 email: "<EMAIL>"
 *                 phone: "******-987-6543"
 *             full_contact:
 *               summary: Complete contact with all optional fields
 *               value:
 *                 name: "John Doe"
 *                 email: "<EMAIL>"
 *                 phone: "******-123-4567"
 *                 country: "United States"
 *                 country_code: "US"
 *                 avatar_url: "https://example.com/avatar.jpg"
 *                 tags:
 *                   - tag_id: "507f1f77bcf86cd799439011"
 *                     tag_name: "VIP Customer"
 *                   - tag_id: "507f1f77bcf86cd799439012"
 *                     tag_name: "Newsletter Subscriber"
 *                 additional_fields:
 *                   company: "Acme Corp"
 *                   department: "Engineering"
 *                   age: 30
 *                   is_premium: true
 *                 status: "active"
 *     responses:
 *       201:
 *         description: Contact created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ContactResponse'
 *             example:
 *               _id: "507f1f77bcf86cd799439013"
 *               org_id: "org_123456789"
 *               created_by: "user_123456789"
 *               name: "John Doe"
 *               email: "<EMAIL>"
 *               phone: "******-123-4567"
 *               country: "United States"
 *               country_code: "US"
 *               avatar_url: "https://example.com/avatar.jpg"
 *               tags:
 *                 - tag_id: "507f1f77bcf86cd799439011"
 *                   tag_name: "VIP Customer"
 *               additional_fields:
 *                 company: "Acme Corp"
 *                 age: 30
 *                 is_premium: true
 *               status: "active"
 *               created_at: "2023-12-01T10:30:00.000Z"
 *               updated_at: "2023-12-01T10:30:00.000Z"
 *       400:
 *         description: Invalid request data or missing organization context
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               validation_error:
 *                 summary: Validation error with field details
 *                 value:
 *                   error: "Validation failed"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "email"
 *                       message: "Email must be a valid email address"
 *                     - field: "phone"
 *                       message: "Phone number is required"
 *               missing_organization:
 *                 summary: Missing organization context
 *                 value:
 *                   error: "Organization context is required"
 *                   errorCode: "ORG_001"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       409:
 *         description: Conflict - Contact with the same email already exists in the organization
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Contact <NAME_EMAIL> already exists in this organization"
 *               errorCode: "CONTACT_001"
 *       422:
 *         description: Unprocessable Entity - Business logic validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Validation failed: Invalid tag reference"
 *               errorCode: "CONTACT_002"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal server error"
 *               errorCode: "INTERNAL_001"
 */
router.post('/', supabaseAuthMiddleware, ContactsValidator.validateCreateContactMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.createContact(req, res);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /contacts/{id}:
 *   get:
 *     summary: Get a contact by ID
 *     description: Retrieves a specific contact by its ID. The contact must belong to the authenticated user's organization.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *         description: MongoDB ObjectId of the contact to retrieve
 *     responses:
 *       200:
 *         description: Contact retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ContactResponse'
 *       400:
 *         description: Invalid contact ID format
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid contact ID format"
 *               errorCode: "CONTACT_002"
 *               details:
 *                 - field: "id"
 *                   message: "Contact ID must be a valid MongoDB ObjectId (24 hexadecimal characters)"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       404:
 *         description: Contact not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Contact with ID 507f1f77bcf86cd799439011 not found in organization org_123456789"
 *               errorCode: "CONTACT_003"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal server error"
 *               errorCode: "INTERNAL_001"
 */
router.get('/:id', supabaseAuthMiddleware, ContactsValidator.validateGetContactByIdMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.getContactById(req, res);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /contacts/csv-headers:
 *   post:
 *     summary: Extract headers from CSV/Excel file
 *     description: Uploads a CSV or Excel file and extracts headers for validation against contact schema
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV or Excel file to upload
 *     responses:
 *       200:
 *         description: Headers extracted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     csvHeaders:
 *                       type: array
 *                       items:
 *                         type: string
 *                     contactSchema:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           type:
 *                             type: string
 *                           required:
 *                             type: boolean
 *                           description:
 *                             type: string
 *                     fileInfo:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                         size:
 *                           type: number
 *                         rowCount:
 *                           type: number
 *       400:
 *         description: Invalid file or validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/csv-headers',
    supabaseAuthMiddleware,
    FileValidator.getUploadMiddleware(),
    FileValidator.handleErrors,
    FileValidator.validateFilePresence,
    ContactsHandler.extractCSVHeaders
);

/**
 * @swagger
 * /contacts/bulk-insert:
 *   post:
 *     summary: Bulk insert contacts from CSV file
 *     description: |
 *       Uploads a CSV file and creates a background job to process and insert contacts in bulk.
 *       Returns a job ID immediately for tracking the processing status.
 *       The job will process contacts row by row, validate each contact, and send email notifications upon completion.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV file containing contact data to import
 *               headerMapping:
 *                 type: string
 *                 description: |
 *                   JSON string containing header mapping configuration.
 *                   Maps CSV column headers to contact model attributes.
 *                   Example: {"First Name": "name", "Email Address": "email", "Phone Number": "phone"}
 *                 example: '{"First Name": "name", "Email Address": "email", "Phone Number": "phone", "Country": "country"}'
 *     responses:
 *       202:
 *         description: Bulk insert job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Bulk insert job created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobId:
 *                       type: string
 *                       description: Unique identifier for the bulk insert job
 *                       example: "job_1234567890abcdef"
 *                     status:
 *                       type: string
 *                       enum: [PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED]
 *                       example: "PENDING"
 *                     message:
 *                       type: string
 *                       example: "Your bulk insert job has been queued for processing. You will receive an email notification when it completes."
 *             example:
 *               success: true
 *               message: "Bulk insert job created successfully"
 *               data:
 *                 jobId: "job_1234567890abcdef"
 *                 status: "PENDING"
 *                 message: "Your bulk insert job has been queued for processing. You will receive an email notification when it completes."
 *       400:
 *         description: Invalid request - file validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               invalid_file_type:
 *                 summary: Invalid file type
 *                 value:
 *                   error: "Invalid file type. Allowed types: .csv, .xlsx"
 *                   errorCode: "INVALID_EXTENSION"
 *                   details:
 *                     - field: "file"
 *                       message: "File extension .txt is not supported"
 *               file_too_large:
 *                 summary: File size exceeds limit
 *                 value:
 *                   error: "File size exceeds maximum limit of 10MB"
 *                   errorCode: "FILE_TOO_LARGE"
 *                   details:
 *                     - field: "file"
 *                       message: "File size 15.5MB exceeds limit"
 *               missing_headers:
 *                 summary: Missing required headers
 *                 value:
 *                   error: "Missing required headers: name, email"
 *                   errorCode: "NO_HEADERS"
 *                   details:
 *                     - field: "name"
 *                       message: "Required header 'name' is missing"
 *                     - field: "email"
 *                       message: "Required header 'email' is missing"
 *               too_many_rows:
 *                 summary: File contains too many rows
 *                 value:
 *                   error: "File contains too many rows. Maximum allowed: 10000"
 *                   errorCode: "TOO_MANY_ROWS"
 *                   details:
 *                     - field: "file"
 *                       message: "File contains 15000 rows"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       413:
 *         description: Payload too large - File size exceeds server limits
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "File too large. Maximum size is 10MB"
 *               errorCode: "FILE_TOO_LARGE"
 *       422:
 *         description: Unprocessable Entity - File structure validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "File structure validation failed"
 *               errorCode: "PROCESSING_ERROR"
 *               details:
 *                 - field: "file"
 *                   message: "CSV file contains invalid structure"
 *       500:
 *         description: Internal server error - Job creation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Failed to create bulk insert job"
 *               errorCode: "JOB_002"
 */
router.post('/bulk-insert',
    supabaseAuthMiddleware,
    FileValidator.getUploadMiddleware(),
    FileValidator.handleErrors,
    FileValidator.validateFilePresence,
    ContactsValidator.validateBulkInsertMiddleware,
    async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.bulkInsertContacts(req, res);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /contacts/bulk-insert/{jobId}/status:
 *   get:
 *     summary: Get bulk insert job status and progress
 *     description: |
 *       Retrieves the current status and progress information for a bulk insert job.
 *       Provides real-time updates on job processing, including progress percentage,
 *       processed items count, error details, and completion status.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique identifier of the bulk insert job
 *         example: "job_1234567890abcdef"
 *     responses:
 *       200:
 *         description: Job status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobId:
 *                       type: string
 *                       description: Unique identifier of the job
 *                       example: "job_1234567890abcdef"
 *                     status:
 *                       type: string
 *                       enum: [PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED]
 *                       description: Current status of the job
 *                       example: "PROCESSING"
 *                     orgId:
 *                       type: string
 *                       description: Organization identifier
 *                       example: "org_123456789"
 *                     createdBy:
 *                       type: string
 *                       description: User ID who created the job
 *                       example: "user_123456789"
 *                     progress:
 *                       type: object
 *                       properties:
 *                         totalItems:
 *                           type: integer
 *                           description: Total number of items to process
 *                           example: 1000
 *                         processedItems:
 *                           type: integer
 *                           description: Number of items processed so far
 *                           example: 750
 *                         successfulItems:
 *                           type: integer
 *                           description: Number of items processed successfully
 *                           example: 720
 *                         failedItems:
 *                           type: integer
 *                           description: Number of items that failed processing
 *                           example: 30
 *                         percentage:
 *                           type: number
 *                           description: Progress percentage (0-100)
 *                           example: 75.0
 *                         currentStep:
 *                           type: string
 *                           description: Current processing step
 *                           example: "Processing contacts"
 *                         estimatedCompletion:
 *                           type: string
 *                           format: date-time
 *                           description: Estimated completion time
 *                           example: "2023-12-01T11:30:00.000Z"
 *                     errors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                             enum: [VALIDATION, DUPLICATE, SYSTEM, DATABASE]
 *                             description: Type of error
 *                           message:
 *                             type: string
 *                             description: Error message
 *                           errorCode:
 *                             type: string
 *                             description: Application-specific error code
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                             description: When the error occurred
 *                           rowNumber:
 *                             type: integer
 *                             description: Row number where error occurred (for CSV processing)
 *                       description: Array of errors encountered during processing
 *                     result:
 *                       type: object
 *                       properties:
 *                         totalRows:
 *                           type: integer
 *                           description: Total number of rows processed
 *                         successfulRows:
 *                           type: integer
 *                           description: Number of rows processed successfully
 *                         failedRows:
 *                           type: integer
 *                           description: Number of rows that failed processing
 *                         processingTime:
 *                           type: integer
 *                           description: Total processing time in milliseconds
 *                         errorReportPath:
 *                           type: string
 *                           description: Path to error report file (if errors occurred)
 *                       description: Final processing results (available when job is completed)
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       description: When the job was created
 *                       example: "2023-12-01T10:00:00.000Z"
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       description: When the job was last updated
 *                       example: "2023-12-01T10:30:00.000Z"
 *                     startedAt:
 *                       type: string
 *                       format: date-time
 *                       description: When the job started processing
 *                       example: "2023-12-01T10:05:00.000Z"
 *                     completedAt:
 *                       type: string
 *                       format: date-time
 *                       description: When the job completed (success or failure)
 *                       example: "2023-12-01T10:35:00.000Z"
 *             examples:
 *               pending_job:
 *                 summary: Pending job status
 *                 value:
 *                   success: true
 *                   data:
 *                     jobId: "job_1234567890abcdef"
 *                     status: "PENDING"
 *                     orgId: "org_123456789"
 *                     createdBy: "user_123456789"
 *                     progress:
 *                       totalItems: 0
 *                       processedItems: 0
 *                       successfulItems: 0
 *                       failedItems: 0
 *                       percentage: 0
 *                     errors: []
 *                     createdAt: "2023-12-01T10:00:00.000Z"
 *                     updatedAt: "2023-12-01T10:00:00.000Z"
 *               processing_job:
 *                 summary: Job in progress
 *                 value:
 *                   success: true
 *                   data:
 *                     jobId: "job_1234567890abcdef"
 *                     status: "PROCESSING"
 *                     orgId: "org_123456789"
 *                     createdBy: "user_123456789"
 *                     progress:
 *                       totalItems: 1000
 *                       processedItems: 750
 *                       successfulItems: 720
 *                       failedItems: 30
 *                       percentage: 75.0
 *                       currentStep: "Processing contacts"
 *                       estimatedCompletion: "2023-12-01T11:30:00.000Z"
 *                     errors: []
 *                     createdAt: "2023-12-01T10:00:00.000Z"
 *                     updatedAt: "2023-12-01T10:30:00.000Z"
 *                     startedAt: "2023-12-01T10:05:00.000Z"
 *               completed_job:
 *                 summary: Completed job with results
 *                 value:
 *                   success: true
 *                   data:
 *                     jobId: "job_1234567890abcdef"
 *                     status: "COMPLETED"
 *                     orgId: "org_123456789"
 *                     createdBy: "user_123456789"
 *                     progress:
 *                       totalItems: 1000
 *                       processedItems: 1000
 *                       successfulItems: 950
 *                       failedItems: 50
 *                       percentage: 100
 *                     errors:
 *                       - type: "VALIDATION"
 *                         message: "Invalid email format"
 *                         errorCode: "VALIDATION_001"
 *                         timestamp: "2023-12-01T10:15:00.000Z"
 *                         rowNumber: 25
 *                     result:
 *                       totalRows: 1000
 *                       successfulRows: 950
 *                       failedRows: 50
 *                       processingTime: 1800000
 *                       errorReportPath: "/tmp/error-report-job_1234567890abcdef.csv"
 *                     createdAt: "2023-12-01T10:00:00.000Z"
 *                     updatedAt: "2023-12-01T10:35:00.000Z"
 *                     startedAt: "2023-12-01T10:05:00.000Z"
 *                     completedAt: "2023-12-01T10:35:00.000Z"
 *               failed_job:
 *                 summary: Failed job with error details
 *                 value:
 *                   success: true
 *                   data:
 *                     jobId: "job_1234567890abcdef"
 *                     status: "FAILED"
 *                     orgId: "org_123456789"
 *                     createdBy: "user_123456789"
 *                     progress:
 *                       totalItems: 1000
 *                       processedItems: 250
 *                       successfulItems: 200
 *                       failedItems: 50
 *                       percentage: 25
 *                     errors:
 *                       - type: "SYSTEM"
 *                         message: "Database connection failed"
 *                         errorCode: "DB_001"
 *                         timestamp: "2023-12-01T10:15:00.000Z"
 *                     createdAt: "2023-12-01T10:00:00.000Z"
 *                     updatedAt: "2023-12-01T10:15:00.000Z"
 *                     startedAt: "2023-12-01T10:05:00.000Z"
 *                     completedAt: "2023-12-01T10:15:00.000Z"
 *       404:
 *         description: Job not found or access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               job_not_found:
 *                 summary: Job not found
 *                 value:
 *                   error: "Job with ID job_1234567890abcdef not found"
 *                   errorCode: "JOB_001"
 *                   details:
 *                     - field: "jobId"
 *                       message: "Job not found or access denied"
 *               access_denied:
 *                 summary: Job belongs to different organization
 *                 value:
 *                   error: "Job with ID job_1234567890abcdef not found"
 *                   errorCode: "JOB_001"
 *                   details:
 *                     - field: "jobId"
 *                       message: "Job not found or access denied"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       500:
 *         description: Internal server error - Failed to retrieve job status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Failed to retrieve job status"
 *               errorCode: "DB_001"
 */
router.get('/bulk-insert/:jobId/status',
    supabaseAuthMiddleware,
    async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.getJobStatus(req, res);
        } catch (error) {
            next(error);
        }
    }
);

export default router;