import Queue, { Job } from 'bull';
import { CommonJobDAO } from '../../lib/db/dao/CommonJobDAO';
import { 
  JobType, 
  JobStatus, 
  JOB_STATUS, 
  JOB_ERROR_CODES,
} from '../../lib/constant/job.constants';
import {
  QUEUE_EVENTS 
} from '../../lib/constant/queue.constants';
import { 
  JobError, 
  JobProgress, 
  BaseJobData,
  JobProcessor 
} from '../../types/job.types';
import { logger } from '../../lib/logger';
import config from '../../lib/config';
import { 
  DatabaseError, 
  InternalError,
  ValidationError,
  AppError
} from '../../lib/errors/error.type';
import { ErrorLogger } from '../../lib/utils/errorLogger';

import { QueueManager } from '../../queues/queue.manager';



/**
 * Abstract base class for all job processors
 * Provides universal job lifecycle management and error handling
 * All job processors must extend this class and implement the abstract methods
 * 
 * Uses QueueManager to get queue instances instead of creating new ones
 * This prevents duplication of queue instances and ensures consistent queue management
 */
export abstract class BaseJobProcessor<T extends BaseJobData = BaseJobData> implements JobProcessor<T> {
  /**
   * Queue name - must be implemented by subclasses
   */
  public abstract readonly queueName: string;

  /**
   * Job type - must be implemented by subclasses
   */
  public abstract readonly jobType: JobType;

  /**
   * Bull MQ queue instance
   */
  protected queue: Queue.Queue;

  /**
   * Logger instance
   */
  protected log = logger(config.logger);

  /**
   * Whether the processor is currently running
   */
  private isRunning = false;

  /**
   * Constructor - gets the queue from QueueManager instead of creating a new one
   * @param queueName - The name of the queue (passed by subclass)
   */
  constructor(queueName: string) {
    try {
      // Get the queue from QueueManager instead of creating a new one
      const queueManager = QueueManager.getInstance();
      this.queue = queueManager.getQueue(queueName);
      
      // Set up event handlers
      this.setupEventHandlers();
      
      this.log.info(`Initialized processor for queue: ${queueName}`);
    } catch (error) {
      this.log.error(`Failed to initialize processor for queue '${queueName}':`, error);
      throw new InternalError(
        `Failed to initialize processor for queue '${queueName}'`,
        JOB_ERROR_CODES.JOB_PROCESSING_FAILED
      );
    }
  }

  /**
   * Abstract method that subclasses must implement to process jobs
   * @param job - The Bull MQ job to process
   * @returns Promise with the job result
   */
  public abstract processJob(job: Job<T>): Promise<any>;

  /**
   * Start processing jobs from the queue
   * Sets up the job processor and begins listening for jobs
   */
  public startProcess(): void {
    const queueName = this.queue.name;
    
    if (this.isRunning) {
      this.log.warn(`Job processor for queue '${queueName}' is already running`);
      return;
    }

    try {
      // Set up job processing
      this.queue.process(async (job: Job<T>) => {
        return await this.handleJobExecution(job);
      });

      this.isRunning = true;
      this.log.info(`Job processor started for queue: ${queueName} (type: ${this.jobType})`);
    } catch (error) {
      this.log.error(`Failed to start job processor for queue '${queueName}':`, error);
      throw new InternalError(
        `Failed to start job processor for queue '${queueName}'`,
        JOB_ERROR_CODES.JOB_PROCESSING_FAILED
      );
    }
  }

  /**
   * Stop processing jobs without closing the queue
   * Gracefully shuts down the processor while leaving the queue instance active
   * The queue will be closed by QueueManager when all processors are being shut down
   */
  public async stopProcess(): Promise<void> {
    const queueName = this.queue.name;
    
    if (!this.isRunning) {
      this.log.warn(`Job processor for queue '${queueName}' is not running`);
      return;
    }

    try {
      this.isRunning = false;
      // Remove the queue.close() call since we're now using shared queue instances
      // The queue will be closed by QueueManager when all processors are being shut down
      await this.queue.pause(true); // Pause the queue with true to prevent new jobs from being processed
      this.log.info(`Job processor stopped for queue: ${queueName}`);
    } catch (error) {
      this.log.error(`Error stopping job processor for queue '${queueName}':`, error);
      throw new InternalError(
        `Failed to stop job processor for queue '${queueName}'`,
        JOB_ERROR_CODES.JOB_PROCESSING_FAILED
      );
    }
  }

  /**
   * Get the Bull MQ queue instance
   * @returns The Bull MQ queue
   */
  public getQueue(): Queue.Queue {
    return this.queue;
  }

  /**
   * Handle the complete job execution lifecycle
   * @param job - The Bull MQ job to execute
   * @returns Promise with the job result
   */
  private async handleJobExecution(job: Job<T>): Promise<any> {
    const jobId = job.data.jobId;
    
    // Create job context for error handling
    const jobContext = this.createJobContext(job);
    
    try {
      this.log.info(`Starting job execution: ${jobId} (type: ${this.jobType})`);

      // Update job status to PROCESSING
      await this.updateJobStatus(jobId, JOB_STATUS.PROCESSING);

      // Initialize progress tracking
      await this.updateJobProgress(jobId, {
        processedItems: 0,
        successfulItems: 0,
        failedItems: 0,
        percentage: 0,
        currentStep: 'Starting job processing',
        startedAt: new Date(),
        lastUpdated: new Date()
      });

      // Process the job using the subclass implementation
      const result = await this.processJob(job);

      // Mark job as completed
      await this.updateJobStatus(jobId, JOB_STATUS.COMPLETED);
      await CommonJobDAO.setJobResult(jobId, result);

      // Call completion handler
      await this.onJobCompleted(job, result);

      this.log.info(`Job completed successfully: ${jobId} (type: ${this.jobType})`);
      return result;

    } catch (error: unknown) {
      // Normalize error to AppError for consistent handling and logging
      const appError: AppError = error instanceof AppError
        ? error
        : new InternalError(
            error instanceof Error ? error.message : 'Job processing failed',
            JOB_ERROR_CODES.JOB_PROCESSING_FAILED,
            false
          );

      // Use ErrorLogger for consistent error logging
      const errorCtx = {
        ...jobContext,
        status: JOB_STATUS.FAILED,
        operation: 'job_failure'
      } as any;
      ErrorLogger.logJobError(appError, errorCtx);

      // Update job status to FAILED
      await this.updateJobStatus(jobId, JOB_STATUS.FAILED);

      // Add error to job record
      const jobError: JobError = {
        type: 'SYSTEM',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        errorCode: error instanceof Error && 'errorCode' in error 
          ? (error as any).errorCode 
          : JOB_ERROR_CODES.JOB_PROCESSING_FAILED,
        timestamp: new Date(),
        stackTrace: error instanceof Error ? error.stack : undefined
      };

      await this.addJobError(jobId, jobError);

      // Call failure handler
      await this.onJobFailed(job, error);

      // If it's an AppError, add context before re-throwing
      if (error instanceof InternalError || error instanceof DatabaseError || error instanceof ValidationError) {
        throw error.withContext(jobContext);
      }

      // Re-throw the error so Bull MQ can handle retries
      throw error;
    }
  }

  /**
   * Update job status in the database
   * @param jobId - The job ID to update
   * @param status - The new status
   */
  protected async updateJobStatus(jobId: string, status: JobStatus): Promise<void> {
    try {
      await CommonJobDAO.updateJobStatus(jobId, status);
      this.log.debug(`Job status updated: ${jobId} -> ${status}`);
    } catch (error) {
      this.log.error(`Failed to update job status for job ${jobId}:`, error);
      throw new DatabaseError(
        `Failed to update job status for job ${jobId}`,
        JOB_ERROR_CODES.JOB_UPDATE_FAILED
      );
    }
  }

  /**
   * Update job progress in the database
   * @param jobId - The job ID to update
   * @param progress - The progress data to update
   */
  protected async updateJobProgress(jobId: string, progress: Partial<JobProgress>): Promise<void> {
    try {
      await CommonJobDAO.updateJobProgress(jobId, progress);
      this.log.debug(`Job progress updated: ${jobId}`, progress);
    } catch (error) {
      this.log.error(`Failed to update job progress for job ${jobId}:`, error);
      throw new DatabaseError(
        `Failed to update job progress for job ${jobId}`,
        JOB_ERROR_CODES.JOB_UPDATE_FAILED
      );
    }
  }

  /**
   * Add an error to the job record
   * @param jobId - The job ID to add error to
   * @param error - The error to add
   */
  protected async addJobError(jobId: string, error: JobError): Promise<void> {
    try {
      await CommonJobDAO.addJobError(jobId, error);
      this.log.debug(`Error added to job: ${jobId}`, error);
    } catch (dbError) {
      this.log.error(`Failed to add error to job ${jobId}:`, dbError);
      // Don't throw here as we don't want to mask the original error
    }
  }

  /**
   * Hook called when a job completes successfully
   * Subclasses can override this to implement custom completion logic
   * @param job - The completed job
   * @param result - The job result
   */
  protected async onJobCompleted(job: Job<T>, result: any): Promise<void> {
    // Default implementation - subclasses can override
    this.log.info(`Job completed: ${job.data.jobId} (type: ${this.jobType})`);
  }

  /**
   * Hook called when a job fails
   * Subclasses can override this to implement custom failure logic
   * @param job - The failed job
   * @param error - The error that caused the failure
   */
  protected async onJobFailed(job: Job<T>, error: any): Promise<void> {
    // Default implementation - subclasses can override
    this.log.error(`Job failed: ${job.data.jobId} (type: ${this.jobType})`, error);
  }

  /**
   * Set up event handlers for the queue
   * Handles all Bull MQ queue events with proper logging
   */
  private setupEventHandlers(): void {
    const queueName = this.queue.name;
    
    // Job waiting in queue
    this.queue.on(QUEUE_EVENTS.WAITING, (jobId) => {
      this.log.debug(`Job ${jobId} waiting in queue '${queueName}'`);
    });

    // Job started processing
    this.queue.on(QUEUE_EVENTS.ACTIVE, (job) => {
      this.log.info(`Job ${job.id} started processing in queue '${queueName}'`);
    });

    // Job completed successfully
    this.queue.on(QUEUE_EVENTS.COMPLETED, (job, result) => {
      this.log.info(`Job ${job.id} completed in queue '${queueName}'`);
    });

    // Job failed
    this.queue.on(QUEUE_EVENTS.FAILED, (job, error) => {
      this.log.error(`Job ${job.id} failed in queue '${queueName}':`, error);
    });

    // Job progress update
    this.queue.on(QUEUE_EVENTS.PROGRESS, (job, progress) => {
      this.log.debug(`Job ${job.id} progress in queue '${queueName}':`, progress);
    });

    // Job stalled (taking too long)
    this.queue.on(QUEUE_EVENTS.STALLED, (job) => {
      this.log.warn(`Job ${job.id} stalled in queue '${queueName}'`);
    });

    // Job removed from queue
    this.queue.on(QUEUE_EVENTS.REMOVED, (job) => {
      this.log.info(`Job ${job.id} removed from queue '${queueName}'`);
    });

    // Queue paused
    this.queue.on(QUEUE_EVENTS.PAUSED, () => {
      this.log.info(`Queue '${queueName}' paused`);
    });

    // Queue resumed
    this.queue.on(QUEUE_EVENTS.RESUMED, () => {
      this.log.info(`Queue '${queueName}' resumed`);
    });

    // Queue cleaned
    this.queue.on(QUEUE_EVENTS.CLEANED, (jobs, type) => {
      this.log.info(`Cleaned ${jobs.length} ${type} jobs from queue '${queueName}'`);
    });

    // Queue error
    this.queue.on('error', (error) => {
      this.log.error(`Queue '${queueName}' error:`, error);
    });
  }

  /**
   * Validate job data before processing
   * Subclasses can override this to implement custom validation
   * @param job - The job to validate
   * @throws ValidationError if job data is invalid
   */
  protected validateJobData(job: Job<T>): void {
    if (!job.data) {
      throw new ValidationError(
        'Job data is missing',
        'JOB_VALIDATION_001',
        [{ field: 'data', message: 'Job data is required' }]
      );
    }

    if (!job.data.jobId) {
      throw new ValidationError(
        'Job ID is missing',
        'JOB_VALIDATION_002',
        [{ field: 'jobId', message: 'Job ID is required' }]
      );
    }

    if (!job.data.orgId) {
      throw new ValidationError(
        'Organization ID is missing',
        'JOB_VALIDATION_003',
        [{ field: 'orgId', message: 'Organization ID is required' }]
      );
    }

    if (!job.data.createdBy) {
      throw new ValidationError(
        'Created by user ID is missing',
        'JOB_VALIDATION_004',
        [{ field: 'createdBy', message: 'Created by user ID is required' }]
      );
    }
  }

  /**
   * Create job context for error handling
   * @param job - The Bull MQ job
   * @returns Job context object for error logging
   */
  protected createJobContext(job: Job<T>): Record<string, any> {
    const { jobId, orgId, createdBy } = job.data;
    
    return {
      jobId,
      jobType: this.jobType,
      userId: createdBy,
      organizationId: orgId,
      endpoint: `queue/${this.queueName}`,
      method: 'PROCESS',
      timestamp: new Date(),
      requestId: jobId
    };
  }

  /**
   * Calculate estimated completion time based on progress
   * @param totalItems - Total number of items to process
   * @param processedItems - Number of items already processed
   * @param startTime - When processing started
   * @returns Estimated completion time
   */
  protected calculateEstimatedCompletion(
    totalItems: number, 
    processedItems: number, 
    startTime: Date
  ): Date | undefined {
    if (processedItems === 0 || totalItems === 0) {
      return undefined;
    }

    const elapsedMs = Date.now() - startTime.getTime();
    const itemsPerMs = processedItems / elapsedMs;
    const remainingItems = totalItems - processedItems;
    const estimatedRemainingMs = remainingItems / itemsPerMs;

    return new Date(Date.now() + estimatedRemainingMs);
  }

  /**
   * Update job progress with automatic percentage calculation
   * @param jobId - The job ID to update
   * @param totalItems - Total number of items to process
   * @param processedItems - Number of items processed so far
   * @param successfulItems - Number of items processed successfully
   * @param failedItems - Number of items that failed
   * @param currentStep - Current processing step description
   * @param startTime - When processing started (for ETA calculation)
   */
  protected async updateJobProgressWithCalculation(
    jobId: string,
    totalItems: number,
    processedItems: number,
    successfulItems: number,
    failedItems: number,
    currentStep: string,
    startTime?: Date
  ): Promise<void> {
    const percentage = totalItems > 0 ? Math.round((processedItems / totalItems) * 100) : 0;
    const estimatedCompletion = startTime 
      ? this.calculateEstimatedCompletion(totalItems, processedItems, startTime)
      : undefined;

    await this.updateJobProgress(jobId, {
      totalItems,
      processedItems,
      successfulItems,
      failedItems,
      percentage,
      currentStep,
      estimatedCompletion,
      lastUpdated: new Date()
    });
  }
}