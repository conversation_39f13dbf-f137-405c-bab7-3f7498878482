import { Job } from 'bull';
import * as fs from 'fs';
import * as path from 'path';
import csvParser from 'csv-parser';
import { BaseJobProcessor } from './base.job.processor';
import { 
  ContactBulkInsertJobData, 
  ContactBulkInsertResult, 
  ContactProcessingError,
  CSVProcessingResult,
  ContactRowData
} from '../../types/contact.bulk.insert.types';
import { ContactStatus, CreateContactData } from '../../types/contact.types';
import { ContactDAO } from '../../lib/db/dao/ContactDAO';
import { 
  JOB_TYPE, 
  JOB_ERROR_CODES,
} from '../../lib/constant/job.constants';
import { QUEUE_NAMES } from '../../lib/constant/queue.constants';
import { 
  ValidationError, 
  DuplicateError, 
  DatabaseError, 
  InternalError 
} from '../../lib/errors/error.type';
import { ErrorLogger } from '../../lib/utils/errorLogger';
import { ContactsValidator } from '../../validators/ContactsValidator';


/**
 * Contact bulk insert processor that extends BaseJobProcessor
 * Handles CSV file processing with row-by-row contact processing and memory management
 */
export class ContactBulkInsertProcessor extends BaseJobProcessor<ContactBulkInsertJobData> {
  public readonly queueName = QUEUE_NAMES.CONTACT_BULK_INSERT;
  public readonly jobType = JOB_TYPE.CONTACT_BULK_INSERT;

  constructor() {
    super(QUEUE_NAMES.CONTACT_BULK_INSERT);
  }

  /**
   * Process the contact bulk insert job
   * @param job - The Bull MQ job containing contact bulk insert data
   * @returns Promise with the processing result
   */
  public async processJob(job: Job<ContactBulkInsertJobData>): Promise<ContactBulkInsertResult> {
    const { jobId, orgId, createdBy, filePath, fileName, fileSize, userEmail, headerMapping } = job.data;

    if (!jobId) {
        throw new InternalError('Missing jobId in job data', JOB_ERROR_CODES.BULK_INSERT_FAILED);
    }

      const startTime = new Date();

    this.log.info(`Starting contact bulk insert processing for job: ${jobId}, file: ${fileName}`, {
      hasClientHeaderMapping: !!headerMapping
    });

    // Validate job data
    this.validateJobData(job);

    // Validate file exists
    if (!fs.existsSync(filePath)) {
      const context = this.createJobContext(job);
      throw new InternalError(
        `CSV file not found: ${filePath}`,
        JOB_ERROR_CODES.BULK_INSERT_FILE_ERROR
      ).withContext(context);
    }

    try {
      // Process the CSV file
      const processingResult = await this.processCSVFile(filePath, orgId, createdBy, jobId, headerMapping);
      
      const endTime = new Date();
      const processingTime = endTime.getTime() - startTime.getTime();

      // Create the final result
      const result: ContactBulkInsertResult = {
        totalRows: processingResult.totalRows,
        successfulRows: processingResult.successfulRows,
        failedRows: processingResult.failedRows,
        processingTime,
        errors: processingResult.errors,
        startedAt: startTime,
        completedAt: endTime
      };

      // Generate error report if there are errors
      if (processingResult.errors.length > 0) {
        result.errorReportPath = await this.generateErrorReport(processingResult.errors, jobId);
      }

      this.log.info(`Contact bulk insert completed for job: ${jobId}. ` +
        `Total: ${result.totalRows}, Success: ${result.successfulRows}, Failed: ${result.failedRows}`);

      return result;

    } catch (error) {
      this.log.error(`Contact bulk insert failed for job: ${jobId}`, error);
      throw error;
    } finally {
      // Clean up the uploaded file
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          this.log.info(`Cleaned up uploaded file: ${filePath}`);
        }
      } catch (cleanupError) {
        this.log.warn(`Failed to clean up uploaded file: ${filePath}`, cleanupError);
      }
    }
  }

  /**
   * Process CSV file with streaming and row-by-row processing
   * @param filePath - Path to the CSV file
   * @param orgId - Organization ID
   * @param createdBy - User ID who created the job
   * @param jobId - Job ID for progress tracking
   * @param clientHeaderMapping - Optional client-provided header mapping configuration
   * @returns Promise with processing result
   */
  private async processCSVFile(
    filePath: string,
    orgId: string,
    createdBy: string,
    jobId: string,
    clientHeaderMapping?: { [key: string]: string }
  ): Promise<CSVProcessingResult> {
    return new Promise((resolve, reject) => {
      const result: CSVProcessingResult = {
        totalRows: 0,
        successfulRows: 0,
        failedRows: 0,
        errors: [],
        processingTime: 0
      };

      const startTime = Date.now();
      let rowNumber = 0;
      let lastProgressUpdate = Date.now();
      const PROGRESS_UPDATE_INTERVAL = 1000; // Update progress every 1 second
      let headerMapping: { [key: string]: string } | null = null;

      // Create readable stream from file
      const fileStream = fs.createReadStream(filePath);
      
      fileStream
        .pipe(csvParser())
        .on('headers', (headers: string[]) => {
          // Header mapping is required - should always be provided
          if (!clientHeaderMapping) {
            const error = new InternalError(
              'Header mapping is required but was not provided',
              JOB_ERROR_CODES.BULK_INSERT_FAILED
            );
            this.log.error('Missing required header mapping for bulk insert job', { jobId, headers });
            return reject(error);
          }
          
          headerMapping = clientHeaderMapping;
          this.log.info(`Using client-provided header mapping:`, { 
            originalHeaders: headers, 
            clientMapping: headerMapping,
            jobId 
          });
        })
        .on('data', async (row: ContactRowData) => {
          rowNumber++;
          result.totalRows++;

          try {
            // Process the contact row directly with header mapping
            const validationResult = await this.validateAndProcessContact(row, headerMapping!, orgId, createdBy, rowNumber, jobId);
            
            if (validationResult.isValid) {
              result.successfulRows++;
            } else {
              result.failedRows++;
              result.errors.push(...validationResult.errors);
            }

            // Update progress periodically to avoid overwhelming the database
            const now = Date.now();
            if (now - lastProgressUpdate >= PROGRESS_UPDATE_INTERVAL) {
              await this.updateJobProgressWithCalculation(
                jobId,
                result.totalRows, // We don't know total until we finish, so use current count
                result.totalRows,
                result.successfulRows,
                result.failedRows,
                `Processing row ${rowNumber}`,
                new Date(startTime)
              );
              lastProgressUpdate = now;
            }

          } catch (error) {
            // Create context for the error
            const context = {
              jobId,
              jobType: this.jobType,
              userId: createdBy,
              organizationId: orgId,
              fileName: path.basename(filePath),
              rowNumber,
              contactData: row,
              operation: 'contact_validation'
            };
            
            // Log the error with context
            ErrorLogger.logBulkInsertError(error, context as any);
            
            this.log.error(`Error processing row ${rowNumber}:`, error);
            result.failedRows++;
            
            // Create error entry for system errors
            const processingError: ContactProcessingError = {
              rowNumber,
              contactData: row,
              error: {
                type: 'SYSTEM',
                message: error instanceof Error ? error.message : 'Unknown error occurred',
                errorCode: JOB_ERROR_CODES.BULK_INSERT_FAILED
              },
              timestamp: new Date()
            };
            result.errors.push(processingError);
          }
        })
        .on('end', async () => {
          result.processingTime = Date.now() - startTime;
          
          // Final progress update
          await this.updateJobProgressWithCalculation(
            jobId,
            result.totalRows,
            result.totalRows,
            result.successfulRows,
            result.failedRows,
            'Processing completed',
            new Date(startTime)
          );

          this.log.info(`CSV processing completed. Total: ${result.totalRows}, ` +
            `Success: ${result.successfulRows}, Failed: ${result.failedRows}`);
          
          resolve(result);
        })
        .on('error', (error) => {
          this.log.error('Error reading CSV file:', error);
          
          // Create context for the error
          const context = {
            jobId,
            jobType: this.jobType,
            userId: createdBy,
            organizationId: orgId,
            fileName: path.basename(filePath),
            operation: 'csv_processing'
          };
          
          // Log the error with context
          ErrorLogger.logBulkInsertError(error, context as any);
          
          reject(new InternalError(
            `Error reading CSV file: ${error.message}`,
            JOB_ERROR_CODES.BULK_INSERT_FILE_ERROR
          ).withContext(context));
        });
    });
  }


  /**
   * Validate contact data from CSV row
   * @param rowData - Raw CSV row data
   * @param headerMapping - Header mapping configuration (contactAttribute -> csvColumnName)
   * @returns Validation result with normalized contact data
   */
  private validateContactData(
    rowData: ContactRowData, 
    headerMapping: { [key: string]: string }
  ): { isValid: boolean; contact?: any; errors: Array<{ field: string; message: string }> } {
    // Create contact data for validation using ES6 spread operator syntax
    const contactDataForValidation: any = {
      ...(rowData[headerMapping['name']]?.trim() ? { name: rowData[headerMapping['name']].trim() } : {}),
      ...(rowData[headerMapping['email']]?.trim() ? { email: rowData[headerMapping['email']].trim() } : {}),
      ...(rowData[headerMapping['phone']]?.trim() ? { phone: rowData[headerMapping['phone']].trim() } : {}),
      ...(rowData[headerMapping['country']]?.trim() ? { country: rowData[headerMapping['country']].trim() } : {}),
      ...(rowData[headerMapping['country_code']]?.trim() ? { country_code: rowData[headerMapping['country_code']].trim().toUpperCase() } : {}),
      ...(rowData[headerMapping['avatar_url']]?.trim() ? { avatar_url: rowData[headerMapping['avatar_url']].trim() } : {}),
      ...(this.parseContactStatus(rowData[headerMapping['status']]) ? { status: this.parseContactStatus(rowData[headerMapping['status']]) } : { status: ContactStatus.ACTIVE }),
      tags: this.extractTagsFromValue(rowData[headerMapping['tags']]),
      additional_fields: this.extractAdditionalFields(rowData, headerMapping)
    };

    // Validate using centralized ContactsValidator
    const validationResult = ContactsValidator.validateCreateContact(contactDataForValidation);
    
    if (validationResult.error) {
      // Convert Joi validation errors to our error format
      const validationErrors = validationResult.error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return { isValid: false, errors: validationErrors };
    }

    return { isValid: true, contact: validationResult.value, errors: [] };
  }

  /**
   * Validate and process a single contact row
   * @param rowData - Raw CSV row data
   * @param headerMapping - Header mapping configuration (contactAttribute -> csvColumnName)
   * @param orgId - Organization ID
   * @param createdBy - User ID who created the job
   * @param rowNumber - Row number for error reporting
   * @param jobId
   * @returns Promise with validation result
   */
  private async validateAndProcessContact(
    rowData: ContactRowData, 
    headerMapping: { [key: string]: string },
    orgId: string, 
    createdBy: string,
    rowNumber: number,
    jobId: string
  ): Promise<{ isValid: boolean; errors: ContactProcessingError[] }> {
    const errors: ContactProcessingError[] = [];

    try {
      // Use the extracted validation method
      const validation = this.validateContactData(rowData, headerMapping);
      
      if (!validation.isValid) {
        const validationError: ContactProcessingError = {
          rowNumber,
          contactData: rowData,
          error: {
            type: 'VALIDATION',
            message: 'Contact validation failed',
            errorCode: JOB_ERROR_CODES.BULK_INSERT_VALIDATION_ERROR,
            details: validation.errors
          },
          timestamp: new Date()
        };
        errors.push(validationError);
        return { isValid: false, errors };
      }

      // Create contact data for database insertion using validated data
      const contactData: CreateContactData = {
        org_id: orgId,
        created_by: createdBy,
        ...validation.contact
      };

      // Attempt to create the contact
      await ContactDAO.createContact(contactData);
      
      return { isValid: true, errors: [] };

    } catch (error) {
      // Create context for the error
      const context = {
        jobId,
        jobType: this.jobType,
        userId: createdBy,
        organizationId: orgId,
        rowNumber,
        contactData: rowData,
        operation: 'contact_validation'
      };
      
      // Log the error with context
      ErrorLogger.logBulkInsertError(error, context as any);
      
      this.log.error(`Error processing contact at row ${rowNumber}:`, error);

      let errorType: 'VALIDATION' | 'DUPLICATE' | 'SYSTEM' | 'DATABASE' = 'SYSTEM';
      let errorCode: string = JOB_ERROR_CODES.BULK_INSERT_FAILED;
      let errorMessage = 'Unknown error occurred';
      let errorDetails: Array<{ field: string; message: string }> | undefined;

      if (error instanceof ValidationError) {
        errorType = 'VALIDATION';
        errorCode = JOB_ERROR_CODES.BULK_INSERT_VALIDATION_ERROR;
        errorMessage = error.message;
        errorDetails = error.details;
      } else if (error instanceof DuplicateError) {
        errorType = 'DUPLICATE';
        errorCode = error.errorCode;
        errorMessage = error.message;
        errorDetails = error.details;
      } else if (error instanceof DatabaseError) {
        errorType = 'DATABASE';
        errorCode = error.errorCode;
        errorMessage = error.message;
        errorDetails = error.details;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      const processingError: ContactProcessingError = {
        rowNumber,
        contactData: rowData,
        error: {
          type: errorType,
          message: errorMessage,
          errorCode,
          details: errorDetails
        },
        timestamp: new Date()
      };

      errors.push(processingError);
      return { isValid: false, errors };
    }
  }


  /**
   * Extract tags from CSV tag value
   * @param tagsValue - Raw tag value from CSV
   * @returns Array of tag objects or empty array
   */
  private extractTagsFromValue(tagsValue: string | undefined | null): any[] {
    if (!tagsValue) {
      return [];
    }

    try {
      const trimmedValue = tagsValue.trim();
      if (!trimmedValue) {
        return [];
      }

      // Try to parse as JSON array first
      if (trimmedValue.startsWith('[') && trimmedValue.endsWith(']')) {
        return JSON.parse(trimmedValue);
      }

      // Otherwise, split by comma and create simple tag objects
      return trimmedValue.split(',').map((tag: string) => ({
        tag_name: tag.trim()
      })).filter((tag: { tag_name: string }) => tag.tag_name);
    } catch (error) {
      this.log.warn(`Failed to parse tags from CSV: ${error}`);
      return [];
    }
  }

  /**
   * Extract additional fields from CSV row data (fields not in standard headerMapping)
   * @param rowData - Raw CSV row data
   * @param headerMapping - Header mapping configuration
   * @returns Object with additional fields or empty object
   */
  private extractAdditionalFields(rowData: ContactRowData, headerMapping: { [key: string]: string }): { [key: string]: any } {
    const mappedColumns = Object.values(headerMapping);
    const additionalFields: { [key: string]: any } = {};

    // Find CSV columns that are not mapped to standard contact fields
    Object.keys(rowData).forEach(csvColumn => {
      // Skip if this column is mapped to a standard field
      if (mappedColumns.includes(csvColumn)) {
        return;
      }

      // Skip empty values
      const value = rowData[csvColumn];
      if (value === undefined || value === null || String(value).trim() === '') {
        return;
      }

      // Add to additional fields with cleaned value
      additionalFields[csvColumn] = String(value).trim();
    });

    return additionalFields;
  }

  /**
   * Parse contact status from string value
   * @param statusValue - Status value from CSV
   * @returns ContactStatus or undefined if invalid
   */
  private parseContactStatus(statusValue: any): ContactStatus | undefined {
    if (!statusValue || typeof statusValue !== 'string') {
      return undefined;
    }

    const normalizedStatus = statusValue.toLowerCase().trim();
    switch (normalizedStatus) {
      case 'active':
        return ContactStatus.ACTIVE;
      case 'archived':
        return ContactStatus.ARCHIVED;
      case 'deleted':
        return ContactStatus.DELETED;
      default:
        return undefined;
    }
  }

  /**
   * Generate error report CSV file
   * @param errors - Array of processing errors
   * @param jobId - Job ID for file naming
   * @returns Promise with error report file path
   */
  private async generateErrorReport(errors: ContactProcessingError[], jobId: string): Promise<string> {
    const reportDir = path.join(process.cwd(), 'temp', 'error-reports');
    const reportPath = path.join(reportDir, `error-report-${jobId}.csv`);

    try {
      // Ensure directory exists
      if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
      }

      // Generate CSV content
      const csvHeader = 'Row Number,Error Type,Error Message,Error Code,Field,Field Message,Original Data\n';
      let csvContent = csvHeader;

      errors.forEach(error => {
        const originalDataJson = JSON.stringify(error.contactData).replace(/"/g, '""');
        
        if (error.error.details && error.error.details.length > 0) {
          // Multiple field errors
          error.error.details.forEach(detail => {
            csvContent += `${error.rowNumber},"${error.error.type}","${error.error.message}","${error.error.errorCode}","${detail.field}","${detail.message}","${originalDataJson}"\n`;
          });
        } else {
          // Single error
          csvContent += `${error.rowNumber},"${error.error.type}","${error.error.message}","${error.error.errorCode}","","","${originalDataJson}"\n`;
        }
      });

      // Write to file
      fs.writeFileSync(reportPath, csvContent, 'utf8');
      
      this.log.info(`Error report generated: ${reportPath}`);
      return reportPath;

    } catch (error) {
      // Create context for the error
      const context = {
        jobId,
        jobType: this.jobType,
        operation: 'error_report_generation'
      };
      
      // Log the error with context
      ErrorLogger.logJobError(error, context as any);
      
      this.log.error('Failed to generate error report:', error);
      throw new InternalError(
        'Failed to generate error report',
        JOB_ERROR_CODES.BULK_INSERT_FAILED
      ).withContext(context);
    }
  }

  /**
   * Hook called when job completes successfully
   * @param job - The completed job
   * @param result - The job result
   */
  protected async onJobCompleted(job: Job<ContactBulkInsertJobData>, result: ContactBulkInsertResult): Promise<void> {
    const { jobId, userEmail } = job.data;
    
    this.log.info(`Contact bulk insert job completed: ${jobId}. ` +
      `Total: ${result.totalRows}, Success: ${result.successfulRows}, Failed: ${result.failedRows}`);

    // TODO: Queue email notification job
    // This will be implemented with the dedicated message service
    // await this.queueEmailNotification(job.data, result);
  }

  /**
   * Hook called when job fails
   * @param job - The failed job
   * @param error - The error that caused the failure
   */
  protected async onJobFailed(job: Job<ContactBulkInsertJobData>, error: any): Promise<void> {
    const { jobId, userEmail } = job.data;
    
    this.log.error(`Contact bulk insert job failed: ${jobId}`, error);

    // TODO: Queue failure email notification
    // This will be implemented with the dedicated message service
    // await this.queueFailureEmailNotification(job.data, error);
  }

  /**
   * Validate job data specific to contact bulk insert
   * @param job - The job to validate
   */
  protected validateJobData(job: Job<ContactBulkInsertJobData>): void {
    // Call parent validation first
    super.validateJobData(job);

    const { filePath, fileName, fileSize, userEmail } = job.data;

    if (!filePath) {
      throw new ValidationError(
        'File path is missing',
        'JOB_VALIDATION_005',
        [{ field: 'filePath', message: 'File path is required' }]
      );
    }

    if (!fileName) {
      throw new ValidationError(
        'File name is missing',
        'JOB_VALIDATION_006',
        [{ field: 'fileName', message: 'File name is required' }]
      );
    }

    if (!fileSize || fileSize <= 0) {
      throw new ValidationError(
        'File size is invalid',
        'JOB_VALIDATION_007',
        [{ field: 'fileSize', message: 'File size must be greater than 0' }]
      );
    }

    if (!userEmail) {
      throw new ValidationError(
        'User email is missing',
        'JOB_VALIDATION_008',
        [{ field: 'userEmail', message: 'User email is required for notifications' }]
      );
    }
  }
}