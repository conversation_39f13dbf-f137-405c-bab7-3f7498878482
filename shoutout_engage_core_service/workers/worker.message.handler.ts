import { parentPort } from 'worker_threads';

// These variables will be set from main.ts
let isRunning: boolean;
let heartbeatInterval: NodeJS.Timeout | null;
let stopJobProcessors: () => Promise<void>;

/**
 * Set the required variables from main.ts
 * @param running The isRunning variable from main.ts
 * @param heartbeat The heartbeatInterval variable from main.ts
 * @param stopProcessors The stopJobProcessors function from main.ts
 */
export function setWorkerVariables(
    running: boolean,
    heartbeat: NodeJS.Timeout | null,
    stopProcessors: () => Promise<void>
): void {
    isRunning = running;
    heartbeatInterval = heartbeat;
    stopJobProcessors = stopProcessors;
}

/**
 * Worker Message Handler Class
 * Handles messages from the parent thread with proper lifecycle management
 */
class WorkerMessageHandler {
    private isShuttingDown = false;
    private messageHandler: ((message: any) => Promise<void>) | null = null;

    /**
     * Initialize the message handler
     * Sets up the message listener on the parent port
     */
    initialize(): void {
        if (!parentPort) return;
        
        // Remove existing listener if any
        if (this.messageHandler) {
            parentPort.removeListener('message', this.messageHandler);
        }
        
        // Create new handler
        this.messageHandler = this.handleMessage.bind(this);
        parentPort.on('message', this.messageHandler);
        
        console.log('Worker message handler initialized');
    }
    
    /**
     * Handle incoming messages from parent thread
     * @param message The message received from parent thread
     */
    private async handleMessage(message: any): Promise<void> {
        try {
            switch (message.type) {
                case 'shutdown':
                    await this.handleShutdown();
                    break;
                case 'health-check':
                    await this.handleHealthCheck();
                    break;
                default:
                    this.handleUnknownMessage(message);
            }
        } catch (error) {
            this.handleError(error);
        }
    }
    
    /**
     * Handle shutdown message from parent thread
     */
    private async handleShutdown(): Promise<void> {
        if (this.isShuttingDown) {
            console.log('Shutdown already in progress, ignoring duplicate message');
            return;
        }
        
        console.log('Received shutdown message from main thread');
        this.isShuttingDown = true;
        isRunning = false;
        
        // Clear heartbeat interval
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            heartbeatInterval = null;
        }
        
        // Stop all job processors gracefully
        await stopJobProcessors();
    }
    
    /**
     * Handle health check message from parent thread
     */
    private async handleHealthCheck(): Promise<void> {
        parentPort?.postMessage({ 
            type: 'health-response', 
            status: isRunning ? 'healthy' : 'shutting-down',
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * Handle unknown message types
     * @param message The unknown message
     */
    private handleUnknownMessage(message: any): void {
        console.warn('Unknown message type:', message.type);
    }
    
    /**
     * Handle errors that occur during message processing
     * @param error The error that occurred
     */
    private handleError(error: unknown): void {
        console.error('Error handling parent port message:', error);
        parentPort?.postMessage({ 
            type: 'error', 
            error: error instanceof Error ? error.message : String(error) 
        });
    }
    
    /**
     * Clean up message handler
     * Removes the message listener from the parent port
     */
    cleanup(): void {
        if (parentPort && this.messageHandler) {
            parentPort.removeListener('message', this.messageHandler);
            this.messageHandler = null;
            console.log('Worker message handler cleaned up');
        }
    }
}

// Initialize message handler once at module level
const messageHandler = new WorkerMessageHandler();
messageHandler.initialize();

// Cleanup on exit
process.on('exit', () => {
    messageHandler.cleanup();
});

export { WorkerMessageHandler, messageHandler };