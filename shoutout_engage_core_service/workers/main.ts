import { parentPort } from 'worker_threads';
import { ContactBulkInsertProcessor } from './processors/contact.bulk.insert.processor';
import { QueueManager } from '../queues/queue.manager'; // Add this import
import { setWorkerVariables } from './worker.message.handler';

// Simple worker without external dependencies to avoid module loading issues
let isRunning = true;
let heartbeatInterval: NodeJS.Timeout | null = null;

// Job processors
let contactBulkInsertProcessor: ContactBulkInsertProcessor | null = null;
let queueManager: QueueManager | null = null; // Add this



// Worker thread main function
(async function main() {
    try {
        console.log('Worker thread started');

        // Send a message to the main thread
        if (parentPort) {
            parentPort.postMessage({ type: 'init', message: 'Worker initialized successfully' });
        }

        // Skip Redis connection test - not required for basic worker functionality
        console.log('Worker initializing without Redis dependency...');

        // Initialize queues first, then processors (with error handling)
        try {
            await initializeQueues();
            await initializeJobProcessors();
        } catch (initError) {
            console.warn('Queue/processor initialization failed, worker will continue without background processing:', initError);
            // Continue without queues if Redis is not available
        }

        // Start heartbeat only if worker is still running
        if (isRunning) {
            heartbeatInterval = setInterval(() => {
                if (isRunning && parentPort) {
                    parentPort.postMessage({ type: 'heartbeat', timestamp: new Date().toISOString() });
                }
            }, 60000); // Every 60 seconds
        }

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorStack = error instanceof Error ? error.stack : 'No stack trace';

        console.error('Worker initialization error:', errorMessage);
        console.error('Error stack:', errorStack);

        if (parentPort) {
            parentPort.postMessage({
                type: 'error',
                error: errorMessage,
                stack: errorStack,
                details: error
            });
        }
        process.exit(1);
    }
}());

/**
 * Initialize queue system
 */
async function initializeQueues() {
    try {
        console.log('Initializing queue system...');

        // Add a small delay to ensure Redis is ready
        await new Promise(resolve => setTimeout(resolve, 1000));

        queueManager = QueueManager.getInstance();
        await queueManager.initializeQueues();

        console.log('Queue system initialized successfully');

        if (parentPort) {
            parentPort.postMessage({
                type: 'queues-initialized',
                message: 'Queue system initialized successfully'
            });
        }
    } catch (error) {
        console.error('Failed to initialize queue system:', error);
        console.error('Error details:', error);

        if (parentPort) {
            parentPort.postMessage({
                type: 'queues-error',
                error: error instanceof Error ? error.message : String(error)
            });
        }

        throw error;
    }
}

/**
 * Initialize and start job processors
 */
async function initializeJobProcessors() {
    try {
        console.log('Initializing job processors...');

        // Initialize ContactBulkInsertProcessor
        contactBulkInsertProcessor = new ContactBulkInsertProcessor();
        contactBulkInsertProcessor.startProcess();
        console.log('ContactBulkInsertProcessor started');

        console.log('EmailProcessor started');

        console.log('All job processors initialized successfully');

        // Send success message to main thread
        if (parentPort) {
            parentPort.postMessage({ 
                type: 'processors-started', 
                message: 'Job processors started successfully',
                processors: ['ContactBulkInsertProcessor', 'EmailProcessor']
            });
        }

    } catch (error) {
        console.error('Failed to initialize job processors:', error);
        
        // Send error message to main thread
        if (parentPort) {
            parentPort.postMessage({ 
                type: 'processors-error', 
                error: error instanceof Error ? error.message : String(error) 
            });
        }
        
        throw error;
    }
}

/**
 * Stop all job processors gracefully
 */
async function stopJobProcessors() {
    try {
        console.log('Stopping job processors...');

        const shutdownPromises: Promise<void>[] = [];

        // Stop ContactBulkInsertProcessor
        if (contactBulkInsertProcessor) {
            console.log('Stopping ContactBulkInsertProcessor...');
            shutdownPromises.push(contactBulkInsertProcessor.stopProcess());
        }

        // Wait for all processors to stop
        await Promise.all(shutdownPromises);

        // Close queues after processors are stopped
        if (queueManager) {
            console.log('Closing queue system...');
            await queueManager.closeQueues();
        }

        console.log('All job processors and queues stopped successfully');

        // Send success message to main thread
        if (parentPort) {
            parentPort.postMessage({ 
                type: 'processors-stopped', 
                message: 'Job processors and queues stopped successfully' 
            });
        }

    } catch (error) {
        console.error('Error stopping job processors:', error);
        
        // Send error message to main thread
        if (parentPort) {
            parentPort.postMessage({ 
                type: 'processors-shutdown-error', 
                error: error instanceof Error ? error.message : String(error) 
            });
        }
    }
}

// Set worker variables in the message handler
setWorkerVariables(isRunning, heartbeatInterval, stopJobProcessors);