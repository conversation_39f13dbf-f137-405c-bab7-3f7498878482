import { createClient, RedisClientType } from 'redis';
import dotenv from 'dotenv';
import config from '../../config';
import {logger} from '../../logger';

dotenv.config();

const log = logger(config.logger);

class RedisConnector {
    private static baseConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: Number(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD && process.env.REDIS_PASSWORD.trim() !== '' 
            ? process.env.REDIS_PASSWORD 
            : undefined,
    };

    /**
     * Get Redis configuration for different clients
     * @param format - 'standard' for Redis client, 'bull' for Bull MQ
     */
    static getConfig(format: 'standard' | 'bull' = 'standard') {
        if (format === 'bull') {
            return {
                ...this.baseConfig,
                // Bull MQ specific options - consolidated from REDIS_QUEUE_CONFIG
                maxRetriesPerRequest: null,
                retryDelayOnFailover: 100,
                enableReadyCheck: false,
                lazyConnect: true,
                maxLoadingTimeout: 0,
            };
        }
        
        return {
            socket: {
                host: this.baseConfig.host,
                port: this.baseConfig.port,
                connectTimeout: 10000,
                lazyConnect: true,
            },
            password: this.baseConfig.password,
        };
    }

    /**
     * Get Redis configuration in Bull MQ compatible format
     * @deprecated Use getConfig('bull') instead
     */
    static getBullConfig() {
        return this.getConfig('bull');
    }

    static async get(key: string): Promise<string | null> {
        const redisClient = await getClient();
        return await redisClient.get(key);
    }

    static async del(...keys: string[]): Promise<number> {
        const redisClient = await getClient();
        return await redisClient.del(keys);
    }

    static async set(key: string, value: string): Promise<(string | null)> {
        const redisClient = await getClient();
        return await redisClient.set(key, value);
    }

    static async mGet(...keys: string[]): Promise<(string | null)[]> {
        const redisClient = await getClient();
        return await redisClient.mGet(keys);
    }

    static async mSet(kvPairs: Record<string, string>): Promise<'OK'> {
        const redisClient = await getClient();
        const flatPairs = Object.entries(kvPairs).flat();
        return await redisClient.mSet(flatPairs as string[]);
    }

    static async expire(key: string, seconds: number): Promise<number> {
        const redisClient = await getClient();
        return await redisClient.expire(key, seconds);
    }

    static async scanAndDelete(pattern: string): Promise<void> {
        try {
            const redisClient = await getClient();
            let cursor:string = "0";
            do {
                const reply = await redisClient.scan(cursor, {
                    MATCH: pattern,
                    COUNT: 1000,
                });
                cursor = String(reply.cursor);
                const keys = reply.keys;
                if (keys.length > 0) await redisClient.del(keys);
            } while (cursor !== '0');
        } catch (e) {
            log.error(e);
        }
    }

    static async closeConnection(): Promise<void> {
        try {
            if (client) {
                await client.quit();
                client = null;
            }
        } catch (err) {
            log.error('Error closing Redis connection', err);
            throw err;
        }
    }
}

// Create Redis client using the unified config
const redisClientOptions = RedisConnector.getConfig('standard');
let client: RedisClientType | null = null;
let isConnecting = false;

async function getClient(): Promise<RedisClientType> {
    if (client && client.isReady) {
        return client;
    }

    if (isConnecting) {
        // Wait for existing connection attempt
        while (isConnecting) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        if (client && client.isReady) {
            return client;
        }
    }

    isConnecting = true;
    try {
        if (!client) {
            client = createClient(redisClientOptions);
            client.on('error', (err: any) => log.error('Redis Client Error', err));
        }

        if (!client.isReady) {
            await client.connect();
            log.info('Redis client connected successfully');
        }

        return client;
    } catch (err) {
        log.error('Failed to connect to Redis:', err);
        throw err;
    } finally {
        isConnecting = false;
    }
}

export default RedisConnector;