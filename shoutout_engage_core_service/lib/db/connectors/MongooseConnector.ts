import mongoose, { Connection } from 'mongoose';
import config from '../../config';
import { logger } from '../../logger';

const log = logger(config.logger);

class MongooseConnector {
    private static connection: Connection;

    /**
     * Initialize the main MongoDB connection
     */
    static async initialize(): Promise<typeof mongoose> {
        if (this.connection) {
            log.info('🟢 MongoDB already connected');
            return mongoose;
        }

        const mongoUrl = process.env.MONGO_URL || 'mongodb://localhost:27017/engage';
        log.info(`🔄 Attempting to connect to MongoDB: ${mongoUrl.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')}`);

        try {
            const conn = await mongoose.connect(mongoUrl, {
                maxPoolSize: 10,
            });

            this.connection = mongoose.connection;

            this.connection.on('error', (err) => {
                log.error('🔴 MongoDB connection error:', err);
            });

            this.connection.once('open', () => {
                log.info('🟢 MongoDB connected successfully');
            });

            this.connection.on('disconnected', () => {
                log.warn('⚠️ MongoDB disconnected');
            });

            log.info('🟢 MongoDB connection initialized');
            return conn;
        } catch (err) {
            log.error('🔴 MongoDB connection failed:', err);
            return Promise.reject(err);
        }
    }

    /**
     * Return the connection instance
     */
    static getConnection(): Connection | undefined {
        return this.connection;
    }

    /**
     * Close the MongoDB connection
     */
    static async close(): Promise<void> {
        try {
            if (this.connection) {
                await this.connection.close();
                log.info('MongoDB connection closed');
            }
        } catch (err) {
            log.error('Error closing MongoDB connection:', err);
            return Promise.reject(err);
        }
    }
}

export default MongooseConnector;
