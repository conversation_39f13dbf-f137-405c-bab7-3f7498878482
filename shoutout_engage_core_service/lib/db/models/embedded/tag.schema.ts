import { Schema } from 'mongoose';
import { ContactTag } from '../../../../types/contact.types';
import { ObjectId } from 'mongodb';

/**
 * Embedded tag schema for contact tags
 * This schema is used within the Contact model for embedded tag documents
 */
export const ContactTagSchema = new Schema<ContactTag>({
  tag_id: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Tag',
    validate: {
      validator: function(v: any) {
        return ObjectId.isValid(v);
      },
      message: 'Invalid tag_id format'
    }
  },
  tag_name: {
    type: String,
    required: true,
    trim: true,
    unique: true,
    minlength: [1, 'Tag name cannot be empty'],
    maxlength: [100, 'Tag name cannot exceed 100 characters'],
    validate: {
      validator: function(v: string) {
        // Tag name should not contain special characters except spaces, hyphens, and underscores
        return /^[a-zA-Z0-9\s\-_]+$/.test(v);
      },
      message: 'Tag name can only contain letters, numbers, spaces, hyphens, and underscores'
    }
  }
}, { 
  _id: false, // Disable _id for embedded documents
  timestamps: false // No timestamps needed for embedded tags
});

// Add index for tag_name for efficient querying
ContactTagSchema.index({ tag_name: 1 });

export default ContactTagSchema;
