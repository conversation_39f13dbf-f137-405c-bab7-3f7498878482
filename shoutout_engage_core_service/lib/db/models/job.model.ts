import { Schema, model } from 'mongoose';
import { JobDocument, JobError, JobProgress, JobMetadata } from '../../../types/job.types';
import { JOB_STATUS, JOB_TYPE, JOB_PRIORITY, JOB_ERROR_TYPE } from '../../constant/job.constants';
import { ensureIndexes } from '../../utils/db/indexUtils';

// Job Error Schema
const jobErrorSchema = new Schema<JobError>({
  type: {
    type: String,
    enum: Object.values(JOB_ERROR_TYPE),
    required: true
  },
  message: {
    type: String,
    required: true
  },
  errorCode: {
    type: String,
    required: true
  },
  details: [{
    field: {
      type: String,
      required: true
    },
    message: {
      type: String,
      required: true
    }
  }],
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  rowNumber: {
    type: Number,
    required: false
  },
  stackTrace: {
    type: String,
    required: false
  }
}, { _id: false });

// Job Progress Schema
const jobProgressSchema = new Schema<JobProgress>({
  totalItems: {
    type: Number,
    required: false,
    min: 0
  },
  processedItems: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  successfulItems: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  failedItems: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  percentage: {
    type: Number,
    required: false,
    min: 0,
    max: 100,
    default: 0
  },
  currentStep: {
    type: String,
    required: false
  },
  estimatedCompletion: {
    type: Date,
    required: false
  },
  startedAt: {
    type: Date,
    required: false
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

// Job Metadata Schema
const jobMetadataSchema = new Schema<JobMetadata>({
  priority: {
    type: Number,
    enum: Object.values(JOB_PRIORITY),
    default: JOB_PRIORITY.NORMAL
  },
  retryCount: {
    type: Number,
    default: 0,
    min: 0
  },
  maxRetries: {
    type: Number,
    default: 3,
    min: 0
  },
  tags: [{
    type: String
  }],
  source: {
    type: String,
    required: false
  },
  userAgent: {
    type: String,
    required: false
  },
  ipAddress: {
    type: String,
    required: false
  }
}, { _id: false, strict: false }); // Allow additional fields

// Main Job Schema
const jobSchema = new Schema<JobDocument>({
  jobId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  jobType: {
    type: String,
    enum: Object.values(JOB_TYPE),
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: Object.values(JOB_STATUS),
    default: JOB_STATUS.PENDING,
    required: true,
    index: true
  },
  orgId: {
    type: String,
    required: true,
    index: true
  },
  createdBy: {
    type: String,
    required: true,
    index: true
  },
  data: {
    type: Schema.Types.Mixed,
    required: true
  },
  result: {
    type: Schema.Types.Mixed,
    required: false
  },
  errors: {
    type: [jobErrorSchema],
    default: []
  },
  progress: {
    type: jobProgressSchema,
    default: () => ({
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      percentage: 0,
      lastUpdated: new Date()
    })
  },
  startedAt: {
    type: Date,
    required: false,
    index: true
  },
  completedAt: {
    type: Date,
    required: false,
    index: true
  },
  metadata: {
    type: jobMetadataSchema,
    required: false
  }
}, {
  timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' },
  versionKey: false,
  suppressReservedKeysWarning: true
});

// Compound indexes for efficient queries
jobSchema.index({ orgId: 1, jobType: 1 });
jobSchema.index({ orgId: 1, status: 1 });
jobSchema.index({ orgId: 1, createdBy: 1 });
jobSchema.index({ orgId: 1, createdAt: -1 });
jobSchema.index({ jobType: 1, status: 1 });
jobSchema.index({ status: 1, createdAt: -1 });

// Index for job cleanup (completed/failed jobs older than X days)
jobSchema.index({ status: 1, completedAt: 1 });

// Index for monitoring and statistics
jobSchema.index({ jobType: 1, status: 1, createdAt: -1 });

// Index for user-specific job queries
jobSchema.index({ createdBy: 1, status: 1, createdAt: -1 });

// Index for priority-based processing
jobSchema.index({ 'metadata.priority': -1, createdAt: 1 });

// Index for tag-based filtering
jobSchema.index({ 'metadata.tags': 1 });

// TTL index for automatic cleanup of old completed jobs (optional)
// Uncomment if you want automatic cleanup after 30 days
// jobSchema.index({ completedAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

// Pre-save middleware to update progress.lastUpdated
jobSchema.pre('save', function(next) {
  if (this.isModified('progress')) {
    this.progress.lastUpdated = new Date();
  }
  next();
});

// Pre-save middleware to set startedAt when status changes to PROCESSING
jobSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === JOB_STATUS.PROCESSING && !this.startedAt) {
    this.startedAt = new Date();
  }
  next();
});

// Pre-save middleware to set completedAt when status changes to COMPLETED or FAILED
jobSchema.pre('save', function(next) {
  if (this.isModified('status') && 
      (this.status === JOB_STATUS.COMPLETED || this.status === JOB_STATUS.FAILED) && 
      !this.completedAt) {
    this.completedAt = new Date();
  }
  next();
});

// Virtual for duration calculation
jobSchema.virtual('duration').get(function() {
  if (this.startedAt && this.completedAt) {
    return this.completedAt.getTime() - this.startedAt.getTime();
  }
  return null;
});

// Virtual for estimated completion based on progress
jobSchema.virtual('estimatedCompletion').get(function() {
  if (this.startedAt && this.progress?.percentage && this.progress.percentage > 0 && this.progress.percentage < 100) {
    const elapsed = Date.now() - this.startedAt.getTime();
    const estimatedTotal = (elapsed / this.progress.percentage) * 100;
    return new Date(this.startedAt.getTime() + estimatedTotal);
  }
  return null;
});

// Export the model
export const JobModel = model<JobDocument>('Job', jobSchema);

// Ensure indexes are created only in non-test environments
ensureIndexes(JobModel, jobSchema, 'Job');