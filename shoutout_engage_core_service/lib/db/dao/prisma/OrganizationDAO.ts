import PrismaConnector from "../../connectors/PrismaConnector";
import { Organization, OrganizationStatus } from "../../../../types/organization.types";
import { logger } from '../../../logger';
import config from '../../../config';
import { PrismaClientKnownRequestError, PrismaClientValidationError } from "../../../../generated/prisma/runtime/library";
import {
    BadRequestError,
    DatabaseError,
    DuplicateError,
    NotFoundError,
    ValidationError
} from "../../../errors/error.type";

const log = logger(config.logger);

export class OrganizationDAO {
    /**
     * Get organization by UUID
     * @param uuid - Organization UUID
     * @returns Promise<Organization | null> - The organization, null if not found
     * @throws NotFoundError if organization is not found
     * @throws BadRequestError if UUID is invalid
     * @throws DatabaseError for database-related errors
     */
    static async getOrganizationByUuid(uuid: string): Promise<Organization | null> {
        try {
            // Validate input
            if (!uuid || typeof uuid !== 'string') {
                throw new BadRequestError(
                    'Invalid organization UUID',
                    'ORG_002',
                    [{ field: 'uuid', message: 'Organization UUID must be a valid UUID string' }]
                );
            }

            const prisma = PrismaConnector.getClient();
            const organization = await prisma.organizations.findUnique({
                where: {
                    uuid: uuid
                }
            });

            if (!organization) {
                return null;
            }

            return {
                uuid: organization.uuid,
                organization_name: organization.organization_name,
                email: organization.email || undefined,
                phone_number: organization.phone_number || undefined,
                status: organization.status as OrganizationStatus,
                created_at: organization.created_at.toISOString(),
                updated_at: organization.updated_at.toISOString()
            };
        } catch (error: unknown) {
            // Rethrow specific errors
            if (error instanceof BadRequestError ||
                error instanceof NotFoundError) {
                throw error;
            }

            // Handle Prisma unique constraint violation
            if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
                const duplicateField = this.extractDuplicateField(error);
                log.error(`Unique constraint violation for field: ${duplicateField}`, error);
                throw new DuplicateError(
                    `Organization with ${duplicateField} already exists`,
                    'ORG_003',
                    [{ field: duplicateField, message: `Organization with this ${duplicateField} already exists` }]
                );
            }

            // Handle validation errors
            if (error instanceof PrismaClientValidationError) {
                log.error('Organization validation error:', error.message);
                throw new ValidationError(
                    `Validation failed: ${error.message}`,
                    'ORG_004',
                    [{ field: 'unknown', message: error.message }]
                );
            }

            // Handle other database errors
            log.error('Error getting organization by UUID:', error);
            throw new DatabaseError(
                'Failed to retrieve organization due to database error',
                'DB_002',
                error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
            );
        }
    }

    /**
     * Get organization by name
     * @param organizationName - Organization name
     * @returns Promise<Organization | null> - The organization, null if not found
     * @throws BadRequestError if organization name is invalid
     * @throws DatabaseError for database-related errors
     */
    static async getOrganizationByName(organizationName: string): Promise<Organization | null> {
        try {
            // Validate input
            if (!organizationName || typeof organizationName !== 'string') {
                throw new BadRequestError(
                    'Invalid organization name',
                    'ORG_005',
                    [{ field: 'organization_name', message: 'Organization name must be a valid string' }]
                );
            }

            const prisma = PrismaConnector.getClient();
            const organization = await prisma.organizations.findFirst({
                where: {
                    organization_name: organizationName
                }
            });

            if (!organization) {
                return null;
            }

            return {
                uuid: organization.uuid,
                organization_name: organization.organization_name,
                email: organization.email || undefined,
                phone_number: organization.phone_number || undefined,
                status: organization.status as OrganizationStatus,
                created_at: organization.created_at.toISOString(),
                updated_at: organization.updated_at.toISOString()
            };
        } catch (error: unknown) {
            // Rethrow specific errors
            if (error instanceof BadRequestError) {
                throw error;
            }

            // Handle other database errors
            log.error('Error getting organization by name:', error);
            throw new DatabaseError(
                'Failed to retrieve organization due to database error',
                'DB_003',
                error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
            );
        }
    }

    /**
     * Create a new organization
     * @param organizationData - Organization data
     * @returns Promise<Organization> - The created organization
     * @throws ValidationError if organization data is invalid
     * @throws DuplicateError if organization with the same name already exists
     * @throws DatabaseError for database-related errors
     */
    static async createOrganization(organizationData: {
        organization_name: string;
        email?: string;
        phone_number?: string;
        status?: OrganizationStatus;
    }): Promise<Organization> {
        try {
            // Validate input
            if (!organizationData.organization_name) {
                throw new ValidationError(
                    'Organization name is required',
                    'ORG_006',
                    [{ field: 'organization_name', message: 'Organization name is required' }]
                );
            }

            const prisma = PrismaConnector.getClient();
            const organization = await prisma.organizations.create({
                data: {
                    organization_name: organizationData.organization_name,
                    email: organizationData.email,
                    phone_number: organizationData.phone_number,
                    status: organizationData.status || 'inactive'
                }
            });

            return {
                uuid: organization.uuid,
                organization_name: organization.organization_name,
                email: organization.email || undefined,
                phone_number: organization.phone_number || undefined,
                status: organization.status as OrganizationStatus,
                created_at: organization.created_at.toISOString(),
                updated_at: organization.updated_at.toISOString()
            };
        } catch (error: unknown) {
            // Rethrow specific errors
            if (error instanceof ValidationError) {
                throw error;
            }

            // Handle Prisma unique constraint violation
            if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
                const duplicateField = this.extractDuplicateField(error);
                log.error(`Unique constraint violation for field: ${duplicateField}`, error);
                throw new DuplicateError(
                    `Organization with ${duplicateField} already exists`,
                    'ORG_007',
                    [{ field: duplicateField, message: `Organization with this ${duplicateField} already exists` }]
                );
            }

            // Handle validation errors
            if (error instanceof PrismaClientValidationError) {
                log.error('Organization validation error:', error.message);
                throw new ValidationError(
                    `Validation failed: ${error.message}`,
                    'ORG_008',
                    [{ field: 'unknown', message: error.message }]
                );
            }

            // Handle other database errors
            log.error('Error creating organization:', error);
            throw new DatabaseError(
                'Failed to create organization due to database error',
                'DB_004',
                error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
            );
        }
    }

    /**
     * Update an organization
     * @param uuid - Organization UUID
     * @param updateData - Organization data to update
     * @returns Promise<Organization> - The updated organization
     * @throws NotFoundError if organization is not found
     * @throws ValidationError if update data is invalid
     * @throws DuplicateError if update would create a duplicate
     * @throws DatabaseError for database-related errors
     */
    static async updateOrganization(
        uuid: string,
        updateData: Partial<{
            organization_name: string;
            email: string;
            phone_number: string;
            status: OrganizationStatus;
        }>
    ): Promise<Organization> {
        try {
            // Validate input
            if (!uuid) {
                throw new BadRequestError(
                    'Organization UUID is required',
                    'ORG_009',
                    [{ field: 'uuid', message: 'Organization UUID is required' }]
                );
            }

            if (Object.keys(updateData).length === 0) {
                throw new ValidationError(
                    'No update data provided',
                    'ORG_010',
                    [{ field: 'updateData', message: 'At least one field must be provided for update' }]
                );
            }

            const prisma = PrismaConnector.getClient();

            // Check if organization exists
            const existingOrg = await prisma.organizations.findUnique({
                where: { uuid }
            });

            if (!existingOrg) {
                throw new NotFoundError(
                    `Organization with UUID ${uuid} not found`,
                    'ORG_011'
                );
            }

            // Perform update
            const organization = await prisma.organizations.update({
                where: { uuid },
                data: updateData
            });

            return {
                uuid: organization.uuid,
                organization_name: organization.organization_name,
                email: organization.email || undefined,
                phone_number: organization.phone_number || undefined,
                status: organization.status as OrganizationStatus,
                created_at: organization.created_at.toISOString(),
                updated_at: organization.updated_at.toISOString()
            };
        } catch (error: unknown) {
            // Rethrow specific errors
            if (error instanceof BadRequestError ||
                error instanceof ValidationError ||
                error instanceof NotFoundError) {
                throw error;
            }

            // Handle Prisma unique constraint violation
            if (error instanceof PrismaClientKnownRequestError && error.code === 'P2002') {
                const duplicateField = this.extractDuplicateField(error);
                log.error(`Unique constraint violation for field: ${duplicateField}`, error);
                throw new DuplicateError(
                    `Organization with ${duplicateField} already exists`,
                    'ORG_012',
                    [{ field: duplicateField, message: `Organization with this ${duplicateField} already exists` }]
                );
            }

            // Handle not found error
            if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
                throw new NotFoundError(
                    `Organization with UUID ${uuid} not found`,
                    'ORG_013'
                );
            }

            // Handle validation errors
            if (error instanceof PrismaClientValidationError) {
                log.error('Organization validation error:', error.message);
                throw new ValidationError(
                    `Validation failed: ${error.message}`,
                    'ORG_014',
                    [{ field: 'unknown', message: error.message }]
                );
            }

            // Handle other database errors
            log.error('Error updating organization:', error);
            throw new DatabaseError(
                'Failed to update organization due to database error',
                'DB_005',
                error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
            );
        }
    }

    /**
     * Extract the duplicate field name from Prisma unique constraint violation error
     * @param error - The Prisma error object
     * @returns string - The field name that caused the unique constraint violation
     */
    private static extractDuplicateField(error: PrismaClientKnownRequestError): string {
        // Prisma P2002 errors include a meta property with target array containing the field names
        if (error.meta?.target && Array.isArray(error.meta.target)) {
            return error.meta.target[0] || 'unknown field';
        }

        // Fallback
        return 'unknown field';
    }
}