import { JobModel } from '../models/job.model';
import { 
  <PERSON>D<PERSON><PERSON>nt, 
  JobError, 
  JobProgress, 
  CreateJobRequest,
  JobQueryFilters,
  JobQueryOptions,
  PaginatedJobResults,
  JobStatusResponse
} from '../../../types/job.types';
import { JobStatus, JOB_STATUS } from '../../constant/job.constants';
import { logger } from '../../logger';
import config from '../../config';
import { 
  DatabaseError, 
  NotFoundError,
  BadRequestError,
  ValidationError
} from '../../errors/error.type';
import { v4 as uuidv4 } from 'uuid';

const log = logger(config.logger);

export class CommonJobDAO {
  /**
   * Create a new job in the database
   * @param jobRequest - The job creation request data
   * @returns Promise<JobDocument> - The created job document
   * @throws ValidationError if job data is invalid
   * @throws DatabaseError if database operation fails
   */
  static async createJob(jobRequest: CreateJobRequest): Promise<JobDocument> {
    try {
      log.info(`Creating job of type: ${jobRequest.jobType} for organization: ${jobRequest.orgId}`);

      // Generate unique job ID
      const jobId = uuidv4();

      // Create job document
      const jobDoc = new JobModel({
        jobId,
        jobType: jobRequest.jobType,
        status: JOB_STATUS.PENDING,
        orgId: jobRequest.orgId,
        createdBy: jobRequest.createdBy,
        data: jobRequest.data,
        errors: [],
        progress: {
          processedItems: 0,
          successfulItems: 0,
          failedItems: 0,
          percentage: 0,
          lastUpdated: new Date()
        },
        metadata: jobRequest.metadata || {}
      });

      // Save the job to database
      const savedJob = await jobDoc.save();

      log.info(`Job created successfully with ID: ${savedJob.jobId} for org: ${jobRequest.orgId}`);
      return savedJob;

    } catch (error: any) {
      log.error('Error creating job:', error);

      // Handle validation errors
      if (error.name === 'ValidationError') {
        const validationDetails = Object.keys(error.errors).map(field => ({
          field,
          message: error.errors[field].message
        }));
        
        throw new ValidationError(
          'Job validation failed',
          'JOB_VALIDATION_001',
          validationDetails
        );
      }

      // Re-throw other errors as DatabaseError
      throw new DatabaseError(
        error.message || 'Failed to create job due to database error',
        'JOB_DB_001',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Update job status
   * @param jobId - The job ID to update
   * @param status - The new status
   * @returns Promise<JobDocument> - The updated job document
   * @throws NotFoundError if job is not found
   * @throws DatabaseError if database operation fails
   */
  static async updateJobStatus(jobId: string, status: JobStatus): Promise<JobDocument> {
    try {
      log.info(`Updating job status for job: ${jobId} to status: ${status}`);

      const updateData: any = { 
        status,
        updatedAt: new Date()
      };

      // Set timestamps based on status
      if (status === JOB_STATUS.PROCESSING) {
        updateData.startedAt = new Date();
      } else if (status === JOB_STATUS.COMPLETED || status === JOB_STATUS.FAILED) {
        updateData.completedAt = new Date();
      }

      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        updateData,
        { new: true, runValidators: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(
          `Job with ID ${jobId} not found`,
          'JOB_NOT_FOUND_001'
        );
      }

      log.info(`Job status updated successfully for job: ${jobId} to status: ${status}`);
      return updatedJob;

    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }

      log.error(`Error updating job status for job: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to update job status for job: ${jobId}`,
        'JOB_DB_002',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Update job progress
   * @param jobId - The job ID to update
   * @param progress - The progress data to update
   * @returns Promise<JobDocument> - The updated job document
   * @throws NotFoundError if job is not found
   * @throws DatabaseError if database operation fails
   */
  static async updateJobProgress(jobId: string, progress: Partial<JobProgress>): Promise<JobDocument> {
    try {
      log.info(`Updating job progress for job: ${jobId}`);

      // Calculate percentage if not provided but we have the data
      if (!progress.percentage && progress.totalItems && progress.processedItems) {
        progress.percentage = Math.round((progress.processedItems / progress.totalItems) * 100);
      }

      // Always update lastUpdated timestamp
      progress.lastUpdated = new Date();

      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        { 
          $set: { 
            'progress': { ...progress },
            updatedAt: new Date()
          }
        },
        { new: true, runValidators: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(
          `Job with ID ${jobId} not found`,
          'JOB_NOT_FOUND_002'
        );
      }

      log.info(`Job progress updated successfully for job: ${jobId}`);
      return updatedJob;

    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }

      log.error(`Error updating job progress for job: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to update job progress for job: ${jobId}`,
        'JOB_DB_003',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Add error to job
   * @param jobId - The job ID to add error to
   * @param error - The error to add
   * @returns Promise<JobDocument> - The updated job document
   * @throws NotFoundError if job is not found
   * @throws DatabaseError if database operation fails
   */
  static async addJobError(jobId: string, error: JobError): Promise<JobDocument> {
    try {
      log.info(`Adding error to job: ${jobId}`);

      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        { 
          $push: { errors: error },
          $set: { updatedAt: new Date() }
        },
        { new: true, runValidators: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(
          `Job with ID ${jobId} not found`,
          'JOB_NOT_FOUND_003'
        );
      }

      log.info(`Error added successfully to job: ${jobId}`);
      return updatedJob;

    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }

      log.error(`Error adding error to job: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to add error to job: ${jobId}`,
        'JOB_DB_004',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Set job result
   * @param jobId - The job ID to set result for
   * @param result - The result data
   * @returns Promise<JobDocument> - The updated job document
   * @throws NotFoundError if job is not found
   * @throws DatabaseError if database operation fails
   */
  static async setJobResult(jobId: string, result: any): Promise<JobDocument> {
    try {
      log.info(`Setting result for job: ${jobId}`);

      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        { 
          $set: { 
            result,
            updatedAt: new Date()
          }
        },
        { new: true, runValidators: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(
          `Job with ID ${jobId} not found`,
          'JOB_NOT_FOUND_004'
        );
      }

      log.info(`Result set successfully for job: ${jobId}`);
      return updatedJob;

    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }

      log.error(`Error setting result for job: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to set result for job: ${jobId}`,
        'JOB_DB_005',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Get job by ID
   * @param jobId - The job ID to retrieve
   * @returns Promise<JobDocument> - The job document
   * @throws NotFoundError if job is not found
   * @throws DatabaseError if database operation fails
   */
  static async getJobById(jobId: string): Promise<JobDocument> {
    try {
      log.info(`Retrieving job with ID: ${jobId}`);

      const job = await JobModel.findOne({ jobId });

      if (!job) {
        throw new NotFoundError(
          `Job with ID ${jobId} not found`,
          'JOB_NOT_FOUND_005'
        );
      }

      log.info(`Successfully retrieved job with ID: ${jobId}`);
      return job;

    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }

      log.error(`Error retrieving job with ID: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to retrieve job with ID: ${jobId}`,
        'JOB_DB_006',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Get job status response (formatted for API)
   * @param jobId - The job ID to get status for
   * @returns Promise<JobStatusResponse> - The formatted job status
   * @throws NotFoundError if job is not found
   * @throws DatabaseError if database operation fails
   */
  static async getJobStatus(jobId: string): Promise<JobStatusResponse> {
    try {
      const job = await this.getJobById(jobId);

      return {
        jobId: job.jobId,
        jobType: job.jobType,
        status: job.status,
        orgId: job.orgId,
        createdBy: job.createdBy,
        progress: job.progress,
        errors: job.errors,
        result: job.result,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        estimatedCompletion: job.progress.estimatedCompletion
      };

    } catch (error: any) {
      // Re-throw existing errors
      if (error.name === 'NotFoundError' || error.name === 'DatabaseError') {
        throw error;
      }

      log.error(`Error getting job status for job: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to get job status for job: ${jobId}`,
        'JOB_DB_007',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Query jobs with filtering, pagination, and sorting
   * @param filters - Query filters
   * @param options - Query options (pagination, sorting)
   * @returns Promise<PaginatedJobResults> - Paginated job results
   * @throws BadRequestError if query parameters are invalid
   * @throws DatabaseError if database operation fails
   */
  static async queryJobs(
    filters: JobQueryFilters = {},
    options: JobQueryOptions = {}
  ): Promise<PaginatedJobResults> {
    try {
      log.info(`Querying jobs with filters: ${JSON.stringify(filters)} and options: ${JSON.stringify(options)}`);

      // Build query
      const query = this.buildJobQuery(filters);

      // Pagination
      const page = Math.max(1, options.page || 1);
      const limit = Math.min(100, Math.max(1, options.limit || 20)); // Max 100, min 1
      const skip = (page - 1) * limit;

      // Sorting
      const sortBy = options.sortBy || 'createdAt';
      const sortOrder = options.sortOrder === 'asc' ? 1 : -1;
      const sort: any = { [sortBy]: sortOrder };

      // Add secondary sort by createdAt if not already sorting by it
      if (sortBy !== 'createdAt') {
        sort.createdAt = -1;
      }

      // Projection (exclude large fields if not needed)
      let projection: any = {};
      if (!options.includeErrors) {
        projection.errors = 0;
      }
      if (!options.includeResult) {
        projection.result = 0;
      }

      // Execute queries
      const [jobs, total] = await Promise.all([
        JobModel.find(query, projection)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .exec(),
        JobModel.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);

      log.info(`Retrieved ${jobs.length} jobs out of ${total} total`);

      return {
        jobs,
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      };

    } catch (error: any) {
      log.error('Error querying jobs:', error);
      throw new DatabaseError(
        error.message || 'Failed to query jobs',
        'JOB_DB_008',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Build MongoDB query from filters
   * @param filters - Query filters
   * @returns MongoDB query object
   */
  private static buildJobQuery(filters: JobQueryFilters): any {
    const query: any = {};

    if (filters.orgId) {
      query.orgId = filters.orgId;
    }

    if (filters.createdBy) {
      query.createdBy = filters.createdBy;
    }

    if (filters.jobType) {
      query.jobType = filters.jobType;
    }

    if (filters.status) {
      query.status = filters.status;
    }

    if (filters.createdAfter || filters.createdBefore) {
      query.createdAt = {};
      if (filters.createdAfter) {
        query.createdAt.$gte = filters.createdAfter;
      }
      if (filters.createdBefore) {
        query.createdAt.$lte = filters.createdBefore;
      }
    }

    if (filters.tags && filters.tags.length > 0) {
      query['metadata.tags'] = { $in: filters.tags };
    }

    return query;
  }

  /**
   * Delete job by ID
   * @param jobId - The job ID to delete
   * @returns Promise<boolean> - True if deleted successfully
   * @throws NotFoundError if job is not found
   * @throws DatabaseError if database operation fails
   */
  static async deleteJob(jobId: string): Promise<boolean> {
    try {
      log.info(`Deleting job with ID: ${jobId}`);

      const result = await JobModel.deleteOne({ jobId });

      if (result.deletedCount === 0) {
        throw new NotFoundError(
          `Job with ID ${jobId} not found`,
          'JOB_NOT_FOUND_006'
        );
      }

      log.info(`Job deleted successfully with ID: ${jobId}`);
      return true;

    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }

      log.error(`Error deleting job with ID: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to delete job with ID: ${jobId}`,
        'JOB_DB_009',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Cancel job by ID
   * @param jobId - The job ID to cancel
   * @returns Promise<JobDocument> - The cancelled job document
   * @throws NotFoundError if job is not found
   * @throws BadRequestError if job cannot be cancelled
   * @throws DatabaseError if database operation fails
   */
  static async cancelJob(jobId: string): Promise<JobDocument> {
    try {
      log.info(`Cancelling job with ID: ${jobId}`);

      // Only allow cancellation of pending or processing jobs
      const job = await JobModel.findOne({ 
        jobId,
        status: { $in: [JOB_STATUS.PENDING, JOB_STATUS.PROCESSING] }
      });

      if (!job) {
        // Check if job exists but is not cancellable
        const existingJob = await JobModel.findOne({ jobId });
        if (existingJob) {
          throw new BadRequestError(
            `Job with ID ${jobId} cannot be cancelled (current status: ${existingJob.status})`,
            'JOB_CANCEL_001'
          );
        } else {
          throw new NotFoundError(
            `Job with ID ${jobId} not found`,
            'JOB_NOT_FOUND_007'
          );
        }
      }

      // Update job status to cancelled
      const updatedJob = await this.updateJobStatus(jobId, JOB_STATUS.CANCELLED);

      log.info(`Job cancelled successfully with ID: ${jobId}`);
      return updatedJob;

    } catch (error: any) {
      if (error.name === 'NotFoundError' || error.name === 'BadRequestError') {
        throw error;
      }

      log.error(`Error cancelling job with ID: ${jobId}`, error);
      throw new DatabaseError(
        error.message || `Failed to cancel job with ID: ${jobId}`,
        'JOB_DB_010',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Get jobs by organization with optional filtering
   * @param orgId - Organization ID
   * @param filters - Additional filters
   * @param options - Query options
   * @returns Promise<PaginatedJobResults> - Paginated job results for the organization
   */
  static async getJobsByOrganization(
    orgId: string,
    filters: Omit<JobQueryFilters, 'orgId'> = {},
    options: JobQueryOptions = {}
  ): Promise<PaginatedJobResults> {
    return this.queryJobs({ ...filters, orgId }, options);
  }

  /**
   * Get jobs by user with optional filtering
   * @param createdBy - User ID who created the jobs
   * @param filters - Additional filters
   * @param options - Query options
   * @returns Promise<PaginatedJobResults> - Paginated job results for the user
   */
  static async getJobsByUser(
    createdBy: string,
    filters: Omit<JobQueryFilters, 'createdBy'> = {},
    options: JobQueryOptions = {}
  ): Promise<PaginatedJobResults> {
    return this.queryJobs({ ...filters, createdBy }, options);
  }

  /**
   * Test database connection
   * @returns Promise<boolean> - True if connection is successful
   */
  static async testConnection(): Promise<boolean> {
    try {
      await JobModel.findOne().limit(1);
      return true;
    } catch (error) {
      log.error('Database connection test failed:', error);
      throw new DatabaseError('Database connection test failed', 'DB_CONNECTION_001');
    }
  }

  /**
   * Update job metadata
   * @param jobId - Job ID to update
   * @param metadata - Metadata to update
   * @returns Promise<JobDocument> - Updated job document
   */
  static async updateJobMetadata(jobId: string, metadata: Record<string, any>): Promise<JobDocument> {
    try {
      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        { 
          $set: { 
            'metadata': metadata,
            updatedAt: new Date()
          }
        },
        { new: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(`Job with ID ${jobId} not found`, 'JOB_NOT_FOUND_008');
      }

      return updatedJob;
    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }
      log.error(`Error updating job metadata for ${jobId}:`, error);
      throw new DatabaseError(`Failed to update job metadata for ${jobId}`, 'JOB_DB_011');
    }
  }

  /**
   * Add a note to the job
   * @param jobId - Job ID to add note to
   * @param note - Note to add
   * @returns Promise<JobDocument> - Updated job document
   */
  static async addJobNote(jobId: string, note: string): Promise<JobDocument> {
    try {
      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        { 
          $push: { 'metadata.notes': { note, timestamp: new Date() } },
          $set: { updatedAt: new Date() }
        },
        { new: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(`Job with ID ${jobId} not found`, 'JOB_NOT_FOUND_009');
      }

      return updatedJob;
    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }
      log.error(`Error adding note to job ${jobId}:`, error);
      throw new DatabaseError(`Failed to add note to job ${jobId}`, 'JOB_DB_012');
    }
  }

  /**
   * Reset job progress
   * @param jobId - Job ID to reset progress for
   * @returns Promise<JobDocument> - Updated job document
   */
  static async resetJobProgress(jobId: string): Promise<JobDocument> {
    try {
      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        { 
          $set: { 
            progress: {
              totalItems: 0,
              processedItems: 0,
              successfulItems: 0,
              failedItems: 0,
              percentage: 0
            },
            updatedAt: new Date()
          }
        },
        { new: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(`Job with ID ${jobId} not found`, 'JOB_NOT_FOUND_010');
      }

      return updatedJob;
    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }
      log.error(`Error resetting job progress for ${jobId}:`, error);
      throw new DatabaseError(`Failed to reset job progress for ${jobId}`, 'JOB_DB_013');
    }
  }

  /**
   * Clear job errors
   * @param jobId - Job ID to clear errors for
   * @returns Promise<JobDocument> - Updated job document
   */
  static async clearJobErrors(jobId: string): Promise<JobDocument> {
    try {
      const updatedJob = await JobModel.findOneAndUpdate(
        { jobId },
        { 
          $set: { 
            errors: [],
            updatedAt: new Date()
          }
        },
        { new: true }
      );

      if (!updatedJob) {
        throw new NotFoundError(`Job with ID ${jobId} not found`, 'JOB_NOT_FOUND_011');
      }

      return updatedJob;
    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        throw error;
      }
      log.error(`Error clearing job errors for ${jobId}:`, error);
      throw new DatabaseError(`Failed to clear job errors for ${jobId}`, 'JOB_DB_014');
    }
  }
}