import { ContactModel, ContactDocument } from '../models/contact.model';
import {
  ContactStatus,
  ContactResponse,
  CreateContactData,
  ContactsQueryParams
} from '../../../types/contact.types';
import { logger } from '../../logger';
import config from '../../config';
import { escapeRegExp } from '../../utils/stringUtils';
import { 
  DuplicateError, 
  DatabaseError, 
  BadRequestError,
  NotFoundError
} from '../../errors/error.type';
import { ObjectId } from 'mongodb';

const log = logger(config.logger);

export class ContactDAO {
  /**
   * Normalize contact data for database storage
   * @param contactData - Raw contact data
   * @returns Normalized contact data ready for database
   */
  private static normalizeContactData(contactData: CreateContactData) {
    return {
      org_id: contactData.org_id,
      created_by: contactData.created_by,
      name: contactData.name.trim(),
      email: contactData.email.toLowerCase().trim(),
      phone: contactData.phone.trim(),
      country: contactData.country?.trim(),
      country_code: contactData.country_code?.toUpperCase().trim(),
      avatar_url: contactData.avatar_url?.trim(),
      tags: contactData.tags || [],
      additional_fields: contactData.additional_fields || {},
      status: contactData.status || ContactStatus.ACTIVE,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Format contact document for API response
   * @param contact - Contact document from database
   * @returns Formatted contact response
   */
  public static formatContactResponse(contact: ContactDocument): ContactResponse {
    return {
      _id: contact._id.toString(),
      org_id: contact.org_id,
      created_by: contact.created_by,
      name: contact.name,
      email: contact.email,
      phone: contact.phone,
      country: contact.country,
      country_code: contact.country_code,
      avatar_url: contact.avatar_url,
      tags: contact.tags.map((tag: any) => ({
        tag_id: tag.tag_id.toString(),
        tag_name: tag.tag_name
      })),
      additional_fields: contact.additional_fields,
      status: contact.status,
      created_at: contact.created_at,
      updated_at: contact.updated_at
    };
  }

  /**
   * Format multiple contacts for API response
   * @param contacts - Array of contact documents
   * @returns Array of formatted contact responses
   */
  static formatContactsResponse(contacts: ContactDocument[]): ContactResponse[] {
    return contacts.map(contact => this.formatContactResponse(contact));
  }

  /**
   * Create a new contact in the database
   * @param contactData - The contact data to create
   * @returns Promise<ContactDocument> - The created contact document
   * @throws DuplicateError if duplicate contact exists
   * @throws DatabaseError if database operation fails
   */
  static async createContact(contactData: CreateContactData): Promise<ContactDocument> {
    try {
      log.info(`Creating contact for organization: ${contactData.org_id}, email: ${contactData.email}`);

      // Normalize and create contact document
      const normalizedData = this.normalizeContactData(contactData);
      const contactDoc = new ContactModel(normalizedData);

      // Save the contact to database - unique indexes will prevent duplicates
      const savedContact = await contactDoc.save();

      log.info(`Contact created successfully with ID: ${savedContact._id} for org: ${contactData.org_id}`);
      return savedContact;

    } catch (error: any) {
      log.error('Error creating contact:', error);

      // Handle MongoDB duplicate key errors
      if (error.code === 11000) {
        const duplicateField = this.getDuplicateField(error.message);
        throw new DuplicateError(
          `Contact with ${duplicateField} already exists in organization`,
          'CONTACT_001'
        );
      }

      // Re-throw other errors as DatabaseError with original error message
      throw new DatabaseError(
        error.message || 'Failed to create contact due to database error',
        'DB_002',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Extract which field caused the duplicate key error
   * @param errorMessage - MongoDB error message
   * @returns string - Human readable duplicate field description
   */
  private static getDuplicateField(errorMessage: string): string {
    if (errorMessage.includes('email')) {
      return 'email';
    } else if (errorMessage.includes('phone')) {
      return 'phone number';
    }
    return 'email or phone number';
  }



  /**
   * Retrieve contacts with pagination, filtering, sorting, and search capabilities
   * @param organizationId - Organization ID to filter contacts by
   * @param params - Query parameters for pagination, filtering, sorting, and search
   * @returns Promise with contacts and total count
   * @throws DatabaseError if database operation fails
   */
  static async getContacts(
    organizationId: string,
    params: ContactsQueryParams): Promise<{ contacts: ContactDocument[], totalCount: number }> {
    try {
      log.info(`Retrieving contacts for organization: ${organizationId} with params: ${JSON.stringify(params)}`);

      // Build query based on parameters
      const query = this.buildContactsQuery(organizationId, params);

      // Calculate pagination
      const page = params.page || 1;
      const pageSize = params.page_size || 20;
      const skip = (page - 1) * pageSize;
      // Build sort options
      const sortField = params.sort_by || 'created_at';
      const sortDirection = params.sort_direction === 'asc' ? 1 : -1;
      const sortOptions: Record<string, any> = { [sortField]: sortDirection };

     // Add secondary sort by created_at if not already sorting by it
      if (sortField !== 'created_at') {
        sortOptions.created_at = -1;
      }

      // Check if we have text search to add text score sorting
      const hasTextSearch = params.search && query.$text;
      let projection = {};

      if (hasTextSearch) {
        // Add text score for relevance sorting
        projection = { score: { $meta: "textScore" } };
        // Prioritize text score for search results
        sortOptions.score = { $meta: "textScore" };
      }

      // Execute single query with conditional projection
      const contacts = await ContactModel.find(query, projection)
          .sort(sortOptions as any)
          .skip(skip)
          .limit(pageSize)
          .exec();

      // Get total count for pagination metadata
      const totalCount = await ContactModel.countDocuments(query);

      log.info(`Retrieved ${contacts.length} contacts out of ${totalCount} total for organization: ${organizationId}`);
      return { contacts, totalCount };
    } catch (error: any) {
      log.error(`Error retrieving contacts for organization: ${organizationId}`, error);
      
      // Throw a DatabaseError with appropriate details
      throw new DatabaseError(
        `Failed to retrieve contacts for organization: ${organizationId}`,
        'DB_003',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Build MongoDB query based on query parameters
   * @param organizationId - Organization ID to filter contacts by
   * @param params - Query parameters for filtering and search
   * @returns MongoDB query object
   * @throws BadRequestError if contactFilterQuery is invalid
   */
  static buildContactsQuery(organizationId: string, params: ContactsQueryParams): Record<string, any> {
    // Start with base conditions that must always be applied
    const baseConditions: Record<string, any>[] = [
      { org_id: organizationId }
    ];

    // Add search condition if provided
    if (params.search) {
      const searchTerm = params.search.trim();
      if (searchTerm) {
        const escapedSearchTerm = escapeRegExp(searchTerm);
        const searchRegex = new RegExp(escapedSearchTerm, 'i');

        baseConditions.push({
          $or: [
            { $text: { $search: searchTerm } }, // Text index search for name (weighted)
            { email: searchRegex },             // Partial match for email
            { phone: searchRegex }              // Partial match for phone
          ]
        });
      }
    }

    // Add filter query condition if provided
    if (params.contactFilterQuery) {
      try {
        const mongoQuery = JSON.parse(params.contactFilterQuery);
        if (mongoQuery && Object.keys(mongoQuery).length > 0) {
          baseConditions.push(mongoQuery);
        }
      } catch (error: any) {
        log.error('Error processing contactFilterQuery:', error);
        
        // Throw a BadRequestError with appropriate details
        throw new BadRequestError(
          'Invalid contactFilterQuery format',
          'QUERY_001',
          [{ field: 'contactFilterQuery', message: 'Must be a valid JSON string representing a MongoDB query' }]
        );
      }
    }

    // If we only have the base org_id condition, return it directly
    if (baseConditions.length === 1) {
      return baseConditions[0];
    }

    // Otherwise, combine all conditions with $and
    return { $and: baseConditions };
  }

  /**
   * Retrieve a contact by ID for a specific organization
   * @param organizationId - Organization ID to filter contacts by
   * @param contactId - Contact ID to retrieve
   * @returns Promise with the contact document
   * @throws NotFoundError if contact is not found
   * @throws DatabaseError if database operation fails
   */
  static async getContactById(
    organizationId: string,
    contactId: string
  ): Promise<ContactDocument> {
    try {
      log.info(`Retrieving contact with ID: ${contactId} for organization: ${organizationId}`);
      
      // Validate contactId format
      if (!ObjectId.isValid(contactId)) {
        throw new BadRequestError(
          'Invalid contact ID format',
          'CONTACT_002',
          [{ field: 'contactId', message: 'Contact ID must be a valid MongoDB ObjectId' }]
        );
      }

      // Find contact by ID and organization ID
      const contact = await ContactModel.findOne({
        _id: new ObjectId(contactId),
        org_id: organizationId
      });

      // If contact is not found, throw NotFoundError
      if (!contact) {
        throw new NotFoundError(
          `Contact with ID ${contactId} not found in organization ${organizationId}`,
          'CONTACT_003'
        );
      }

      log.info(`Successfully retrieved contact with ID: ${contactId} for organization: ${organizationId}`);
      return contact;
    } catch (error: any) {
      // If error is already an AppError (NotFoundError, BadRequestError), rethrow it
      if (error.name === 'NotFoundError' || error.name === 'BadRequestError') {
        throw error;
      }

      log.error(`Error retrieving contact with ID: ${contactId} for organization: ${organizationId}`, error);
      
      // Throw a DatabaseError with appropriate details
      throw new DatabaseError(
        error.message || `Failed to retrieve contact with ID: ${contactId} for organization: ${organizationId}`,
        'DB_004',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }
}
