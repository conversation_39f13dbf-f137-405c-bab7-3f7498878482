/**
 * File validation constants
 */
export const FILE_CONSTRAINTS = {
  allowedExtensions: ['.csv', '.xlsx', '.xls'],
  maxSizeBytes: 25 * 1024 * 1024, // 25MB
  maxRows: 400000,
  requireHeaders: true,
  singleSheetOnly: true
};

/**
 * Error codes for file validation
 */
export const ERROR_CODES = {
  INVALID_EXTENSION: 'FILE_001',
  FILE_TOO_LARGE: 'FILE_002',
  TOO_MANY_ROWS: 'FILE_003',
  MULTIPLE_SHEETS: 'FILE_004',
  NO_HEADERS: 'FILE_005',
  PROCESSING_ERROR: 'FILE_006'
};