/**
 * Job status constants for universal job management
 */
export const JOB_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
} as const;

export type JobStatus = typeof JOB_STATUS[keyof typeof JOB_STATUS];

/**
 * Job type constants for different job processors
 */
export const JOB_TYPE = {
  CONTACT_BULK_INSERT: 'CONTACT_BULK_INSERT',
  EMAIL: 'EMAIL'
  // Future job types can be added here:
  // EMAIL_CAMPAIGN: 'EMAIL_CAMPAIGN',
  // SMS_CAMPAIGN: 'SMS_CAMPAIGN',
  // DATA_EXPORT: 'DATA_EXPORT'
} as const;

export type JobType = typeof JOB_TYPE[keyof typeof JOB_TYPE];

/**
 * Job error codes for universal job error handling
 */
export const JO<PERSON>_ERROR_CODES = {
  // General job errors
  JOB_NOT_FOUND: 'JOB_001',
  JOB_CREATION_FAILED: 'JOB_002',
  JO<PERSON>_PROCESSING_FAILED: 'JOB_003',
  JOB_UPDATE_FAILED: 'JOB_004',
  JOB_CANCELLATION_FAILED: 'JOB_005',
  
  // Bulk insert specific errors
  BULK_INSERT_FAILED: 'BULK_INSERT_001',
  BULK_INSERT_FILE_ERROR: 'BULK_INSERT_002',
  BULK_INSERT_VALIDATION_ERROR: 'BULK_INSERT_003',
  
  // Email specific errors
  EMAIL_QUEUE_FAILED: 'EMAIL_QUEUE_001',
  EMAIL_SEND_FAILED: 'EMAIL_QUEUE_002',
  EMAIL_TEMPLATE_ERROR: 'EMAIL_QUEUE_003'
} as const;

/**
 * Job priority levels
 */
export const JOB_PRIORITY = {
  LOW: 1,
  NORMAL: 5,
  HIGH: 10,
  CRITICAL: 15
} as const;

export type JobPriority = typeof JOB_PRIORITY[keyof typeof JOB_PRIORITY];

/**
 * Job error types for categorizing errors
 */
export const JOB_ERROR_TYPE = {
  VALIDATION: 'VALIDATION',
  DUPLICATE: 'DUPLICATE',
  SYSTEM: 'SYSTEM',
  DATABASE: 'DATABASE',
  NETWORK: 'NETWORK',
  TIMEOUT: 'TIMEOUT'
} as const;

export type JobErrorType = typeof JOB_ERROR_TYPE[keyof typeof JOB_ERROR_TYPE];

/**
 * Time constants for calculations
 */
export const TIME_CONSTANTS = {
  MS_PER_DAY: 1000 * 60 * 60 * 24 // Milliseconds in a day
} as const;

/**
 * Default job configuration constants
 */
export const JOB_DEFAULTS = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 2000, // 2 seconds
  TIMEOUT: 300000, // 5 minutes
  PROGRESS_UPDATE_INTERVAL: 1000, // 1 second
  CLEANUP_AFTER_DAYS: 7
} as const;

/**
 * File management constants
 */
export const FILE_CONSTANTS = {
  FILE_RETENTION_DAYS: 7 // Keep files for 7 days (using TIME_CONSTANTS.MS_PER_DAY for conversion)
} as const;