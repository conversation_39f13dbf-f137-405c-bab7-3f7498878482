
/**
 * Queue names for different job types
 */
export const QUEUE_NAMES = {
  CONTACT_BULK_INSERT: 'contact.bulkInsert.queue',
  EMAIL: 'email'
  // Future queue names can be added here:
  // EMAIL_CAMPAIGN: 'email-campaign',
  // SMS_CAMPAIGN: 'sms-campaign',
  // DATA_EXPORT: 'data-export'
} as const;

export type QueueName = typeof QUEUE_NAMES[keyof typeof QUEUE_NAMES];

/**
 * Default queue options for Bull MQ
 */
export const DEFAULT_QUEUE_OPTIONS = {
  removeOnComplete: 100,
  removeOnFail: 50,
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
  delay: 0,
  priority: 0
} as const;

/**
 * Queue-specific options for different job types
 */
export const QUEUE_OPTIONS = {
  [QUEUE_NAMES.CONTACT_BULK_INSERT]: {
    ...DEFAULT_QUEUE_OPTIONS,
    attempts: 2, // Fewer retries for bulk operations
    removeOnComplete: 50,
    removeOnFail: 25,
    backoff: {
      type: 'exponential' as const,
      delay: 5000, // Longer delay for bulk operations
    }
  },
  [QUEUE_NAMES.EMAIL]: {
    ...DEFAULT_QUEUE_OPTIONS,
    attempts: 5, // More retries for email delivery
    removeOnComplete: 200,
    removeOnFail: 100,
    backoff: {
      type: 'exponential' as const,
      delay: 1000, // Shorter delay for email retries
    }
  }
} as const;

// REDIS_QUEUE_CONFIG removed - now consolidated in RedisConnector

/**
 * Queue monitoring and health check constants
 */
export const QUEUE_MONITORING = {
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  STATS_UPDATE_INTERVAL: 10000, // 10 seconds
  MAX_STALLED_COUNT: 10,
  MAX_FAILED_COUNT: 100,
  CLEANUP_INTERVAL: 3600000 // 1 hour
} as const;

/**
 * Queue event types for monitoring
 */
export const QUEUE_EVENTS = {
  WAITING: 'waiting',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  FAILED: 'failed',
  PAUSED: 'paused',
  RESUMED: 'resumed',
  CLEANED: 'cleaned',
  STALLED: 'stalled',
  PROGRESS: 'progress',
  REMOVED: 'removed'
} as const;

export type QueueEvent = typeof QUEUE_EVENTS[keyof typeof QUEUE_EVENTS];