import { OAS3Options } from 'swagger-jsdoc';
import envConfig from '../config';

class Swagger {
    static getOptions(): OAS3Options {
        return {
            definition: {
                openapi: '3.0.0',
                info: {
                    title: 'Shoutute Engage Core Service',
                    version: '1.0.0',
                    description:
                        'This is the engage core service api under the Shoutute enterprise services stack',
                    contact: {
                        name: 'Shoutute',
                        url: 'https://getshoutout.com/',
                        email: '<EMAIL>',
                    },
                },
                servers: [
                    {
                        url: envConfig.SWAGGER_SERVER_URL || 'http://localhost:3001/api/engagecoreservice',
                    },
                ],
                components: {
                    securitySchemes: {
                        Token: {
                            scheme: 'bearer',
                            type: 'http',
                            in: 'header',
                        },
                    },
                },
            },
            apis: ['./routes/*.ts'],
        };
    }
}

export default Swagger;
