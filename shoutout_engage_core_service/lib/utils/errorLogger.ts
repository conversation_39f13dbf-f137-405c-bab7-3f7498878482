/**
 * Context-Aware Error Logger
 * Centralized logging utility with automatic context extraction
 */

import { logger } from '../logger';
import { AppError } from '../errors/app.error';
import { InternalError } from '../errors/error.type';
import { ErrorContext, BulkInsertContext, JobContext } from "../../types/errors.types";
export type { BulkInsertContext, JobContext } from "../../types/errors.types";

const log = logger({ name: 'errorLogger' });


export class ErrorLogger {
    /**
     * Convert unknown error types to AppError
     */
    private static toAppError(error: unknown): AppError {
        if (error instanceof AppError) {
            return error;
        }
        if (error instanceof Error) {
            // Preserve original message; mark as non-operational internal error
            return new InternalError(error.message);
        }
        // Unknown non-error value (string, null, undefined, etc.)
        return new InternalError(typeof error === 'string' ? error : 'Unknown error occurred');
    }

    /**
     * Log error with context and metadata
     */
    static logError(error: unknown, context: ErrorContext, metadata?: Record<string, any>): void { 
        const appError = ErrorLogger.toAppError(error);
        const logData = {
            error: {
                name: appError.name,
                message: appError.message,
                code: appError.errorCode,
                statusCode: appError.statusCode,
                isOperational: appError.isOperational,
                details: appError.details
            },
            context: ErrorLogger.sanitizeContext(context),
            metadata: metadata ? ErrorLogger.sanitizeData(metadata) : undefined,
            timestamp: new Date().toISOString()
        };

        // Include stack trace for programming errors in development
        if (!appError.isOperational && process.env.NODE_ENV !== 'production') {
            (logData.error as any)['stack'] = appError.stack;
        }

        // Log at appropriate level
        if (appError.isOperational) {
            log.warn('Operational error:', logData);
        } else {
            log.error('Programming error:', logData);
        }
    }

    /**
     * Log bulk insert operation errors
     */
    static logBulkInsertError(
        error: unknown,
        context: BulkInsertContext,
        rowData?: any
    ): void { 
        const appError = ErrorLogger.toAppError(error);
        const bulkContext = {
            ...context,
            bulkInsert: {
                jobId: context.jobId,
                fileName: context.fileName,
                totalRows: context.totalRows,
                processedRows: context.processedRows,
                errorRows: context.errorRows,
                currentRowData: rowData ? ErrorLogger.sanitizeData(rowData) : undefined
            }
        };

        ErrorLogger.logError(appError, bulkContext);

        // Track bulk operation metrics
        ErrorLogger.trackBulkErrorMetrics(appError, context);
    }

    /**
     * Log job processing errors
     */
    static logJobError(
        error: unknown,
        context: JobContext,
        jobData?: any
    ): void { 
        const appError = ErrorLogger.toAppError(error);
        const jobContext = {
            ...context,
            job: {
                jobId: context.jobId,
                jobType: context.jobType,
                status: context.status,
                progress: context.progress,
                jobData: jobData ? ErrorLogger.sanitizeData(jobData) : undefined
            }
        };

        ErrorLogger.logError(appError, jobContext);

        // Track job error metrics
        ErrorLogger.trackJobErrorMetrics(appError, context);
    }

    /**
     * Track error metrics for monitoring
     */
    static trackErrorMetrics(error: AppError, context: ErrorContext): void {
        const metrics = {
            errorType: error.constructor.name,
            errorCode: error.errorCode,
            statusCode: error.statusCode,
            endpoint: context.endpoint,
            method: context.method,
            isOperational: error.isOperational,
            userId: context.userId,
            organizationId: context.organizationId,
            timestamp: context.timestamp
        };

        log.info('Error metrics tracked:', { metrics });
    }

    /**
     * Track bulk operation error metrics
     */
    private static trackBulkErrorMetrics(error: AppError, context: BulkInsertContext): void {
        const metrics = {
            errorType: error.constructor.name,
            errorCode: error.errorCode,
            jobId: context.jobId,
            fileName: context.fileName,
            totalRows: context.totalRows,
            errorRows: context.errorRows,
            errorRate: context.totalRows ? (context.errorRows || 0) / context.totalRows : 0,
            timestamp: context.timestamp
        };

        log.info('Bulk operation error metrics:', { metrics });
    }

    /**
     * Track job error metrics
     */
    private static trackJobErrorMetrics(error: AppError, context: JobContext): void {
        const metrics = {
            errorType: error.constructor.name,
            errorCode: error.errorCode,
            jobId: context.jobId,
            jobType: context.jobType,
            status: context.status,
            progress: context.progress,
            timestamp: context.timestamp
        };

        log.info('Job error metrics:', { metrics });
    }

    /**
     * Sanitize context data for logging
     */
    private static sanitizeContext(context: ErrorContext): any {
        const sanitized = { ...context };

        // Remove or mask sensitive data
        if (sanitized.userAgent) {
            sanitized.userAgent = sanitized.userAgent.substring(0, 100); // Truncate long user agents
        }

        return sanitized;
    }

    /**
     * Sanitize sensitive data from logs
     */
    private static sanitizeData(data: any): any {
        if (!data || typeof data !== 'object') {
            return data;
        }

        const sensitiveFields = [
            'password', 'token', 'secret', 'key', 'auth', 'authorization',
            'apiKey', 'accessToken', 'refreshToken', 'privateKey'
        ];

        const sanitized = Array.isArray(data) ? [...data] : { ...data };

        const sanitizeObject = (obj: any): any => {
            if (!obj || typeof obj !== 'object') {
                return obj;
            }

            const result = Array.isArray(obj) ? [] : {} as Record<string, any>;

            Object.keys(obj).forEach(key => {
                const lowerKey = key.toLowerCase();

                if (sensitiveFields.some(field => lowerKey.includes(field))) {
                    if (Array.isArray(result)) {
                        (result as any)[key] = '[REDACTED]';
                    } else {
                        result[key] = '[REDACTED]';
                    }
                } else if (typeof obj[key] === 'object') {
                    if (Array.isArray(result)) {
                        (result as any)[key] = sanitizeObject(obj[key]);
                    } else {
                        result[key] = sanitizeObject(obj[key]);
                    }
                } else {
                    if (Array.isArray(result)) {
                        (result as any)[key] = obj[key];
                    } else {
                        result[key] = obj[key];
                    }
                }
            });

            return result;
        };

        return sanitizeObject(sanitized);
    }
}