/**
 * Enhanced Express Error Middleware
 * Centralized error handling for all application errors
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../logger';
import { AppError, InternalError, ValidationError, DatabaseError, DuplicateError } from '../errors/error.type';
import { ErrorContext } from '../../types/errors.types';
const log = logger({ name: 'ErrorMiddleware' });


export class ErrorMiddleware {
    /**
     * Main Express error handling middleware
     */
    static handleError = (
        error: any,
        req: Request,
        res: Response,
        next: NextFunction
    ): void => {
        try {
            // Transform non-AppError instances to AppError
            const appError = ErrorMiddleware.transformToAppError(error);

            // Extract error context from request
            const context = ErrorMiddleware.extractErrorContext(req);

            // Log error with context
            ErrorMiddleware.logError(appError, context, req);

            // Track error metrics
            ErrorMiddleware.trackErrorMetrics(appError, context);

            // Generate HTTP response
            ErrorMiddleware.sendErrorResponse(appError, res);

        } catch (middlewareError) {
            log.error('Error in error middleware:', middlewareError);

            // Fallback error response
            res.status(500).json({
                error: 'Internal server error',
                errorCode: 'INTERNAL_ERROR_001'
            });
        }
    };

    /**
     * Transform any error to AppError instance
     */
    static transformToAppError(error: any): AppError {
        if (error instanceof AppError) {
            return error;
        }

        // Handle specific error types
        if (error.name === 'ValidationError') {
            return new ValidationError(
                'Validation failed',
                'VALIDATION_001',
                ErrorMiddleware.extractValidationDetails(error)
            );
        }

        if (error.name === 'MongoError' || error.code === 11000) {
            return new DuplicateError(
                'Resource already exists',
                'DUPLICATE_001'
            );
        }

        if (error.name === 'MongoError' || error.name === 'MongoServerError') {
            return new DatabaseError(
                'Database operation failed',
                'DB_001'
            );
        }

        if (error.name === 'PrismaClientKnownRequestError') {
            return ErrorMiddleware.transformPrismaError(error);
        }

        if (error.name === 'CastError') {
            return new ValidationError(
                'Invalid ID format',
                'VALIDATION_002',
                [{ field: error.path, message: 'Invalid ID format' }]
            );
        }

        // Default transformation for unknown errors
        const shouldExposeDetails = process.env.NODE_ENV === 'development';

        return new InternalError(
            shouldExposeDetails ? error.message : 'An unexpected error occurred',
            'INTERNAL_001',
            shouldExposeDetails
        );
    }

    /**
     * Extract error context from Express request
     */
    static extractErrorContext(req: Request): ErrorContext {
        const context: ErrorContext = {
            requestId: req.requestId || `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            endpoint: req.originalUrl,
            method: req.method,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress,
            timestamp: new Date()
        };

        // Extract user context if available
        if (req.user) {
            context.userId = req.user.id;
            context.organizationId = req.user.organizationId;
        }

        // Extract additional context from request object
        if (req.errorContext) {
            Object.assign(context, req.errorContext);
        }

        return context;
    }

    /**
     * Log error with context and metadata
     */
    static logError(error: AppError, context: ErrorContext, req: Request): void {
        const logData = {
            error: {
                name: error.name,
                message: error.message,
                code: error.errorCode,
                statusCode: error.statusCode,
                isOperational: error.isOperational,
                details: error.details
            },
            context: {
                requestId: context.requestId,
                userId: context.userId,
                organizationId: context.organizationId,
                endpoint: context.endpoint,
                method: context.method,
                ip: context.ip,
                userAgent: context.userAgent,
                timestamp: context.timestamp.toISOString()
            },
            request: {
                query: ErrorMiddleware.sanitizeData(req.query),
                params: ErrorMiddleware.sanitizeData(req.params),
                body: ErrorMiddleware.sanitizeData(req.body)
            }
        };

        // Include stack trace for non-operational errors
        if (!error.isOperational && process.env.NODE_ENV !== 'production') {
            (logData.error as any)['stack'] = error.stack;
        }

        // Log at appropriate level based on error type
        if (error.isOperational) {
            log.warn('Operational error occurred:', logData);
        } else {
            log.error('Programming error occurred:', logData);
        }
    }

    /**
     * Track error metrics
     */
    static trackErrorMetrics(error: AppError, context: ErrorContext): void {
        // This would integrate with your metrics system (e.g., Prometheus, DataDog)
        // For now, we'll log metrics data
        const metrics = {
            errorType: error.constructor.name,
            errorCode: error.errorCode,
            statusCode: error.statusCode,
            endpoint: context.endpoint,
            method: context.method,
            isOperational: error.isOperational,
            timestamp: context.timestamp.toISOString()
        };

        log.info('Error metrics:', { metrics });
    }

    /**
     * Send standardized error response
     */
    static sendErrorResponse(error: AppError, res: Response): void {
        const response: any = {
            error: error.message,
            errorCode: error.errorCode
        };

        // Include validation details if available
        if (error.details && error.details.length > 0) {
            response.details = error.details;
        }

        // Include additional error data for development
        if (process.env.NODE_ENV === 'development' && !error.isOperational) {
            response.stack = error.stack;
        }

        res.status(error.statusCode).json(response);
    }

    /**
     * Extract validation details from error
     */
    private static extractValidationDetails(error: any): Array<{ field: string; message: string }> {
        const details: Array<{ field: string; message: string }> = [];

        if (error.errors) {
            Object.keys(error.errors).forEach(field => {
                const fieldError = error.errors[field];
                details.push({
                    field,
                    message: fieldError.message || fieldError.toString()
                });
            });
        }

        return details;
    }

    /**
     * Transform Prisma errors to AppError
     */
    private static transformPrismaError(error: any): AppError {
        switch (error.code) {
            case 'P2002':
                return new DuplicateError(
                    'A record with this information already exists',
                    'DUPLICATE_002'
                );
            case 'P2025':
                return new ValidationError(
                    'Record not found',
                    'NOT_FOUND_001'
                );
            default:
                return new DatabaseError(
                    'Database operation failed',
                    'DB_002'
                );
        }
    }

    /**
     * Sanitize sensitive data from logs
     */
    private static sanitizeData(data: any): any {
        if (!data || typeof data !== 'object') {
            return data;
        }

        const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'authorization'];
        const sanitized = { ...data };

        Object.keys(sanitized).forEach(key => {
            const lowerKey = key.toLowerCase();
            if (sensitiveFields.some(field => lowerKey.includes(field))) {
                sanitized[key] = '[REDACTED]';
            }
        });

        return sanitized;
    }
}