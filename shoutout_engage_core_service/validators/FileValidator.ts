import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import { ERROR_CODES, FILE_CONSTRAINTS } from '../lib/constant/file.constants';
import { BadRequestError } from '../lib/errors/error.type';
import { ErrorContext } from '../lib/utils/errorContext';
import { ErrorMiddleware } from '../lib/middlewares/error.middleware';

/**
 * File validation middleware class
 */
export class FileValidator {
  /**
   * Configure multer storage
   */
  private static storage = multer.memoryStorage();

  /**
   * Create multer upload instance
   */
  private static upload = multer({
    storage: this.storage,
    limits: {
      fileSize: FILE_CONSTRAINTS.maxSizeBytes
    },
    fileFilter: (req, file, cb) => {
      this.validateFileType(file, cb);
    }
  });

  /**
   * Validate file type based on extension
   */
  private static validateFileType(file: Express.Multer.File, cb: multer.FileFilterCallback): void {
    const ext = path.extname(file.originalname).toLowerCase();
    if (FILE_CONSTRAINTS.allowedExtensions.includes(ext)) {
      return cb(null, true);
    }
    const error = new BadRequestError(
      `Only ${FILE_CONSTRAINTS.allowedExtensions.join(', ')} files are allowed`,
      ERROR_CODES.INVALID_EXTENSION,
      [{ field: 'file', message: `Invalid file extension: ${ext}. Allowed extensions: ${FILE_CONSTRAINTS.allowedExtensions.join(', ')}` }]
    ) as any;
    cb(error, false);
  }

  /**
   * Get middleware for file upload
   */
  public static getUploadMiddleware() {
    return this.upload.single('file');
  }

  /**
   * Handle multer errors
   * Converts multer errors to AppError instances and passes them to the ErrorMiddleware
   */
  public static handleErrors(err: any, req: Request, res: Response, next: NextFunction) {
    if (!err) {
      return next();
    }

    // Create error context for enhanced error logging
    const context = new ErrorContext(req);

    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        const error = new BadRequestError(
          `File too large. Maximum size is ${FILE_CONSTRAINTS.maxSizeBytes / (1024 * 1024)}MB`,
          ERROR_CODES.FILE_TOO_LARGE,
          [{ 
            field: 'file', 
            message: `File too large. Please split your file or reduce its size.`
          }]
        ).withContext(context.toLogObject());
        
        return ErrorMiddleware.handleError(error, req, res, next);
      }
      
      const error = new BadRequestError(
        err.message,
        'FILE_ERROR'
      ).withContext(context.toLogObject());
      
      return ErrorMiddleware.handleError(error, req, res, next);
    }

    // If it's already an AppError, just add context
    if (err.statusCode && err.errorCode) {
      return ErrorMiddleware.handleError(
        err.withContext ? err.withContext(context.toLogObject()) : err, 
        req, 
        res, 
        next
      );
    }

    // Convert generic error to BadRequestError
    const error = new BadRequestError(
      err.message,
      err.code || 'FILE_ERROR'
    ).withContext(context.toLogObject());
    
    return ErrorMiddleware.handleError(error, req, res, next);
  }

  /**
   * Validate that a file was uploaded
   * Throws BadRequestError if no file is present
   */
  public static validateFilePresence(req: Request, res: Response, next: NextFunction) {
    if (!req.file) {
      const context = new ErrorContext(req);
      throw new BadRequestError(
        'No file uploaded',
        'FILE_MISSING',
        [{ field: 'file', message: 'File is required but was not provided' }]
      ).withContext(context.toLogObject());
    }
    next();
  }
}
