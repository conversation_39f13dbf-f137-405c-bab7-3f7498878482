import Joi from 'joi';
import { ContactStatus } from '../types/contact.types';
import { ValidationError, BadRequestError } from '../lib/errors/error.type';
import {FILE_CONSTRAINTS} from "../lib/constant/file.constants";

/**
 * Joi validation schemas for contact-related operations
 */
export class ContactsValidator {
  /**
   * Schema for validating GET contacts query parameters
   */
  static readonly getContactsSchema = Joi.object({
    // Pagination parameters (required)
    page: Joi.number()
      .integer()
      .min(1)
      .required()
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
        'any.required': 'Page is required'
      }),
    
    page_size: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .required()
      .messages({
        'number.base': 'Page size must be a number',
        'number.integer': 'Page size must be an integer',
        'number.min': 'Page size must be at least 1',
        'number.max': 'Page size cannot exceed 100',
        'any.required': 'Page size is required'
      }),
    
    // Sorting parameters
    sort_by: Joi.string()
      .valid('name', 'email', 'phone', 'created_at', 'updated_at')
      .default('created_at')
      .messages({
        'any.only': 'Sort field must be one of: name, email, phone, created_at, updated_at'
      }),
    
    sort_direction: Joi.string()
      .valid('asc', 'desc')
      .default('desc')
      .messages({
        'any.only': 'Sort direction must be either asc or desc'
      }),
    
    // Search parameter
    search: Joi.string()
      .trim()
      .min(1)
      .messages({
        'string.min': 'Search term must not be empty'
      }),
    
    // MongoDB query for advanced filtering
    contactFilterQuery: Joi.string()
      .custom((value, helpers) => {
        try {
          const parsed = JSON.parse(value);
          
          // Basic validation of MongoDB query structure
          if (!parsed || typeof parsed !== 'object') {
            return helpers.error('string.mongoQuery.structure');
          }
          
          return parsed;
        } catch (error) {
          return helpers.error('string.mongoQuery.parse');
        }
      })
      .messages({
        'string.mongoQuery.parse': 'Contact filter query must be a valid JSON string',
        'string.mongoQuery.structure': 'Contact filter query must be a valid MongoDB query object'
      })
  }).options({
    stripUnknown: true,
    abortEarly: false
  });

  /**
   * Schema for validating contact creation requests
   */
  static readonly createContactSchema = Joi.object({
    // Required fields
    name: Joi.string()
      .trim()
      .min(1)
      .max(255)
      .required()
      .messages({
        'string.empty': 'Name is required',
        'string.min': 'Name must not be empty',
        'string.max': 'Name must not exceed 255 characters',
        'any.required': 'Name is required'
      }),

    email: Joi.string()
      .email({ tlds: { allow: false } })
      .trim()
      .lowercase()
      .required()
      .messages({
        'string.email': 'Email must be a valid email address',
        'string.empty': 'Email is required',
        'any.required': 'Email is required'
      }),

    phone: Joi.string()
      .trim()
      .pattern(/^[+]?[0-9\s\-\(\)]+$/)
      .min(7)
      .max(20)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must contain only numbers, spaces, hyphens, parentheses, and optional plus sign',
        'string.min': 'Phone number must be at least 7 characters',
        'string.max': 'Phone number must not exceed 20 characters',
        'string.empty': 'Phone number is required',
        'any.required': 'Phone number is required'
      }),

    // Optional fields
    country: Joi.string()
      .trim()
      .max(100)
      .optional()
      .messages({
        'string.max': 'Country must not exceed 100 characters'
      }),

    country_code: Joi.string()
      .trim()
      .pattern(/^[A-Z]{2}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Country code must be a 2-letter uppercase ISO code (e.g., US, CA, GB)'
      }),

    avatar_url: Joi.string()
      .uri({ scheme: ['http', 'https'] })
      .optional()
      .messages({
        'string.uri': 'Avatar URL must be a valid HTTP or HTTPS URL'
      }),

    // Tags array validation
    tags: Joi.array()
      .items(
        Joi.object({
          tag_id: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .required()
            .messages({
              'string.pattern.base': 'Tag ID must be a valid MongoDB ObjectId',
              'any.required': 'Tag ID is required'
            }),
          tag_name: Joi.string()
            .trim()
            .min(1)
            .max(100)
            .required()
            .messages({
              'string.empty': 'Tag name is required',
              'string.min': 'Tag name must not be empty',
              'string.max': 'Tag name must not exceed 100 characters',
              'any.required': 'Tag name is required'
            })
        })
      )
      .default([])
      .messages({
        'array.base': 'Tags must be an array'
      }),

    // Additional fields validation
    additional_fields: Joi.object()
      .pattern(
        Joi.string().min(1).max(50),
        Joi.alternatives().try(
          Joi.string().max(500),
          Joi.number(),
          Joi.boolean()
        )
      )
      .default({})
      .messages({
        'object.base': 'Additional fields must be an object',
        'object.pattern.match': 'Additional field values must be strings (max 500 chars), numbers, or booleans'
      }),

    // Status field (optional, defaults to active)
    status: Joi.string()
      .valid(...Object.values(ContactStatus))
      .default(ContactStatus.ACTIVE)
      .messages({
        'any.only': `Status must be one of: ${Object.values(ContactStatus).join(', ')}`
      })
  }).options({
    stripUnknown: true,
    abortEarly: false
  });

  /**
   * Schema for validating contact ID in route parameters
   */
  static readonly getContactByIdSchema = Joi.object({
    id: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.empty': 'Contact ID is required',
        'string.pattern.base': 'Contact ID must be a valid MongoDB ObjectId (24 hexadecimal characters)',
        'any.required': 'Contact ID is required'
      })
  }).options({
    stripUnknown: true,
    abortEarly: false
  });

  /**
   * Schema for validating bulk insert request parameters
   */
  static readonly bulkInsertSchema = Joi.object({
    // File validation is handled by FileValidator middleware
    // Header mapping JSON string sent from frontend to map contact attributes to CSV column names
    headerMapping: Joi.string()
      .required()
      .custom((value, helpers) => {
        try {
          const parsed = JSON.parse(value);
          
          // Validate that it's an object
          if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
            return helpers.error('string.headerMapping.structure');
          }
          
          // Validate allowed keys
          const allowedKeys = ['name', 'email', 'phone', 'country', 'country_code', 'avatar_url', 'status'];
          const providedKeys = Object.keys(parsed);
          
          for (const key of providedKeys) {
            if (!allowedKeys.includes(key)) {
              return helpers.error('string.headerMapping.invalidKey', { key });
            }
            
            // Validate that values are strings
            if (typeof parsed[key] !== 'string') {
              return helpers.error('string.headerMapping.invalidValue', { key });
            }
          }
          
          return parsed;
        } catch (error) {
          return helpers.error('string.headerMapping.parse');
        }
      })
      .description('Dynamic mapping JSON string that maps contact attributes to CSV column names')
      .messages({
        'string.headerMapping.parse': 'Header mapping must be a valid JSON string',
        'string.headerMapping.structure': 'Header mapping must be a valid JSON object',
        'string.headerMapping.invalidKey': 'Header mapping key "{{#key}}" is not valid. Allowed keys: name, email, phone, country, country_code, avatar_url, status',
        'string.headerMapping.invalidValue': 'Header mapping value for "{{#key}}" must be a string',
        'any.required': 'Header mapping is required for bulk insert operations'
      })
  }).options({
    stripUnknown: true,
    abortEarly: false,
    allowUnknown: true // Allow file and other multer fields
  });

  // ========================================
  // VALIDATION METHODS
  // ========================================

  /**
   * Validates a contact creation request payload
   * @param payload - The request payload to validate
   * @returns Validation result with value and error
   */
  static validateCreateContact(payload: any) {
    return this.createContactSchema.validate(payload);
  }
  
  /**
   * Validates GET contacts query parameters
   * @param queryParams - The query parameters to validate
   * @returns Validation result with value and error
   * @throws BadRequestError if contactFilterQuery is not a valid JSON string
   */
  static validateGetContacts(queryParams: any) {
    ContactsValidator.validateContactFilterQuery(queryParams);
    return this.getContactsSchema.validate(queryParams);
  }

  /**
   * Validates contact ID in route parameters
   * @param params - The route parameters to validate
   * @returns Validation result with value and error
   */
  static validateGetContactById(params: any) {
    return this.getContactByIdSchema.validate(params);
  }

  /**
   * Validates bulk insert request parameters
   * @param requestData - The request data to validate (body + file info)
   * @returns Validation result with value and error
   */
  static validateBulkInsert(requestData: any) {
    return this.bulkInsertSchema.validate(requestData);
  }

  // ========================================
  // MIDDLEWARE METHODS
  // ========================================

  /**
   * Express middleware for validating contact creation requests
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the request body fails validation
   */
  static validateCreateContactMiddleware(req: any, res: any, next: any) {
    try {
      const { error, value } = ContactsValidator.validateCreateContact(req.body);
      
      ContactsValidator.handleValidationError(
        error,
        'Validation failed for contact creation',
        'CONTACT_VALIDATION_001'
      );

      req.body = value;
      next();
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Express middleware for validating GET contacts query parameters
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the query parameters fail validation
   * @throws BadRequestError if contactFilterQuery is not a valid JSON string
   */
  static validateGetContactsMiddleware(req: any, res: any, next: any) {
    try {
      const { error, value } = ContactsValidator.validateGetContacts(req.query);
      
      ContactsValidator.handleValidationError(
        error,
        'Invalid query parameters for contacts retrieval',
        'CONTACT_QUERY_VALIDATION_001'
      );

      ContactsValidator.processContactFilterQuery(value);
      
      req.query = value;
      next();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Express middleware for validating contact ID in route parameters
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the contact ID fails validation
   */
  static validateGetContactByIdMiddleware(req: any, res: any, next: any) {
    try {
      const { error, value } = ContactsValidator.validateGetContactById(req.params);
      
      ContactsValidator.handleValidationError(
        error,
        'Invalid contact ID',
        'CONTACT_ID_VALIDATION_001'
      );

      req.params = value;
      next();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Express middleware for validating bulk insert requests
   * @param req - Express request object with file from multer
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the request fails validation
   * @throws BadRequestError if file validation fails
   */
  static validateBulkInsertMiddleware(req: any, res: any, next: any) {
    try {
      const file = req.file as Express.Multer.File;

      ContactsValidator.ensureValidFileName(file);
      ContactsValidator.ensureValidExtension(file);
      ContactsValidator.ensureValidSize(file);
      ContactsValidator.ensureValidMimeType(file);

      const { error, value } = ContactsValidator.validateBulkInsert(req.body);
      
      ContactsValidator.handleValidationError(
        error,
        'Validation failed for bulk insert request',
        'BULK_INSERT_VALIDATION_001'
      );

      req.body = value;
      next();
    } catch (error) {
      throw error;
    }
  }

  // ========================================
  // PRIVATE HELPER METHODS
  // ========================================

  /**
   * Validate contactFilterQuery parameter for JSON format
   * @param queryParams - Query parameters object
   * @throws BadRequestError if contactFilterQuery is not valid JSON
   */
  private static validateContactFilterQuery(queryParams: any): void {
    if (queryParams.contactFilterQuery && typeof queryParams.contactFilterQuery === 'string') {
      try {
        JSON.parse(queryParams.contactFilterQuery);
      } catch (error) {
        throw new BadRequestError(
          'Invalid contact filter query format',
          'QUERY_001',
          [{
            field: 'contactFilterQuery',
            message: 'Contact filter query must be a valid JSON string'
          }]
        );
      }
    }
  }

  /**
   * Process contactFilterQuery by parsing JSON string to object
   * @param value - Validated query parameters
   */
  private static processContactFilterQuery(value: any): void {
    if (value.contactFilterQuery && typeof value.contactFilterQuery === 'string') {
      try {
        value.contactFilterQueryObject = JSON.parse(value.contactFilterQuery);
      } catch (error) {
        throw new BadRequestError(
          'Invalid contact filter query JSON',
          'QUERY_002',
          [{
            field: 'contactFilterQuery',
            message: 'Contact filter query must be a valid JSON string'
          }]
        );
      }
    }
  }

  /**
   * Handle validation errors consistently across all middleware
   * @param error - Joi validation error
   * @param message - Error message
   * @param errorCode - Error code
   * @throws ValidationError if validation fails
   */
  private static handleValidationError(error: any, message: string, errorCode: string): void {
    if (error) {
      const validationErrors = error.details.map((detail: any) => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      throw new ValidationError(message, errorCode, validationErrors);
    }
  }


  /**
   * Ensure that the file has a valid name
   * @param file - The uploaded file object
   * @throws ValidationError if the file name is invalid
   */
  private static ensureValidFileName(file: Express.Multer.File): void {
    if (!file.originalname || file.originalname.trim().length === 0) {
      throw new ValidationError(
        'Invalid file name',
        'FILE_VALIDATION_001',
        [{ field: 'file', message: 'File must have a valid name' }]
      );
    }
  }

  /**
   * Ensure that the file has a valid extension
   * @param file - The uploaded file object
   * @throws BadRequestError if the file extension is not supported
   */
  private static ensureValidExtension(file: Express.Multer.File): void {
    const allowedExtensions = FILE_CONSTRAINTS.allowedExtensions;
    const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));

    if (!allowedExtensions.includes(fileExtension)) {
      throw new BadRequestError(
        `Invalid file type. Allowed types: ${allowedExtensions.join(', ')}`,
        'INVALID_EXTENSION',
        [{ field: 'file', message: `File extension ${fileExtension} is not supported for bulk insert` }]
      );
    }
  }

  /**
   * Ensure that the file size is within limits
   * @param file - The uploaded file object
   * @throws BadRequestError if the file size exceeds the maximum limit
   */
  private static ensureValidSize(file: Express.Multer.File): void {
    const maxSizeBytes = FILE_CONSTRAINTS.maxSizeBytes;
    if (file.size > maxSizeBytes) {
      throw new BadRequestError(
        `File size exceeds maximum limit of ${maxSizeBytes / (1024 * 1024)}MB`,
        'FILE_TOO_LARGE',
        [{ field: 'file', message: `File size ${(file.size / (1024 * 1024)).toFixed(2)}MB exceeds limit` }]
      );
    }
  }

  /**
   * Ensure that the file has a valid MIME type
   * @param file - The uploaded file object
   * @throws BadRequestError if the MIME type is not supported
   */
  private static ensureValidMimeType(file: Express.Multer.File): void {
    const allowedMimeTypes = [
      'text/csv',
      'application/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (file.mimetype && !allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestError(
        'Invalid file format',
        'INVALID_MIME_TYPE',
        [{ field: 'file', message: `MIME type ${file.mimetype} is not supported` }]
      );
    }
  }
}