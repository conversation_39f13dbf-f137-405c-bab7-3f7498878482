import { ValidationResult } from '../types/file.types';
import {FILE_CONSTRAINTS } from '../lib/constant/file.constants';
import { ValidationError, InternalError } from '../lib/errors/error.type';
import csvParser from 'csv-parser';
import * as XLSX from 'xlsx';
import { Readable } from 'stream';

/**
 * Service for handling CSV and Excel file operations
 */
export class CSVService {
  /**
   * Extract headers from a CSV or Excel file
   * @param file File uploaded by user
   * @returns Promise resolving to array of header strings
   */
  public static async extractHeaders(file: Express.Multer.File): Promise<string[]> {
    const fileExtension = this.getFileExtension(file.originalname);

    if (['.csv'].includes(fileExtension)) {
      return this.extractCSVHeaders(file);
    } else if (['.xlsx', '.xls'].includes(fileExtension)) {
      return this.extractExcelHeaders(file);
    } else {
      throw new ValidationError(
        `Unsupported file type: ${fileExtension}`,
        'FILE_TYPE_UNSUPPORTED',
        [{ field: 'file', message: `File type ${fileExtension} is not supported` }]
      );
    }
  }

  /**
   * Validate file structure based on constraints
   * @param file File uploaded by user
   * @returns Promise resolving to validation result
   */
  public static async validateFileStructure(file: Express.Multer.File): Promise<ValidationResult> {
    const fileExtension = this.getFileExtension(file.originalname);
    const result: ValidationResult = { isValid: true, errors: [] };

    try {
      if (['.csv'].includes(fileExtension)) {
        const rowCount = await this.countCSVRows(file);
        result.rowCount = rowCount;

        if (rowCount === 0 && FILE_CONSTRAINTS.requireHeaders) {
          result.isValid = false;
          result.errors.push('File contains no data or headers');
        }

        if (rowCount > FILE_CONSTRAINTS.maxRows) {
          result.isValid = false;
          result.errors.push(`File contains too many rows. Maximum is ${FILE_CONSTRAINTS.maxRows}`);
        }
      } else if (['.xlsx', '.xls'].includes(fileExtension)) {
        const excelInfo = this.getExcelInfo(file);
        result.rowCount = excelInfo.rowCount;
        result.sheetCount = excelInfo.sheetCount;

        if (excelInfo.rowCount === 0 && FILE_CONSTRAINTS.requireHeaders) {
          result.isValid = false;
          result.errors.push('File contains no data or headers');
        }

        if (excelInfo.rowCount > FILE_CONSTRAINTS.maxRows) {
          result.isValid = false;
          result.errors.push(`File contains too many rows. Maximum is ${FILE_CONSTRAINTS.maxRows}`);
        }

        if (FILE_CONSTRAINTS.singleSheetOnly && excelInfo.sheetCount > 1) {
          result.isValid = false;
          result.errors.push('Only single sheet Excel files are supported');
        }
      }

      return result;
    } catch (error) {
      throw new InternalError(
        `Error validating file structure: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'FILE_VALIDATION_ERROR'
      );
    }
  }

  /**
   * Get file extension from filename
   * @param filename Original filename
   * @returns Lowercase file extension with dot
   */
  private static getFileExtension(filename: string): string {
    return (filename.slice(filename.lastIndexOf('.')).toLowerCase());
  }

  /**
   * Extract headers from CSV file
   * @param file CSV file
   * @returns Promise resolving to array of header strings
   */
  private static extractCSVHeaders(file: Express.Multer.File): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const headers: string[] = [];
      const fileBuffer = Buffer.from(file.buffer);
      const stream = Readable.from(fileBuffer);
      let isFirstRow = true;

      stream
        .pipe(csvParser({ headers: false }))
        .on('data', (row: any) => {
          if (isFirstRow) {
            // The first row contains the headers when headers: false
            // csv-parser returns an object with numeric keys (0, 1, 2, etc.)
            const rowValues = Object.values(row) as string[];
            headers.push(...rowValues.filter(value => value !== undefined && value !== null && value.toString().trim() !== ''));
            isFirstRow = false;
            stream.destroy(); // Stop after reading the first row
          }
        })
        .on('end', () => {
          if (headers.length === 0 && FILE_CONSTRAINTS.requireHeaders) {
            reject(new ValidationError(
              'No headers found in CSV file',
              'CSV_NO_HEADERS',
              [{ field: 'file', message: 'CSV file must contain headers' }]
            ));
          } else {
            resolve(headers);
          }
        })
        .on('error', (error) => {
          reject(new InternalError(
            `Error parsing CSV file: ${error.message}`,
            'CSV_PARSE_ERROR'
          ));
        });
    });
  }

  /**
   * Extract headers from Excel file
   * @param file Excel file
   * @returns Promise resolving to array of header strings
   */
  private static extractExcelHeaders(file: Express.Multer.File): Promise<string[]> {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // Convert the first row to an array of header strings
      const headers: string[] = [];
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: range.s.r, c: col });
        const cell = worksheet[cellAddress];
        if (cell && cell.v) {
          headers.push(String(cell.v).trim());
        }
      }

      if (headers.length === 0 && FILE_CONSTRAINTS.requireHeaders) {
        throw new ValidationError(
          'No headers found in Excel file',
          'EXCEL_NO_HEADERS',
          [{ field: 'file', message: 'Excel file must contain headers' }]
        );
      }

      return Promise.resolve(headers);
    } catch (error) {
      const message =
          error instanceof Error
              ? error.message
              : typeof error === "string"
                  ? error
                  : "Unknown error";

      return Promise.reject(new InternalError(
        `Error parsing Excel file: ${message}`,
        'EXCEL_PARSE_ERROR'
      ));
    }

  }

  /**
   * Count rows in a CSV file
   * @param file CSV file
   * @returns Promise resolving to row count
   */
  private static countCSVRows(file: Express.Multer.File): Promise<number> {
    return new Promise((resolve, reject) => {
      let rowCount = 0;
      const fileBuffer = Buffer.from(file.buffer);
      const stream = Readable.from(fileBuffer);

      stream
        .pipe(csvParser())
        .on('data', () => {
          rowCount++;
          // Stop counting if we exceed the maximum
          if (rowCount > FILE_CONSTRAINTS.maxRows) {
            stream.destroy();
          }
        })
        .on('end', () => {
          resolve(rowCount);
        })
        .on('error', (error) => {
          reject(new InternalError(
            `Error counting CSV rows: ${error.message}`,
            'CSV_COUNT_ERROR'
          ));
        });
    });
  }

  /**
   * Get Excel file information
   * @param file Excel file
   * @returns Object with row count and sheet count
   */
  private static getExcelInfo(file: Express.Multer.File): { rowCount: number; sheetCount: number } {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // Get row count from the worksheet reference
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      const rowCount = range.e.r - range.s.r + 1;

      return {
        rowCount,
        sheetCount: workbook.SheetNames.length
      };
    } catch (error) {
      throw new InternalError(
        `Error getting Excel info: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'EXCEL_INFO_ERROR'
      );
    }
  }
}
