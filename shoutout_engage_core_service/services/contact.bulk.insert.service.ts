/**
 * Service for handling bulk contact insertion operations
 * 
 * This service manages:
 * - Creating and tracking bulk insert jobs
 * - File storage and cleanup
 * - Job status monitoring
 */

import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { JOB_TYPE, TIME_CONSTANTS, FILE_CONSTANTS } from '../lib/constant/job.constants';
import { logger } from '../lib/logger';
import config from '../lib/config';
import { 
  BadRequestError, 
  NotFoundError,
  DatabaseError 
} from '../lib/errors/error.type';
import { ObjectId } from 'mongodb';
import { CommonJobDAO } from '../lib/db/dao/CommonJobDAO';
import { CreateJobRequest } from '../types/job.types';
import {ContactBulkInsertJobData, ContactBulkInsertQueue} from "../queues";


const log = logger(config.logger);

// Constants
const UPLOAD_DIR = path.join(process.cwd(), 'uploads');

/**
 * Service for handling bulk contact insertion operations
 */
export class ContactBulkInsertService {
  /**
   * Creates a new bulk insert job
   * 
   * @param file - The uploaded CSV file
   * @param organizationId - The organization ID
   * @param userId - The user ID
   * @param headerMapping - Required header mapping configuration from client
   * @param organizationEmail - The organization email for notifications
   * @returns The created job ID
   */
  public static async createBulkInsertJob(file: Express.Multer.File, organizationId: string, userId: string, headerMapping: { [key: string]: string }, organizationEmail?: string): Promise<{ jobId: string }> {
    try {
      // Ensure upload directory exists
      if (!fs.existsSync(UPLOAD_DIR)) {
        fs.mkdirSync(UPLOAD_DIR, { recursive: true });
      }
      if (!organizationEmail) {
        throw new Error('Organization email is required for bulk insert job');
      }

      // Generate unique filename
      const uniqueId = uuidv4();
      const fileExtension = path.extname(file.originalname);
      const filename = `${uniqueId}${fileExtension}`;
      const filePath = path.join(UPLOAD_DIR, filename);

      // Save file to disk
      fs.writeFileSync(filePath, file.buffer);

      // Create job record in database using data access layer
      const jobRequest: CreateJobRequest = {
        jobType: JOB_TYPE.CONTACT_BULK_INSERT,
        orgId: organizationId,
        createdBy: userId,
        data: {
          filePath,
          originalFilename: file.originalname,
          fileSize: file.size,
          notificationEmail: organizationEmail,
          headerMapping
        }
      };

      const job = await CommonJobDAO.createJob(jobRequest);

      // Add job to processing queue
      const jobData: ContactBulkInsertJobData = {
        jobId: job._id.toString(),
        orgId: organizationId,
        createdBy: userId,
        filePath,
        fileName: path.basename(filePath), // Extract filename from path
        fileSize: fs.statSync(filePath).size, // Get actual file size
        userEmail: organizationEmail,
        headerMapping
      };
      await ContactBulkInsertQueue.addJob(jobData);


      return { jobId: job._id.toString() };
    } catch (error) {
      log.error('Error creating bulk insert job:', error);
      
      if (error instanceof Error) {
        throw new BadRequestError(
          `Failed to create bulk insert job: ${error.message}`,
          'BULK_INSERT_001',
          [{ field: 'file', message: error.message }]
        );
      }
      
      throw new BadRequestError(
        'Failed to create bulk insert job',
        'BULK_INSERT_001'
      );
    }
  }

  /**
   * Gets the status of a bulk insert job
   * 
   * @param jobId - The job ID
   * @returns The job status information
   */
  public static async getJobStatus(jobId: string): Promise<{
    jobId: string;
    status: string;
    progress: number;
    total: number;
    processed: number;
    successful: number;
    failed: number;
    orgId: string;
    createdAt: Date;
    updatedAt: Date;
    errors?: Array<{ row: number; message: string }>;
  }> {
    try {
      if (!ObjectId.isValid(jobId)) {
        throw new BadRequestError(
          'Invalid job ID format',
          'JOB_002',
          [{ field: 'jobId', message: 'Job ID must be a valid MongoDB ObjectId' }]
        );
      }

      // Use data access layer to get job
      const job = await CommonJobDAO.getJobById(jobId);

      // Transform errors to match the expected format
      const formattedErrors = job.errors?.map(error => ({
        row: error.rowNumber || 0,
        message: error.message
      }));

      return {
        jobId: job._id.toString(),
        status: job.status,
        progress: job.progress?.percentage || 0,
        total: job.progress?.totalItems || 0,
        processed: job.progress?.processedItems || 0,
        successful: job.progress?.successfulItems || 0,
        failed: job.progress?.failedItems || 0,
        orgId: job.orgId,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        errors: formattedErrors
      };
    } catch (error) {
      log.error('Error getting job status:', error);
      
      if (error instanceof NotFoundError || error instanceof BadRequestError) {
        throw error;
      }
      
      throw new DatabaseError(
        'Failed to retrieve job status',
        'DB_001',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Checks if a file exists at the given path
   * 
   * @param filePath - The file path to check
   * @returns True if the file exists, false otherwise
   */
  public static async fileExists(filePath: string): Promise<boolean> {
    try {
      return fs.existsSync(filePath);
    } catch (error) {
      log.error('Error checking file existence:', error);
      return false;
    }
  }

  /**
   * Cleans up old files that exceed the retention period
   * 
   * @returns The number of files cleaned up
   */
  public static async cleanupOldFiles(): Promise<number> {
    try {
      if (!fs.existsSync(UPLOAD_DIR)) {
        return 0;
      }

      const files = fs.readdirSync(UPLOAD_DIR);
      const now = new Date();
      let cleanedCount = 0;

      for (const file of files) {
        const filePath = path.join(UPLOAD_DIR, file);
        const stats = fs.statSync(filePath);
        const fileAge = (now.getTime() - stats.mtime.getTime()) / TIME_CONSTANTS.MS_PER_DAY;

        if (fileAge > FILE_CONSTANTS.FILE_RETENTION_DAYS) {
          fs.unlinkSync(filePath);
          cleanedCount++;
          log.info(`Cleaned up old file: ${filePath}`);
        }
      }

      return cleanedCount;
    } catch (error) {
      log.error('Error cleaning up old files:', error);
      return 0;
    }
  }
}