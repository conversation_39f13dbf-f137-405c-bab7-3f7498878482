import fs from 'fs';
import path from 'path';
import { logger } from '../lib/logger';
import config from '../lib/config';
import { InternalError } from '../lib/errors/error.type';
import { v4 as uuidv4 } from 'uuid';

const log = logger(config.logger);

/**
 * Interface for error report data
 */
export interface ErrorReportData {
  rowNumber: number;
  originalData: any;
  errors: Array<{
    field: string;
    message: string;
    errorCode?: string;
  }>;
  timestamp: Date;
}

/**
 * Interface for error report options
 */
export interface ErrorReportOptions {
  includeHeaders?: boolean;
  includeTimestamp?: boolean;
  includeRowNumbers?: boolean;
  customHeaders?: string[];
  filename?: string;
  directory?: string;
}

/**
 * Service for generating CSV error reports for failed bulk operations
 */
export class ErrorReportService {
  private static readonly DEFAULT_TEMP_DIR = path.join(process.cwd(), 'temp', 'error-reports');
  private static readonly DEFAULT_RETENTION_HOURS = 24; // Keep error reports for 24 hours

  /**
   * Generate CSV error report from error data
   * @param errors - Array of error report data
   * @param options - Options for report generation
   * @returns Promise with the path to the generated report file
   */
  static async generateErrorReport(
    errors: ErrorReportData[],
    options: ErrorReportOptions = {}
  ): Promise<string> {
    try {
      if (!errors || errors.length === 0) {
        throw new InternalError(
          'No error data provided for report generation',
          'ERROR_REPORT_001'
        );
      }

      log.info(`Generating error report for ${errors.length} errors`);

      // Ensure temp directory exists
      const reportDir = options.directory || this.DEFAULT_TEMP_DIR;
      await this.ensureDirectoryExists(reportDir);

      // Generate filename
      const filename = options.filename || `error-report-${uuidv4()}.csv`;
      const filePath = path.join(reportDir, filename);

      // Generate CSV content
      const csvContent = this.generateCSVContent(errors, options);

      // Write file
      await fs.promises.writeFile(filePath, csvContent, 'utf8');

      log.info(`Error report generated successfully: ${filePath}`);

      // Schedule cleanup
      this.scheduleCleanup(filePath);

      return filePath;

    } catch (error) {
      log.error('Failed to generate error report:', error);
      throw new InternalError(
        `Failed to generate error report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ERROR_REPORT_002'
      );
    }
  }

  /**
   * Generate CSV error report for contact bulk insert errors
   * @param jobId - ID of the bulk insert job
   * @param errors - Array of contact processing errors
   * @param originalHeaders - Original CSV headers
   * @returns Promise with the path to the generated report file
   */
  static async generateContactBulkInsertErrorReport(
    jobId: string,
    errors: Array<{
      rowNumber: number;
      contactData: any;
      error: {
        message: string;
        errorCode?: string;
        details?: Array<{ field: string; message: string }>;
      };
      timestamp: Date;
    }>,
    originalHeaders: string[] = []
  ): Promise<string> {
    try {
      log.info(`Generating contact bulk insert error report for job ${jobId}`);

      // Transform errors to ErrorReportData format
      const errorReportData: ErrorReportData[] = errors.map(error => ({
        rowNumber: error.rowNumber,
        originalData: error.contactData,
        errors: error.error.details || [{
          field: 'general',
          message: error.error.message,
          errorCode: error.error.errorCode
        }],
        timestamp: error.timestamp
      }));

      // Generate custom headers for contact data
      const contactHeaders = originalHeaders.length > 0 
        ? originalHeaders 
        : this.extractContactHeaders(errors.map(e => e.contactData));

      const options: ErrorReportOptions = {
        includeHeaders: true,
        includeTimestamp: true,
        includeRowNumbers: true,
        customHeaders: contactHeaders,
        filename: `contact-bulk-insert-errors-${jobId}.csv`
      };

      return await this.generateErrorReport(errorReportData, options);

    } catch (error) {
      log.error(`Failed to generate contact bulk insert error report for job ${jobId}:`, error);
      throw new InternalError(
        `Failed to generate contact bulk insert error report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ERROR_REPORT_003'
      );
    }
  }

  /**
   * Generate CSV content from error data
   * @param errors - Array of error report data
   * @param options - Report generation options
   * @returns CSV content as string
   */
  private static generateCSVContent(errors: ErrorReportData[], options: ErrorReportOptions): string {
    const lines: string[] = [];

    // Generate headers
    if (options.includeHeaders !== false) {
      const headers = this.generateHeaders(errors, options);
      lines.push(this.escapeCSVRow(headers));
    }

    // Generate data rows
    for (const error of errors) {
      const row = this.generateDataRow(error, options);
      lines.push(this.escapeCSVRow(row));
    }

    return lines.join('\n');
  }

  /**
   * Generate CSV headers
   * @param errors - Array of error report data
   * @param options - Report generation options
   * @returns Array of header strings
   */
  private static generateHeaders(errors: ErrorReportData[], options: ErrorReportOptions): string[] {
    const headers: string[] = [];

    // Add row number header
    if (options.includeRowNumbers !== false) {
      headers.push('Row Number');
    }

    // Add original data headers
    if (options.customHeaders && options.customHeaders.length > 0) {
      headers.push(...options.customHeaders);
    } else if (errors.length > 0) {
      // Extract headers from first error's original data
      const firstError = errors[0];
      if (firstError.originalData && typeof firstError.originalData === 'object') {
        headers.push(...Object.keys(firstError.originalData));
      }
    }

    // Add error information headers
    headers.push('Error Field', 'Error Message', 'Error Code');

    // Add timestamp header
    if (options.includeTimestamp !== false) {
      headers.push('Error Timestamp');
    }

    return headers;
  }

  /**
   * Generate data row for an error
   * @param error - Error report data
   * @param options - Report generation options
   * @returns Array of data values
   */
  private static generateDataRow(error: ErrorReportData, options: ErrorReportOptions): string[] {
    // Handle multiple errors per row by creating separate rows for each error
    const rows: string[][] = [];

    for (const errorDetail of error.errors) {
      const row: string[] = [];

      // Add row number
      if (options.includeRowNumbers !== false) {
        row.push(error.rowNumber.toString());
      }

      // Add original data values
      if (options.customHeaders && options.customHeaders.length > 0) {
        for (const header of options.customHeaders) {
          const value = error.originalData && error.originalData[header] 
            ? error.originalData[header].toString() 
            : '';
          row.push(value);
        }
      } else if (error.originalData && typeof error.originalData === 'object') {
        for (const value of Object.values(error.originalData)) {
          row.push(value ? value.toString() : '');
        }
      }

      // Add error information
      row.push(errorDetail.field || '');
      row.push(errorDetail.message || '');
      row.push(errorDetail.errorCode || '');

      // Add timestamp
      if (options.includeTimestamp !== false) {
        row.push(error.timestamp.toISOString());
      }

      rows.push(row);
    }

    // For now, return the first row. In the future, we might want to handle multiple errors differently
    return rows[0] || [];
  }

  /**
   * Escape CSV row values
   * @param row - Array of values to escape
   * @returns Escaped CSV row string
   */
  private static escapeCSVRow(row: string[]): string {
    return row.map(value => {
      // Convert to string and handle null/undefined
      const stringValue = value != null ? value.toString() : '';
      
      // Escape quotes and wrap in quotes if necessary
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      
      return stringValue;
    }).join(',');
  }

  /**
   * Extract headers from contact data objects
   * @param contactDataArray - Array of contact data objects
   * @returns Array of unique headers
   */
  private static extractContactHeaders(contactDataArray: any[]): string[] {
    const headerSet = new Set<string>();

    for (const contactData of contactDataArray) {
      if (contactData && typeof contactData === 'object') {
        Object.keys(contactData).forEach(key => headerSet.add(key));
      }
    }

    return Array.from(headerSet).sort();
  }

  /**
   * Ensure directory exists, create if it doesn't
   * @param dirPath - Directory path to ensure
   */
  private static async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.promises.access(dirPath);
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.promises.mkdir(dirPath, { recursive: true });
      log.info(`Created error report directory: ${dirPath}`);
    }
  }

  /**
   * Schedule cleanup of error report file
   * @param filePath - Path to the file to clean up
   * @param retentionHours - Hours to keep the file (default: 24)
   */
  private static scheduleCleanup(filePath: string, retentionHours: number = this.DEFAULT_RETENTION_HOURS): void {
    const cleanupDelay = retentionHours * 60 * 60 * 1000; // Convert hours to milliseconds

    setTimeout(async () => {
      try {
        await fs.promises.unlink(filePath);
        log.info(`Cleaned up error report file: ${filePath}`);
      } catch (error) {
        log.warn(`Failed to clean up error report file: ${filePath}`, error);
      }
    }, cleanupDelay);
  }

  /**
   * Clean up old error report files
   * @param directory - Directory to clean up (optional, uses default if not provided)
   * @param maxAgeHours - Maximum age of files to keep (default: 24 hours)
   */
  static async cleanupOldReports(
    directory?: string, 
    maxAgeHours: number = this.DEFAULT_RETENTION_HOURS
  ): Promise<void> {
    try {
      const reportDir = directory || this.DEFAULT_TEMP_DIR;
      
      // Check if directory exists
      try {
        await fs.promises.access(reportDir);
      } catch (error) {
        // Directory doesn't exist, nothing to clean up
        return;
      }

      const files = await fs.promises.readdir(reportDir);
      const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

      let cleanedCount = 0;

      for (const file of files) {
        const filePath = path.join(reportDir, file);
        
        try {
          const stats = await fs.promises.stat(filePath);
          
          if (stats.isFile() && stats.mtime.getTime() < cutoffTime) {
            await fs.promises.unlink(filePath);
            cleanedCount++;
          }
        } catch (error) {
          log.warn(`Failed to process file during cleanup: ${filePath}`, error);
        }
      }

      if (cleanedCount > 0) {
        log.info(`Cleaned up ${cleanedCount} old error report files from ${reportDir}`);
      }

    } catch (error) {
      log.error('Failed to clean up old error reports:', error);
      throw new InternalError(
        `Failed to clean up old error reports: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ERROR_REPORT_004'
      );
    }
  }

  /**
   * Get error report file info
   * @param filePath - Path to the error report file
   * @returns File information
   */
  static async getReportInfo(filePath: string): Promise<{
    exists: boolean;
    size?: number;
    created?: Date;
    modified?: Date;
  }> {
    try {
      const stats = await fs.promises.stat(filePath);
      
      return {
        exists: true,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      };
    } catch (error) {
      return { exists: false };
    }
  }

  /**
   * Delete error report file
   * @param filePath - Path to the error report file to delete
   */
  static async deleteReport(filePath: string): Promise<void> {
    try {
      await fs.promises.unlink(filePath);
      log.info(`Deleted error report file: ${filePath}`);
    } catch (error) {
      log.error(`Failed to delete error report file: ${filePath}`, error);
      throw new InternalError(
        `Failed to delete error report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ERROR_REPORT_005'
      );
    }
  }
}

export default ErrorReportService;