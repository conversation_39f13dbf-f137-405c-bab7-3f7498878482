import Queue, { Job, JobOptions } from 'bull';
import RedisConnector from '../lib/db/connectors/RedisConnector';
import { QUEUE_NAMES, DEFAULT_QUEUE_OPTIONS, QUEUE_OPTIONS, QUEUE_MONITORING } from '../lib/constant/queue.constants';
import { logger } from '../lib/logger';
import config from '../lib/config';

const log = logger(config.logger);

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
}

export interface QueueHealth {
  name: string;
  isHealthy: boolean;
  stats: QueueStats;
  lastError?: string;
  lastErrorTime?: Date;
}

/**
 * Universal Queue Manager for handling all job types
 * Implements singleton pattern for centralized queue management
 */
export class QueueManager {
  private static instance: QueueManager;
  private queues: Map<string, Queue.Queue> = new Map();
  private healthCheckInterval?: NodeJS.Timeout;
  private statsUpdateInterval?: NodeJS.Timeout;
  private isInitialized = false;

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get singleton instance of QueueManager
   */
  static getInstance(): QueueManager {
    if (!QueueManager.instance) {
      QueueManager.instance = new QueueManager();
    }
    return QueueManager.instance;
  }

  /**
   * Initialize all queues with Redis configuration
   */
  public async initializeQueues(): Promise<void> {
    if (this.isInitialized) {
      log.warn('QueueManager already initialized');
      return;
    }

    try {
      const redisConfig = RedisConnector.getConfig('bull');
      // Initialize all defined queues
      for (const [queueKey, queueName] of Object.entries(QUEUE_NAMES)) {
        const queueOptions = QUEUE_OPTIONS[queueName] || DEFAULT_QUEUE_OPTIONS;
        
        const queue = new Queue(queueName, {
          redis: redisConfig, // No need to spread additional configs
          defaultJobOptions: queueOptions
        });

        // Set up queue event handlers
        this.setupQueueEventHandlers(queue);
        
        this.queues.set(queueName, queue);
        log.info(`Initialized queue: ${queueName}`);
      }

      // Start monitoring
      this.startMonitoring();
      
      this.isInitialized = true;
      log.info('QueueManager initialized successfully');
    } catch (error) {
      log.error('Failed to initialize QueueManager:', error);
      throw error;
    }
  }

  /**
   * Get a specific queue by name
   */
  public getQueue(queueName: string): Queue.Queue {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found. Available queues: ${Array.from(this.queues.keys()).join(', ')}`);
    }
    return queue;
  }

  /**
   * Add a job to a specific queue
   */
  public async addJob(queueName: string, jobData: any, options?: JobOptions): Promise<Job> {
    try {
      const queue = this.getQueue(queueName);
      const job = await queue.add(jobData, options);
      
      log.info(`Job added to queue '${queueName}':`, {
        jobId: job.id,
        queueName,
        options
      });
      
      return job;
    } catch (error) {
      log.error(`Failed to add job to queue '${queueName}':`, error);
      throw error;
    }
  }

  /**
   * Get job by ID from a specific queue
   */
  public async getJob(queueName: string, jobId: string): Promise<Job | null> {
    try {
      const queue = this.getQueue(queueName);
      return await queue.getJob(jobId);
    } catch (error) {
      log.error(`Failed to get job ${jobId} from queue '${queueName}':`, error);
      throw error;
    }
  }

  /**
   * Cancel a job in a specific queue
   */
  public async cancelJob(queueName: string, jobId: string): Promise<void> {
    try {
      const queue = this.getQueue(queueName);
      const job = await queue.getJob(jobId);
      
      if (job) {
        await job.remove();
        log.info(`Job ${jobId} cancelled from queue '${queueName}'`);
      } else {
        log.warn(`Job ${jobId} not found in queue '${queueName}'`);
      }
    } catch (error) {
      log.error(`Failed to cancel job ${jobId} from queue '${queueName}':`, error);
      throw error;
    }
  }

  /**
   * Retry a failed job in a specific queue
   */
  public async retryJob(queueName: string, jobId: string): Promise<void> {
    try {
      const queue = this.getQueue(queueName);
      const job = await queue.getJob(jobId);
      
      if (job) {
        await job.retry();
        log.info(`Job ${jobId} retried in queue '${queueName}'`);
      } else {
        log.warn(`Job ${jobId} not found in queue '${queueName}'`);
      }
    } catch (error) {
      log.error(`Failed to retry job ${jobId} from queue '${queueName}':`, error);
      throw error;
    }
  }

  /**
   * Get statistics for a specific queue
   */
  public async getQueueStats(queueName: string): Promise<QueueStats> {
    try {
      const queue = this.getQueue(queueName);
      
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed()
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: await queue.isPaused()
      };
    } catch (error) {
      log.error(`Failed to get stats for queue '${queueName}':`, error);
      throw error;
    }
  }

  /**
   * Get statistics for all queues
   */
  public async getAllQueueStats(): Promise<Map<string, QueueStats>> {
    const allStats = new Map<string, QueueStats>();
    
    for (const queueName of this.queues.keys()) {
      try {
        const stats = await this.getQueueStats(queueName);
        allStats.set(queueName, stats);
      } catch (error) {
        log.error(`Failed to get stats for queue '${queueName}':`, error);
      }
    }
    
    return allStats;
  }

  /**
   * Pause a specific queue
   */
  public async pauseQueue(queueName: string): Promise<void> {
    try {
      const queue = this.getQueue(queueName);
      await queue.pause();
      log.info(`Queue '${queueName}' paused`);
    } catch (error) {
      log.error(`Failed to pause queue '${queueName}':`, error);
      throw error;
    }
  }

  /**
   * Resume a specific queue
   */
  public async resumeQueue(queueName: string): Promise<void> {
    try {
      const queue = this.getQueue(queueName);
      await queue.resume();
      log.info(`Queue '${queueName}' resumed`);
    } catch (error) {
      log.error(`Failed to resume queue '${queueName}':`, error);
      throw error;
    }
  }

  /**
   * Get health status for all queues
   */
  public async getQueueHealth(): Promise<QueueHealth[]> {
    const healthStatus: QueueHealth[] = [];
    
    for (const queueName of this.queues.keys()) {
      try {
        const stats = await this.getQueueStats(queueName);
        const isHealthy = this.isQueueHealthy(stats);
        
        healthStatus.push({
          name: queueName,
          isHealthy,
          stats
        });
      } catch (error) {
        healthStatus.push({
          name: queueName,
          isHealthy: false,
          stats: {
            waiting: 0,
            active: 0,
            completed: 0,
            failed: 0,
            delayed: 0,
            paused: true
          },
          lastError: error instanceof Error ? error.message : 'Unknown error',
          lastErrorTime: new Date()
        });
      }
    }
    
    return healthStatus;
  }

  /**
   * Clean up old jobs from all queues
   */
  public async cleanupQueues(): Promise<void> {
    for (const [queueName, queue] of this.queues) {
      try {
        // Clean completed jobs older than 1 day
        await queue.clean(24 * 60 * 60 * 1000, 'completed');
        // Clean failed jobs older than 7 days
        await queue.clean(7 * 24 * 60 * 60 * 1000, 'failed');
        
        log.info(`Cleaned up old jobs from queue '${queueName}'`);
      } catch (error) {
        log.error(`Failed to cleanup queue '${queueName}':`, error);
      }
    }
  }

  /**
   * Close all queues and stop monitoring
   */
  public async closeQueues(): Promise<void> {
    try {
      // Stop monitoring
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
      }
      if (this.statsUpdateInterval) {
        clearInterval(this.statsUpdateInterval);
      }

      // Close all queues
      const closePromises = Array.from(this.queues.values()).map(queue => queue.close());
      await Promise.all(closePromises);
      
      this.queues.clear();
      this.isInitialized = false;
      
      log.info('All queues closed successfully');
    } catch (error) {
      log.error('Failed to close queues:', error);
      throw error;
    }
  }

  /**
   * Set up event handlers for a queue
   */
  private setupQueueEventHandlers(queue: Queue.Queue): void {
    queue.on('error', (error) => {
      log.error(`Queue '${queue.name}' error:`, error);
    });

    queue.on('waiting', (jobId) => {
      log.debug(`Job ${jobId} waiting in queue '${queue.name}'`);
    });

    queue.on('active', (job) => {
      log.debug(`Job ${job.id} active in queue '${queue.name}'`);
    });

    queue.on('completed', (job, result) => {
      log.info(`Job ${job.id} completed in queue '${queue.name}'`);
    });

    queue.on('failed', (job, error) => {
      log.error(`Job ${job.id} failed in queue '${queue.name}':`, error);
    });

    queue.on('paused', () => {
      log.info(`Queue '${queue.name}' paused`);
    });

    queue.on('resumed', () => {
      log.info(`Queue '${queue.name}' resumed`);
    });

    queue.on('cleaned', (jobs, type) => {
      log.info(`Cleaned ${jobs.length} ${type} jobs from queue '${queue.name}'`);
    });

    queue.on('stalled', (job) => {
      log.warn(`Job ${job.id} stalled in queue '${queue.name}'`);
    });
  }

  /**
   * Start monitoring queues
   */
  private startMonitoring(): void {
    // Health check monitoring
    this.healthCheckInterval = setInterval(async () => {
      try {
        const health = await this.getQueueHealth();
        const unhealthyQueues = health.filter(q => !q.isHealthy);
        
        if (unhealthyQueues.length > 0) {
          log.warn('Unhealthy queues detected:', unhealthyQueues.map(q => q.name));
        }
      } catch (error) {
        log.error('Health check failed:', error);
      }
    }, QUEUE_MONITORING.HEALTH_CHECK_INTERVAL);

    // Stats update monitoring
    this.statsUpdateInterval = setInterval(async () => {
      try {
        const stats = await this.getAllQueueStats();
        log.debug('Queue statistics:', Object.fromEntries(stats));
      } catch (error) {
        log.error('Stats update failed:', error);
      }
    }, QUEUE_MONITORING.STATS_UPDATE_INTERVAL);

    // Cleanup interval
    setInterval(async () => {
      await this.cleanupQueues();
    }, QUEUE_MONITORING.CLEANUP_INTERVAL);
  }

  /**
   * Check if a queue is healthy based on its statistics
   */
  private isQueueHealthy(stats: QueueStats): boolean {
    // Queue is unhealthy if:
    // - It's paused
    // - Too many failed jobs
    // - No activity (all counters are 0) - might indicate connection issues
    
    if (stats.paused) {
      return false;
    }
    
    if (stats.failed > QUEUE_MONITORING.MAX_FAILED_COUNT) {
      return false;
    }
    
    // If there are active or waiting jobs, the queue is working
    if (stats.active > 0 || stats.waiting > 0) {
      return true;
    }
    
    // If there are completed jobs, the queue has been working
    if (stats.completed > 0) {
      return true;
    }
    
    // Empty queue is still healthy
    return true;
  }
}

export default QueueManager;