import Queue, { JobOptions } from 'bull';
import { QueueManager } from './queue.manager';
import { logger } from '../lib/logger';
import config from '../lib/config';

const log = logger(config.logger);

/**
 * Base Queue Class
 * Provides common queue operations for all queue types
 */
export abstract class BaseQueue {
  protected static queueManager = QueueManager.getInstance();
  
  /**
   * Get the queue name - must be overridden by child classes
   * @throws Error if not overridden by a child class
   */
  protected static getQueueName(): string {
    throw new Error('getQueueName() must be implemented by subclass');
  }
  
  /**
   * Get job by ID from the queue
   */
  static async getJob<T = any>(jobId: string): Promise<Queue.Job<T> | null> {
    try {
      const job = await this.queueManager.getJob(this.getQueueName(), jobId);
      return job as Queue.Job<T> | null;
    } catch (error) {
      log.error(`Failed to get job ${jobId} from queue '${this.getQueueName()}':`, error);
      throw error;
    }
  }
  
  /**
   * Cancel a job in the queue
   */
  static async cancelJob(jobId: string): Promise<void> {
    try {
      await this.queueManager.cancelJob(this.getQueueName(), jobId);
      log.info(`Job ${jobId} cancelled from queue '${this.getQueueName()}'`);
    } catch (error) {
      log.error(`Failed to cancel job ${jobId} from queue '${this.getQueueName()}':`, error);
      throw error;
    }
  }
  
  /**
   * Retry a failed job in the queue
   */
  static async retryJob(jobId: string): Promise<void> {
    try {
      await this.queueManager.retryJob(this.getQueueName(), jobId);
      log.info(`Job ${jobId} retried in queue '${this.getQueueName()}'`);
    } catch (error) {
      log.error(`Failed to retry job ${jobId} from queue '${this.getQueueName()}':`, error);
      throw error;
    }
  }
  
  /**
   * Get queue statistics
   */
  static async getQueueStats() {
    try {
      return await this.queueManager.getQueueStats(this.getQueueName());
    } catch (error) {
      log.error(`Failed to get queue stats for '${this.getQueueName()}':`, error);
      throw error;
    }
  }
  
  /**
   * Pause the queue
   */
  static async pauseQueue(): Promise<void> {
    try {
      await this.queueManager.pauseQueue(this.getQueueName());
      log.info(`Queue '${this.getQueueName()}' paused`);
    } catch (error) {
      log.error(`Failed to pause queue '${this.getQueueName()}':`, error);
      throw error;
    }
  }
  
  /**
   * Resume the queue
   */
  static async resumeQueue(): Promise<void> {
    try {
      await this.queueManager.resumeQueue(this.getQueueName());
      log.info(`Queue '${this.getQueueName()}' resumed`);
    } catch (error) {
      log.error(`Failed to resume queue '${this.getQueueName()}':`, error);
      throw error;
    }
  }
  
  /**
   * Get the underlying Bull queue instance
   */
  static getQueue(): Queue.Queue {
    return this.queueManager.getQueue(this.getQueueName());
  }
  
  /**
   * Add a job to the queue - to be used by child classes with proper typing
   */
  protected static async addJobToQueue<T, O extends JobOptions>(
    jobData: T, 
    options?: O
  ): Promise<Queue.Job<T>> {
    try {
      const job = await this.queueManager.addJob(
        this.getQueueName(),
        jobData,
        options
      );
      
      return job as Queue.Job<T>;
    } catch (error) {
      log.error(`Failed to add job to queue '${this.getQueueName()}':`, error);
      throw error;
    }
  }
}