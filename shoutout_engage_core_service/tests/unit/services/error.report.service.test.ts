import { ErrorReportService, ErrorReportData, ErrorReportOptions } from '../../../services/error.report.service';
import * as fs from 'fs';
import * as path from 'path';

// Mock fs with promises API
jest.mock('fs', () => ({
  promises: {
    writeFile: jest.fn(),
    access: jest.fn(),
    mkdir: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn(),
    unlink: jest.fn()
  }
}));

jest.mock('path');

jest.mock('../../../lib/config', () => ({
  default: {
    logger: {
      level: 'info',
      name: 'test'
    }
  } 
}));

jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));

describe('ErrorReportService', () => {
  let mockPath: jest.Mocked<typeof path>;
  let mockFs: jest.Mocked<typeof fs>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockPath = path as jest.Mocked<typeof path>;
    mockFs = fs as jest.Mocked<typeof fs>;

    // Mock path operations
    (mockPath.join as jest.Mock).mockImplementation((...args: string[]) => {
      return args.filter(arg => arg != null && arg !== '').join('/');
    });

    // Mock process.cwd
    jest.spyOn(process, 'cwd').mockReturnValue('/test/cwd');

    // Setup fs operation mocks
    (mockFs.promises.writeFile as jest.Mock).mockResolvedValue(undefined);
    (mockFs.promises.access as jest.Mock).mockRejectedValue(new Error('Directory does not exist'));
    (mockFs.promises.mkdir as jest.Mock).mockResolvedValue(undefined);
    (mockFs.promises.readdir as jest.Mock).mockResolvedValue([]);
    (mockFs.promises.stat as jest.Mock).mockResolvedValue({
      isFile: () => true,
      mtime: new Date(),
      birthtime: new Date(),
      size: 1024
    } as any);
    (mockFs.promises.unlink as jest.Mock).mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Core Functionality', () => {
    const mockErrorData: ErrorReportData[] = [
      {
        rowNumber: 1,
        originalData: { name: 'John Doe', email: '<EMAIL>' },
        errors: [
          { field: 'email', message: 'Email already exists', errorCode: 'DUPLICATE_001' }
        ],
        timestamp: new Date('2023-01-01T10:00:00Z')
      }
    ];

    it('should generate error report successfully', async () => {
      const options: ErrorReportOptions = {
        filename: 'test-error-report.csv',
        includeHeaders: true
      };

      const result = await ErrorReportService.generateErrorReport(mockErrorData, options);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(mockFs.promises.writeFile).toHaveBeenCalled();
    });

    it('should handle empty error data', async () => {
      await expect(ErrorReportService.generateErrorReport([])).rejects.toThrow();
    });

    it('should handle file writing errors', async () => {
      (mockFs.promises.writeFile as jest.Mock).mockRejectedValue(new Error('Write failed'));

      await expect(ErrorReportService.generateErrorReport(mockErrorData)).rejects.toThrow();
    });

    it('should generate contact bulk insert error report', async () => {
      const mockContactErrors = [
        {
          rowNumber: 1,
          contactData: { name: 'John Doe', email: '<EMAIL>' },
          error: {
            message: 'Contact already exists',
            errorCode: 'DUPLICATE_001',
            details: [{ field: 'email', message: 'Email already exists' }]
          },
          timestamp: new Date()
        }
      ];

      const result = await ErrorReportService.generateContactBulkInsertErrorReport(
        'test-job-id',
        mockContactErrors,
        ['name', 'email']
      );

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(mockFs.promises.writeFile).toHaveBeenCalled();
    });
  });

  describe('File Operations', () => {
    it('should get report info when file exists', async () => {
      const result = await ErrorReportService.getReportInfo('/path/to/report.csv');

      expect(result.exists).toBe(true);
      expect(result.size).toBeDefined();
      expect(result.created).toBeDefined();
      expect(result.modified).toBeDefined();
    });

    it('should return exists false when file does not exist', async () => {
      (mockFs.promises.stat as jest.Mock).mockRejectedValue(new Error('File not found'));

      const result = await ErrorReportService.getReportInfo('/path/to/missing.csv');

      expect(result.exists).toBe(false);
    });

    it('should delete report file successfully', async () => {
      await ErrorReportService.deleteReport('/path/to/report.csv');

      expect(mockFs.promises.unlink).toHaveBeenCalledWith('/path/to/report.csv');
    });

    it('should handle deletion errors', async () => {
      (mockFs.promises.unlink as jest.Mock).mockRejectedValue(new Error('Delete failed'));

      await expect(ErrorReportService.deleteReport('/path/to/report.csv')).rejects.toThrow();
    });
  });

  describe('Cleanup Operations', () => {
    beforeEach(() => {
      (mockFs.promises.readdir as jest.Mock).mockResolvedValue(['old-file.csv', 'new-file.csv']);
      (mockFs.promises.stat as jest.Mock).mockImplementation((filePath: string) => {
        const fileName = (filePath as string).split('/').pop();
        const isOld = fileName === 'old-file.csv';
        return Promise.resolve({
          isFile: () => true,
          mtime: new Date(Date.now() - (isOld ? 25 * 60 * 60 * 1000 : 1 * 60 * 60 * 1000))
        } as any);
      });
    });

    it('should clean up old report files', async () => {
      (mockFs.promises.access as jest.Mock).mockResolvedValue(undefined);

      await ErrorReportService.cleanupOldReports();

      expect(mockFs.promises.unlink).toHaveBeenCalledTimes(1);
      expect(mockFs.promises.unlink).toHaveBeenCalledWith(expect.stringContaining('old-file.csv'));
    });

    it('should handle non-existent directory gracefully', async () => {
      (mockFs.promises.access as jest.Mock).mockRejectedValue(new Error('Directory does not exist'));

      await expect(ErrorReportService.cleanupOldReports()).resolves.toBeUndefined();
      expect(mockFs.promises.readdir).not.toHaveBeenCalled();
    });

    it('should handle cleanup errors', async () => {
      (mockFs.promises.access as jest.Mock).mockResolvedValue(undefined);
      (mockFs.promises.readdir as jest.Mock).mockRejectedValue(new Error('Readdir failed'));

      await expect(ErrorReportService.cleanupOldReports()).rejects.toThrow();
    });
  });

  describe('Utility Functions', () => {
    it('should escape CSV values correctly', () => {
      const row = ['John Doe', 'Manager, Sales', '<EMAIL>'];
      const result = (ErrorReportService as any).escapeCSVRow(row);

      expect(result).toContain('John Doe');
      expect(result).toContain('"Manager, Sales"');
      expect(result).toContain('<EMAIL>');
    });

    it('should handle null values in CSV', () => {
      const row = ['John Doe', null, undefined, '<EMAIL>'];
      const result = (ErrorReportService as any).escapeCSVRow(row);

      expect(result).toBe('John Doe,,,<EMAIL>');
    });

    it('should extract headers from contact data', () => {
      const contactData = [
        { name: 'John', email: '<EMAIL>', phone: '123' },
        { name: 'Jane', email: '<EMAIL>', company: 'Acme' }
      ];

      const result = (ErrorReportService as any).extractContactHeaders(contactData);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result).toContain('name');
      expect(result).toContain('email');
    });

    it('should handle empty contact data array', () => {
      const result = (ErrorReportService as any).extractContactHeaders([]);
      expect(result).toEqual([]);
    });
  });

  describe('Directory Management', () => {
    it('should create directory when it does not exist', async () => {
      const errorData = [{
        rowNumber: 1,
        originalData: { name: 'John' },
        errors: [{ field: 'email', message: 'Required' }],
        timestamp: new Date()
      }];

      await ErrorReportService.generateErrorReport(errorData);

      expect(mockFs.promises.mkdir).toHaveBeenCalled();
    });

    it('should skip directory creation when it exists', async () => {
      (mockFs.promises.access as jest.Mock).mockResolvedValue(undefined);

      const errorData = [{
        rowNumber: 1,
        originalData: { name: 'John' },
        errors: [{ field: 'email', message: 'Required' }],
        timestamp: new Date()
      }];

      await ErrorReportService.generateErrorReport(errorData);

      expect(mockFs.promises.mkdir).not.toHaveBeenCalled();
    });
  });
});