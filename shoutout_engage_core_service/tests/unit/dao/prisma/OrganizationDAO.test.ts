import { OrganizationDAO } from '../../../../lib/db/dao/prisma/OrganizationDAO';
import PrismaConnector from '../../../../lib/db/connectors/PrismaConnector';
import {OrganizationStatus } from '../../../../types/organization.types';
import { closeLoggers } from '../../../../lib/logger';

// Mock PrismaConnector
jest.mock('../../../../lib/db/connectors/PrismaConnector', () => ({
  getClient: jest.fn(),
  initialize: jest.fn(),
  close: jest.fn()
}));

describe('OrganizationDAO', () => {
  let createdOrgId: string;
  const mockPrismaClient = {
    organizations: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn()
    }
  };

  beforeAll(async () => {
    // Initialize Prisma
    PrismaConnector.initialize();
    // Set up mock client
    (PrismaConnector.getClient as jest.Mock).mockReturnValue(mockPrismaClient);
  });

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Set up test data
    createdOrgId = '123e4567-e89b-12d3-a456-************';
  });

  afterAll(async () => {
    // Close Prisma connection
    await PrismaConnector.close();
    // Close loggers to prevent open handles
    await closeLoggers();
  }, 30000); // Increase timeout for cleanup operations

  it('should create an organization with email and phone_number', async () => {
    // Mock the create method to return a test organization
    const mockOrg = {
      uuid: createdOrgId,
      organization_name: 'Test Organization with Contact Info',
      email: '<EMAIL>',
      phone_number: '+1234567890',
      status: 'active' as OrganizationStatus,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    mockPrismaClient.organizations.create.mockResolvedValue(mockOrg);

    const newOrg = await OrganizationDAO.createOrganization({
      organization_name: 'Test Organization with Contact Info',
      email: '<EMAIL>',
      phone_number: '+1234567890',
      status: 'active'
    });

    // Verify the create method was called with the correct parameters
    expect(mockPrismaClient.organizations.create).toHaveBeenCalledWith({
      data: {
        organization_name: 'Test Organization with Contact Info',
        email: '<EMAIL>',
        phone_number: '+1234567890',
        status: 'active'
      }
    });

    // Verify the returned organization has the expected values
    expect(newOrg).toBeDefined();
    expect(newOrg.organization_name).toBe('Test Organization with Contact Info');
    expect(newOrg.email).toBe('<EMAIL>');
    expect(newOrg.phone_number).toBe('+1234567890');
    expect(newOrg.status).toBe('active');
    expect(newOrg.uuid).toBe(createdOrgId);
  });

  it('should get an organization by UUID with email and phone_number', async () => {
    // Mock the findUnique method to return a test organization
    const mockOrg = {
      uuid: createdOrgId,
      organization_name: 'Test Organization with Contact Info',
      email: '<EMAIL>',
      phone_number: '+1234567890',
      status: 'active' as OrganizationStatus,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    mockPrismaClient.organizations.findUnique.mockResolvedValue(mockOrg);

    const org = await OrganizationDAO.getOrganizationByUuid(createdOrgId);

    // Verify the findUnique method was called with the correct parameters
    expect(mockPrismaClient.organizations.findUnique).toHaveBeenCalledWith({
      where: {
        uuid: createdOrgId
      }
    });

    // Verify the returned organization has the expected values
    expect(org).toBeDefined();
    expect(org?.uuid).toBe(createdOrgId);
    expect(org?.organization_name).toBe('Test Organization with Contact Info');
    expect(org?.email).toBe('<EMAIL>');
    expect(org?.phone_number).toBe('+1234567890');
    expect(org?.status).toBe('active');
  });

  it('should update an organization with email and phone_number', async () => {
    // Mock the findUnique method to return a test organization (for existence check)
    const existingOrg = {
      uuid: createdOrgId,
      organization_name: 'Test Organization with Contact Info',
      email: '<EMAIL>',
      phone_number: '+1234567890',
      status: 'active' as OrganizationStatus,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    // Mock the update method to return an updated organization
    const updatedOrgData = {
      uuid: createdOrgId,
      organization_name: 'Test Organization with Contact Info',
      email: '<EMAIL>',
      phone_number: '+9876543210',
      status: 'active' as OrganizationStatus,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    mockPrismaClient.organizations.findUnique.mockResolvedValue(existingOrg);
    mockPrismaClient.organizations.update.mockResolvedValue(updatedOrgData);

    const updatedOrg = await OrganizationDAO.updateOrganization(createdOrgId, {
      email: '<EMAIL>',
      phone_number: '+9876543210'
    });

    // Verify the findUnique method was called with the correct parameters
    expect(mockPrismaClient.organizations.findUnique).toHaveBeenCalledWith({
      where: { uuid: createdOrgId }
    });

    // Verify the update method was called with the correct parameters
    expect(mockPrismaClient.organizations.update).toHaveBeenCalledWith({
      where: { uuid: createdOrgId },
      data: {
        email: '<EMAIL>',
        phone_number: '+9876543210'
      }
    });

    // Verify the returned organization has the expected values
    expect(updatedOrg).toBeDefined();
    expect(updatedOrg.uuid).toBe(createdOrgId);
    expect(updatedOrg.organization_name).toBe('Test Organization with Contact Info');
    expect(updatedOrg.email).toBe('<EMAIL>');
    expect(updatedOrg.phone_number).toBe('+9876543210');
    expect(updatedOrg.status).toBe('active');
  });

  it('should get an organization by name with email and phone_number', async () => {
    // Mock the findFirst method to return a test organization
    const mockOrg = {
      uuid: createdOrgId,
      organization_name: 'Test Organization with Contact Info',
      email: '<EMAIL>',
      phone_number: '+9876543210',
      status: 'active' as OrganizationStatus,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    mockPrismaClient.organizations.findFirst.mockResolvedValue(mockOrg);

    const org = await OrganizationDAO.getOrganizationByName('Test Organization with Contact Info');

    // Verify the findFirst method was called with the correct parameters
    expect(mockPrismaClient.organizations.findFirst).toHaveBeenCalledWith({
      where: {
        organization_name: 'Test Organization with Contact Info'
      }
    });

    // Verify the returned organization has the expected values
    expect(org).toBeDefined();
    expect(org?.uuid).toBe(createdOrgId);
    expect(org?.organization_name).toBe('Test Organization with Contact Info');
    expect(org?.email).toBe('<EMAIL>');
    expect(org?.phone_number).toBe('+9876543210');
    expect(org?.status).toBe('active');
  });
});