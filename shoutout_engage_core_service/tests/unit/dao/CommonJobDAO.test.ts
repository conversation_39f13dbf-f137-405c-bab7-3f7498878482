import { CommonJobDAO } from '../../../lib/db/dao/CommonJobDAO';
import { JobModel } from '../../../lib/db/models/job.model';
import { JOB_STATUS, JOB_TYPE } from '../../../lib/constant/job.constants';
import { 
  CreateJobRequest, 
  JobError, 
  JobProgress, 
  JobQueryFilters, 
  JobQueryOptions 
} from '../../../types/job.types';
import { 
  ValidationError, 
  NotFoundError, 
  BadRequestError, 
  DatabaseError 
} from '../../../lib/errors/error.type';

// Mock the JobModel
jest.mock('../../../lib/db/models/job.model');

describe('CommonJobDAO', () => {
  let mockJobModel: jest.Mocked<typeof JobModel>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockJobModel = JobModel as jest.Mocked<typeof JobModel>;
  });

  describe('createJob', () => {
    const validJobRequest: CreateJobRequest = {
      jobType: JOB_TYPE.CONTACT_BULK_INSERT,
      orgId: 'test-org-id',
      createdBy: 'test-user-id',
      data: {
        filePath: '/tmp/test.csv',
        fileName: 'test.csv'
      },
      metadata: {
        priority: 5,
        tags: ['bulk-insert']
      }
    };

    it('should create a job successfully', async () => {
      const mockSavedJob = {
        jobId: 'generated-uuid',
        jobType: JOB_TYPE.CONTACT_BULK_INSERT,
        status: JOB_STATUS.PENDING,
        orgId: 'test-org-id',
        createdBy: 'test-user-id',
        data: validJobRequest.data,
        errors: [],
        progress: {
          processedItems: 0,
          successfulItems: 0,
          failedItems: 0,
          percentage: 0,
          lastUpdated: expect.any(Date)
        },
        metadata: validJobRequest.metadata,
        save: jest.fn().mockResolvedValue(this)
      };

      const mockJobInstance = {
        ...mockSavedJob,
        save: jest.fn().mockResolvedValue(mockSavedJob)
      };

      (JobModel as any).mockImplementation(() => mockJobInstance);

      const result = await CommonJobDAO.createJob(validJobRequest);

      expect(result).toEqual(mockSavedJob);
      expect(mockJobInstance.save).toHaveBeenCalled();
    });

    it('should throw ValidationError for invalid job data', async () => {
      const validationError = {
        name: 'ValidationError',
        errors: {
          orgId: { message: 'Organization ID is required' },
          jobType: { message: 'Job type is required' }
        }
      };

      const mockJobInstance = {
        save: jest.fn().mockRejectedValue(validationError)
      };

      (JobModel as any).mockImplementation(() => mockJobInstance);

      await expect(CommonJobDAO.createJob(validJobRequest)).rejects.toThrow(ValidationError);
    });

    it('should throw DatabaseError for database failures', async () => {
      const dbError = new Error('Database connection failed');

      const mockJobInstance = {
        save: jest.fn().mockRejectedValue(dbError)
      };

      (JobModel as any).mockImplementation(() => mockJobInstance);

      await expect(CommonJobDAO.createJob(validJobRequest)).rejects.toThrow(DatabaseError);
    });
  });

  describe('updateJobStatus', () => {
    const jobId = 'test-job-id';
    const newStatus = JOB_STATUS.PROCESSING;

    it('should update job status successfully', async () => {
      const mockUpdatedJob = {
        jobId,
        status: newStatus,
        updatedAt: expect.any(Date),
        startedAt: expect.any(Date)
      };

      mockJobModel.findOneAndUpdate.mockResolvedValue(mockUpdatedJob as any);

      const result = await CommonJobDAO.updateJobStatus(jobId, newStatus);

      expect(result).toEqual(mockUpdatedJob);
      expect(mockJobModel.findOneAndUpdate).toHaveBeenCalledWith(
        { jobId },
        expect.objectContaining({
          status: newStatus,
          updatedAt: expect.any(Date),
          startedAt: expect.any(Date)
        }),
        { new: true, runValidators: true }
      );
    });

    it('should set completedAt timestamp for completed/failed status', async () => {
      const mockUpdatedJob = {
        jobId,
        status: JOB_STATUS.COMPLETED,
        completedAt: expect.any(Date)
      };

      mockJobModel.findOneAndUpdate.mockResolvedValue(mockUpdatedJob as any);

      await CommonJobDAO.updateJobStatus(jobId, JOB_STATUS.COMPLETED);

      expect(mockJobModel.findOneAndUpdate).toHaveBeenCalledWith(
        { jobId },
        expect.objectContaining({
          status: JOB_STATUS.COMPLETED,
          completedAt: expect.any(Date)
        }),
        { new: true, runValidators: true }
      );
    });

    it('should throw NotFoundError if job not found', async () => {
      mockJobModel.findOneAndUpdate.mockResolvedValue(null);

      await expect(CommonJobDAO.updateJobStatus(jobId, newStatus)).rejects.toThrow(NotFoundError);
    });

    it('should throw DatabaseError for database failures', async () => {
      mockJobModel.findOneAndUpdate.mockRejectedValue(new Error('Database error'));

      await expect(CommonJobDAO.updateJobStatus(jobId, newStatus)).rejects.toThrow(DatabaseError);
    });
  });

  describe('updateJobProgress', () => {
    const jobId = 'test-job-id';
    const progress: Partial<JobProgress> = {
      processedItems: 50,
      totalItems: 100,
      successfulItems: 45,
      failedItems: 5,
      currentStep: 'Processing contacts'
    };

    it('should update job progress successfully', async () => {
      const expectedProgress = {
        ...progress,
        percentage: 50,
        lastUpdated: expect.any(Date)
      };

      const mockUpdatedJob = {
        jobId,
        progress: expectedProgress
      };

      mockJobModel.findOneAndUpdate.mockResolvedValue(mockUpdatedJob as any);

      const result = await CommonJobDAO.updateJobProgress(jobId, progress);

      expect(result).toEqual(mockUpdatedJob);
      expect(mockJobModel.findOneAndUpdate).toHaveBeenCalledWith(
        { jobId },
        {
          $set: {
            progress: expectedProgress,
            updatedAt: expect.any(Date)
          }
        },
        { new: true, runValidators: true }
      );
    });

    it('should calculate percentage if not provided', async () => {
      const progressWithoutPercentage = {
        processedItems: 25,
        totalItems: 100
      };

      const expectedProgress = {
        ...progressWithoutPercentage,
        percentage: 25,
        lastUpdated: expect.any(Date)
      };

      mockJobModel.findOneAndUpdate.mockResolvedValue({ jobId, progress: expectedProgress } as any);

      await CommonJobDAO.updateJobProgress(jobId, progressWithoutPercentage);

      expect(mockJobModel.findOneAndUpdate).toHaveBeenCalledWith(
        { jobId },
        {
          $set: {
            progress: expectedProgress,
            updatedAt: expect.any(Date)
          }
        },
        { new: true, runValidators: true }
      );
    });

    it('should throw NotFoundError if job not found', async () => {
      mockJobModel.findOneAndUpdate.mockResolvedValue(null);

      await expect(CommonJobDAO.updateJobProgress(jobId, progress)).rejects.toThrow(NotFoundError);
    });

    it('should throw DatabaseError for database failures', async () => {
      mockJobModel.findOneAndUpdate.mockRejectedValue(new Error('Database error'));

      await expect(CommonJobDAO.updateJobProgress(jobId, progress)).rejects.toThrow(DatabaseError);
    });
  });

  describe('addJobError', () => {
    const jobId = 'test-job-id';
    const jobError: JobError = {
      type: 'VALIDATION',
      message: 'Invalid contact data',
      errorCode: 'CONTACT_001',
      timestamp: new Date(),
      rowNumber: 5
    };

    it('should add job error successfully', async () => {
      const mockUpdatedJob = {
        jobId,
        errors: [jobError]
      };

      mockJobModel.findOneAndUpdate.mockResolvedValue(mockUpdatedJob as any);

      const result = await CommonJobDAO.addJobError(jobId, jobError);

      expect(result).toEqual(mockUpdatedJob);
      expect(mockJobModel.findOneAndUpdate).toHaveBeenCalledWith(
        { jobId },
        {
          $push: { errors: jobError },
          $set: { updatedAt: expect.any(Date) }
        },
        { new: true, runValidators: true }
      );
    });

    it('should throw NotFoundError if job not found', async () => {
      mockJobModel.findOneAndUpdate.mockResolvedValue(null);

      await expect(CommonJobDAO.addJobError(jobId, jobError)).rejects.toThrow(NotFoundError);
    });

    it('should throw DatabaseError for database failures', async () => {
      mockJobModel.findOneAndUpdate.mockRejectedValue(new Error('Database error'));

      await expect(CommonJobDAO.addJobError(jobId, jobError)).rejects.toThrow(DatabaseError);
    });
  });

  describe('setJobResult', () => {
    const jobId = 'test-job-id';
    const result = {
      totalRows: 100,
      successfulRows: 95,
      failedRows: 5,
      processingTime: 30000
    };

    it('should set job result successfully', async () => {
      const mockUpdatedJob = {
        jobId,
        result
      };

      mockJobModel.findOneAndUpdate.mockResolvedValue(mockUpdatedJob as any);

      const response = await CommonJobDAO.setJobResult(jobId, result);

      expect(response).toEqual(mockUpdatedJob);
      expect(mockJobModel.findOneAndUpdate).toHaveBeenCalledWith(
        { jobId },
        {
          $set: {
            result,
            updatedAt: expect.any(Date)
          }
        },
        { new: true, runValidators: true }
      );
    });

    it('should throw NotFoundError if job not found', async () => {
      mockJobModel.findOneAndUpdate.mockResolvedValue(null);

      await expect(CommonJobDAO.setJobResult(jobId, result)).rejects.toThrow(NotFoundError);
    });

    it('should throw DatabaseError for database failures', async () => {
      mockJobModel.findOneAndUpdate.mockRejectedValue(new Error('Database error'));

      await expect(CommonJobDAO.setJobResult(jobId, result)).rejects.toThrow(DatabaseError);
    });
  });

  describe('getJobById', () => {
    const jobId = 'test-job-id';

    it('should retrieve job successfully', async () => {
      const mockJob = {
        jobId,
        jobType: JOB_TYPE.CONTACT_BULK_INSERT,
        status: JOB_STATUS.COMPLETED,
        orgId: 'test-org-id',
        createdBy: 'test-user-id'
      };

      mockJobModel.findOne.mockResolvedValue(mockJob as any);

      const result = await CommonJobDAO.getJobById(jobId);

      expect(result).toEqual(mockJob);
      expect(mockJobModel.findOne).toHaveBeenCalledWith({ jobId });
    });

    it('should throw NotFoundError if job not found', async () => {
      mockJobModel.findOne.mockResolvedValue(null);

      await expect(CommonJobDAO.getJobById(jobId)).rejects.toThrow(NotFoundError);
    });

    it('should throw DatabaseError for database failures', async () => {
      mockJobModel.findOne.mockRejectedValue(new Error('Database error'));

      await expect(CommonJobDAO.getJobById(jobId)).rejects.toThrow(DatabaseError);
    });
  });

  describe('getJobStatus', () => {
    const jobId = 'test-job-id';

    it('should return formatted job status', async () => {
      const mockJob = {
        jobId,
        jobType: JOB_TYPE.CONTACT_BULK_INSERT,
        status: JOB_STATUS.COMPLETED,
        progress: {
          processedItems: 100,
          successfulItems: 95,
          failedItems: 5,
          percentage: 100
        },
        errors: [],
        result: { totalRows: 100 },
        createdAt: new Date(),
        startedAt: new Date(),
        completedAt: new Date()
      };

      mockJobModel.findOne.mockResolvedValue(mockJob as any);

      const result = await CommonJobDAO.getJobStatus(jobId);

      expect(result).toEqual({
        jobId: mockJob.jobId,
        jobType: mockJob.jobType,
        status: mockJob.status,
        progress: mockJob.progress,
        errors: mockJob.errors,
        result: mockJob.result,
        createdAt: mockJob.createdAt,
        startedAt: mockJob.startedAt,
        completedAt: mockJob.completedAt
      });
    });

    it('should throw NotFoundError if job not found', async () => {
      mockJobModel.findOne.mockResolvedValue(null);

      await expect(CommonJobDAO.getJobStatus(jobId)).rejects.toThrow(NotFoundError);
    });
  });

  describe('queryJobs', () => {
    const mockJobs = [
      {
        jobId: 'job-1',
        jobType: JOB_TYPE.CONTACT_BULK_INSERT,
        status: JOB_STATUS.COMPLETED,
        orgId: 'test-org-id',
        createdAt: new Date()
      },
      {
        jobId: 'job-2',
        jobType: JOB_TYPE.EMAIL,
        status: JOB_STATUS.PROCESSING,
        orgId: 'test-org-id',
        createdAt: new Date()
      }
    ];

    it('should query jobs with default options', async () => {
      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockJobs)
      };

      mockJobModel.find.mockReturnValue(mockQuery as any);
      mockJobModel.countDocuments.mockResolvedValue(2);

      const result = await CommonJobDAO.queryJobs();

      expect(result).toEqual({
        jobs: mockJobs,
        total: 2,
        page: 1,
        limit: 20,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      });
    });

    it('should apply filters correctly', async () => {
      const filters: JobQueryFilters = {
        orgId: 'test-org-id',
        status: JOB_STATUS.COMPLETED,
        jobType: JOB_TYPE.CONTACT_BULK_INSERT
      };

      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([mockJobs[0]])
      };

      mockJobModel.find.mockReturnValue(mockQuery as any);
      mockJobModel.countDocuments.mockResolvedValue(1);

      await CommonJobDAO.queryJobs(filters);

      expect(mockJobModel.find).toHaveBeenCalledWith({
        orgId: 'test-org-id',
        status: JOB_STATUS.COMPLETED,
        jobType: JOB_TYPE.CONTACT_BULK_INSERT
      }, {
        errors: 0,
        result: 0
      });
    });

    it('should apply pagination correctly', async () => {
      const options: JobQueryOptions = {
        page: 2,
        limit: 10
      };

      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockJobs)
      };

      mockJobModel.find.mockReturnValue(mockQuery as any);
      mockJobModel.countDocuments.mockResolvedValue(25);

      const result = await CommonJobDAO.queryJobs({}, options);

      expect(mockQuery.skip).toHaveBeenCalledWith(10); // (page - 1) * limit
      expect(mockQuery.limit).toHaveBeenCalledWith(10);
      expect(result.page).toBe(2);
      expect(result.totalPages).toBe(3);
      expect(result.hasNext).toBe(true);
      expect(result.hasPrev).toBe(true);
    });

    it('should apply sorting correctly', async () => {
      const options: JobQueryOptions = {
        sortBy: 'status',
        sortOrder: 'asc'
      };

      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockJobs)
      };

      mockJobModel.find.mockReturnValue(mockQuery as any);
      mockJobModel.countDocuments.mockResolvedValue(2);

      await CommonJobDAO.queryJobs({}, options);

      expect(mockQuery.sort).toHaveBeenCalledWith({
        status: 1,
        createdAt: -1
      });
    });

    it('should exclude fields based on options', async () => {
      const options: JobQueryOptions = {
        includeErrors: false,
        includeResult: false
      };

      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockJobs)
      };

      mockJobModel.find.mockReturnValue(mockQuery as any);
      mockJobModel.countDocuments.mockResolvedValue(2);

      await CommonJobDAO.queryJobs({}, options);

      expect(mockJobModel.find).toHaveBeenCalledWith({}, {
        errors: 0,
        result: 0
      });
    });
  });

  describe('deleteJob', () => {
    const jobId = 'test-job-id';

    it('should delete job successfully', async () => {
      mockJobModel.deleteOne.mockResolvedValue({ deletedCount: 1 } as any);

      const result = await CommonJobDAO.deleteJob(jobId);

      expect(result).toBe(true);
      expect(mockJobModel.deleteOne).toHaveBeenCalledWith({ jobId });
    });

    it('should throw NotFoundError if job not found', async () => {
      mockJobModel.deleteOne.mockResolvedValue({ deletedCount: 0 } as any);

      await expect(CommonJobDAO.deleteJob(jobId)).rejects.toThrow(NotFoundError);
    });

    it('should throw DatabaseError for database failures', async () => {
      mockJobModel.deleteOne.mockRejectedValue(new Error('Database error'));

      await expect(CommonJobDAO.deleteJob(jobId)).rejects.toThrow(DatabaseError);
    });
  });

  describe('cancelJob', () => {
    const jobId = 'test-job-id';

    it('should cancel pending job successfully', async () => {
      const mockJob = {
        jobId,
        status: JOB_STATUS.PENDING
      };

      const mockUpdatedJob = {
        ...mockJob,
        status: JOB_STATUS.CANCELLED
      };

      mockJobModel.findOne.mockResolvedValue(mockJob as any);
      mockJobModel.findOneAndUpdate.mockResolvedValue(mockUpdatedJob as any);

      const result = await CommonJobDAO.cancelJob(jobId);

      expect(result).toEqual(mockUpdatedJob);
      expect(mockJobModel.findOne).toHaveBeenCalledWith({
        jobId,
        status: { $in: [JOB_STATUS.PENDING, JOB_STATUS.PROCESSING] }
      });
    });

    it('should cancel processing job successfully', async () => {
      const mockJob = {
        jobId,
        status: JOB_STATUS.PROCESSING
      };

      mockJobModel.findOne.mockResolvedValue(mockJob as any);
      mockJobModel.findOneAndUpdate.mockResolvedValue({ ...mockJob, status: JOB_STATUS.CANCELLED } as any);

      await CommonJobDAO.cancelJob(jobId);

      expect(mockJobModel.findOne).toHaveBeenCalledWith({
        jobId,
        status: { $in: [JOB_STATUS.PENDING, JOB_STATUS.PROCESSING] }
      });
    });

    it('should throw BadRequestError for completed job', async () => {
      const mockExistingJob = {
        jobId,
        status: JOB_STATUS.COMPLETED
      };

      mockJobModel.findOne
        .mockResolvedValueOnce(null) // First call for cancellable jobs
        .mockResolvedValueOnce(mockExistingJob as any); // Second call to check if job exists

      await expect(CommonJobDAO.cancelJob(jobId)).rejects.toThrow(BadRequestError);
    });

    it('should throw NotFoundError if job does not exist', async () => {
      mockJobModel.findOne.mockResolvedValue(null);

      await expect(CommonJobDAO.cancelJob(jobId)).rejects.toThrow(NotFoundError);
    });
  });

  describe('getJobsByOrganization', () => {
    it('should get jobs by organization', async () => {
      const orgId = 'test-org-id';
      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([])
      };

      mockJobModel.find.mockReturnValue(mockQuery as any);
      mockJobModel.countDocuments.mockResolvedValue(0);

      await CommonJobDAO.getJobsByOrganization(orgId);

      expect(mockJobModel.find).toHaveBeenCalledWith({ orgId }, {
        errors: 0,
        result: 0
      });
    });
  });

  describe('getJobsByUser', () => {
    it('should get jobs by user', async () => {
      const createdBy = 'test-user-id';
      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([])
      };

      mockJobModel.find.mockReturnValue(mockQuery as any);
      mockJobModel.countDocuments.mockResolvedValue(0);

      await CommonJobDAO.getJobsByUser(createdBy);

      expect(mockJobModel.find).toHaveBeenCalledWith({ createdBy }, {
        errors: 0,
        result: 0
      });
    });
  });
});