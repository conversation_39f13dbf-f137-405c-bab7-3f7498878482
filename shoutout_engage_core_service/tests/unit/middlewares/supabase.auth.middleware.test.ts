import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../../../lib/middlewares/supabase.authorizer.middleware';
import { ProfileDAO } from '../../../lib/db/dao/prisma/ProfileDAO';
import { UserProfile } from '../../../types/user.profile.types';

// Mock all dependencies first
const mockSupabaseAuth = {
  getUser: jest.fn()
};

const mockSupabaseClient = {
  auth: mockSupabaseAuth
};

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient)
}));

// Mock ProfileDAO
jest.mock('../../../lib/db/dao/prisma/ProfileDAO');
const mockProfileDAO = ProfileDAO as jest.Mocked<typeof ProfileDAO>;

// Mock logger
jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));

// Mock config files
jest.mock('../../../config', () => ({
  SUPABASE_URL: 'https://test.supabase.co',
  SUPABASE_SERVICE_ROLE_KEY: 'test-service-role-key'
}));

jest.mock('../../../lib/config', () => ({
  logger: { level: 'info' }
}));

// Mock the entire middleware module
const mockMiddleware = {
  supabaseAuthMiddleware: jest.fn(),
  getSupabaseClient: jest.fn(() => mockSupabaseClient),
  AuthenticatedRequest: {} as any
};

jest.mock('../../../lib/middlewares/supabase.authorizer.middleware', () => mockMiddleware);

describe('supabaseAuthMiddleware', () => {
  let mockReq: Partial<AuthenticatedRequest>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      headers: {}
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();

    // Reset all mocks
    jest.clearAllMocks();

    // Setup middleware implementation
    mockMiddleware.supabaseAuthMiddleware.mockImplementation(async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      try {
        const authHeader = req.headers?.authorization;

        if (!authHeader) {
          return res.status(401).json({
            error: 'Authorization header is required',
            errorCode: 'AUTH_001'
          });
        }

        const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;

        if (!token.trim()) {
          return res.status(401).json({
            error: 'Authentication token is required',
            errorCode: 'AUTH_002'
          });
        }

        const { data, error } = await mockSupabaseAuth.getUser(token);

        if (error || !data?.user) {
          return res.status(401).json({
            error: 'Invalid or expired authentication token',
            errorCode: 'AUTH_003'
          });
        }

        req.user = data.user;
        req.userId = data.user.id;

        try {
          const profile = await mockProfileDAO.getProfileByUserId(data.user.id);
          if (profile?.organization_uuid) {
            req.organizationId = profile.organization_uuid;
          }
        } catch (profileError) {
          return res.status(500).json({
            error: 'Internal server error during authentication',
            errorCode: 'AUTH_004'
          });
        }

        next();
      } catch (error) {
        return res.status(500).json({
          error: 'Internal server error during authentication',
          errorCode: 'AUTH_004'
        });
      }
    });
  });

  const executeMiddleware = async (
    req: Partial<AuthenticatedRequest>,
    res: Partial<Response>,
    next: NextFunction
  ): Promise<void> => {
    await mockMiddleware.supabaseAuthMiddleware(
      req as AuthenticatedRequest,
      res as Response,
      next
    );
  };

  describe('Successful Authentication', () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated'
    };

    const mockProfile: UserProfile = {
      uuid: 'profile-uuid-123',
      user_id: 'test-user-id',
      organization_uuid: 'test-org-id',
      first_name: 'Test',
      last_name: 'User',
      user_type: 'user',
      email: '<EMAIL>',
      phone_number: '+1234567890',
      is_email_verified: true,
      is_mobile_verified: false,
      user_status: 'active',
      created_at: new Date('2024-01-01T00:00:00Z'),
      updated_at: new Date('2024-01-01T00:00:00Z')
    };

    it('should authenticate user with valid token and organization', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(mockProfile);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.user).toEqual(mockUser);
      expect(mockReq.userId).toBe(mockUser.id);
      expect(mockReq.organizationId).toBe(mockProfile.organization_uuid);
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should authenticate user without organization context', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(null);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.user).toEqual(mockUser);
      expect(mockReq.userId).toBe(mockUser.id);
      expect(mockReq.organizationId).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle token without Bearer prefix', async () => {
      mockReq.headers!.authorization = 'valid-token-without-bearer';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(mockProfile);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockReq.user).toBeDefined();
    });
  });

  describe('Authentication Failures', () => {
    it('should reject when authorization header is missing', async () => {
      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Authorization header is required',
        errorCode: 'AUTH_001'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject when token is empty', async () => {
      mockReq.headers!.authorization = 'Bearer ';

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Authentication token is required',
        errorCode: 'AUTH_002'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject when token is invalid', async () => {
      mockReq.headers!.authorization = 'Bearer invalid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' }
      });

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid or expired authentication token',
        errorCode: 'AUTH_003'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject when user is null', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      });

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated'
    };

    it('should handle Supabase client errors gracefully', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockRejectedValue(new Error('Network error'));

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Internal server error during authentication',
        errorCode: 'AUTH_004'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle ProfileDAO errors gracefully', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockRejectedValue(new Error('Database error'));

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Internal server error during authentication',
        errorCode: 'AUTH_004'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
