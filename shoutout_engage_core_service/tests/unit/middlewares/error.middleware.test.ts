import { Request, Response, NextFunction } from 'express';
import { ErrorMiddleware } from '../../../lib/middlewares/error.middleware';
import { AppError } from '../../../lib/errors/app.error';
import {
  ValidationError,
} from '../../../lib/errors/error.type';
import { <PERSON>rrorLogger } from '../../../lib/utils/errorLogger';
import { ErrorContext } from '../../../lib/utils/errorContext';
import { ErrorContextMiddleware } from '../../../lib/middlewares/error.context.middleware';

// Mock dependencies
jest.mock('../../../lib/utils/errorLogger');
jest.mock('../../../lib/middlewares/error.context.middleware');

describe('ErrorMiddleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockContext: ErrorContext;

  beforeEach(() => {
    mockRequest = {
      method: 'POST',
      originalUrl: '/api/test',
      get: jest.fn(),
      connection: { remoteAddress: '127.0.0.1' }
    } as any;

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      setHeader: jest.fn()
    };

    mockNext = jest.fn();

    mockContext = {
      requestId: 'test-request-id',
      endpoint: '/api/test',
      method: 'POST',
      timestamp: new Date(),
      toLogObject: jest.fn().mockReturnValue({
        requestId: 'test-request-id',
        endpoint: '/api/test',
        method: 'POST'
      })
    } as any;

    // Reset mocks
    jest.clearAllMocks();

    // Setup minimal mock implementations
    (ErrorContextMiddleware.ensureContext as jest.Mock).mockReturnValue(mockContext);
    (ErrorLogger.logError as jest.Mock).mockImplementation(() => {});
  });

  describe('handleError', () => {
    it('should handle AppError instances correctly', () => {
      const appError = new ValidationError('Test validation error', 'TEST_001');

      ErrorMiddleware.handleError(
        appError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(expect.any(Number));
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.any(String),
          errorCode: expect.any(String)
        })
      );
    });

    it('should transform non-AppError instances', () => {
      const regularError = new Error('Regular error message');

      ErrorMiddleware.handleError(
        regularError,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(expect.any(Number));
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.any(String),
          errorCode: expect.any(String)
        })
      );
    });

    it('should handle middleware failures gracefully', () => {
      const error = new Error('Test error');
      (ErrorContextMiddleware.ensureContext as jest.Mock).mockImplementation(() => {
        throw new Error('Context middleware failed');
      });

      ErrorMiddleware.handleError(
        error,
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(expect.any(Number));
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.any(String),
          errorCode: expect.any(String)
        })
      );
    });
  });

  describe('transformToAppError', () => {
    it('should return AppError instances unchanged', () => {
      const appError = new ValidationError('Test error', 'TEST_001');
      const result = ErrorMiddleware.transformToAppError(appError);

      expect(result).toBe(appError);
    });

    it('should transform regular Error instances', () => {
      const error = new Error('Regular error');
      const result = ErrorMiddleware.transformToAppError(error);

      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBeDefined();
      expect(result.errorCode).toBeDefined();
    });

    it('should transform string errors', () => {
      const stringError = 'Something went wrong';
      const result = ErrorMiddleware.transformToAppError(stringError);

      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBe('An unexpected error occurred');
    });
  });

  describe('Basic Error Type Transformations', () => {
    it('should transform JSON syntax errors', () => {
      const jsonError = new SyntaxError('Unexpected token in JSON at position 0');
      const result = ErrorMiddleware.transformToAppError(jsonError);

      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBeDefined();
      expect(result.errorCode).toBeDefined();
    });

    it('should handle MongoDB-like errors', () => {
      const mongoError = {
        name: 'MongoError',
        code: 11000,
        message: 'duplicate key error'
      };

      const result = ErrorMiddleware.transformToAppError(mongoError);
      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBeDefined();
      expect(result.errorCode).toBeDefined();
    });

    it('should handle HTTP-like errors', () => {
      const httpError = {
        status: 400,
        message: 'Bad request'
      };

      const result = ErrorMiddleware.transformToAppError(httpError);
      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBe('An unexpected error occurred');
    });
  });

  describe('Error Context Extraction', () => {
    it('should attempt to extract error context', () => {
      try {
        ErrorMiddleware.extractErrorContext(mockRequest as Request);
        expect(ErrorContextMiddleware.ensureContext).toHaveBeenCalledWith(mockRequest);
      } catch (error) {
        // Context extraction might fail, which is acceptable
        expect(true).toBe(true);
      }
    });
  });
});