import { Request, Response, NextFunction } from 'express';
import { 
  ErrorContextMiddleware, 
  RequestWithErrorContext,
  captureErrorContext,
  getErrorContext,
  ensureErrorContext,
  withAdditionalContext
} from '../../../lib/middlewares/error.context.middleware';
import { ErrorContext } from '../../../lib/utils/errorContext';

// Mock MongoDB ObjectId
jest.mock('mongoose', () => ({
  ...jest.requireActual('mongoose'),
  Types: {
    ObjectId: jest.fn(() => ({
      toString: () => 'mocked-uuid-123',
      toHexString: () => 'mocked-uuid-123'
    }))
  }
}));

// Also mock direct mongodb import if used
jest.mock('mongodb', () => ({
  ObjectId: jest.fn(() => ({
    toString: () => 'mocked-uuid-123',
    toHexString: () => 'mocked-uuid-123'
  }))
}));

describe('ErrorContextMiddleware', () => {
  let mockRequest: Partial<RequestWithErrorContext>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      originalUrl: '/api/contacts',
      url: '/api/contacts',
      method: 'POST',
      get: jest.fn(),
      connection: { remoteAddress: '***********' } as any,
      socket: { remoteAddress: '***********' } as any
    };

    mockResponse = {
      setHeader: jest.fn()
    };

    mockNext = jest.fn();

    // Mock the get method for headers
    (mockRequest.get as jest.Mock).mockImplementation((header: string) => {
      switch (header) {
        case 'User-Agent':
          return 'Mozilla/5.0 (Test Browser)';
        default:
          return null;
      }
    });

    // Clear console.error mock
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('captureContext', () => {
    it('should generate request ID and create error context', () => {
      ErrorContextMiddleware.captureContext(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.requestId).toBeDefined();
      expect(mockRequest.errorContext).toBeInstanceOf(ErrorContext);
      expect(mockRequest.errorContext!.requestId).toBeDefined();
      expect(mockRequest.errorContext!.endpoint).toBe('/api/contacts');
      expect(mockRequest.errorContext!.method).toBe('POST');
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', expect.any(String));
      expect(mockNext).toHaveBeenCalled();
    });

    it('should use existing request ID if present', () => {
      const existingRequestId = 'existing-request-id';
      mockRequest.requestId = existingRequestId;

      ErrorContextMiddleware.captureContext(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.requestId).toBe(existingRequestId);
      expect(mockRequest.errorContext!.requestId).toBe(existingRequestId);
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', existingRequestId);
    });

    it('should include user information from authenticated request', () => {
      (mockRequest as any).userId = 'user-123';
      (mockRequest as any).organizationId = 'org-456';

      ErrorContextMiddleware.captureContext(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.errorContext!.userId).toBe('user-123');
      expect(mockRequest.errorContext!.organizationId).toBe('org-456');
    });

    it('should handle errors gracefully and continue middleware chain', () => {
      // Mock ErrorContext constructor to throw an error
      const originalErrorContext = ErrorContext;
      (ErrorContext as any) = jest.fn(() => {
        throw new Error('Context creation failed');
      });

      ErrorContextMiddleware.captureContext(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(console.error).toHaveBeenCalledWith('Failed to capture error context:', expect.any(Error));
      expect(mockNext).toHaveBeenCalled();

      // Restore original ErrorContext
      (ErrorContext as any) = originalErrorContext;
    });
  });

  describe('getContext', () => {
    it('should return error context when present', () => {
      const mockErrorContext = new ErrorContext(mockRequest as Request);
      mockRequest.errorContext = mockErrorContext;

      const result = ErrorContextMiddleware.getContext(mockRequest as Request);

      expect(result).toBe(mockErrorContext);
    });

    it('should return null when error context is not present', () => {
      const result = ErrorContextMiddleware.getContext(mockRequest as Request);

      expect(result).toBeNull();
    });
  });

  describe('ensureContext', () => {
    it('should return existing context when present', () => {
      const mockErrorContext = new ErrorContext(mockRequest as Request);
      mockRequest.errorContext = mockErrorContext;

      const result = ErrorContextMiddleware.ensureContext(mockRequest as Request);

      expect(result).toBe(mockErrorContext);
    });

    it('should create new context when not present', () => {
      const result = ErrorContextMiddleware.ensureContext(mockRequest as Request);

      expect(result).toBeInstanceOf(ErrorContext);
      expect(result.endpoint).toBe('/api/contacts');
      expect(result.method).toBe('POST');
    });
  });

  describe('withAdditionalContext', () => {
    it('should add additional context to existing error context', () => {
      const originalContext = new ErrorContext(mockRequest as Request);
      mockRequest.errorContext = originalContext;

      const contextExtractor = jest.fn().mockReturnValue({
        jobId: 'job-123',
        fileName: 'test.csv',
        rowNumber: 5
      });

      const middleware = ErrorContextMiddleware.withAdditionalContext(contextExtractor);

      middleware(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(contextExtractor).toHaveBeenCalledWith(mockRequest);
      // The context might be modified in place or replaced - either is valid
      expect(mockRequest.errorContext).toBeDefined();
      expect(mockRequest.errorContext!.jobId).toBe('job-123');
      expect(mockRequest.errorContext!.fileName).toBe('test.csv');
      expect(mockRequest.errorContext!.rowNumber).toBe(5);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should not modify context when no additional context is provided', () => {
      const originalContext = new ErrorContext(mockRequest as Request);
      mockRequest.errorContext = originalContext;

      const contextExtractor = jest.fn().mockReturnValue({});

      const middleware = ErrorContextMiddleware.withAdditionalContext(contextExtractor);

      middleware(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.errorContext).toBe(originalContext);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should continue when no error context exists', () => {
      const contextExtractor = jest.fn().mockReturnValue({
        jobId: 'job-123'
      });

      const middleware = ErrorContextMiddleware.withAdditionalContext(contextExtractor);

      middleware(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(contextExtractor).toHaveBeenCalledWith(mockRequest);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle errors gracefully and continue middleware chain', () => {
      const originalContext = new ErrorContext(mockRequest as Request);
      mockRequest.errorContext = originalContext;

      const contextExtractor = jest.fn().mockImplementation(() => {
        throw new Error('Context extraction failed');
      });

      const middleware = ErrorContextMiddleware.withAdditionalContext(contextExtractor);

      middleware(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(console.error).toHaveBeenCalledWith('Failed to add additional context:', expect.any(Error));
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('convenience functions', () => {
    it('should export captureErrorContext function', () => {
      expect(captureErrorContext).toBe(ErrorContextMiddleware.captureContext);
    });

    it('should export getErrorContext function', () => {
      expect(getErrorContext).toBe(ErrorContextMiddleware.getContext);
    });

    it('should export ensureErrorContext function', () => {
      expect(ensureErrorContext).toBe(ErrorContextMiddleware.ensureContext);
    });

    it('should export withAdditionalContext function', () => {
      expect(withAdditionalContext).toBe(ErrorContextMiddleware.withAdditionalContext);
    });
  });

  describe('integration scenarios', () => {
    it('should work with complete request flow', () => {
      // Simulate authenticated request
      (mockRequest as any).userId = 'user-123';
      (mockRequest as any).organizationId = 'org-456';

      // Capture initial context
      ErrorContextMiddleware.captureContext(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      // Add additional context
      const contextExtractor = (req: Request) => ({
        jobId: 'bulk-insert-job',
        fileName: 'contacts.csv'
      });

      const additionalContextMiddleware = ErrorContextMiddleware.withAdditionalContext(contextExtractor);
      
      additionalContextMiddleware(
        mockRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      // Verify final context
      const finalContext = ErrorContextMiddleware.getContext(mockRequest as Request);
      
      expect(finalContext).toBeDefined();
      expect(finalContext!.userId).toBe('user-123');
      expect(finalContext!.organizationId).toBe('org-456');
      expect(finalContext!.jobId).toBe('bulk-insert-job');
      expect(finalContext!.fileName).toBe('contacts.csv');
      expect(finalContext!.endpoint).toBe('/api/contacts');
      expect(finalContext!.method).toBe('POST');
    });

    it('should handle missing request properties gracefully', () => {
      // Create minimal request with required properties for ErrorContext
      const minimalRequest: Partial<RequestWithErrorContext> = {
        method: 'GET',
        url: '/test',
        get: jest.fn().mockReturnValue(null),
        connection: { remoteAddress: '127.0.0.1' } as any
      };

      ErrorContextMiddleware.captureContext(
        minimalRequest as RequestWithErrorContext,
        mockResponse as Response,
        mockNext
      );

      expect(minimalRequest.requestId).toBeDefined();
      expect(typeof minimalRequest.requestId).toBe('string');
      expect(minimalRequest.errorContext).toBeInstanceOf(ErrorContext);
      expect(mockNext).toHaveBeenCalled();
    });
  });
});