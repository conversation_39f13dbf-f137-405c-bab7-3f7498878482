import { AppError } from '../../../lib/errors/app.error';
import { ErrorContextData } from '../../../lib/utils/errorContext';

describe('AppError', () => {
  describe('constructor', () => {
    it('should create an AppError with default values', () => {
      const error = new AppError('Test error');

      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(500);
      expect(error.errorCode).toBe('INTERNAL_001');
      expect(error.isOperational).toBe(true);
      expect(error.details).toBeUndefined();
      expect(error.context).toBeUndefined();
      expect(error.name).toBe('AppError');
    });

    it('should create an AppError with custom values', () => {
      const details = [{ field: 'email', message: 'Invalid email format' }];
      const error = new AppError(
        'Validation failed',
        400,
        'VALIDATION_001',
        true,
        details
      );

      expect(error.message).toBe('Validation failed');
      expect(error.statusCode).toBe(400);
      expect(error.errorCode).toBe('VALIDATION_001');
      expect(error.isOperational).toBe(true);
      expect(error.details).toEqual(details);
      expect(error.context).toBeUndefined();
    });

    it('should create an AppError with context', () => {
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/test'
      };

      const error = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        context
      );

      expect(error.message).toBe('Test error');
      expect(error.context).toEqual(context);
    });

    it('should capture stack trace', () => {
      const error = new AppError('Test error');
      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('AppError');
    });
  });

  describe('withContext', () => {
    it('should add context to an error without existing context', () => {
      const originalError = new AppError('Test error', 400, 'TEST_001');
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/test'
      };

      const errorWithContext = originalError.withContext(context);

      expect(errorWithContext).not.toBe(originalError); // Should be a new instance
      expect(errorWithContext.message).toBe('Test error');
      expect(errorWithContext.statusCode).toBe(400);
      expect(errorWithContext.errorCode).toBe('TEST_001');
      expect(errorWithContext.context).toEqual(context);
      expect(errorWithContext.stack).toBe(originalError.stack); // Stack should be preserved
    });

    it('should merge context with existing context', () => {
      const existingContext: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456'
      };

      const originalError = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        existingContext
      );

      const additionalContext: Partial<ErrorContextData> = {
        endpoint: '/api/test',
        organizationId: 'org-789'
      };

      const errorWithContext = originalError.withContext(additionalContext);

      expect(errorWithContext.context).toEqual({
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/test',
        organizationId: 'org-789'
      });
    });

    it('should override existing context properties', () => {
      const existingContext: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/old'
      };

      const originalError = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        existingContext
      );

      const newContext: Partial<ErrorContextData> = {
        endpoint: '/api/new',
        organizationId: 'org-789'
      };

      const errorWithContext = originalError.withContext(newContext);

      expect(errorWithContext.context).toEqual({
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/new', // Should be overridden
        organizationId: 'org-789'
      });
    });

    it('should preserve all original error properties', () => {
      const details = [{ field: 'email', message: 'Invalid email' }];
      const originalError = new AppError(
        'Validation error',
        422,
        'VALIDATION_001',
        true,
        details
      );

      const context: Partial<ErrorContextData> = {
        requestId: 'req-123'
      };

      const errorWithContext = originalError.withContext(context);

      expect(errorWithContext.message).toBe('Validation error');
      expect(errorWithContext.statusCode).toBe(422);
      expect(errorWithContext.errorCode).toBe('VALIDATION_001');
      expect(errorWithContext.isOperational).toBe(true);
      expect(errorWithContext.details).toEqual(details);
      expect(errorWithContext.name).toBe('AppError');
    });

    it('should handle empty context object', () => {
      const originalError = new AppError('Test error');
      const errorWithContext = originalError.withContext({});

      expect(errorWithContext.context).toEqual({});
    });
  });

  describe('getContext', () => {
    it('should return context when available', () => {
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456'
      };

      const error = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        context
      );

      expect(error.getContext()).toEqual(context);
    });

    it('should return undefined when no context is available', () => {
      const error = new AppError('Test error');
      expect(error.getContext()).toBeUndefined();
    });
  });

  describe('hasContext', () => {
    it('should return true when context is available', () => {
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123'
      };

      const error = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        context
      );

      expect(error.hasContext()).toBe(true);
    });

    it('should return false when no context is available', () => {
      const error = new AppError('Test error');
      expect(error.hasContext()).toBe(false);
    });

    it('should return false when context is empty object', () => {
      const error = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        {}
      );

      expect(error.hasContext()).toBe(false);
    });
  });

  describe('toErrorResponse', () => {
    it('should convert error to ErrorResponse format without context', () => {
      const error = new AppError('Test error', 400, 'TEST_001');
      const response = error.toErrorResponse();

      expect(response).toEqual({
        error: 'Test error',
        errorCode: 'TEST_001'
      });
    });

    it('should convert error to ErrorResponse format with details', () => {
      const details = [
        { field: 'email', message: 'Invalid email format' },
        { field: 'name', message: 'Name is required' }
      ];

      const error = new AppError(
        'Validation failed',
        400,
        'VALIDATION_001',
        true,
        details
      );

      const response = error.toErrorResponse();

      expect(response).toEqual({
        error: 'Validation failed',
        errorCode: 'VALIDATION_001',
        details: details
      });
    });

    it('should maintain backward compatibility - context should not appear in response', () => {
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/test'
      };

      const error = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        context
      );

      const response = error.toErrorResponse();

      expect(response).toEqual({
        error: 'Test error',
        errorCode: 'TEST_001'
      });

      // Ensure context is not exposed in the response
      expect(response).not.toHaveProperty('context');
      expect(response).not.toHaveProperty('requestId');
      expect(response).not.toHaveProperty('userId');
    });
  });

  describe('backward compatibility', () => {
    it('should work with existing code that does not use context', () => {
      // This simulates existing code that creates AppError without context
      const error = new AppError('Legacy error', 500, 'LEGACY_001', false);

      expect(error.message).toBe('Legacy error');
      expect(error.statusCode).toBe(500);
      expect(error.errorCode).toBe('LEGACY_001');
      expect(error.isOperational).toBe(false);
      expect(error.context).toBeUndefined();
      expect(error.hasContext()).toBe(false);

      // Should still work with toErrorResponse
      const response = error.toErrorResponse();
      expect(response).toEqual({
        error: 'Legacy error',
        errorCode: 'LEGACY_001'
      });
    });

    it('should maintain instanceof checks', () => {
      const error = new AppError('Test error');
      const errorWithContext = error.withContext({ requestId: 'req-123' });

      expect(error instanceof AppError).toBe(true);
      expect(error instanceof Error).toBe(true);
      expect(errorWithContext instanceof AppError).toBe(true);
      expect(errorWithContext instanceof Error).toBe(true);
    });
  });

  describe('immutability', () => {
    it('should not modify original error when adding context', () => {
      const originalError = new AppError('Test error', 400, 'TEST_001');
      const originalContext = originalError.getContext();

      const context: Partial<ErrorContextData> = {
        requestId: 'req-123'
      };

      const errorWithContext = originalError.withContext(context);

      // Original error should remain unchanged
      expect(originalError.getContext()).toBe(originalContext);
      expect(originalError.hasContext()).toBe(false);

      // New error should have context
      expect(errorWithContext.hasContext()).toBe(true);
      expect(errorWithContext.getContext()).toEqual(context);
    });

    it('should not modify original error when merging context', () => {
      const originalContext: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456'
      };

      const originalError = new AppError(
        'Test error',
        400,
        'TEST_001',
        true,
        undefined,
        originalContext
      );

      const additionalContext: Partial<ErrorContextData> = {
        endpoint: '/api/test'
      };

      const errorWithContext = originalError.withContext(additionalContext);

      // Original error context should remain unchanged
      expect(originalError.getContext()).toEqual(originalContext);

      // New error should have merged context
      expect(errorWithContext.getContext()).toEqual({
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/test'
      });
    });
  });

  describe('integration with error subclasses', () => {
    it('should work with ValidationError subclass', () => {
      // Import ValidationError for testing
      const { ValidationError } = require('../../../lib/errors/error.type');
      
      const error = new ValidationError('Validation failed', 'VAL_001');
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456'
      };

      const errorWithContext = error.withContext(context);

      expect(errorWithContext instanceof ValidationError).toBe(true);
      expect(errorWithContext.hasContext()).toBe(true);
      expect(errorWithContext.getContext()).toEqual(context);
      expect(errorWithContext.message).toBe('Validation failed');
      expect(errorWithContext.statusCode).toBe(422);
      expect(errorWithContext.errorCode).toBe('VAL_001');
    });

    it('should work with DuplicateError subclass', () => {
      const { DuplicateError } = require('../../../lib/errors/error.type');
      
      const error = new DuplicateError('Resource exists', 'DUP_001');
      const context: Partial<ErrorContextData> = {
        requestId: 'req-789',
        organizationId: 'org-123'
      };

      const errorWithContext = error.withContext(context);

      expect(errorWithContext instanceof DuplicateError).toBe(true);
      expect(errorWithContext.hasContext()).toBe(true);
      expect(errorWithContext.getContext()).toEqual(context);
      expect(errorWithContext.statusCode).toBe(409);
    });
  });

  describe('requirements verification', () => {
    it('should satisfy requirement 6.1 - maintain backward compatibility', () => {
      // Test that existing error response formats remain unchanged
      const error = new AppError('Test error', 400, 'TEST_001');
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456'
      };

      const errorWithContext = error.withContext(context);
      const response = errorWithContext.toErrorResponse();

      // Response should not include context information
      expect(response).toEqual({
        error: 'Test error',
        errorCode: 'TEST_001'
      });

      expect(response).not.toHaveProperty('context');
      expect(response).not.toHaveProperty('requestId');
      expect(response).not.toHaveProperty('userId');
    });

    it('should satisfy requirement 6.2 - maintain backward compatibility with details', () => {
      const details = [{ field: 'email', message: 'Invalid email' }];
      const error = new AppError('Validation error', 422, 'VAL_001', true, details);
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123'
      };

      const errorWithContext = error.withContext(context);
      const response = errorWithContext.toErrorResponse();

      expect(response).toEqual({
        error: 'Validation error',
        errorCode: 'VAL_001',
        details: details
      });

      expect(response).not.toHaveProperty('context');
    });

    it('should satisfy requirement 1.4 - support context for enhanced logging', () => {
      const error = new AppError('Test error', 500, 'INTERNAL_001');
      const context: Partial<ErrorContextData> = {
        requestId: 'req-123',
        userId: 'user-456',
        endpoint: '/api/test',
        method: 'POST',
        timestamp: new Date(),
        organizationId: 'org-789'
      };

      const errorWithContext = error.withContext(context);

      expect(errorWithContext.hasContext()).toBe(true);
      expect(errorWithContext.getContext()).toEqual(context);
      
      // Context should be available for logging purposes
      const contextData = errorWithContext.getContext();
      expect(contextData?.requestId).toBe('req-123');
      expect(contextData?.userId).toBe('user-456');
      expect(contextData?.endpoint).toBe('/api/test');
      expect(contextData?.organizationId).toBe('org-789');
    });
  });
});