import { ContactsHandler } from '../../../handlers/ContactsHandler';
import { ContactDAO } from '../../../lib/db/dao/ContactDAO';
import { ContactStatus } from '../../../types/contact.types';
import { contactFixtures, organizationFixtures } from '../../fixtures/contactFixtures';
import { MockResponseHelper } from '../../utils/testHelpers';
import { ObjectId } from 'mongodb';
import { ValidationError, NotFoundError, BadRequestError } from '../../../lib/errors/error.type';

// Mock the ContactDAO
jest.mock('../../../lib/db/dao/ContactDAO');
const mockContactDAO = ContactDAO as jest.Mocked<typeof ContactDAO>;

// Mock the formatContactResponse method
mockContactDAO.formatContactResponse = jest.fn().mockImplementation((contact) => {
  if (!contact) return undefined;
  
  return {
    _id: contact._id.toString(),
    org_id: contact.org_id,
    created_by: contact.created_by,
    name: contact.name,
    email: contact.email,
    phone: contact.phone,
    country: contact.country,
    country_code: contact.country_code,
    avatar_url: contact.avatar_url,
    tags: contact.tags.map((tag: any) => ({
      tag_id: tag.tag_id.toString(),
      tag_name: tag.tag_name
    })),
    additional_fields: contact.additional_fields,
    status: contact.status,
    created_at: contact.created_at,
    updated_at: contact.updated_at
  };
});

// For getContacts tests
mockContactDAO.getContacts = jest.fn();
mockContactDAO.formatContactsResponse = jest.fn();

// Mock the logger
jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));

describe('ContactsHandler', () => {
  describe('createContact', () => {
    let mockReq: any;
    let mockRes: any;

    beforeEach(() => {
      mockReq = {
        userId: organizationFixtures.sampleUser.id,
        organizationId: organizationFixtures.sampleOrganization.id,
        body: { ...contactFixtures.validContactRequest }
      };
      mockRes = MockResponseHelper.createMockResponse();
      
      // Reset all mocks
      jest.clearAllMocks();
    });

    describe('Successful contact creation', () => {
      it('should create contact with complete data', async () => {
        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.validContactRequest.name,
          email: contactFixtures.validContactRequest.email,
          phone: contactFixtures.validContactRequest.phone,
          country: contactFixtures.validContactRequest.country,
          country_code: contactFixtures.validContactRequest.country_code,
          avatar_url: undefined,
          tags: contactFixtures.validContactRequest.tags?.map(tag => ({
            tag_id: new ObjectId(tag.tag_id),
            tag_name: tag.tag_name
          })) || [],
          additional_fields: contactFixtures.validContactRequest.additional_fields || {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith({
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.validContactRequest.name,
          email: contactFixtures.validContactRequest.email,
          phone: contactFixtures.validContactRequest.phone,
          country: contactFixtures.validContactRequest.country,
          country_code: contactFixtures.validContactRequest.country_code,
          avatar_url: contactFixtures.validContactRequest.avatar_url,
          tags: expect.arrayContaining([
            expect.objectContaining({
              tag_id: expect.any(ObjectId),
              tag_name: expect.any(String)
            })
          ]),
          additional_fields: contactFixtures.validContactRequest.additional_fields,
          status: ContactStatus.ACTIVE
        });

        expect(mockRes.status).toHaveBeenCalledWith(201);
        expect(mockRes.json).toHaveBeenCalledWith({
          _id: mockCreatedContact._id.toString(),
          org_id: mockCreatedContact.org_id,
          created_by: mockCreatedContact.created_by,
          name: mockCreatedContact.name,
          email: mockCreatedContact.email,
          phone: mockCreatedContact.phone,
          country: mockCreatedContact.country,
          country_code: mockCreatedContact.country_code,
          avatar_url: mockCreatedContact.avatar_url,
          tags: mockCreatedContact.tags.map((tag: any) => ({
            tag_id: tag.tag_id.toString(),
            tag_name: tag.tag_name
          })),
          additional_fields: mockCreatedContact.additional_fields,
          status: mockCreatedContact.status,
          created_at: mockCreatedContact.created_at,
          updated_at: mockCreatedContact.updated_at
        });
      });

      it('should create contact with minimal data', async () => {
        mockReq.body = { ...contactFixtures.minimalValidContactRequest };

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.minimalValidContactRequest.name,
          email: contactFixtures.minimalValidContactRequest.email,
          phone: contactFixtures.minimalValidContactRequest.phone,
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith({
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.minimalValidContactRequest.name,
          email: contactFixtures.minimalValidContactRequest.email,
          phone: contactFixtures.minimalValidContactRequest.phone,
          country: undefined,
          country_code: undefined,
          avatar_url: undefined,
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE
        });

        expect(mockRes.status).toHaveBeenCalledWith(201);
      });

      it('should handle tags conversion from string to ObjectId', async () => {
        const tagId = new ObjectId().toString();
        mockReq.body = {
          ...contactFixtures.minimalValidContactRequest,
          tags: [
            {
              tag_id: tagId,
              tag_name: 'test-tag'
            }
          ]
        };

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: mockReq.body.name,
          email: mockReq.body.email,
          phone: mockReq.body.phone,
          tags: [
            {
              tag_id: new ObjectId(tagId),
              tag_name: 'test-tag'
            }
          ],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith(
          expect.objectContaining({
            tags: [
              {
                tag_id: expect.any(ObjectId),
                tag_name: 'test-tag'
              }
            ]
          })
        );
      });

      it('should use default status when not provided', async () => {
        mockReq.body = { ...contactFixtures.minimalValidContactRequest };
        delete mockReq.body.status;

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: mockReq.body.name,
          email: mockReq.body.email,
          phone: mockReq.body.phone,
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith(
          expect.objectContaining({
            status: ContactStatus.ACTIVE
          })
        );
      });
    });

    describe('Error handling', () => {
      it('should throw OrganizationError when organization context is missing', async () => {
        mockReq.organizationId = undefined;

        await expect(ContactsHandler.createContact(mockReq, mockRes)).rejects.toThrow('Organization context is required');
        expect(mockContactDAO.createContact).not.toHaveBeenCalled();
      });

      it('should handle duplicate contact error', async () => {
        const duplicateError = new Error('Contact with email already exists in organization');
        mockContactDAO.createContact.mockRejectedValue(duplicateError);

        await expect(ContactsHandler.createContact(mockReq, mockRes)).rejects.toThrow('Failed to create contact');
      });

      it('should handle validation errors', async () => {
        const validationError = new Error('Validation failed: Invalid tag reference');
        mockContactDAO.createContact.mockRejectedValue(validationError);

        await expect(ContactsHandler.createContact(mockReq, mockRes)).rejects.toThrow('Failed to create contact');
      });

      it('should handle organization-related errors', async () => {
        const orgError = new Error('Invalid organization context');
        mockContactDAO.createContact.mockRejectedValue(orgError);

        await expect(ContactsHandler.createContact(mockReq, mockRes)).rejects.toThrow('Failed to create contact');
      });

      it('should handle database errors', async () => {
        const dbError = new Error('Database connection failed');
        mockContactDAO.createContact.mockRejectedValue(dbError);

        await expect(ContactsHandler.createContact(mockReq, mockRes)).rejects.toThrow('Failed to create contact');
      });

      it('should handle generic errors', async () => {
        const genericError = new Error('Something unexpected happened');
        mockContactDAO.createContact.mockRejectedValue(genericError);

        await expect(ContactsHandler.createContact(mockReq, mockRes)).rejects.toThrow('Failed to create contact');
      });

      it('should handle non-Error objects', async () => {
        const nonErrorObject = 'String error';
        mockContactDAO.createContact.mockRejectedValue(nonErrorObject);

        await expect(ContactsHandler.createContact(mockReq, mockRes)).rejects.toThrow('Failed to create contact');
      });
    });

    describe('Response formatting', () => {
      it('should properly format ObjectIds in response', async () => {
        const tagObjectId = new ObjectId();
        const contactObjectId = new ObjectId();

        const mockCreatedContact = {
          _id: contactObjectId,
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [
            {
              tag_id: tagObjectId,
              tag_name: 'test-tag'
            }
          ],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            _id: contactObjectId.toString(),
            tags: [
              {
                tag_id: tagObjectId.toString(),
                tag_name: 'test-tag'
              }
            ]
          })
        );
      });

      it('should handle empty tags array', async () => {
        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            tags: []
          })
        );
      });

      it('should preserve additional_fields in response', async () => {
        const additionalFields = {
          company: 'Test Corp',
          department: 'Engineering',
          priority: 'high',
          score: 95
        };

        mockReq.body.additional_fields = additionalFields;

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [],
          additional_fields: additionalFields,
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            additional_fields: additionalFields
          })
        );
      });
    });
  });

  // Tests from ContactsHandler.getContacts.test.ts
  describe('getContacts', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    
    test('should throw OrganizationError if organization ID is missing', async () => {
      // Create mock request without organization ID
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = undefined;
      
      const res = MockResponseHelper.createMockResponse();
      
      await expect(ContactsHandler.getContacts(req, res)).rejects.toThrow('Organization context is required');
    });
    
    test('should process query parameters correctly', async () => {
      // Create mock request with query parameters
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      
      // Create a MongoDB query that includes status and tag_id filters
      const mongoQuery = {
        "status": ContactStatus.ACTIVE,
        "tags.tag_id": "507f1f77bcf86cd799439011"
      };
      
      req.query = {
        page: '2',
        page_size: '50',
        sort_by: 'name',
        sort_direction: 'asc',
        search: 'John Doe',
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify ContactDAO.getContacts was called with correct parameters
      expect(mockContactDAO.getContacts).toHaveBeenCalledWith(
        'org_123456789',
        expect.objectContaining({
          page: 2,
          page_size: 50,
          sort_by: 'name',
          sort_direction: 'asc',
          search: 'John Doe',
          contactFilterQuery: JSON.stringify(mongoQuery)
        })
      );
    });
    
    test('should handle ValidationError when pagination parameters are missing', async () => {
      // Create mock request without pagination parameters
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock Number conversion to throw a ValidationError
      // This simulates what would happen if the validation middleware caught the missing parameters
      const originalNumberConstructor = Number;
      global.Number = jest.fn().mockImplementation((value) => {
        if (value === undefined) {
          throw new ValidationError(
            'Invalid query parameters for contacts retrieval',
            'CONTACT_QUERY_VALIDATION_001',
            [{ field: 'page', message: 'Page is required' }]
          );
        }
        return originalNumberConstructor(value);
      }) as any;
      
      await expect(ContactsHandler.getContacts(req, res)).rejects.toThrow('Invalid query parameters for contacts retrieval');
      
      // Restore original Number constructor
      global.Number = originalNumberConstructor;
      
      // Verify ContactDAO.getContacts was not called
      expect(mockContactDAO.getContacts).not.toHaveBeenCalled();
    });
    
    test('should calculate pagination metadata correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        page: '2',
        page_size: '10'
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Create mock contacts with all required properties
      const mockContacts = [
        { 
          _id: new ObjectId(), 
          name: 'John Doe',
          org_id: 'org_123456789',
          created_by: 'user_123456789',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        },
        { 
          _id: new ObjectId(), 
          name: 'Jane Smith',
          org_id: 'org_123456789',
          created_by: 'user_123456789',
          email: '<EMAIL>',
          phone: '+0987654321',
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        }
      ];
      
      // Mock ContactDAO.getContacts to return contacts and total count
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: mockContacts as any, // Use type assertion to bypass type checking
        totalCount: 25 // Total of 25 contacts (3 pages with page_size 10)
      });
      
      // Mock ContactDAO.formatContactsResponse to return contacts with string IDs
      const formattedContacts = mockContacts.map(contact => ({
        ...contact,
        _id: contact._id.toString()
      }));
      mockContactDAO.formatContactsResponse.mockReturnValue(formattedContacts);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        data: formattedContacts,
        pagination: {
          total_count: 25,
          page: 2,
          page_size: 10,
          total_pages: 3,
          has_next_page: true,
          has_prev_page: true
        }
      });
    });
    
    test('should handle first page pagination metadata correctly', async () => {
      // Create mock request for first page
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        page: '1',
        page_size: '10'
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return contacts and total count
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 25 // Total of 25 contacts (3 pages with page_size 10)
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify pagination metadata for first page
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        pagination: expect.objectContaining({
          page: 1,
          has_next_page: true,
          has_prev_page: false // First page should not have previous page
        })
      }));
    });
    
    test('should handle last page pagination metadata correctly', async () => {
      // Create mock request for last page
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        page: '3',
        page_size: '10'
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return contacts and total count
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 25 // Total of 25 contacts (3 pages with page_size 10)
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify pagination metadata for last page
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        pagination: expect.objectContaining({
          page: 3,
          has_next_page: false, // Last page should not have next page
          has_prev_page: true
        })
      }));
    });
    
    test('should handle empty results correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify response for empty results
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        data: [],
        pagination: {
          total_count: 0,
          page: 1,
          page_size: 20,
          total_pages: 0,
          has_next_page: false,
          has_prev_page: false
        }
      });
    });
    
    test('should handle database errors correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to throw a database error
      mockContactDAO.getContacts.mockRejectedValue(new Error('Database connection failed'));
      
      await expect(ContactsHandler.getContacts(req, res)).rejects.toThrow('Failed to retrieve contacts');
    });
    
    test('should handle generic errors correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to throw a generic error
      mockContactDAO.getContacts.mockRejectedValue(new Error('Unexpected error'));
      
      await expect(ContactsHandler.getContacts(req, res)).rejects.toThrow('Failed to retrieve contacts');
    });
    
    test('should process contactFilterQuery parameter correctly', async () => {
      // Create mock request with contactFilterQuery parameter
      const mongoQuery = {
        "name": { "$regex": "John", "$options": "i" }
      };
      
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify ContactDAO.getContacts was called with correct filter parameters
      expect(mockContactDAO.getContacts).toHaveBeenCalledWith(
        'org_123456789',
        expect.objectContaining({
          contactFilterQuery: JSON.stringify(mongoQuery)
        })
      );
    });
    
    test('should process complex contactFilterQuery parameter correctly', async () => {
      // Create mock request with complex contactFilterQuery parameter
      const mongoQuery = {
        "$and": [
          { "firstName": { "$regex": "^Stev" } },
          { "lastName": { "$in": ["Vai", "Vaughan"] } },
          { "age": { "$gt": "28" } },
          { "$or": [
            { "isMusician": true },
            { "instrument": "Guitar" }
          ]},
          { "$eq": ["$groupedField1", "$groupedField4"] },
          { "birthdate": { "$gte": "1954-10-03", "$lte": "1960-06-06" } }
        ]
      };
      
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify ContactDAO.getContacts was called with correct filter parameter
      expect(mockContactDAO.getContacts).toHaveBeenCalledWith(
        'org_123456789',
        expect.objectContaining({
          contactFilterQuery: JSON.stringify(mongoQuery)
        })
      );
    });
  });

  describe('getContactById', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    
    test('should throw OrganizationError if organization ID is missing', async () => {
      // Create mock request without organization ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = undefined;
      
      const res = MockResponseHelper.createMockResponse();
      
      await expect(ContactsHandler.getContactById(req, res)).rejects.toThrow('Organization context is required');
    });
    
    test('should throw DatabaseError if contact is not found', async () => {
      // Create mock request with valid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContactById to throw NotFoundError
      const notFoundError = new NotFoundError(
        'Contact with ID 507f1f77bcf86cd799439011 not found in organization org_123456789',
        'CONTACT_003'
      );
      mockContactDAO.getContactById.mockImplementation(() => {
        throw notFoundError;
      });
      
      await expect(ContactsHandler.getContactById(req, res)).rejects.toThrow('Contact with ID 507f1f77bcf86cd799439011 not found in organization org_123456789');
    });
    
    test('should throw DatabaseError if contact ID is invalid', async () => {
      // Create mock request with invalid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: 'invalid-id'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContactById to throw BadRequestError
      const badRequestError = new BadRequestError(
        'Invalid contact ID format',
        'CONTACT_002',
        [{ field: 'contactId', message: 'Contact ID must be a valid MongoDB ObjectId' }]
      );
      mockContactDAO.getContactById.mockImplementation(() => {
        throw badRequestError;
      });
      
      await expect(ContactsHandler.getContactById(req, res)).rejects.toThrow('Invalid contact ID format');
    });
    
    test('should throw DatabaseError if database error occurs', async () => {
      // Create mock request with valid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContactById to throw DatabaseError
      mockContactDAO.getContactById.mockRejectedValue({
        name: 'DatabaseError',
        message: 'Failed to retrieve contact',
        errorCode: 'DB_001',
        statusCode: 500
      });
      
      await expect(ContactsHandler.getContactById(req, res)).rejects.toThrow('Failed to retrieve contact');
    });
    
    test('should return 200 with contact data if contact is found', async () => {
      // Create mock request with valid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Create mock contact document with type assertion to satisfy TypeScript
      const mockContact = {
        _id: new ObjectId('507f1f77bcf86cd799439011'),
        org_id: 'org_123456789',
        created_by: 'user_123456789',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-123-4567',
        status: ContactStatus.ACTIVE,
        tags: [],
        additional_fields: {},
        created_at: new Date(),
        updated_at: new Date()
      } as any; // Type assertion to avoid TypeScript errors with Mongoose Document methods
      
      // Create mock formatted contact response
      const mockFormattedContact = {
        _id: '507f1f77bcf86cd799439011',
        org_id: 'org_123456789',
        created_by: 'user_123456789',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-123-4567',
        status: ContactStatus.ACTIVE,
        tags: [],
        additional_fields: {},
        created_at: mockContact.created_at,
        updated_at: mockContact.updated_at
      };
      
      // Mock ContactDAO.getContactById to return mock contact
      mockContactDAO.getContactById.mockResolvedValue(mockContact);
      
      // Mock ContactDAO.formatContactResponse to return formatted contact
      mockContactDAO.formatContactResponse.mockReturnValue(mockFormattedContact);
      
      await ContactsHandler.getContactById(req, res);
      
      // Verify ContactDAO.getContactById was called with correct parameters
      expect(mockContactDAO.getContactById).toHaveBeenCalledWith(
        'org_123456789',
        '507f1f77bcf86cd799439011'
      );
      
      // Verify ContactDAO.formatContactResponse was called with mock contact
      expect(mockContactDAO.formatContactResponse).toHaveBeenCalledWith(mockContact);
      
      // Verify that a 200 response with contact data is returned
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(mockFormattedContact);
    });
  });
});