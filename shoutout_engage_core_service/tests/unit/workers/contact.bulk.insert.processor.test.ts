import { ContactBulkInsertProcessor } from '../../../workers/processors/contact.bulk.insert.processor';
import { ContactDAO } from '../../../lib/db/dao/ContactDAO';
import { CommonJobDAO } from '../../../lib/db/dao/CommonJobDAO';
import { JOB_TYPE, JOB_ERROR_CODES } from '../../../lib/constant/job.constants';
import { QUEUE_NAMES } from '../../../lib/constant/queue.constants';
import { ContactStatus } from '../../../types/contact.types';
import { 
  ContactBulkInsertJobData, 
  ContactBulkInsertResult,
  ContactRowData 
} from '../../../types/contact.bulk.insert.types';
import { 
  ValidationError, 
  DuplicateError, 
  DatabaseError, 
  InternalError 
} from '../../../lib/errors/error.type';
import { Job } from 'bull';
import * as fs from 'fs';
import { QueueManager } from '../../../queues/queue.manager';

// Mock dependencies
jest.mock('../../../lib/db/dao/ContactDAO');
jest.mock('../../../lib/db/dao/CommonJobDAO');
jest.mock('../../../lib/db/connectors/RedisConnector');
jest.mock('../../../queues/queue.manager');
jest.mock('../../../lib/config', () => ({
  default: {
    logger: {
      level: 'info',
      name: 'test'
    }
  }
}));
jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));
jest.mock('bull');
jest.mock('fs', () => ({
  existsSync: jest.fn(),
  unlinkSync: jest.fn(),
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
  createReadStream: jest.fn()
}));
jest.mock('csv-parser');


// Mock Redis connector
const mockRedisConnector = {
  getBullConfig: jest.fn().mockReturnValue({
    host: 'localhost',
    port: 6379,
    db: 0
  })
};

jest.doMock('../../../lib/db/connectors/RedisConnector', () => ({
  default: mockRedisConnector
}));

describe('ContactBulkInsertProcessor', () => {
  let processor: ContactBulkInsertProcessor;
  let mockJob: jest.Mocked<Job<ContactBulkInsertJobData>>;
  let mockContactDAO: jest.Mocked<typeof ContactDAO>;
  let mockCommonJobDAO: jest.Mocked<typeof CommonJobDAO>;
  let mockFs: jest.Mocked<typeof fs>;

  const validJobData: ContactBulkInsertJobData = {
    jobId: 'test-job-id',
    orgId: 'test-org-id',
    createdBy: 'test-user-id',
    filePath: '/tmp/test-contacts.csv',
    fileName: 'test-contacts.csv',
    fileSize: 1024,
    userEmail: '<EMAIL>',
    headerMapping: {
      'Name': 'name',
      'Email': 'email',
      'Phone': 'phone'
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Bull Queue
    const mockQueue = {
      name: QUEUE_NAMES.CONTACT_BULK_INSERT,
      process: jest.fn(),
      close: jest.fn(),
      on: jest.fn()
    } as any;

    (require('bull') as jest.MockedClass<any>).mockImplementation(() => mockQueue);

    // Mock QueueManager
    const mockQueueManager = {
      getQueue: jest.fn().mockReturnValue(mockQueue),
      getInstance: jest.fn()
    };
    
    (QueueManager.getInstance as jest.Mock) = jest.fn().mockReturnValue(mockQueueManager);

    // Mock job
    mockJob = {
      id: 'bull-job-id',
      data: validJobData
    } as any;

    // Mock DAOs
    mockContactDAO = ContactDAO as jest.Mocked<typeof ContactDAO>;
    mockCommonJobDAO = CommonJobDAO as jest.Mocked<typeof CommonJobDAO>;
    mockFs = fs as jest.Mocked<typeof fs>;

    // Mock file system
    mockFs.existsSync.mockReturnValue(true);
    mockFs.unlinkSync.mockImplementation(() => {});
    mockFs.mkdirSync.mockImplementation(() => '');
    mockFs.writeFileSync.mockImplementation(() => {});
    


    processor = new ContactBulkInsertProcessor();
  });

  describe('Constructor', () => {
    it('should initialize with correct queue name and job type', () => {
      expect(processor.queueName).toBe(QUEUE_NAMES.CONTACT_BULK_INSERT);
      expect(processor.jobType).toBe(JOB_TYPE.CONTACT_BULK_INSERT);
    });
  });

  describe('processJob', () => {
    beforeEach(() => {
      // Mock progress tracking methods
      jest.spyOn(processor as any, 'updateJobProgressWithCalculation').mockResolvedValue(undefined);
    });

    it('should process CSV file successfully', async () => {
      // Mock CSV processing
      const mockCSVResult = {
        totalRows: 3,
        successfulRows: 2,
        failedRows: 1,
        errors: [{
          rowNumber: 3,
          contactData: { name: '', email: 'invalid', phone: '123' },
          error: {
            type: 'VALIDATION' as const,
            message: 'Validation failed',
            errorCode: JOB_ERROR_CODES.BULK_INSERT_VALIDATION_ERROR,
            details: [{ field: 'name', message: 'Name is required' }]
          },
          timestamp: new Date()
        }],
        processingTime: 1000
      };

      jest.spyOn(processor as any, 'processCSVFile').mockResolvedValue(mockCSVResult);
      jest.spyOn(processor as any, 'generateErrorReport').mockResolvedValue('/tmp/error-report.csv');

      const result = await processor.processJob(mockJob);

      expect(result).toEqual({
        totalRows: 3,
        successfulRows: 2,
        failedRows: 1,
        processingTime: expect.any(Number),
        errors: mockCSVResult.errors,
        errorReportPath: '/tmp/error-report.csv',
        startedAt: expect.any(Date),
        completedAt: expect.any(Date)
      });

      expect(mockFs.unlinkSync).toHaveBeenCalledWith('/tmp/test-contacts.csv');
    });

    it('should throw error if file does not exist', async () => {
      mockFs.existsSync.mockReturnValue(false);

      await expect(processor.processJob(mockJob)).rejects.toThrow(InternalError);
      await expect(processor.processJob(mockJob)).rejects.toThrow('CSV file not found');
    });

    it('should clean up file even if processing fails', async () => {
      jest.spyOn(processor as any, 'processCSVFile').mockRejectedValue(new Error('Processing failed'));

      await expect(processor.processJob(mockJob)).rejects.toThrow('Processing failed');
      expect(mockFs.unlinkSync).toHaveBeenCalledWith('/tmp/test-contacts.csv');
    });

    it('should handle cleanup failure gracefully', async () => {
      const mockCSVResult = {
        totalRows: 1,
        successfulRows: 1,
        failedRows: 0,
        errors: [],
        processingTime: 500
      };

      jest.spyOn(processor as any, 'processCSVFile').mockResolvedValue(mockCSVResult);
      mockFs.unlinkSync.mockImplementation(() => {
        throw new Error('Cleanup failed');
      });

      const result = await processor.processJob(mockJob);

      expect(result.totalRows).toBe(1);
      // Should not throw error even if cleanup fails
    });
  });

  describe('processCSVFile', () => {
    beforeEach(() => {
      // Mock progress tracking
      jest.spyOn(processor as any, 'updateJobProgressWithCalculation').mockResolvedValue(undefined);
    });

    it('should process CSV rows successfully', async () => {
      const mockRows = [
        { name: 'John Doe', email: '<EMAIL>', phone: '+1234567890' },
        { name: 'Jane Smith', email: '<EMAIL>', phone: '+0987654321' },
        { name: '', email: 'invalid-email', phone: '123' } // Invalid row
      ];

      // Mock stream events
      const mockStream: any = {
        pipe: jest.fn().mockReturnThis(),
        on: jest.fn((event: string, callback: (data?: any) => void) => {
          if (event === 'data') {
            // Simulate processing each row
            mockRows.forEach((row, index) => {
              callback(row);
            });
          } else if (event === 'end') {
            callback();
          }
          return mockStream;
        })
      };

      (mockFs.createReadStream as jest.Mock).mockReturnValue(mockStream);
      
      // Mock csv-parser
      const csvParser = require('csv-parser');
      csvParser.mockReturnValue(mockStream);

      // Mock contact validation and processing
      jest.spyOn(processor as any, 'validateAndProcessContact')
        .mockResolvedValueOnce({ isValid: true, errors: [] })
        .mockResolvedValueOnce({ isValid: true, errors: [] })
        .mockResolvedValueOnce({ 
          isValid: false, 
          errors: [{
            rowNumber: 3,
            contactData: mockRows[2],
            error: {
              type: 'VALIDATION',
              message: 'Validation failed',
              errorCode: JOB_ERROR_CODES.BULK_INSERT_VALIDATION_ERROR
            },
            timestamp: expect.any(Date)
          }]
        });

      const result = await (processor as any).processCSVFile(
        '/tmp/test.csv', 
        'test-org-id', 
        'test-user-id', 
        'test-job-id'
      );

      expect(result.totalRows).toBe(3);
      expect(result.successfulRows).toBe(2);
      expect(result.failedRows).toBe(1);
      expect(result.errors).toHaveLength(1);
    });

    it('should handle CSV parsing errors', async () => {
      const mockStream: any = {
        pipe: jest.fn().mockReturnThis(),
        on: jest.fn((event: string, callback: (error?: Error) => void) => {
          if (event === 'error') {
            callback(new Error('CSV parsing failed'));
          }
          return mockStream;
        })
      };

      (mockFs.createReadStream as jest.Mock).mockReturnValue(mockStream);
      
      // Mock csv-parser
      const csvParser = require('csv-parser');
      csvParser.mockReturnValue(mockStream);

      await expect((processor as any).processCSVFile(
        '/tmp/test.csv', 
        'test-org-id', 
        'test-user-id', 
        'test-job-id'
      )).rejects.toThrow(InternalError);
    });

    it('should handle row processing errors gracefully', async () => {
      const mockRows = [
        { name: 'John Doe', email: '<EMAIL>', phone: '+1234567890' }
      ];

      const mockStream: any = {
        pipe: jest.fn().mockReturnThis(),
        on: jest.fn((event: string, callback: (data?: any) => void) => {
          if (event === 'data') {
            callback(mockRows[0]);
          } else if (event === 'end') {
            callback();
          }
          return mockStream;
        })
      };

      (mockFs.createReadStream as jest.Mock).mockReturnValue(mockStream);
      
      // Mock csv-parser
      const csvParser = require('csv-parser');
      csvParser.mockReturnValue(mockStream);

      // Mock contact processing to throw error
      jest.spyOn(processor as any, 'validateAndProcessContact')
        .mockRejectedValue(new Error('Processing error'));

      const result = await (processor as any).processCSVFile(
        '/tmp/test.csv', 
        'test-org-id', 
        'test-user-id', 
        'test-job-id'
      );

      expect(result.totalRows).toBe(1);
      expect(result.successfulRows).toBe(0);
      expect(result.failedRows).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error.type).toBe('SYSTEM');
    });
  });

  describe('validateAndProcessContact', () => {
    const validRowData: ContactRowData = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      country: 'United States',
      country_code: 'US'
    };

    beforeEach(() => {
      mockContactDAO.createContact.mockResolvedValue({
        _id: 'contact-id',
        ...validRowData,
        org_id: 'test-org-id',
        created_by: 'test-user-id'
      } as any);
    });

    it('should validate and process valid contact successfully', async () => {
      jest.spyOn(processor as any, 'validateContactData').mockReturnValue({
        isValid: true,
        contact: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          country: 'United States',
          country_code: 'US',
          status: ContactStatus.ACTIVE,
          tags: [],
          additional_fields: {}
        },
        errors: []
      });

      const result = await (processor as any).validateAndProcessContact(
        validRowData, 
        validJobData.headerMapping,
        'test-org-id', 
        'test-user-id', 
        1,
        'test-job-id'
      );

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(mockContactDAO.createContact).toHaveBeenCalledWith({
        org_id: 'test-org-id',
        created_by: 'test-user-id',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        country: 'United States',
        country_code: 'US',
        avatar_url: undefined,
        tags: [],
        additional_fields: {},
        status: ContactStatus.ACTIVE
      });
    });

    it('should handle validation errors', async () => {
      jest.spyOn(processor as any, 'validateContactData').mockReturnValue({
        isValid: false,
        errors: [
          { field: 'name', message: 'Name is required' },
          { field: 'email', message: 'Email is invalid' }
        ]
      });

      const result = await (processor as any).validateAndProcessContact(
        { name: '', email: 'invalid' }, 
        'test-org-id', 
        'test-user-id', 
        1
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error.type).toBe('VALIDATION');
      expect(result.errors[0].error.details).toHaveLength(2);
      expect(mockContactDAO.createContact).not.toHaveBeenCalled();
    });

    it('should handle duplicate contact errors', async () => {
      jest.spyOn(processor as any, 'validateContactData').mockReturnValue({
        isValid: true,
        contact: validRowData,
        errors: []
      });

      mockContactDAO.createContact.mockRejectedValue(new DuplicateError(
        'Contact already exists',
        'CONTACT_DUPLICATE_001',
        [{ field: 'email', message: 'Email already exists' }]
      ));

      const result = await (processor as any).validateAndProcessContact(
        validRowData, 
        'test-org-id', 
        'test-user-id', 
        1
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error.type).toBe('DUPLICATE');
    });

    it('should handle database errors', async () => {
      jest.spyOn(processor as any, 'validateContactData').mockReturnValue({
        isValid: true,
        contact: validRowData,
        errors: []
      });

      mockContactDAO.createContact.mockRejectedValue(new DatabaseError(
        'Database connection failed',
        'DB_001'
      ));

      const result = await (processor as any).validateAndProcessContact(
        validRowData, 
        'test-org-id', 
        'test-user-id', 
        1
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error.type).toBe('DATABASE');
    });

    it('should handle system errors', async () => {
      jest.spyOn(processor as any, 'validateContactData').mockReturnValue({
        isValid: true,
        contact: validRowData,
        errors: []
      });

      mockContactDAO.createContact.mockRejectedValue(new Error('Unexpected error'));

      const result = await (processor as any).validateAndProcessContact(
        validRowData, 
        'test-org-id', 
        'test-user-id', 
        1
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error.type).toBe('SYSTEM');
      expect(result.errors[0].error.message).toBe('Unexpected error');
    });
  });

  describe('validateContactData', () => {
    const headerMapping = {
      'name': 'name',
      'email': 'email',
      'phone': 'phone',
      'country': 'country',
      'country_code': 'country_code',
      'status': 'status',
      'tags': 'tags',
      'avatar_url': 'avatar_url'
    };

    it('should validate valid contact data', () => {
      const validData: ContactRowData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        country: 'United States',
        country_code: 'US',
        status: 'active'
      };

      const result = (processor as any).validateContactData(validData, headerMapping);

      expect(result.isValid).toBe(true);
      expect(result.contact.name).toBe('John Doe');
      expect(result.contact.email).toBe('<EMAIL>');
      expect(result.contact.phone).toBe('+1234567890');
      expect(result.contact.country).toBe('United States');
      expect(result.contact.country_code).toBe('US');
      expect(result.contact.status).toBe(ContactStatus.ACTIVE);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate required fields', () => {
      const invalidData: ContactRowData = {
        name: '',
        email: '',
        phone: ''
      };

      const result = (processor as any).validateContactData(invalidData, headerMapping);

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(3);
      expect(result.errors.find((e: any) => e.field === 'name')).toBeDefined();
      expect(result.errors.find((e: any) => e.field === 'email')).toBeDefined();
      expect(result.errors.find((e: any) => e.field === 'phone')).toBeDefined();
    });

    it('should validate email format', () => {
      const invalidEmailData: ContactRowData = {
        name: 'John Doe',
        email: 'invalid-email',
        phone: '+1234567890'
      };

      const result = (processor as any).validateContactData(invalidEmailData, headerMapping);

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].field).toBe('email');
      expect(result.errors[0].message).toContain('valid email address');
    });

    it('should handle additional fields', () => {
      const dataWithAdditionalFields: ContactRowData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        company: 'Acme Corp',
        department: 'Engineering',
        notes: 'VIP customer'
      };

      const result = (processor as any).validateContactData(dataWithAdditionalFields, headerMapping);

      expect(result.isValid).toBe(true);
      expect(result.contact.additional_fields).toEqual({
        company: 'Acme Corp',
        department: 'Engineering',
        notes: 'VIP customer'
      });
    });

    it('should normalize email to lowercase', () => {
      const dataWithUppercaseEmail: ContactRowData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890'
      };

      const result = (processor as any).validateContactData(dataWithUppercaseEmail, headerMapping);

      expect(result.isValid).toBe(true);
      expect(result.contact.email).toBe('<EMAIL>');
    });

    it('should normalize country code to uppercase', () => {
      const dataWithLowercaseCountryCode: ContactRowData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        country_code: 'us'
      };

      const result = (processor as any).validateContactData(dataWithLowercaseCountryCode, headerMapping);

      expect(result.isValid).toBe(true);
      expect(result.contact.country_code).toBe('US');
    });
  });

  describe('parseContactStatus', () => {
    it('should parse valid status values', () => {
      expect((processor as any).parseContactStatus('active')).toBe(ContactStatus.ACTIVE);
      expect((processor as any).parseContactStatus('ACTIVE')).toBe(ContactStatus.ACTIVE);
      expect((processor as any).parseContactStatus('archived')).toBe(ContactStatus.ARCHIVED);
      expect((processor as any).parseContactStatus('ARCHIVED')).toBe(ContactStatus.ARCHIVED);
      expect((processor as any).parseContactStatus('deleted')).toBe(ContactStatus.DELETED);
      expect((processor as any).parseContactStatus('DELETED')).toBe(ContactStatus.DELETED);
    });

    it('should return undefined for invalid status values', () => {
      expect((processor as any).parseContactStatus('invalid')).toBeUndefined();
      expect((processor as any).parseContactStatus('')).toBeUndefined();
      expect((processor as any).parseContactStatus(null)).toBeUndefined();
      expect((processor as any).parseContactStatus(undefined)).toBeUndefined();
      expect((processor as any).parseContactStatus(123)).toBeUndefined();
    });

    it('should handle whitespace in status values', () => {
      expect((processor as any).parseContactStatus('  active  ')).toBe(ContactStatus.ACTIVE);
      expect((processor as any).parseContactStatus('\tarchived\n')).toBe(ContactStatus.ARCHIVED);
    });
  });

  describe('generateErrorReport', () => {
    const mockErrors = [
      {
        rowNumber: 1,
        contactData: { name: '', email: 'invalid', phone: '123' },
        error: {
          type: 'VALIDATION' as const,
          message: 'Validation failed',
          errorCode: JOB_ERROR_CODES.BULK_INSERT_VALIDATION_ERROR,
          details: [
            { field: 'name', message: 'Name is required' },
            { field: 'email', message: 'Email is invalid' }
          ]
        },
        timestamp: new Date()
      },
      {
        rowNumber: 2,
        contactData: { name: 'John Doe', email: '<EMAIL>', phone: '+1234567890' },
        error: {
          type: 'DUPLICATE' as const,
          message: 'Contact already exists',
          errorCode: 'CONTACT_DUPLICATE_001'
        },
        timestamp: new Date()
      }
    ];

    it('should generate error report CSV file', async () => {
      // Mock directory doesn't exist to trigger mkdirSync
      mockFs.existsSync.mockReturnValueOnce(false); // For directory check
      
      const reportPath = await (processor as any).generateErrorReport(mockErrors, 'test-job-id');

      expect(reportPath).toContain('error-report-test-job-id.csv');
      expect(mockFs.mkdirSync).toHaveBeenCalled();
      expect(mockFs.writeFileSync).toHaveBeenCalled();

      const csvContent = (mockFs.writeFileSync as jest.Mock).mock.calls[0][1];
      expect(csvContent).toContain('Row Number,Error Type,Error Message');
      expect(csvContent).toContain('1,"VALIDATION"');
      expect(csvContent).toContain('2,"DUPLICATE"');
    });

    it('should handle errors with multiple field details', async () => {
      await (processor as any).generateErrorReport(mockErrors, 'test-job-id');

      const csvContent = (mockFs.writeFileSync as jest.Mock).mock.calls[0][1];
      
      // Should have separate lines for each field error
      const lines = csvContent.split('\n');
      const validationLines = lines.filter((line: string) => line.includes('"VALIDATION"'));
      expect(validationLines).toHaveLength(2); // One for each field error
    });

    it('should handle errors without field details', async () => {
      const errorWithoutDetails = [{
        rowNumber: 1,
        contactData: { name: 'John' },
        error: {
          type: 'SYSTEM' as const,
          message: 'System error',
          errorCode: 'SYS_001'
        },
        timestamp: new Date()
      }];

      await (processor as any).generateErrorReport(errorWithoutDetails, 'test-job-id');

      const csvContent = (mockFs.writeFileSync as jest.Mock).mock.calls[0][1];
      expect(csvContent).toContain('1,"SYSTEM","System error","SYS_001","","",');
    });

    it('should throw error if file writing fails', async () => {
      mockFs.writeFileSync.mockImplementation(() => {
        throw new Error('Write failed');
      });

      await expect((processor as any).generateErrorReport(mockErrors, 'test-job-id'))
        .rejects.toThrow(InternalError);
    });
  });

  describe('validateJobData', () => {
    it('should validate valid job data', () => {
      expect(() => (processor as any).validateJobData(mockJob)).not.toThrow();
    });

    it('should throw error for missing file path', () => {
      const invalidJob = {
        ...mockJob,
        data: { ...mockJob.data, filePath: '' }
      };

      expect(() => (processor as any).validateJobData(invalidJob)).toThrow(ValidationError);
      expect(() => (processor as any).validateJobData(invalidJob)).toThrow('File path is missing');
    });

    it('should throw error for missing file name', () => {
      const invalidJob = {
        ...mockJob,
        data: { ...mockJob.data, fileName: '' }
      };

      expect(() => (processor as any).validateJobData(invalidJob)).toThrow(ValidationError);
      expect(() => (processor as any).validateJobData(invalidJob)).toThrow('File name is missing');
    });

    it('should throw error for invalid file size', () => {
      const invalidJob = {
        ...mockJob,
        data: { ...mockJob.data, fileSize: 0 }
      };

      expect(() => (processor as any).validateJobData(invalidJob)).toThrow(ValidationError);
      expect(() => (processor as any).validateJobData(invalidJob)).toThrow('File size is invalid');
    });

    it('should throw error for missing user email', () => {
      const invalidJob = {
        ...mockJob,
        data: { ...mockJob.data, userEmail: '' }
      };

      expect(() => (processor as any).validateJobData(invalidJob)).toThrow(ValidationError);
      expect(() => (processor as any).validateJobData(invalidJob)).toThrow('User email is missing');
    });
  });

  describe('Job Lifecycle Hooks', () => {
    it('should handle job completion', async () => {
      const result: ContactBulkInsertResult = {
        totalRows: 10,
        successfulRows: 8,
        failedRows: 2,
        processingTime: 5000,
        errors: [],
        startedAt: new Date(),
        completedAt: new Date()
      };

      await (processor as any).onJobCompleted(mockJob, result);

      // Should not throw error - just logs completion
      expect(true).toBe(true);
    });

    it('should handle job failure', async () => {
      const error = new Error('Processing failed');

      await (processor as any).onJobFailed(mockJob, error);

      // Should not throw error - just logs failure
      expect(true).toBe(true);
    });
  });

  describe('Memory Management', () => {
    it('should update progress periodically during processing', async () => {
      const mockRows = Array(100).fill(null).map((_, i) => ({
        name: `User ${i}`,
        email: `user${i}@example.com`,
        phone: `+123456789${i}`
      }));

      const mockStream: any = {
        pipe: jest.fn().mockReturnThis(),
        on: jest.fn((event: string, callback: (data?: any) => void) => {
          if (event === 'data') {
            // Simulate processing many rows
            mockRows.forEach(row => callback(row));
          } else if (event === 'end') {
            callback();
          }
          return mockStream;
        })
      };

      (mockFs.createReadStream as jest.Mock).mockReturnValue(mockStream);
      
      // Mock csv-parser
      const csvParser = require('csv-parser');
      csvParser.mockReturnValue(mockStream);

      jest.spyOn(processor as any, 'validateAndProcessContact')
        .mockResolvedValue({ isValid: true, errors: [] });

      const updateProgressSpy = jest.spyOn(processor as any, 'updateJobProgressWithCalculation')
        .mockResolvedValue(undefined);

      await (processor as any).processCSVFile(
        '/tmp/test.csv', 
        'test-org-id', 
        'test-user-id', 
        'test-job-id'
      );

      // Should have called progress update at least once (final update)
      expect(updateProgressSpy).toHaveBeenCalled();
    });
  });
});