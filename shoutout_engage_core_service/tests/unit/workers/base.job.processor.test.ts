import { BaseJobProcessor } from '../../../workers/processors/base.job.processor';
import { CommonJobDAO } from '../../../lib/db/dao/CommonJobDAO';
import { JOB_STATUS, JOB_TYPE } from '../../../lib/constant/job.constants';
import { QUEUE_NAMES } from '../../../lib/constant/queue.constants';
import { BaseJobData} from '../../../types/job.types';
import { ValidationError, InternalError } from '../../../lib/errors/error.type';
import Queue, { Job } from 'bull';
import { QueueManager } from '../../../queues/queue.manager';

// Mock dependencies
jest.mock('../../../lib/db/dao/CommonJobDAO');
jest.mock('../../../lib/db/connectors/RedisConnector');
jest.mock('bull');
jest.mock('../../../queues/queue.manager');
jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));

// Mock Redis connector
const mockRedisConnector = {
  getBullConfig: jest.fn().mockReturnValue({
    host: 'localhost',
    port: 6379,
    db: 0
  })
};

jest.doMock('../../../lib/db/connectors/RedisConnector', () => ({
  default: mockRedisConnector
}));

// Test implementation of BaseJobProcessor
class TestJobProcessor extends BaseJobProcessor<BaseJobData> {
  public readonly queueName = QUEUE_NAMES.CONTACT_BULK_INSERT;
  public readonly jobType = JOB_TYPE.CONTACT_BULK_INSERT;

  public async processJob(job: Job<BaseJobData>): Promise<any> {
    return { success: true, processedItems: 10 };
  }

  // Expose protected methods for testing
  public async testUpdateJobStatus(jobId: string, status: any): Promise<void> {
    return this.updateJobStatus(jobId, status);
  }

  public testValidateJobData(job: Job<BaseJobData>): void {
    return this.validateJobData(job);
  }

  public testCalculateEstimatedCompletion(totalItems: number, processedItems: number, startTime: Date): Date | undefined {
    return this.calculateEstimatedCompletion(totalItems, processedItems, startTime);
  }
}

describe('BaseJobProcessor', () => {
  let processor: TestJobProcessor;
  let mockQueue: jest.Mocked<Queue.Queue>;
  let mockJob: jest.Mocked<Job<BaseJobData>>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Bull Queue
    mockQueue = {
      name: QUEUE_NAMES.CONTACT_BULK_INSERT,
      process: jest.fn(),
      close: jest.fn(),
      on: jest.fn(),
      add: jest.fn(),
      getJob: jest.fn(),
      isPaused: jest.fn().mockResolvedValue(false),
      pause: jest.fn().mockResolvedValue(undefined)
    } as any;

    (Queue as jest.MockedClass<typeof Queue>).mockImplementation(() => mockQueue);

    // Mock QueueManager
    const mockQueueManager = {
      getQueue: jest.fn().mockReturnValue(mockQueue),
      getInstance: jest.fn()
    };
    
    (QueueManager.getInstance as jest.Mock) = jest.fn().mockReturnValue(mockQueueManager);

    // Mock job data
    mockJob = {
      id: 'test-job-id',
      data: {
        jobId: 'test-job-uuid',
        orgId: 'test-org-id',
        createdBy: 'test-user-id',
        userEmail: '<EMAIL>'
      }
    } as any;

    // Mock CommonJobDAO methods
    (CommonJobDAO.updateJobStatus as jest.Mock).mockResolvedValue({});
    (CommonJobDAO.updateJobProgress as jest.Mock).mockResolvedValue({});
    (CommonJobDAO.addJobError as jest.Mock).mockResolvedValue({});
    (CommonJobDAO.setJobResult as jest.Mock).mockResolvedValue({});

    processor = new TestJobProcessor(QUEUE_NAMES.CONTACT_BULK_INSERT);
  });

  describe('Core Functionality', () => {
    it('should initialize queue properly', () => {
      expect(QueueManager.getInstance).toHaveBeenCalled();
      // Note: We can't directly test the getQueue call since it happens during construction
      // The fact that the processor is created without throwing an error verifies the queue initialization
    });

    it('should set up basic event handlers', () => {
      expect(mockQueue.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockQueue.on).toHaveBeenCalledWith('completed', expect.any(Function));
      expect(mockQueue.on).toHaveBeenCalledWith('failed', expect.any(Function));
    });

    it('should start processing jobs', () => {
      processor.startProcess();
      expect(mockQueue.process).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should stop processing and pause queue', async () => {
      processor.startProcess();
      await processor.stopProcess();
      expect(mockQueue.pause).toHaveBeenCalledWith(true);
    });

    it('should return the queue instance', () => {
      const queue = processor.getQueue();
      expect(queue).toBe(mockQueue);
    });
  });

  describe('Job Status Management', () => {
    it('should update job status successfully', async () => {
      await processor.testUpdateJobStatus('test-job-uuid', JOB_STATUS.PROCESSING);
      expect(CommonJobDAO.updateJobStatus).toHaveBeenCalledWith('test-job-uuid', JOB_STATUS.PROCESSING);
    });

    it('should handle job status update failures', async () => {
      (CommonJobDAO.updateJobStatus as jest.Mock).mockRejectedValue(new Error('Database error'));

      await expect(processor.testUpdateJobStatus('test-job-uuid', JOB_STATUS.PROCESSING))
        .rejects.toThrow();
    });
  });

  describe('Job Validation', () => {
    it('should validate valid job data', () => {
      expect(() => processor.testValidateJobData(mockJob)).not.toThrow();
    });

    it('should throw ValidationError for missing job data', () => {
      const invalidJob = { ...mockJob, data: null } as any;
      expect(() => processor.testValidateJobData(invalidJob)).toThrow(ValidationError);
    });

    it('should throw ValidationError for missing required fields', () => {
      const invalidJob = {
        ...mockJob,
        data: { ...mockJob.data, jobId: undefined }
      } as any;
      expect(() => processor.testValidateJobData(invalidJob)).toThrow(ValidationError);
    });
  });

  describe('Progress Calculation', () => {
    it('should calculate estimated completion time', () => {
      const startTime = new Date(Date.now() - 10000); // 10 seconds ago
      const result = processor.testCalculateEstimatedCompletion(100, 25, startTime);

      expect(result).toBeInstanceOf(Date);
      expect(result!.getTime()).toBeGreaterThan(Date.now());
    });

    it('should return undefined when no progress made', () => {
      const startTime = new Date();
      const result = processor.testCalculateEstimatedCompletion(100, 0, startTime);
      expect(result).toBeUndefined();
    });

    it('should handle zero total items', () => {
      const startTime = new Date();
      const result = processor.testCalculateEstimatedCompletion(0, 0, startTime);
      expect(result).toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle queue setup failures', () => {
      mockQueue.process.mockImplementation(() => {
        throw new Error('Queue setup failed');
      });

      expect(() => processor.startProcess()).toThrow(InternalError);
    });

    it('should handle queue pause failures', async () => {
      processor.startProcess();
      mockQueue.pause.mockRejectedValue(new Error('Pause failed'));

      await expect(processor.stopProcess()).rejects.toThrow(InternalError);
    });

    it('should prevent multiple starts', () => {
      processor.startProcess();
      processor.startProcess(); // Second call

      expect(mockQueue.process).toHaveBeenCalledTimes(1);
    });

    it('should handle stop when not running', async () => {
      await processor.stopProcess();
      expect(mockQueue.close).not.toHaveBeenCalled();
    });
  });

  describe('Job Execution Flow', () => {
    let processJobSpy: jest.SpyInstance;

    beforeEach(() => {
      processJobSpy = jest.spyOn(processor, 'processJob');
    });

    it('should execute job successfully', async () => {
      const expectedResult = { success: true, processedItems: 10 };
      processJobSpy.mockResolvedValue(expectedResult);

      processor.startProcess();

      const processFunction = mockQueue.process.mock.calls[0][0] as unknown as (job: any) => Promise<any>;
      const result = await processFunction(mockJob);

      expect(result).toEqual(expectedResult);
      expect(CommonJobDAO.updateJobStatus).toHaveBeenCalledWith('test-job-uuid', JOB_STATUS.PROCESSING);
      expect(CommonJobDAO.updateJobStatus).toHaveBeenCalledWith('test-job-uuid', JOB_STATUS.COMPLETED);
    });

    it('should handle job execution failure', async () => {
      const error = new Error('Processing failed');
      processJobSpy.mockRejectedValue(error);

      processor.startProcess();

      const processFunction = mockQueue.process.mock.calls[0][0] as unknown as (job: any) => Promise<any>;

      await expect(processFunction(mockJob)).rejects.toThrow(error);

      expect(CommonJobDAO.updateJobStatus).toHaveBeenCalledWith('test-job-uuid', JOB_STATUS.PROCESSING);
      expect(CommonJobDAO.updateJobStatus).toHaveBeenCalledWith('test-job-uuid', JOB_STATUS.FAILED);
      expect(CommonJobDAO.addJobError).toHaveBeenCalledWith('test-job-uuid', expect.objectContaining({
        type: 'SYSTEM',
        message: 'Processing failed',
        timestamp: expect.any(Date)
      }));
    });
  });
});