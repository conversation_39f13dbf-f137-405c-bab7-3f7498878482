import QueueManager from '../../../queues/queue.manager';
import { QUEUE_NAMES, QUEUE_MONITORING } from '../../../lib/constant/queue.constants';
import Queue, { Job, JobOptions } from 'bull';

// Mock dependencies
jest.mock('bull');
jest.mock('../../../lib/db/connectors/RedisConnector');

// Mock Redis connector
const mockRedisConnector = {
  getBullConfig: jest.fn().mockReturnValue({
    host: 'localhost',
    port: 6379,
    db: 0
  })
};

jest.doMock('../../../lib/db/connectors/RedisConnector', () => ({
  default: mockRedisConnector
}));

describe('QueueManager', () => {
  let queueManager: QueueManager;
  let mockQueue: jest.Mocked<Queue.Queue>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Reset singleton instance
    (QueueManager as any).instance = undefined;
    queueManager = QueueManager.getInstance();

    // Mock Bull Queue
    mockQueue = {
      name: QUEUE_NAMES.CONTACT_BULK_INSERT,
      add: jest.fn(),
      getJob: jest.fn(),
      close: jest.fn(),
      on: jest.fn(),
      pause: jest.fn(),
      resume: jest.fn(),
      isPaused: jest.fn().mockResolvedValue(false),
      getWaiting: jest.fn().mockResolvedValue([]),
      getActive: jest.fn().mockResolvedValue([]),
      getCompleted: jest.fn().mockResolvedValue([]),
      getFailed: jest.fn().mockResolvedValue([]),
      getDelayed: jest.fn().mockResolvedValue([]),
      clean: jest.fn()
    } as any;

    (Queue as jest.MockedClass<typeof Queue>).mockImplementation(() => mockQueue);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = QueueManager.getInstance();
      const instance2 = QueueManager.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe('initializeQueues', () => {
    it('should initialize all queues successfully', async () => {
      await queueManager.initializeQueues();

      // Should create queues for each queue name
      expect(Queue).toHaveBeenCalledTimes(Object.keys(QUEUE_NAMES).length);
      
      // Should set up event handlers for each queue
      expect(mockQueue.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockQueue.on).toHaveBeenCalledWith('waiting', expect.any(Function));
      expect(mockQueue.on).toHaveBeenCalledWith('active', expect.any(Function));
      expect(mockQueue.on).toHaveBeenCalledWith('completed', expect.any(Function));
      expect(mockQueue.on).toHaveBeenCalledWith('failed', expect.any(Function));
    });

    it('should not initialize if already initialized', async () => {
      await queueManager.initializeQueues();
      const firstCallCount = (Queue as jest.MockedClass<typeof Queue>).mock.calls.length;

      await queueManager.initializeQueues();
      const secondCallCount = (Queue as jest.MockedClass<typeof Queue>).mock.calls.length;

      expect(secondCallCount).toBe(firstCallCount);
    });

    it('should throw error if initialization fails', async () => {
      (Queue as jest.MockedClass<typeof Queue>).mockImplementation(() => {
        throw new Error('Redis connection failed');
      });

      await expect(queueManager.initializeQueues()).rejects.toThrow('Redis connection failed');
    });

    it('should start monitoring after initialization', async () => {
      await queueManager.initializeQueues();

      // Fast forward to trigger monitoring intervals
      jest.advanceTimersByTime(QUEUE_MONITORING.HEALTH_CHECK_INTERVAL);
      jest.advanceTimersByTime(QUEUE_MONITORING.STATS_UPDATE_INTERVAL);

      // Monitoring should be active (no specific assertions needed as it's internal)
      expect(true).toBe(true);
    });
  });

  describe('getQueue', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should return existing queue', () => {
      const queue = queueManager.getQueue(QUEUE_NAMES.CONTACT_BULK_INSERT);
      expect(queue).toBe(mockQueue);
    });

    it('should throw error for non-existent queue', () => {
      expect(() => queueManager.getQueue('non-existent-queue')).toThrow(
        "Queue 'non-existent-queue' not found"
      );
    });
  });

  describe('addJob', () => {
    const jobData = {
      jobId: 'test-job-id',
      orgId: 'test-org-id',
      createdBy: 'test-user-id'
    };

    const jobOptions: JobOptions = {
      priority: 10,
      delay: 1000
    };

    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should add job to queue successfully', async () => {
      const mockJob = { id: 'bull-job-id', data: jobData } as Job;
      mockQueue.add.mockResolvedValue(mockJob);

      const result = await queueManager.addJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobData, jobOptions);

      expect(result).toBe(mockJob);
      expect(mockQueue.add).toHaveBeenCalledWith(jobData, jobOptions);
    });

    it('should throw error if queue not found', async () => {
      await expect(queueManager.addJob('non-existent-queue', jobData)).rejects.toThrow(
        "Queue 'non-existent-queue' not found"
      );
    });

    it('should throw error if adding job fails', async () => {
      mockQueue.add.mockRejectedValue(new Error('Queue is full'));

      await expect(queueManager.addJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobData)).rejects.toThrow(
        'Queue is full'
      );
    });
  });

  describe('getJob', () => {
    const jobId = 'test-job-id';

    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should get job from queue successfully', async () => {
      const mockJob = { id: jobId, data: {} } as Job;
      mockQueue.getJob.mockResolvedValue(mockJob);

      const result = await queueManager.getJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId);

      expect(result).toBe(mockJob);
      expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);
    });

    it('should return null if job not found', async () => {
      mockQueue.getJob.mockResolvedValue(null);

      const result = await queueManager.getJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId);

      expect(result).toBeNull();
    });

    it('should throw error if queue not found', async () => {
      await expect(queueManager.getJob('non-existent-queue', jobId)).rejects.toThrow(
        "Queue 'non-existent-queue' not found"
      );
    });
  });

  describe('cancelJob', () => {
    const jobId = 'test-job-id';

    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should cancel job successfully', async () => {
      const mockJob = {
        id: jobId,
        remove: jest.fn().mockResolvedValue(undefined)
      } as any;
      mockQueue.getJob.mockResolvedValue(mockJob);

      await queueManager.cancelJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId);

      expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);
      expect(mockJob.remove).toHaveBeenCalled();
    });

    it('should handle job not found gracefully', async () => {
      mockQueue.getJob.mockResolvedValue(null);

      await expect(queueManager.cancelJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId)).resolves.toBeUndefined();
    });

    it('should throw error if cancellation fails', async () => {
      const mockJob = {
        id: jobId,
        remove: jest.fn().mockRejectedValue(new Error('Cannot remove job'))
      } as any;
      mockQueue.getJob.mockResolvedValue(mockJob);

      await expect(queueManager.cancelJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId)).rejects.toThrow(
        'Cannot remove job'
      );
    });
  });

  describe('retryJob', () => {
    const jobId = 'test-job-id';

    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should retry job successfully', async () => {
      const mockJob = {
        id: jobId,
        retry: jest.fn().mockResolvedValue(undefined)
      } as any;
      mockQueue.getJob.mockResolvedValue(mockJob);

      await queueManager.retryJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId);

      expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);
      expect(mockJob.retry).toHaveBeenCalled();
    });

    it('should handle job not found gracefully', async () => {
      mockQueue.getJob.mockResolvedValue(null);

      await expect(queueManager.retryJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId)).resolves.toBeUndefined();
    });

    it('should throw error if retry fails', async () => {
      const mockJob = {
        id: jobId,
        retry: jest.fn().mockRejectedValue(new Error('Cannot retry job'))
      } as any;
      mockQueue.getJob.mockResolvedValue(mockJob);

      await expect(queueManager.retryJob(QUEUE_NAMES.CONTACT_BULK_INSERT, jobId)).rejects.toThrow(
        'Cannot retry job'
      );
    });
  });

  describe('getQueueStats', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should return queue statistics', async () => {
      const mockWaiting = [{ id: '1' }, { id: '2' }];
      const mockActive = [{ id: '3' }];
      const mockCompleted = [{ id: '4' }, { id: '5' }, { id: '6' }];
      const mockFailed = [{ id: '7' }];
      const mockDelayed: Array<{ id: string }> = [];

      mockQueue.getWaiting.mockResolvedValue(mockWaiting as any);
      mockQueue.getActive.mockResolvedValue(mockActive as any);
      mockQueue.getCompleted.mockResolvedValue(mockCompleted as any);
      mockQueue.getFailed.mockResolvedValue(mockFailed as any);
      mockQueue.getDelayed.mockResolvedValue(mockDelayed as any);
      mockQueue.isPaused.mockResolvedValue(false);

      const stats = await queueManager.getQueueStats(QUEUE_NAMES.CONTACT_BULK_INSERT);

      expect(stats).toEqual({
        waiting: 2,
        active: 1,
        completed: 3,
        failed: 1,
        delayed: 0,
        paused: false
      });
    });

    it('should throw error if getting stats fails', async () => {
      mockQueue.getWaiting.mockRejectedValue(new Error('Redis connection lost'));

      await expect(queueManager.getQueueStats(QUEUE_NAMES.CONTACT_BULK_INSERT)).rejects.toThrow(
        'Redis connection lost'
      );
    });
  });

  describe('getAllQueueStats', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should return stats for all queues', async () => {
      mockQueue.getWaiting.mockResolvedValue([]);
      mockQueue.getActive.mockResolvedValue([]);
      mockQueue.getCompleted.mockResolvedValue([]);
      mockQueue.getFailed.mockResolvedValue([]);
      mockQueue.getDelayed.mockResolvedValue([]);
      mockQueue.isPaused.mockResolvedValue(false);

      const allStats = await queueManager.getAllQueueStats();

      expect(allStats.size).toBe(Object.keys(QUEUE_NAMES).length);
      
      for (const queueName of Object.values(QUEUE_NAMES)) {
        expect(allStats.has(queueName)).toBe(true);
      }
    });

    it('should handle individual queue failures gracefully', async () => {
      mockQueue.getWaiting.mockRejectedValue(new Error('Queue error'));

      const allStats = await queueManager.getAllQueueStats();

      // Should still return stats for queues that don't fail
      expect(allStats.size).toBeGreaterThanOrEqual(0);
    });
  });

  describe('pauseQueue', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should pause queue successfully', async () => {
      await queueManager.pauseQueue(QUEUE_NAMES.CONTACT_BULK_INSERT);

      expect(mockQueue.pause).toHaveBeenCalled();
    });

    it('should throw error if pause fails', async () => {
      mockQueue.pause.mockRejectedValue(new Error('Cannot pause queue'));

      await expect(queueManager.pauseQueue(QUEUE_NAMES.CONTACT_BULK_INSERT)).rejects.toThrow(
        'Cannot pause queue'
      );
    });
  });

  describe('resumeQueue', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should resume queue successfully', async () => {
      await queueManager.resumeQueue(QUEUE_NAMES.CONTACT_BULK_INSERT);

      expect(mockQueue.resume).toHaveBeenCalled();
    });

    it('should throw error if resume fails', async () => {
      mockQueue.resume.mockRejectedValue(new Error('Cannot resume queue'));

      await expect(queueManager.resumeQueue(QUEUE_NAMES.CONTACT_BULK_INSERT)).rejects.toThrow(
        'Cannot resume queue'
      );
    });
  });

  describe('getQueueHealth', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should return health status for all queues', async () => {
      const mockCompletedJob = {
        id: '1',
        data: {},
        opts: {},
        attemptsMade: 0,
        queue: {},
        timestamp: Date.now(),
        processedOn: Date.now(),
        finishedOn: Date.now(),
        progress: 0,
        returnvalue: null,
        failedReason: null,
        stacktrace: null,
        name: 'test-job',
        delay: 0,
        repeatJobKey: null
      } as any;

      mockQueue.getWaiting.mockResolvedValue([]);
      mockQueue.getActive.mockResolvedValue([]);
      mockQueue.getCompleted.mockResolvedValue([mockCompletedJob]); // Has completed jobs = healthy
      mockQueue.getFailed.mockResolvedValue([]);
      mockQueue.getDelayed.mockResolvedValue([]);
      mockQueue.isPaused.mockResolvedValue(false);

      const health = await queueManager.getQueueHealth();

      expect(health).toHaveLength(Object.keys(QUEUE_NAMES).length);
      
      health.forEach(queueHealth => {
        expect(queueHealth).toHaveProperty('name');
        expect(queueHealth).toHaveProperty('isHealthy');
        expect(queueHealth).toHaveProperty('stats');
        expect(queueHealth.isHealthy).toBe(true);
      });
    });

    it('should mark queue as unhealthy if paused', async () => {
      mockQueue.getWaiting.mockResolvedValue([]);
      mockQueue.getActive.mockResolvedValue([]);
      mockQueue.getCompleted.mockResolvedValue([]);
      mockQueue.getFailed.mockResolvedValue([]);
      mockQueue.getDelayed.mockResolvedValue([]);
      mockQueue.isPaused.mockResolvedValue(true); // Paused = unhealthy

      const health = await queueManager.getQueueHealth();

      health.forEach(queueHealth => {
        expect(queueHealth.isHealthy).toBe(false);
      });
    });

    it('should mark queue as unhealthy if too many failed jobs', async () => {
      const manyFailedJobs = Array(QUEUE_MONITORING.MAX_FAILED_COUNT + 1).fill({ id: 'failed' });
      
      mockQueue.getWaiting.mockResolvedValue([]);
      mockQueue.getActive.mockResolvedValue([]);
      mockQueue.getCompleted.mockResolvedValue([]);
      mockQueue.getFailed.mockResolvedValue(manyFailedJobs as any);
      mockQueue.getDelayed.mockResolvedValue([]);
      mockQueue.isPaused.mockResolvedValue(false);

      const health = await queueManager.getQueueHealth();

      health.forEach(queueHealth => {
        expect(queueHealth.isHealthy).toBe(false);
      });
    });

    it('should handle queue errors gracefully', async () => {
      mockQueue.getWaiting.mockRejectedValue(new Error('Queue connection lost'));

      const health = await queueManager.getQueueHealth();

      health.forEach(queueHealth => {
        expect(queueHealth.isHealthy).toBe(false);
        expect(queueHealth.lastError).toBe('Queue connection lost');
        expect(queueHealth.lastErrorTime).toBeInstanceOf(Date);
      });
    });
  });

  describe('cleanupQueues', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should clean up old jobs from all queues', async () => {
      await queueManager.cleanupQueues();

      expect(mockQueue.clean).toHaveBeenCalledWith(24 * 60 * 60 * 1000, 'completed');
      expect(mockQueue.clean).toHaveBeenCalledWith(7 * 24 * 60 * 60 * 1000, 'failed');
    });

    it('should handle cleanup failures gracefully', async () => {
      mockQueue.clean.mockRejectedValue(new Error('Cleanup failed'));

      await expect(queueManager.cleanupQueues()).resolves.toBeUndefined();
    });
  });

  describe('closeQueues', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should close all queues successfully', async () => {
      await queueManager.closeQueues();

      expect(mockQueue.close).toHaveBeenCalledTimes(Object.keys(QUEUE_NAMES).length);
    });

    it('should clear intervals and reset state', async () => {
      await queueManager.closeQueues();

      // Should be able to initialize again after closing
      await queueManager.initializeQueues();
      expect(Queue).toHaveBeenCalled();
    });

    it('should throw error if closing fails', async () => {
      mockQueue.close.mockRejectedValue(new Error('Cannot close queue'));

      await expect(queueManager.closeQueues()).rejects.toThrow('Cannot close queue');
    });
  });

  describe('Monitoring', () => {
    beforeEach(async () => {
      await queueManager.initializeQueues();
    });

    it('should perform health checks at regular intervals', async () => {
      const mockCompletedJob = {
        id: '1',
        data: {},
        opts: {},
        attemptsMade: 0,
        queue: {},
        timestamp: Date.now(),
        processedOn: Date.now(),
        finishedOn: Date.now(),
        progress: 0,
        returnvalue: null,
        failedReason: null,
        stacktrace: null,
        name: 'test-job',
        delay: 0,
        repeatJobKey: null
      } as any;

      mockQueue.getWaiting.mockResolvedValue([]);
      mockQueue.getActive.mockResolvedValue([]);
      mockQueue.getCompleted.mockResolvedValue([mockCompletedJob]);
      mockQueue.getFailed.mockResolvedValue([]);
      mockQueue.getDelayed.mockResolvedValue([]);
      mockQueue.isPaused.mockResolvedValue(false);

      // Fast forward to trigger health check
      jest.advanceTimersByTime(QUEUE_MONITORING.HEALTH_CHECK_INTERVAL);

      // Health check should have been performed (no specific assertions as it's internal)
      expect(true).toBe(true);
    });

    it('should update stats at regular intervals', async () => {
      mockQueue.getWaiting.mockResolvedValue([]);
      mockQueue.getActive.mockResolvedValue([]);
      mockQueue.getCompleted.mockResolvedValue([]);
      mockQueue.getFailed.mockResolvedValue([]);
      mockQueue.getDelayed.mockResolvedValue([]);
      mockQueue.isPaused.mockResolvedValue(false);

      // Fast forward to trigger stats update
      jest.advanceTimersByTime(QUEUE_MONITORING.STATS_UPDATE_INTERVAL);

      // Stats update should have been performed (no specific assertions as it's internal)
      expect(true).toBe(true);
    });

    it('should perform cleanup at regular intervals', async () => {
      // Fast forward to trigger cleanup
      jest.advanceTimersByTime(QUEUE_MONITORING.CLEANUP_INTERVAL);

      expect(mockQueue.clean).toHaveBeenCalled();
    });
  });
});