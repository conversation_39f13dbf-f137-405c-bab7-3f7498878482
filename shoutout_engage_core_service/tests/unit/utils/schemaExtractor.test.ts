import { SchemaExtractor } from '../../../lib/utils/schemaExtractor';

describe('SchemaExtractor', () => {
  describe('getContactFields', () => {
    it('should return the contact schema fields', () => {
      const fields = SchemaExtractor.getContactFields();

      // Verify the returned fields include required contact properties
      expect(fields).toContainEqual(expect.objectContaining({
        name: 'name',
        type: 'string',
        required: true
      }));

      expect(fields).toContainEqual(expect.objectContaining({
        name: 'email',
        type: 'string',
        required: true
      }));

      expect(fields).toContainEqual(expect.objectContaining({
        name: 'phone',
        type: 'string',
        required: true
      }));

      // Check for optional fields
      expect(fields).toContainEqual(expect.objectContaining({
        name: 'country',
        type: 'string',
        required: false
      }));

      // Verify the correct number of fields is returned
      expect(fields.length).toBeGreaterThanOrEqual(8); // At least 8 fields defined in the schema
    });

    it('should include description for each field', () => {
      const fields = SchemaExtractor.getContactFields();

      // Each field should have a description
      fields.forEach(field => {
        expect(field).toHaveProperty('description');
        expect(typeof field.description).toBe('string');
        expect(field.description?.length).toBeGreaterThan(0);
      });
    });
  });
});
