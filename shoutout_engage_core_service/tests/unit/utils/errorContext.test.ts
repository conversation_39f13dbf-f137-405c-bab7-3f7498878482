import { Request } from 'express';
import { ErrorContext, ErrorContextData } from '../../../lib/utils/errorContext';

describe('ErrorContext', () => {
  let mockRequest: Partial<Request>;

  beforeEach(() => {
    mockRequest = {
      originalUrl: '/api/contacts',
      url: '/api/contacts',
      method: 'POST',
      get: jest.fn(),
      header: jest.fn(),
      connection: { remoteAddress: '***********' } as any,
      socket: { remoteAddress: '***********' } as any
    };

    // Mock the get method for headers
    (mockRequest.get as jest.Mock).mockImplementation((header: string) => {
      switch (header) {
        case 'User-Agent':
          return 'Mozilla/5.0 (Test Browser)';
        case 'X-Forwarded-For':
          return null;
        case 'X-Real-IP':
          return null;
        default:
          return null;
      }
    });

    // Mock the header method as alias for get
    (mockRequest.header as jest.Mock).mockImplementation((header: string) => {
      return (mockRequest.get as jest.Mock)(header);
    });
  });

  describe('constructor', () => {
    it('should create ErrorContext with basic request information', () => {
      const context = new ErrorContext(mockRequest as Request);

      expect(context.requestId).toBeDefined();
      expect(context.endpoint).toBe('/api/contacts');
      expect(context.method).toBe('POST');
      expect(context.userAgent).toBe('Mozilla/5.0 (Test Browser)');
      expect(context.ip).toBe('***********');
      expect(context.timestamp).toBeInstanceOf(Date);
    });

    it('should use existing requestId if present', () => {
      const existingRequestId = 'existing-request-id';
      (mockRequest as any).requestId = existingRequestId;

      const context = new ErrorContext(mockRequest as Request);

      expect(context.requestId).toBe(existingRequestId);
    });

    it('should extract user information from authenticated request', () => {
      (mockRequest as any).userId = 'user-123';
      (mockRequest as any).organizationId = 'org-456';

      const context = new ErrorContext(mockRequest as Request);

      expect(context.userId).toBe('user-123');
      expect(context.organizationId).toBe('org-456');
    });

    it('should include additional context when provided', () => {
      const additionalContext = {
        jobId: 'job-789',
        fileName: 'contacts.csv',
        rowNumber: 42
      };

      const context = new ErrorContext(mockRequest as Request, additionalContext);

      expect(context.jobId).toBe('job-789');
      expect(context.fileName).toBe('contacts.csv');
      expect(context.rowNumber).toBe(42);
    });

    it('should handle missing originalUrl by using url', () => {
      delete mockRequest.originalUrl;
      mockRequest.url = '/fallback-url';

      const context = new ErrorContext(mockRequest as Request);

      expect(context.endpoint).toBe('/fallback-url');
    });
  });

  describe('extractClientIP', () => {
    it('should extract IP from X-Forwarded-For header', () => {
      (mockRequest.get as jest.Mock).mockImplementation((header: string) => {
        if (header === 'X-Forwarded-For') return '***********, ************';
        return null;
      });

      const context = new ErrorContext(mockRequest as Request);

      expect(context.ip).toBe('***********');
    });

    it('should extract IP from X-Real-IP header when X-Forwarded-For is not available', () => {
      (mockRequest.get as jest.Mock).mockImplementation((header: string) => {
        if (header === 'X-Real-IP') return '***********';
        return null;
      });

      const context = new ErrorContext(mockRequest as Request);

      expect(context.ip).toBe('***********');
    });

    it('should fall back to connection.remoteAddress', () => {
      (mockRequest.get as jest.Mock).mockReturnValue(null);
      mockRequest.connection = { remoteAddress: '***********' } as any;

      const context = new ErrorContext(mockRequest as Request);

      expect(context.ip).toBe('***********');
    });

    it('should fall back to socket.remoteAddress when connection is not available', () => {
      (mockRequest.get as jest.Mock).mockReturnValue(null);
      delete mockRequest.connection;
      mockRequest.socket = { remoteAddress: '***********' } as any;

      const context = new ErrorContext(mockRequest as Request);

      expect(context.ip).toBe('***********');
    });

    it('should return "unknown" when no IP can be determined', () => {
      (mockRequest.get as jest.Mock).mockReturnValue(null);
      delete mockRequest.connection;
      delete mockRequest.socket;

      const context = new ErrorContext(mockRequest as Request);

      expect(context.ip).toBe('unknown');
    });
  });

  describe('toLogObject', () => {
    it('should convert context to log object with all required fields', () => {
      const context = new ErrorContext(mockRequest as Request);
      const logObject = context.toLogObject();

      expect(logObject).toHaveProperty('requestId');
      expect(logObject).toHaveProperty('endpoint', '/api/contacts');
      expect(logObject).toHaveProperty('method', 'POST');
      expect(logObject).toHaveProperty('timestamp');
      expect(logObject).toHaveProperty('ip', '***********');
      expect(logObject).toHaveProperty('userAgent', 'Mozilla/5.0 (Test Browser)');
    });

    it('should include optional fields when present', () => {
      (mockRequest as any).userId = 'user-123';
      (mockRequest as any).organizationId = 'org-456';
      
      const additionalContext = {
        jobId: 'job-789',
        fileName: 'contacts.csv',
        rowNumber: 42
      };

      const context = new ErrorContext(mockRequest as Request, additionalContext);
      const logObject = context.toLogObject();

      expect(logObject).toHaveProperty('userId', 'user-123');
      expect(logObject).toHaveProperty('organizationId', 'org-456');
      expect(logObject).toHaveProperty('jobId', 'job-789');
      expect(logObject).toHaveProperty('fileName', 'contacts.csv');
      expect(logObject).toHaveProperty('rowNumber', 42);
    });

    it('should not include undefined optional fields', () => {
      const context = new ErrorContext(mockRequest as Request);
      const logObject = context.toLogObject();

      expect(logObject).not.toHaveProperty('userId');
      expect(logObject).not.toHaveProperty('organizationId');
      expect(logObject).not.toHaveProperty('jobId');
      expect(logObject).not.toHaveProperty('fileName');
      expect(logObject).not.toHaveProperty('rowNumber');
    });

    it('should include rowNumber when it is 0', () => {
      const additionalContext = { rowNumber: 0 };
      const context = new ErrorContext(mockRequest as Request, additionalContext);
      const logObject = context.toLogObject();

      expect(logObject).toHaveProperty('rowNumber', 0);
    });
  });

  describe('withAdditionalContext', () => {
    it('should create new context with additional data', () => {
      const originalContext = new ErrorContext(mockRequest as Request);
      const additionalContext = {
        jobId: 'job-789',
        fileName: 'contacts.csv'
      };

      const newContext = originalContext.withAdditionalContext(additionalContext);

      expect(newContext).not.toBe(originalContext);
      expect(newContext.jobId).toBe('job-789');
      expect(newContext.fileName).toBe('contacts.csv');
      expect(newContext.requestId).toBe(originalContext.requestId);
      expect(newContext.endpoint).toBe(originalContext.endpoint);
    });

    it('should preserve existing additional context when not overridden', () => {
      const originalAdditionalContext = {
        jobId: 'original-job',
        fileName: 'original.csv',
        rowNumber: 10
      };
      
      const originalContext = new ErrorContext(mockRequest as Request, originalAdditionalContext);
      
      const newAdditionalContext = {
        fileName: 'new.csv'
      };

      const newContext = originalContext.withAdditionalContext(newAdditionalContext);

      expect(newContext.jobId).toBe('original-job'); // Preserved
      expect(newContext.fileName).toBe('new.csv'); // Overridden
      expect(newContext.rowNumber).toBe(10); // Preserved
    });

    it('should handle undefined rowNumber correctly', () => {
      const originalContext = new ErrorContext(mockRequest as Request);
      const additionalContext = { rowNumber: 0 };

      const newContext = originalContext.withAdditionalContext(additionalContext);

      expect(newContext.rowNumber).toBe(0);
    });
  });

  describe('fromObject', () => {
    it('should create ErrorContext from plain object', () => {
      const contextData: ErrorContextData = {
        requestId: 'test-request-id',
        userId: 'user-123',
        organizationId: 'org-456',
        endpoint: '/api/test',
        method: 'GET',
        userAgent: 'Test Agent',
        ip: '***********00',
        timestamp: new Date('2023-01-01T00:00:00Z'),
        jobId: 'job-789',
        fileName: 'test.csv',
        rowNumber: 5
      };

      const context = ErrorContext.fromObject(contextData);

      expect(context.requestId).toBe('test-request-id');
      expect(context.userId).toBe('user-123');
      expect(context.organizationId).toBe('org-456');
      expect(context.endpoint).toBe('/api/test');
      expect(context.method).toBe('GET');
      expect(context.userAgent).toBe('Test Agent');
      expect(context.ip).toBe('***********00');
      expect(context.jobId).toBe('job-789');
      expect(context.fileName).toBe('test.csv');
      expect(context.rowNumber).toBe(5);
    });

    it('should handle minimal context data', () => {
      const contextData: ErrorContextData = {
        requestId: 'minimal-request-id',
        endpoint: '/api/minimal',
        method: 'POST',
        timestamp: new Date()
      };

      const context = ErrorContext.fromObject(contextData);

      expect(context.requestId).toBe('minimal-request-id');
      expect(context.endpoint).toBe('/api/minimal');
      expect(context.method).toBe('POST');
      expect(context.userId).toBeUndefined();
      expect(context.organizationId).toBeUndefined();
      expect(context.jobId).toBeUndefined();
    });
  });

  describe('timestamp handling', () => {
    it('should create timestamp close to current time', () => {
      const beforeCreation = new Date();
      const context = new ErrorContext(mockRequest as Request);
      const afterCreation = new Date();

      expect(context.timestamp.getTime()).toBeGreaterThanOrEqual(beforeCreation.getTime());
      expect(context.timestamp.getTime()).toBeLessThanOrEqual(afterCreation.getTime());
    });

    it('should format timestamp as ISO string in log object', () => {
      const context = new ErrorContext(mockRequest as Request);
      const logObject = context.toLogObject();

      expect(typeof logObject.timestamp).toBe('string');
      expect(new Date(logObject.timestamp)).toBeInstanceOf(Date);
    });
  });
});