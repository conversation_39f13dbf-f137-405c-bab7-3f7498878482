import { <PERSON>rrorLogger, BulkInsertContext, JobContext } from '../../../lib/utils/errorLogger';
import { AppError } from '../../../lib/errors/app.error';
import { ErrorContext } from '../../../lib/utils/errorContext';

// Mock logger
jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    info: jest.fn()
  })
}));

describe('ErrorLogger', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('logError', () => {
    it('should log operational errors without throwing', () => {
      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);

      expect(() => {
        ErrorLogger.logError(error, context);
      }).not.toThrow();
    });

    it('should log non-operational errors without throwing', () => {
      const error = new AppError('System error', 500, 'SYS_001', false);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'POST',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);

      expect(() => {
        ErrorLogger.logError(error, context);
      }).not.toThrow();
    });

    it('should handle non-AppError instances', () => {
      const error = new Error('Generic error');
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);

      expect(() => {
        ErrorLogger.logError(error, context);
      }).not.toThrow();
    });

    it('should handle string errors', () => {
      const error = 'String error message';
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);

      expect(() => {
        ErrorLogger.logError(error, context);
      }).not.toThrow();
    });

    it('should handle metadata without throwing', () => {
      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);
      const metadata = {
        userId: 'user123',
        email: '<EMAIL>',
        password: 'secret123'
      };

      expect(() => {
        ErrorLogger.logError(error, context, metadata);
      }).not.toThrow();
    });
  });

  describe('logBulkInsertError', () => {
    it('should log bulk insert errors with sanitized contact data', () => {
      const error = new AppError('Bulk insert failed', 400, 'BULK_INSERT_001', true);
      const context: BulkInsertContext = {
        requestId: 'req123',
        userId: 'user123',
        organizationId: 'org123',
        endpoint: '/bulk-insert',
        method: 'POST',
        timestamp: new Date(),
        jobId: 'job123',
        fileName: 'contacts.csv',
        rowNumber: 5,
        contactData: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '1234567890',
          password: 'secret123'
        },
        operation: 'contact_validation'
      };

      expect(() => {
        ErrorLogger.logBulkInsertError(error, context);
      }).not.toThrow();
    });

    it('should handle bulk insert errors without contact data', () => {
      const error = new AppError('File validation failed', 400, 'FILE_001', true);
      const context: BulkInsertContext = {
        requestId: 'req123',
        userId: 'user123',
        organizationId: 'org123',
        endpoint: '/bulk-insert',
        method: 'POST',
        timestamp: new Date(),
        jobId: 'job123',
        fileName: 'contacts.csv',
        operation: 'file_validation'
      };

      expect(() => {
        ErrorLogger.logBulkInsertError(error, context);
      }).not.toThrow();
    });
  });

  describe('logJobError', () => {
    it('should log job errors with progress information', () => {
      const error = new AppError('Job processing failed', 500, 'JOB_003', false);
      const context: JobContext = {
        requestId: 'req123',
        userId: 'user123',
        organizationId: 'org123',
        endpoint: '/jobs',
        method: 'POST',
        timestamp: new Date(),
        jobId: 'job123',
        jobType: 'CONTACT_BULK_INSERT',
        status: 'PROCESSING',
        operation: 'job_processing',
        progress: {
          totalItems: 100,
          processedItems: 50,
          successfulItems: 45,
          failedItems: 5
        }
      };

      expect(() => {
        ErrorLogger.logJobError(error, context);
      }).not.toThrow();
    });

    it('should handle job errors without progress information', () => {
      const error = new AppError('Job creation failed', 500, 'JOB_002', false);
      const context: JobContext = {
        requestId: 'req123',
        userId: 'user123',
        organizationId: 'org123',
        endpoint: '/jobs',
        method: 'POST',
        timestamp: new Date(),
        jobId: 'job123',
        jobType: 'EMAIL',
        status: 'FAILED',
        operation: 'job_creation'
      };

      expect(() => {
        ErrorLogger.logJobError(error, context);
      }).not.toThrow();
    });
  });

  describe('error categorization', () => {
    const testCases = [
      { errorCode: 'AUTH_001', expectedType: 'AUTHENTICATION' },
      { errorCode: 'UNAUTHORIZED_001', expectedType: 'AUTHENTICATION' },
      { errorCode: 'FORBIDDEN_001', expectedType: 'AUTHENTICATION' },
      { errorCode: 'FILE_001', expectedType: 'FILE_VALIDATION' },
      { errorCode: 'FILE_002', expectedType: 'FILE_VALIDATION' },
      { errorCode: 'JOB_001', expectedType: 'JOB_PROCESSING' },
      { errorCode: 'CONTACT_001', expectedType: 'CONTACT_OPERATION' },
      { errorCode: 'DB_001', expectedType: 'DATABASE' },
      { errorCode: 'VALIDATION_001', expectedType: 'VALIDATION' },
      { errorCode: 'ORG_001', expectedType: 'ORGANIZATION' },
      { errorCode: 'BULK_INSERT_001', expectedType: 'BULK_INSERT' },
      { errorCode: 'EMAIL_001', expectedType: 'EMAIL' },
      { errorCode: 'UNKNOWN_001', expectedType: 'SYSTEM' }
    ];

    testCases.forEach(({ errorCode, expectedType }) => {
      it(`should categorize ${errorCode} as ${expectedType}`, () => {
        const error = new AppError('Test error', 400, errorCode, true);
        const context = new ErrorContext({
          originalUrl: '/test',
          url: '/test',
          method: 'GET',
          get: () => null,
          connection: { remoteAddress: '127.0.0.1' }
        } as any);

        expect(() => {
          ErrorLogger.logError(error, context);
        }).not.toThrow();
      });
    });
  });

  describe('PII sanitization', () => {
    it('should handle email masking without throwing', () => {
      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);
      const metadata = { email: '<EMAIL>' };

      expect(() => {
        ErrorLogger.logError(error, context, metadata);
      }).not.toThrow();
    });

    it('should handle phone masking without throwing', () => {
      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);
      const metadata = { phone: '1234567890' };

      expect(() => {
        ErrorLogger.logError(error, context, metadata);
      }).not.toThrow();
    });

    it('should handle sensitive field removal without throwing', () => {
      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);
      const metadata = {
        userId: 'user123',
        password: 'secret123',
        token: 'abc123',
        secret: 'mysecret',
        key: 'mykey',
        normalField: 'normalValue'
      };

      expect(() => {
        ErrorLogger.logError(error, context, metadata);
      }).not.toThrow();
    });

    it('should handle nested object sanitization without throwing', () => {
      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);
      const metadata = {
        user: {
          id: 'user123',
          email: '<EMAIL>',
          password: 'secret123'
        },
        contacts: [
          {
            name: 'John',
            email: '<EMAIL>',
            phone: '1234567890'
          }
        ]
      };

      expect(() => {
        ErrorLogger.logError(error, context, metadata);
      }).not.toThrow();
    });

    it('should handle contact data sanitization in bulk insert errors', () => {
      const error = new AppError('Bulk insert failed', 400, 'BULK_INSERT_001', true);
      const context: BulkInsertContext = {
        requestId: 'req123',
        userId: 'user123',
        organizationId: 'org123',
        endpoint: '/bulk-insert',
        method: 'POST',
        timestamp: new Date(),
        jobId: 'job123',
        fileName: 'contacts.csv',
        contactData: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '1234567890',
          password: 'secret123',
          ssn: '***********',
          creditCard: '4111-1111-1111-1111'
        },
        operation: 'contact_validation'
      };

      expect(() => {
        ErrorLogger.logBulkInsertError(error, context);
      }).not.toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle null/undefined errors gracefully', () => {
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);

      expect(() => {
        ErrorLogger.logError(null, context);
        ErrorLogger.logError(undefined, context);
      }).not.toThrow();
    });

    it('should handle empty metadata gracefully', () => {
      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);

      expect(() => {
        ErrorLogger.logError(error, context, {});
        ErrorLogger.logError(error, context, null as any);
        ErrorLogger.logError(error, context, undefined);
      }).not.toThrow();
    });

    it('should handle malformed contact data gracefully', () => {
      const error = new AppError('Bulk insert failed', 400, 'BULK_INSERT_001', true);
      const context: BulkInsertContext = {
        requestId: 'req123',
        userId: 'user123',
        organizationId: 'org123',
        endpoint: '/bulk-insert',
        method: 'POST',
        timestamp: new Date(),
        jobId: 'job123',
        fileName: 'contacts.csv',
        contactData: 'invalid-data',
        operation: 'contact_validation'
      };

      expect(() => {
        ErrorLogger.logBulkInsertError(error, context);
      }).not.toThrow();
    });
  });

  describe('sanitization methods (tested indirectly)', () => {
    it('should properly mask email addresses', () => {
      const testEmails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        'invalid-email',
        '',
        null,
        undefined
      ];

      testEmails.forEach(email => {
        const error = new AppError('Test error', 400, 'TEST_001', true);
        const context = new ErrorContext({
          originalUrl: '/test',
          url: '/test',
          method: 'GET',
          get: () => null,
          connection: { remoteAddress: '127.0.0.1' }
        } as any);

        expect(() => {
          ErrorLogger.logError(error, context, { email });
        }).not.toThrow();
      });
    });

    it('should properly mask phone numbers', () => {
      const testPhones = [
        '1234567890',
        '******-567-8900',
        '(*************',
        '12345',
        '1234',
        '',
        null,
        undefined
      ];

      testPhones.forEach(phone => {
        const error = new AppError('Test error', 400, 'TEST_001', true);
        const context = new ErrorContext({
          originalUrl: '/test',
          url: '/test',
          method: 'GET',
          get: () => null,
          connection: { remoteAddress: '127.0.0.1' }
        } as any);

        expect(() => {
          ErrorLogger.logError(error, context, { phone });
        }).not.toThrow();
      });
    });

    it('should handle error categorization correctly', () => {
      const errorCodes = [
        'AUTH_001', 'UNAUTHORIZED_001', 'FORBIDDEN_001',
        'FILE_001', 'FILE_002',
        'JOB_001', 'BULK_INSERT_001',
        'CONTACT_001', 'DB_001', 'VALIDATION_001',
        'ORG_001', 'EMAIL_001', 'UNKNOWN_001'
      ];

      errorCodes.forEach(errorCode => {
        const error = new AppError('Test error', 400, errorCode, true);
        const context = new ErrorContext({
          originalUrl: '/test',
          url: '/test',
          method: 'GET',
          get: () => null,
          connection: { remoteAddress: '127.0.0.1' }
        } as any);

        expect(() => {
          ErrorLogger.logError(error, context);
        }).not.toThrow();
      });
    });

    it('should handle complex nested data structures', () => {
      const complexData = {
        level1: {
          level2: {
            level3: {
              email: '<EMAIL>',
              phone: '1234567890',
              password: 'secret',
              normalField: 'value'
            }
          },
          array: [
            { email: '<EMAIL>', phone: '1111111111' },
            { email: '<EMAIL>', phone: '2222222222' }
          ]
        },
        topLevel: {
          token: 'secret-token',
          publicData: 'visible'
        }
      };

      const error = new AppError('Test error', 400, 'TEST_001', true);
      const context = new ErrorContext({
        originalUrl: '/test',
        url: '/test',
        method: 'GET',
        get: () => null,
        connection: { remoteAddress: '127.0.0.1' }
      } as any);

      expect(() => {
        ErrorLogger.logError(error, context, complexData);
      }).not.toThrow();
    });
  });
});