import { ObjectId } from 'mongodb';

/**
 * Mock Mongoose model for testing
 */
export class MockMongooseModel {
  private data: any;

  constructor(data: any) {
    this.data = { ...data, _id: new ObjectId() };
  }

  async save() {
    return this.data;
  }

  toObject() {
    return this.data;
  }

  static async findOne(query: any) {
    return null; // Default to not found
  }

  static async find(query: any) {
    return [];
  }

  static async create(data: any) {
    return new MockMongooseModel(data);
  }

  static async findById(id: string) {
    return null;
  }

  static async findByIdAndUpdate(id: string, update: any, options: any) {
    return null;
  }

  static async deleteOne(query: any) {
    return { deletedCount: 1 };
  }
}

/**
 * Mock Mongoose connection
 */
export const mockMongooseConnection = {
  readyState: 1, // Connected
  collections: {},
  dropDatabase: jest.fn(),
  close: jest.fn()
};

/**
 * Mongoose mocking utilities
 */
export class MongooseMockUtils {
  static mockModel(modelName: string, mockImplementation?: any) {
    const mockModel = mockImplementation || MockMongooseModel;

    // Mock static methods
    mockModel.findOne = jest.fn();
    mockModel.find = jest.fn();
    mockModel.create = jest.fn();
    mockModel.findById = jest.fn();
    mockModel.findByIdAndUpdate = jest.fn();
    mockModel.deleteOne = jest.fn();

    return mockModel;
  }

  static resetAllMocks() {
    jest.clearAllMocks();
  }
}
