import { ObjectId } from 'mongodb';
import { Contact, ContactStatus, ContactTag } from '../../types/contact.types';
import { Application } from 'express';
import http from 'http';
import request from 'supertest';
import createApp from '../../app';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { closeLoggers } from '../../lib/logger';

// Polyfill for Number.isSafeInteger to fix BSON serialization issues in Jest environment
if (typeof Number.isSafeInteger === 'undefined') {
  Number.isSafeInteger = function(value: any): value is number {
    return typeof value === 'number' && 
           isFinite(value) && 
           Math.floor(value) === value &&
           value >= Number.MIN_SAFE_INTEGER &&
           value <= Number.MAX_SAFE_INTEGER;
  };
}

// Extend Jest matchers globally
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidObjectId(): R;
      toBeValidEmail(): R;
      toBeValidPhoneNumber(): R;
    }
  }
}

/**
 * Test data factory for creating mock contacts
 */
export class TestDataFactory {
  static createValidContactData(overrides: Partial<Contact> = {}): Partial<Contact> {
    return {
      org_id: new ObjectId().toString(),
      created_by: new ObjectId().toString(),
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      country: 'United States',
      country_code: 'US',
      tags: [],
      additional_fields: {},
      status: ContactStatus.ACTIVE,
      ...overrides
    };
  }

  static createValidContactTag(overrides: Partial<ContactTag> = {}): ContactTag {
    return {
      tag_id: new ObjectId(),
      tag_name: 'test-tag',
      ...overrides
    };
  }

  static createInvalidContactData(): Partial<Contact> {
    return {
      // Missing required fields intentionally
      org_id: new ObjectId().toString(),
      additional_fields: {}
    };
  }
}

/**
 * Mock response helpers for testing HTTP responses
 */
export class MockResponseHelper {
  static createMockResponse() {
    const res: any = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    return res;
  }

  static createMockRequest(body: any = {}, params: any = {}, query: any = {}) {
    const userId = new ObjectId().toString();
    const orgId = new ObjectId().toString();
    
    // Create the request object with user property
    const req: any = {
      body,
      params,
      query,
      userId,
      user: {
        id: userId,
        org_id: orgId
      }
    };
    
    // Define a getter for organizationId that derives it from user.org_id
    Object.defineProperty(req, 'organizationId', {
      get: function() {
        // Return the org_id from the user object
        return this.user?.org_id;
      },
      // Allow tests to override the organizationId if needed
      set: function(value) {
        // If organizationId is explicitly set, define it as a regular property
        Object.defineProperty(this, 'organizationId', {
          value: value,
          writable: true,
          configurable: true
        });
      },
      configurable: true
    });
    
    return req;
  }
}

/**
 * Database test utilities
 */
export class DatabaseTestUtils {
  static async clearCollection(collectionName: string) {
    const mongoose = await import('mongoose');
    if (mongoose.connection && mongoose.connection.collections) {
      const collection = mongoose.connection.collections[collectionName];
      if (collection) {
        await collection.deleteMany({});
      }
    }
  }

  static async getCollectionCount(collectionName: string): Promise<number> {
    const mongoose = await import('mongoose');
    if (mongoose.connection && mongoose.connection.collections) {
      const collection = mongoose.connection.collections[collectionName];
      return collection ? await collection.countDocuments({}) : 0;
    }
    return 0;
  }
}

/**
 * Async test utilities
 */
export class AsyncTestUtils {
  static async expectAsync(asyncFn: () => Promise<any>): Promise<any> {
    try {
      return await asyncFn();
    } catch (error) {
      throw error;
    }
  }

  static async expectAsyncToThrow(asyncFn: () => Promise<any>, expectedError?: string | RegExp): Promise<void> {
    try {
      await asyncFn();
      throw new Error('Expected function to throw, but it did not');
    } catch (error) {
      if (expectedError) {
        if (typeof expectedError === 'string') {
          expect((error as Error).message).toContain(expectedError);
        } else {
          expect((error as Error).message).toMatch(expectedError);
        }
      }
    }
  }
}

/**
 * Server test utilities for integration tests
 * Provides a single entry point for connecting to the app server
 */
export class ServerTestUtils {
  private static instance: ServerTestUtils | null = null;
  private app: Application | null = null;
  private server: http.Server | null = null;

  private constructor() {}

  /**
   * Get the singleton instance of ServerTestUtils
   */
  static getInstance(): ServerTestUtils {
    if (!ServerTestUtils.instance) {
      ServerTestUtils.instance = new ServerTestUtils();
    }
    return ServerTestUtils.instance;
  }

  /**
   * Initialize the app server for testing
   * @param skipDbInit Whether to skip database initialization
   * @returns The initialized app instance
   */
  async initializeApp(skipDbInit: boolean = true): Promise<Application> {
    if (!this.app) {
      const { app, server } = await createApp(skipDbInit);
      this.app = app;
      this.server = server;
    }
    return this.app;
  }

  /**
   * Get a supertest instance for making requests to the app
   * @returns A supertest instance connected to the app
   */
  async getRequestAgent() {
    const app = await this.initializeApp();
    return request(app);
  }

  /**
   * Close the server and clean up resources
   */
  async closeServer(): Promise<void> {
    if (this.server) {
      await new Promise<void>((resolve, reject) => {
        this.server!.close((err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      this.server = null;
      this.app = null;
    }
  }
}

/**
 * Comprehensive Test Setup Manager
 * Single point of access for all test setup and teardown operations
 */
export class TestSetupManager {
  private static instance: TestSetupManager | null = null;
  private mongoServer: MongoMemoryServer | null = null;
  private isSetup: boolean = false;

  private constructor() {}

  /**
   * Get the singleton instance of TestSetupManager
   */
  static getInstance(): TestSetupManager {
    if (!TestSetupManager.instance) {
      TestSetupManager.instance = new TestSetupManager();
    }
    return TestSetupManager.instance;
  }

  /**
   * Setup MongoDB Memory Server and database connections
   * Call this in beforeAll hooks
   */
  async setupDatabase(): Promise<void> {
    if (this.isSetup) {
      return;
    }

    try {
      // Create MongoDB Memory Server
      this.mongoServer = await MongoMemoryServer.create();
      const mongoUri = this.mongoServer.getUri();

      // Connect to MongoDB
      await mongoose.connect(mongoUri);

      // Ensure indexes are created for all models
      const { ContactModel } = await import('../../lib/db/models/contact.model');
      await ContactModel.createIndexes();

      this.isSetup = true;
    } catch (error) {
      console.error('Failed to setup test database:', error);
      throw error;
    }
  }

  /**
   * Clean up database collections after each test
   * Call this in afterEach hooks
   */
  async cleanupCollections(): Promise<void> {
    if (!this.isSetup) {
      return;
    }

    try {
      const collections = mongoose.connection.collections;

      for (const key in collections) {
        const collection = collections[key];
        await collection.deleteMany({});
      }
    } catch (error) {
      console.error('Failed to cleanup collections:', error);
      throw error;
    }
  }

  /**
   * Teardown database and close all connections
   * Call this in afterAll hooks
   */
  async teardownDatabase(): Promise<void> {
    if (!this.isSetup) {
      return;
    }

    try {
      // Close database connections
      if (mongoose.connection) {
        await mongoose.connection.dropDatabase();
        await mongoose.connection.close();
      }

      // Stop MongoDB Memory Server
      if (this.mongoServer) {
        await this.mongoServer.stop();
        this.mongoServer = null;
      }

      // Close all logger instances to prevent open handles
      await closeLoggers();

      this.isSetup = false;
    } catch (error) {
      console.error('Failed to teardown test database:', error);
      throw error;
    }
  }

  /**
   * Get database connection status
   */
  isConnected(): boolean {
    return this.isSetup && mongoose.connection.readyState === 1;
  }
}

/**
 * Custom Jest Matchers
 * Initialize custom matchers for domain-specific testing
 */
export class TestMatchers {
  static initialize() {
    expect.extend({
      toBeValidObjectId(received: any) {
        const objectIdRegex = /^[0-9a-fA-F]{24}$/;
        const pass = typeof received === 'string' && objectIdRegex.test(received);

        if (pass) {
          return {
            message: () => `expected ${received} not to be a valid ObjectId`,
            pass: true,
          };
        } else {
          return {
            message: () => `expected ${received} to be a valid ObjectId`,
            pass: false,
          };
        }
      },

      toBeValidEmail(received: any) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const pass = typeof received === 'string' && emailRegex.test(received);

        if (pass) {
          return {
            message: () => `expected ${received} not to be a valid email`,
            pass: true,
          };
        } else {
          return {
            message: () => `expected ${received} to be a valid email`,
            pass: false,
          };
        }
      },

      toBeValidPhoneNumber(received: any) {
        const phoneRegex = /^[+]?[0-9\s\-\(\)]+$/;
        const pass = typeof received === 'string' && phoneRegex.test(received) && received.length >= 10;

        if (pass) {
          return {
            message: () => `expected ${received} not to be a valid phone number`,
            pass: true,
          };
        } else {
          return {
            message: () => `expected ${received} to be a valid phone number`,
            pass: false,
          };
        }
      }
    });
  }
}

// Global test setup using TestSetupManager as single point of access
const testSetupManager = TestSetupManager.getInstance();

// Initialize custom matchers
TestMatchers.initialize();

// Setup database before all tests
beforeAll(async () => {
  await testSetupManager.setupDatabase();
}, 30000);

// Clean up after each test
afterEach(async () => {
  await testSetupManager.cleanupCollections();
});

// Cleanup after all tests
afterAll(async () => {
  await testSetupManager.teardownDatabase();
}, 30000);
