/**
 * Test fixtures for file-related tests
 */
export const fileFixtures = {
  validFile: {
    originalname: 'contacts.csv',
    buffer: Buffer.from('Name,Email,Phone\nJohn <PERSON>,<EMAIL>,+1234567890'),
    size: 1024,
    mimetype: 'text/csv'
  },
  
  largeFile: {
    originalname: 'large_contacts.csv',
    buffer: Buffer.from('Name,Email,Phone\nJohn <PERSON>,<EMAIL>,+1234567890'),
    size: 30 * 1024 * 1024, // 30MB
    mimetype: 'text/csv'
  },
  
  invalidFileType: {
    originalname: 'contacts.txt',
    buffer: Buffer.from('Name,Email,Phone\nJohn <PERSON>,<EMAIL>,+1234567890'),
    size: 1024,
    mimetype: 'text/plain'
  },
  
  emptyFile: {
    originalname: 'empty.csv',
    buffer: Buffer.from(''),
    size: 0,
    mimetype: 'text/csv'
  },
  
  fileWithoutHeaders: {
    originalname: 'no_headers.csv',
    buffer: Buffer.from('<PERSON>,<EMAIL>,+1234567890'),
    size: 1024,
    mimetype: 'text/csv'
  },
  
  fileWithTooManyRows: {
    originalname: 'too_many_rows.csv',
    buffer: Buffer.from('Name,Email,Phone\nJohn Doe,<EMAIL>,+1234567890'),
    size: 1024,
    mimetype: 'text/csv'
  },
  
  excelFile: {
    originalname: 'contacts.xlsx',
    buffer: Buffer.from('dummy excel content'),
    size: 2048,
    mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  },
  
  csvFileWithHeaders: {
    originalname: 'contacts_with_headers.csv',
    buffer: Buffer.from('Name,Email,Phone,Country\nJohn Doe,<EMAIL>,+1234567890,USA'),
    size: 1024,
    mimetype: 'text/csv'
  }
};