// Main user profile interface
export type UserType = 'super_admin' | 'admin' | 'user';

export interface UserProfile {
 uuid: string;
 user_id: string;
 organization_uuid?: string | null;
 first_name?: string | null;
 last_name?: string | null;
 user_type: UserType;
 email?: string | null;
 phone_number?: string | null;
 is_email_verified: boolean;
 is_mobile_verified: boolean;
 user_status: 'active' | 'inactive' | string;
 created_at: Date; // ISO string format
 updated_at: Date; // ISO string format
}
