import { ContactSchemaField } from './contact.types';

/**
 * File validation rules interface
 */
export interface FileValidationRules {
  allowedExtensions: string[];
  maxSizeBytes: number;
  maxRows: number;
  requireHeaders: boolean;
  singleSheetOnly: boolean;
}

/**
 * File validation error interface
 */
export interface FileValidationError {
  code: string;
  message: string;
  field?: string;
}

/**
 * Result of file structure validation
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  rowCount?: number;
  sheetCount?: number;
}

/**
 * Response interface for CSV header extraction
 */
export interface CSVHeaderExtractionResponse {
  success: boolean;
  data: {
    csvHeaders: string[];
    contactSchema: ContactSchemaField[];
    fileInfo: {
      name: string;
      size: number;
      rowCount: number;
    };
  };
}

/**
 * Error response interface for file processing errors
 */
export interface FileErrorResponse {
  success: false;
  error: string;
  errorCode: string;
  details?: FileValidationError[];
}
