export interface ErrorContext {
    requestId: string;
    userId?: string;
    organizationId?: string;
    endpoint: string;
    method: string;
    userAgent?: string;
    ip?: string;
    timestamp: Date;
    jobId?: string;
    fileName?: string;
    rowNumber?: number;
}

export interface RequestErrorContext {
    jobId?: string;
    fileName?: string;
    rowNumber?: number;
    bulkOperationId?: string;
    [key: string]: any;
}

export interface BulkInsertContext extends ErrorContext {
    jobId: string;
    fileName?: string;
    totalRows?: number;
    processedRows?: number;
    errorRows?: number;
    contactData?: unknown;
    operation?: string;
}

export interface JobContext extends ErrorContext {
    jobId: string;
    jobType: string;
    status: string;
    progress?: unknown;
    operation?: string;
}
