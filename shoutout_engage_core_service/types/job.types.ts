import { Schema } from 'mongoose';
import { JobStatus, JobType, JobPriority, JobErrorType } from '../lib/constant/job.constants';

/**
 * Universal job error interface
 */
export interface JobError {
  type: JobErrorType;
  message: string;
  errorCode: string;
  details?: Array<{ field: string; message: string }>;
  timestamp: Date;
  rowNumber?: number; // For CSV processing errors
  stackTrace?: string;
}

/**
 * Universal job progress interface
 */
export interface JobProgress {
  totalItems?: number;    // Total contacts, emails, SMS, etc.
  processedItems?: number; // Processed contacts, emails, SMS, etc.
  successfulItems?: number; // Successful contacts, emails, SMS, etc.
  failedItems?: number;    // Failed contacts, emails, SMS, etc.
  percentage?: number;     // Progress percentage (0-100)
  currentStep?: string;    // Current processing step description
  estimatedCompletion?: Date; // Estimated completion time
  startedAt?: Date;       // When processing actually started
  lastUpdated?: Date;     // Last progress update timestamp
}

/**
 * Universal job metadata interface
 */
export interface JobMetadata {
  priority?: JobPriority;
  retryCount?: number;
  maxRetries?: number;
  tags?: string[];
  source?: string;        // Where the job originated from
  userAgent?: string;     // User agent if from web request
  ipAddress?: string;     // IP address if from web request
  [key: string]: any;     // Extensible for job-specific metadata
}

/**
 * Base job data interface that all job types extend
 */
export interface BaseJobData {
  jobId: string;
  orgId: string;
  createdBy: string;
  userEmail?: string;
  metadata?: JobMetadata;
}

/**
 * Universal job document interface for MongoDB
 */
export interface JobDocument {
  _id: Schema.Types.ObjectId;
  jobId: string;
  jobType: JobType;
  status: JobStatus;
  orgId: string;
  createdBy: string;
  
  // Universal job data (flexible for any job type)
  data: any; // Will be typed specifically in each job processor
  result?: any; // Job-specific result data
  
  // Universal error tracking
  errors: JobError[];
  
  // Universal progress tracking
  progress: JobProgress;
  
  // Universal timing
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Universal metadata
  metadata?: JobMetadata;
}

/**
 * Job status response interface for API responses
 */
export interface JobStatusResponse {
  jobId: string;
  jobType: JobType;
  status: JobStatus;
  orgId: string;
  createdBy: string;
  progress: JobProgress;
  errors: JobError[];
  result?: any;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  estimatedCompletion?: Date;
}

/**
 * Job creation request interface
 */
export interface CreateJobRequest {
  jobType: JobType;
  orgId: string;
  createdBy: string;
  data: any;
  metadata?: JobMetadata;
}

/**
 * Job query filters interface
 */
export interface JobQueryFilters {
  orgId?: string;
  createdBy?: string;
  jobType?: JobType;
  status?: JobStatus;
  createdAfter?: Date;
  createdBefore?: Date;
  tags?: string[];
}

/**
 * Job query options interface
 */
export interface JobQueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  includeErrors?: boolean;
  includeResult?: boolean;
}

/**
 * Paginated job results interface
 */
export interface PaginatedJobResults {
  jobs: JobDocument[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Queue statistics interface
 */
export interface QueueStats {
  queueName: string;
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
  isPaused: boolean;
}

/**
 * Job processor interface that all processors must implement
 */
export interface JobProcessor<T = any> {
  queueName: string;
  jobType: JobType;
  startProcess(): void;
  stopProcess(): Promise<void>;
  getQueue(): any; // Bull Queue type
  processJob(job: any): Promise<any>; // Bull Job type
}

/**
 * Job event data interface for monitoring
 */
export interface JobEventData {
  jobId: string;
  jobType: JobType;
  event: string;
  data?: any;
  timestamp: Date;
}