# Complete API Specifications - ShoutOut Engage Microservices

## API Design Principles

### Common Patterns
- **Base URL Pattern**: `https://api.shoutout.com/{service}/v1`
- **Authentication**: Bearer JWT tokens from Supabase
- **Response Format**: Consistent JSON structure
- **Error Handling**: Standardized error codes and messages
- **Pagination**: Cursor-based pagination for large datasets
- **Rate Limiting**: Per-organization and per-endpoint limits

### Standard Response Format
```typescript
// Success Response
{
  "success": true,
  "data": any,
  "meta"?: {
    "pagination"?: PaginationMeta,
    "timestamp": string,
    "request_id": string
  }
}

// Error Response
{
  "success": false,
  "error": {
    "code": string,
    "message": string,
    "details"?: any,
    "timestamp": string,
    "request_id": string
  }
}
```

## 1. Campaign Service API

### Base URL: `https://api.shoutout.com/campaign/v1`

#### Campaign Management

##### Create Campaign
```http
POST /campaigns
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Summer Sale 2024",
  "description": "Promotional campaign for summer collection",
  "type": "BROADCAST",
  "target_config": {
    "type": "SEGMENTS",
    "segment_ids": ["64a1b2c3d4e5f6789012345"],
    "filters": {
      "exclude_unsubscribed": true,
      "exclude_bounced": true,
      "last_activity_days": 30
    }
  },
  "channels": {
    "sms": {
      "enabled": true,
      "sender_id": "SUMMER24",
      "content": "🌞 Summer Sale! Get {{discount}}% off. Use: {{code}}. Shop: {{url}}",
      "template_variables": {
        "discount": "30",
        "code": "SUMMER30",
        "url": "https://shop.example.com/summer"
      }
    },
    "email": {
      "enabled": true,
      "sender_email": "<EMAIL>",
      "sender_name": "Example Store",
      "subject": "🌞 Summer Sale - {{discount}}% Off Everything!",
      "content": "Dear {{contact.name}}, don't miss our summer sale...",
      "html_content": "<html><body>...</body></html>",
      "template_variables": {
        "discount": "30"
      }
    }
  },
  "schedule_config": {
    "is_scheduled": true,
    "scheduled_at": "2024-08-15T10:00:00Z",
    "timezone": "America/New_York"
  },
  "ab_test_config": {
    "enabled": true,
    "variants": [
      {
        "name": "30% Off",
        "percentage": 50,
        "content": {
          "sms": "🌞 Summer Sale! 30% off everything!",
          "email": { "subject": "30% Off Summer Collection!" }
        }
      },
      {
        "name": "Buy 2 Get 1 Free",
        "percentage": 50,
        "content": {
          "sms": "🌞 Summer Sale! Buy 2 Get 1 FREE!",
          "email": { "subject": "Buy 2 Get 1 Free - Summer Sale!" }
        }
      }
    ],
    "winner_criteria": "CLICK_RATE",
    "test_duration": 24
  },
  "tags": ["summer", "promotional", "2024"],
  "metadata": {
    "budget_limit": 2000,
    "expected_roi": 4.0
  }
}

Response (201 Created):
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012346",
    "name": "Summer Sale 2024",
    "type": "BROADCAST",
    "status": "DRAFT",
    "estimated_recipients": 1250,
    "estimated_cost": {
      "sms": 62.50,
      "email": 12.50,
      "total": 75.00,
      "currency": "USD"
    },
    "created_at": "2024-08-11T15:30:00Z",
    "updated_at": "2024-08-11T15:30:00Z"
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

##### Build Campaign
```http
POST /campaigns/{campaign_id}/build
Authorization: Bearer <jwt_token>

Response (200 OK):
{
  "success": true,
  "data": {
    "job_id": "build_job_64a1b2c3d4e5f6789012348",
    "status": "BUILDING",
    "estimated_recipients": 1250,
    "estimated_completion": "2024-08-11T15:35:00Z",
    "progress_url": "/campaigns/64a1b2c3d4e5f6789012346/build-status"
  }
}
```

##### Send Campaign
```http
POST /campaigns/{campaign_id}/send
Authorization: Bearer <jwt_token>

{
  "send_immediately": true,
  "override_schedule": false
}

Response (200 OK):
{
  "success": true,
  "data": {
    "campaign_id": "64a1b2c3d4e5f6789012346",
    "status": "SENDING",
    "messages_queued": 1250,
    "estimated_completion": "2024-08-11T16:00:00Z",
    "channels_active": ["SMS", "EMAIL"]
  }
}
```

#### Segment Management

##### Create Segment
```http
POST /segments
Authorization: Bearer <jwt_token>

{
  "name": "High Value Customers",
  "description": "Customers with orders > $500 in last 6 months",
  "criteria": {
    "conditions": [
      {
        "field": "total_orders_value",
        "operator": "greater_than",
        "value": 500,
        "logic": "AND"
      },
      {
        "field": "last_order_date",
        "operator": "greater_than",
        "value": "2024-02-11T00:00:00Z",
        "logic": "AND"
      }
    ],
    "tags": {
      "include": ["premium", "vip"],
      "exclude": ["churned"]
    },
    "date_filters": {
      "created_after": "2023-01-01T00:00:00Z",
      "last_activity_after": "2024-05-01T00:00:00Z"
    }
  }
}

Response (201 Created):
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012349",
    "name": "High Value Customers",
    "status": "ACTIVE",
    "contact_count": 0,
    "last_calculated_at": null,
    "created_at": "2024-08-11T15:30:00Z"
  }
}
```

##### Calculate Segment
```http
POST /segments/{segment_id}/calculate
Authorization: Bearer <jwt_token>

Response (200 OK):
{
  "success": true,
  "data": {
    "job_id": "segment_calc_64a1b2c3d4e5f678901234a",
    "status": "CALCULATING",
    "estimated_completion": "2024-08-11T15:32:00Z"
  }
}
```

## 2. Message Service API

### Base URL: `https://api.shoutout.com/message/v1`

#### Message Operations

##### Send Single Message
```http
POST /messages
Authorization: Bearer <jwt_token>

{
  "transport": "SMS",
  "provider": "TWILIO",
  "from": "STORE",
  "to": "+94771234567",
  "content": "Your order #12345 is ready for pickup!",
  "campaign_id": "64a1b2c3d4e5f6789012346",
  "priority": "HIGH",
  "metadata": {
    "order_id": "12345",
    "customer_id": "cust_789"
  }
}

Response (201 Created):
{
  "success": true,
  "data": {
    "message_id": "msg_64a1b2c3d4e5f678901234b",
    "status": "QUEUED",
    "provider": "TWILIO",
    "estimated_delivery": "2024-08-11T15:31:00Z",
    "cost": 0.05,
    "currency": "USD",
    "tracking_url": "/messages/msg_64a1b2c3d4e5f678901234b"
  }
}
```

##### Send Bulk Messages
```http
POST /messages/bulk
Authorization: Bearer <jwt_token>

{
  "messages": [
    {
      "transport": "SMS",
      "provider": "TWILIO",
      "from": "STORE",
      "to": "+94771234567",
      "content": "Hi John, your order is ready!",
      "metadata": { "customer_id": "cust_001" }
    },
    {
      "transport": "EMAIL",
      "provider": "SENDGRID",
      "from": "<EMAIL>",
      "to": "<EMAIL>",
      "subject": "Order Ready for Pickup",
      "content": "Hi Jane, your order #12346 is ready!",
      "metadata": { "customer_id": "cust_002" }
    }
  ],
  "batch_options": {
    "priority": "NORMAL",
    "schedule_at": "2024-08-11T16:00:00Z"
  }
}

Response (201 Created):
{
  "success": true,
  "data": {
    "batch_id": "batch_64a1b2c3d4e5f678901234c",
    "total_messages": 2,
    "accepted_messages": 2,
    "rejected_messages": 0,
    "estimated_cost": 0.15,
    "currency": "USD",
    "messages": [
      {
        "message_id": "msg_64a1b2c3d4e5f678901234d",
        "status": "QUEUED",
        "index": 0
      },
      {
        "message_id": "msg_64a1b2c3d4e5f678901234e",
        "status": "QUEUED",
        "index": 1
      }
    ]
  }
}
```

##### Get Message Status
```http
GET /messages/{message_id}
Authorization: Bearer <jwt_token>

Response (200 OK):
{
  "success": true,
  "data": {
    "message_id": "msg_64a1b2c3d4e5f678901234b",
    "transport": "SMS",
    "provider": "TWILIO",
    "status": "DELIVERED",
    "from": "STORE",
    "to": "+94771234567",
    "content": "Your order #12345 is ready for pickup!",
    "cost": 0.05,
    "currency": "USD",
    "queued_at": "2024-08-11T15:30:00Z",
    "sent_at": "2024-08-11T15:30:15Z",
    "delivered_at": "2024-08-11T15:30:18Z",
    "provider_message_id": "SM1234567890abcdef",
    "status_history": [
      {
        "status": "QUEUED",
        "timestamp": "2024-08-11T15:30:00Z"
      },
      {
        "status": "SENT",
        "timestamp": "2024-08-11T15:30:15Z",
        "provider_status": "accepted"
      },
      {
        "status": "DELIVERED",
        "timestamp": "2024-08-11T15:30:18Z",
        "provider_status": "delivered"
      }
    ]
  }
}
```

#### Provider Management

##### List Providers
```http
GET /providers
Authorization: Bearer <jwt_token>

Response (200 OK):
{
  "success": true,
  "data": [
    {
      "provider_name": "TWILIO",
      "provider_type": "SMS",
      "is_active": true,
      "health_status": "HEALTHY",
      "supported_countries": ["US", "CA", "GB", "AU"],
      "cost_per_message": 0.05,
      "currency": "USD",
      "rate_limits": {
        "requests_per_second": 10,
        "requests_per_minute": 600
      }
    },
    {
      "provider_name": "SENDGRID",
      "provider_type": "EMAIL",
      "is_active": true,
      "health_status": "HEALTHY",
      "cost_per_message": 0.01,
      "currency": "USD"
    }
  ]
}
```

## 3. Payment Service API

### Base URL: `https://api.shoutout.com/payment/v1`

#### Payment Methods

##### Get Payment Methods
```http
GET /payment-methods
Authorization: Bearer <jwt_token>

Response (200 OK):
{
  "success": true,
  "data": {
    "ipg": "stripe",
    "payment_methods": [
      {
        "id": "pm_1234567890abcdef",
        "type": "card",
        "card": {
          "brand": "visa",
          "last4": "4242",
          "exp_month": 12,
          "exp_year": 2025
        },
        "is_default": true,
        "created_at": "2024-08-01T10:00:00Z"
      }
    ]
  }
}
```

##### Add Payment Method
```http
POST /payment-methods
Authorization: Bearer <jwt_token>

{
  "payment_method_id": "pm_1234567890abcdef",
  "is_default": true
}

Response (201 Created):
{
  "success": true,
  "data": {
    "id": "pm_1234567890abcdef",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    },
    "is_default": true,
    "status": "active"
  }
}
```

#### Subscriptions

##### Create Subscription
```http
POST /subscriptions
Authorization: Bearer <jwt_token>

{
  "plan_id": "starter_monthly",
  "payment_method_id": "pm_1234567890abcdef",
  "billing_cycle": "MONTHLY",
  "trial_days": 14
}

Response (201 Created):
{
  "success": true,
  "data": {
    "subscription_id": "sub_1234567890abcdef",
    "plan_id": "starter_monthly",
    "status": "TRIALING",
    "billing_cycle": "MONTHLY",
    "amount": 29.99,
    "currency": "USD",
    "trial_start": "2024-08-11T15:30:00Z",
    "trial_end": "2024-08-25T15:30:00Z",
    "current_period_start": "2024-08-11T15:30:00Z",
    "current_period_end": "2024-09-11T15:30:00Z",
    "next_billing_date": "2024-08-25T15:30:00Z",
    "usage_limits": {
      "sms_messages": 1000,
      "email_messages": 5000,
      "contacts": 2000,
      "campaigns": 10
    },
    "usage_current": {
      "sms_messages": 0,
      "email_messages": 0,
      "contacts": 0,
      "campaigns": 0
    }
  }
}
```

This comprehensive API specification provides detailed endpoints for all microservices with proper request/response formats, authentication, and error handling patterns.
