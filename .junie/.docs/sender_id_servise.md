# ShoutOUT Utility Service REST API Documentation

## Overview
This document provides comprehensive documentation for the ShoutOUT Utility Service REST API, which manages SMS Senders, Phone Numbers, and Email Senders following REST API architecture patterns.

## Base URL
```
https://api.shoutout.io/utility
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <token>
```

## API Endpoints

### 1. SMS Senders Management

#### 1.1 Get All Sender IDs
```
GET /senders
```
**Description**: Retrieve all sender IDs for the authenticated user.

**Response**:
```json
[
  {
    "_id": "string",
    "ownerId": "string",
    "senderId": "string",
    "displayName": "string",
    "status": 0,
    "transport": "SMS",
    "registeredCountries": [
      {
        "countryISOCode": "LK",
        "countryName": "Sri Lanka",
        "status": 1
      }
    ],
    "metadata": {
      "userType": "BUSINESS",
      "companyName": "My Company",
      "businessNature": "Restaurant",
      "userName": "<PERSON>",
      "userDesignation": "Marketing Manager",
      "sampleContent": "Your order is ready for pickup",
      "enableInternationalUsage": true,
      "usage": "transactional",
      "countryISOCodes": ["LK"]
    },
    "createdOn": "2025-01-01T00:00:00.000Z",
    "modifiedOn": "2025-01-01T00:00:00.000Z"
  }
]
```

#### 1.2 Create New Sender ID
```
POST /senders
```
**Description**: Create a new SMS sender ID with validation rules.

**Validation Rules**:
- Sender ID name: 3-11 alphanumeric characters only
- Required fields: senderId, transport, metadata.userType
- Countries must be specified in countryISOCodes array

**Request Body**:
```json
{
  "senderId": "MYSTORE",
  "transport": "SMS",
  "metadata": {
    "userType": "BUSINESS",
    "companyName": "My Company",
    "businessNature": "Restaurant",
    "userName": "Sam De Silva",
    "userDesignation": "Marketing Manager",
    "sampleContent": "Your order is ready for pickup",
    "enableInternationalUsage": true,
    "countryISOCodes": ["LK"],
    "usage": "transactional"
  }
}
```

**Individual Purpose Request**:
```json
{
  "senderId": "JOHN123",
  "transport": "SMS",
  "metadata": {
    "userType": "INDIVIDUAL",
    "userName": "John Doe",
    "userIdentificationNumber": "123456789V",
    "sampleContent": "Meeting reminder",
    "enableInternationalUsage": false,
    "countryISOCodes": ["LK"],
    "usage": "transactional"
  }
}
```

**Response** (201 Created):
```json
{
  "_id": "string",
  "senderId": "MYSTORE",
  "status": 0,
  "message": "Sender ID request submitted successfully"
}
```

#### 1.3 Delete Sender ID
```
DELETE /senders/{id}
```
**Description**: Delete a specific sender ID.

**Response** (200 OK):
```json
{
  "message": "Sender ID deleted successfully"
}
```

### 2. Sender ID Artifacts Management

#### 2.1 Generate Artifact Template
```
POST /senders/artifacts/generate
```
**Description**: Generate artifact templates (agreements, business registration copies).

**Request Body**:
```json
{
  "senderId": "MYSTORE",
  "registeredCountryId": "country_id",
  "artifactType": "SENDER_ID_AGREEMENT"
}
```

**Artifact Types**:
- `SENDER_ID_AGREEMENT`
- `BUSINESS_REGISTRATION_COPY`

**Response**: PDF file download

#### 2.2 Upload Artifact
```
POST /senders/artifacts/upload
```
**Description**: Upload completed artifacts for sender ID approval.

**Content-Type**: `multipart/form-data`

**Form Data**:
- `file`: PDF/Image file
- `senderId`: string
- `registeredCountryId`: string
- `artifactType`: string (SENDER_ID_AGREEMENT | BUSINESS_REGISTRATION_COPY | PERSONAL_ID_COPY)

**Response** (200 OK):
```json
{
  "_id": "string",
  "senderId": "MYSTORE",
  "status": 1,
  "message": "Artifact uploaded successfully"
}
```

#### 2.3 Download Artifact
```
POST /senders/artifacts/download
```
**Description**: Download previously uploaded artifacts.

**Request Body**:
```json
{
  "senderId": "MYSTORE",
  "artifactId": "artifact_id",
  "registeredCountryId": "country_id"
}
```

**Response** (200 OK):
```json
{
  "url": "https://s3.amazonaws.com/bucket/artifact.pdf"
}
```

### 3. Email Verification

#### 3.1 Verify Email Sender
```
GET /emails/verify?token={verification_token}
```
**Description**: Verify email sender ID using verification token.

**Response** (200 OK):
```json
{
  "redirectUrl": "https://app.shoutout.io/dashboard",
  "queryData": {
    "message": "Email verified successfully"
  }
}
```

### 4. Phone Numbers Management

#### 4.1 Get Available Phone Numbers
```
GET /numbers/available
```
**Description**: Retrieve available phone numbers for purchase.

**Query Parameters**:
- `countryISO`: string (optional)
- `type`: string (local|toll-free) (optional)
- `service`: string (sms|voice) (optional)

**Response**:
```json
[
  {
    "phoneNumber": "+94771234567",
    "services": ["sms", "voice"],
    "monthlyCost": 5.00,
    "activationCost": 1.00,
    "type": "local",
    "currency": "USD",
    "countryISO": "LK",
    "countryName": "Sri Lanka",
    "providerId": "twilio",
    "serviceCosts": {
      "inbound": {
        "sms": 0.01,
        "voice": 0.02
      },
      "outbound": {
        "sms": 0.05,
        "voice": 0.10
      }
    }
  }
]
```

#### 4.2 Get Purchased Phone Numbers
```
GET /numbers
```
**Description**: Retrieve all purchased phone numbers for the authenticated user.

**Response**:
```json
[
  {
    "_id": "string",
    "phoneNumber": "+94771234567",
    "services": ["sms", "voice"],
    "monthlyCost": 5.00,
    "activationCost": 1.00,
    "type": "local",
    "currency": "USD",
    "countryISO": "LK",
    "countryName": "Sri Lanka",
    "providerId": "twilio",
    "createdOn": "2025-01-01T00:00:00.000Z",
    "updatedOn": "2025-01-01T00:00:00.000Z",
    "serviceCosts": {
      "inbound": {
        "sms": 0.01,
        "voice": 0.02
      },
      "outbound": {
        "sms": 0.05,
        "voice": 0.10
      }
    }
  }
]
```

#### 4.3 Purchase Phone Number
```
POST /numbers
```
**Description**: Purchase a phone number.

**Request Body**:
```json
{
  "phoneNumber": "+94771234567",
  "providerId": "twilio",
  "services": ["sms", "voice"]
}
```

**Response** (201 Created):
```json
{
  "_id": "string",
  "phoneNumber": "+94771234567",
  "status": "active",
  "message": "Phone number purchased successfully"
}
```

#### 4.4 Release Phone Number
```
DELETE /numbers/{id}
```
**Description**: Release a purchased phone number.

**Response** (200 OK):
```json
{
  "message": "Phone number released successfully"
}
```

## Data Models

### Sender ID Request Model
```json
{
  "senderId": {
    "type": "string",
    "required": true,
    "minLength": 3,
    "maxLength": 11,
    "pattern": "^[A-Za-z0-9]+$",
    "description": "Alphanumeric characters only, 3-11 characters"
  },
  "transport": {
    "type": "string",
    "enum": ["SMS", "EMAIL", "FACEBOOK_MESSENGER"],
    "required": true
  },
  "metadata": {
    "type": "object",
    "properties": {
      "userType": {
        "type": "string",
        "enum": ["BUSINESS", "INDIVIDUAL"],
        "required": true
      },
      "companyName": {
        "type": "string",
        "description": "Required for BUSINESS type"
      },
      "businessNature": {
        "type": "string",
        "description": "Required for BUSINESS type"
      },
      "userName": {
        "type": "string",
        "required": true
      },
      "userDesignation": {
        "type": "string",
        "description": "Required for BUSINESS type"
      },
      "userIdentificationNumber": {
        "type": "string",
        "description": "Required for INDIVIDUAL type"
      },
      "sampleContent": {
        "type": "string",
        "required": true,
        "description": "Sample SMS content you intend to send"
      },
      "enableInternationalUsage": {
        "type": "boolean",
        "required": true
      },
      "countryISOCodes": {
        "type": "array",
        "items": {
          "type": "string"
        },
        "required": true,
        "description": "Countries where you want to register the sender ID"
      },
      "usage": {
        "type": "string",
        "enum": ["transactional", "promotional"],
        "required": true
      }
    }
  }
}
```

### Phone Number Model
```json
{
  "phoneNumber": {
    "type": "string",
    "required": true,
    "description": "E.164 format phone number"
  },
  "services": {
    "type": "array",
    "items": {
      "type": "string",
      "enum": ["sms", "voice"]
    }
  },
  "monthlyCost": {
    "type": "number",
    "description": "Monthly recurring cost"
  },
  "activationCost": {
    "type": "number",
    "description": "One-time activation cost"
  },
  "type": {
    "type": "string",
    "enum": ["local", "toll-free"]
  },
  "currency": {
    "type": "string",
    "default": "USD"
  },
  "countryISO": {
    "type": "string",
    "description": "ISO 3166-1 alpha-2 country code"
  }
}
```

## Error Responses

### Standard Error Format
```json
{
  "status": 400,
  "error": "Validation failed: senderId must be between 3 and 11 alphanumeric characters"
}
```

### Common Error Codes
- `400 Bad Request`: Invalid request data or validation errors
- `401 Unauthorized`: Missing or invalid authentication token
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists
- `500 Internal Server Error`: Server error

## Validation Rules

### SMS Sender ID Validation
1. **Length**: 3-11 characters
2. **Characters**: Alphanumeric only (A-Z, a-z, 0-9)
3. **Required Fields**:
    - For Business: companyName, businessNature, userName, userDesignation
    - For Individual: userName, userIdentificationNumber
4. **Countries**: At least one country must be specified
5. **Sample Content**: Required to demonstrate intended use
6. **Usage Type**: Must be either "transactional" or "promotional"

### Phone Number Validation
1. **Format**: Must be in E.164 format
2. **Services**: Must specify at least one service (sms or voice)
3. **Availability**: Number must be available for purchase

## Rate Limits
- 100 requests per minute per user
- 1000 requests per hour per user

## SDKs and Libraries
- JavaScript/Node.js: `@shoutout/utility-sdk`
- Python: `shoutout-utility-python`
- PHP: `shoutout/utility-sdk`
- Java: `io.shoutout.utility-sdk`

## Support
For technical support and questions:
- Email: <EMAIL>
- Documentation: https://docs.shoutout.io
- API Status: https://status.shoutout.io