# ShoutOut Engage Payment Old Service API Documentation

## Project Overview

The ShoutOut Payment Service is a comprehensive Node.js Express application that handles payment processing, subscriptions, and financial transactions. It's built as a serverless application with Stripe integration and uses MongoDB for data storage.

### Tech Stack

#### Backend Framework
- **Node.js** with **Express.js** framework
- **Serverless** architecture (deployable to AWS Lambda)
- **JWT Authentication** using Passport.js
- **MongoDB** with Mongoose ODM
- **Swagger** for API documentation

#### Key Dependencies
- `express` - Web application framework
- `stripe` - Payment processing integration
- `mongoose` - MongoDB object modeling
- `passport` & `@shoutout-labs/passport-jwt-authorizer` - Authentication
- `swagger-jsdoc` & `swagger-ui-express` - API documentation
- `cors` - Cross-origin resource sharing
- `joi` - Data validation
- `bunyan` - Logging
- `serverless-http` - Serverless deployment

## Architecture Overview

### Application Structure
```
app.js                 # Main application entry point
├── routes/            # API route definitions
├── lib/               # Core business logic and services
├── config/            # Configuration files
├── test/              # Test files
├── views/             # Template files
└── resources/         # Static resources
```

### Key Architectural Components

#### 1. Authentication Layer
- JWT-based authentication using `@shoutout-labs/passport-jwt-authorizer`
- All endpoints except webhooks require authentication
- User context available in `req.user` with id, name, and email

#### 2. Payment Gateway Integration
- Primary integration with **Stripe**
- Supports multiple Internet Payment Gateways (IPG)
- Country-based IPG selection through `IPGService`

#### 3. Database Layer
- MongoDB with Mongoose ODM
- Automatic connection initialization per request
- Connection handled through `MongooseConnector`

#### 4. Error Handling
- Custom HTTP error handling with `CustomHttpError`
- Standardized error responses with status codes
- Development vs production error modes

## API Endpoints Documentation

### Base Configuration
- **Base Path**: Configurable via `config.api.base_path`
- **Authentication**: JWT Bearer token required (except webhooks)
- **Documentation**: Available at `/docs` and `/docs-admin`

### 1. Payment Methods (`/paymentmethods`)

#### GET /paymentmethods
- **Purpose**: Retrieve user's payment methods
- **Authentication**: Required
- **Response**: List of payment methods with IPG information
- **Response Format**:
```json
{
  "iPG": "stripe",
  "paymentMethods": [
    {
      "id": "pm_xxx",
      "name": "Card name",
      "brand": "visa",
      "type": "card"
    }
  ]
}
```

#### POST /paymentmethods
- **Purpose**: Create/attach a new payment method
- **Authentication**: Required
- **Request Body**:
```json
{
  "paymentMethodId": "pm_xxx"
}
```
- **Query Parameters**: `?isDefault=true` (optional)
- **Response**: Payment method object from IPG

### 2. Payment Intents (`/paymentintents`)

#### POST /paymentintents
- **Purpose**: Create a payment intent for one-time payments
- **Authentication**: Required
- **Request Body**:
```json
{
  "planId": "plan_xxx",
  "paymentMethodId": "pm_xxx",  // optional
  "quantity": 1                 // min: 1, max: 5
}
```
- **Response Types**:
    - `success`: Payment completed
    - `pending_payment`: Requires client-side completion
    - `pending_authentication`: Requires 3D Secure authentication
- **Response Format**:
```json
{
  "status": "pending_authentication",
  "message": "Payment requires authentication",
  "clientSecret": "pi_xxx_secret_xxx",
  "paymentMethodId": "pm_xxx"
}
```

### 3. Subscriptions (`/subscriptions`)

#### POST /subscriptions
- **Purpose**: Create a new subscription
- **Authentication**: Required
- **Request Body**:
```json
{
  "planId": "plan_xxx",
  "paymentMethodId": "pm_xxx",  // optional if token provided
  "token": {}                   // optional payment token
}
```
- **Response Format**:
```json
{
  "message": "Subscription created successfully",
  "status": "success"  // or "pending_authentication"
}
```

#### GET /subscriptions/unsubscribe/{id}
- **Purpose**: Cancel a subscription
- **Authentication**: Required
- **Parameters**: `id` - subscription ID
- **Response**:
```json
{
  "message": "success"
}
```

#### PUT /subscriptions/change
- **Purpose**: Change subscription plan
- **Authentication**: Required
- **Request Body**:
```json
{
  "id": "sub_xxx",
  "planId": "new_plan_xxx"
}
```

### 4. Invoices (`/invoices`)
- **Purpose**: Manage billing and invoice operations
- **Authentication**: Required
- **Features**: Invoice generation, payment tracking, billing history

### 5. Users (`/users`)
- **Purpose**: User account management
- **Authentication**: Required
- **Features**: Profile management, payment preferences, account settings

### 6. Packages (`/packages`)
- **Purpose**: Available service packages/plans
- **Authentication**: Required
- **Features**: Package listing, pricing information

### 7. Bank Transfer (`/banktransfer`)
- **Purpose**: Handle bank transfer payments
- **Authentication**: Required
- **Features**: Bank transfer initiation, status tracking

### 8. Global Prices (`/globalprices`)
- **Purpose**: Manage pricing across different regions
- **Authentication**: Required
- **Features**: Regional pricing, currency conversion

### 9. Webhooks (`/webhook`)
- **Purpose**: Handle Stripe webhook events
- **Authentication**: Not required (uses Stripe signature verification)
- **Supported Events**:
    - `payment_intent.created`
    - `payment_intent.succeeded`
    - `charge.succeeded`
    - `invoice.created`
    - `invoice.paid`
    - `invoice.payment_succeeded`

### 10. Admin Endpoints (`/admin/*`)

#### /admin/banktransfer
- **Purpose**: Administrative bank transfer management
- **Authentication**: Required (admin privileges)

#### /admin/globalprices
- **Purpose**: Administrative pricing management
- **Authentication**: Required (admin privileges)

#### /admin/invoices
- **Purpose**: Administrative invoice management
- **Authentication**: Required (admin privileges)

#### /admin/subscriptions
- **Purpose**: Administrative subscription management
- **Authentication**: Required (admin privileges)

## Recreating in TypeScript and Node.js

### Project Setup

#### 1. Initialize Project
```bash
mkdir payment-service-ts
cd payment-service-ts
npm init -y
```

#### 2. Install Dependencies
```bash
# Core dependencies
npm install express mongoose stripe cors
npm install passport jsonwebtoken joi
npm install swagger-jsdoc swagger-ui-express
npm install bunyan dotenv uuid moment lodash

# TypeScript dependencies
npm install -D typescript @types/node @types/express
npm install -D @types/mongoose @types/cors @types/passport
npm install -D @types/jsonwebtoken @types/joi
npm install -D ts-node nodemon
```

#### 3. TypeScript Configuration
Create `tsconfig.json`:
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "test"]
}
```

### Project Structure in TypeScript

```
src/
├── app.ts                    # Main application
├── server.ts                 # Server entry point
├── types/                    # TypeScript type definitions
│   ├── express.d.ts
│   ├── user.ts
│   ├── payment.ts
│   └── subscription.ts
├── routes/                   # Route handlers
│   ├── paymentMethods.ts
│   ├── subscriptions.ts
│   ├── paymentIntents.ts
│   └── admin/
├── controllers/              # Business logic controllers
├── services/                 # External service integrations
│   ├── StripeService.ts
│   ├── IPGService.ts
│   └── EmailService.ts
├── models/                   # Database models
│   ├── User.ts
│   ├── Subscription.ts
│   └── PaymentMethod.ts
├── middleware/               # Custom middleware
│   ├── auth.ts
│   ├── errorHandler.ts
│   └── validation.ts
├── config/                   # Configuration
│   ├── database.ts
│   ├── stripe.ts
│   └── swagger.ts
└── utils/                    # Utility functions
    ├── logger.ts
    └── errors.ts
```

### Key Implementation Patterns

#### 1. Type Definitions
```typescript
// types/user.ts
export interface User {
  id: string;
  name: string;
  email: string;
  customerId?: string;
}

// types/payment.ts
export interface PaymentMethod {
  id: string;
  name: string;
  brand: string;
  type: string;
  isDefault: boolean;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'success' | 'failed';
  clientSecret?: string;
}
```

#### 2. Express Route with TypeScript
```typescript
// routes/paymentMethods.ts
import { Router, Request, Response } from 'express';
import { AuthenticatedRequest } from '../types/express';
import { PaymentMethodService } from '../services/PaymentMethodService';

const router = Router();

router.get('/', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const paymentMethods = await PaymentMethodService.getUserPaymentMethods(
      req.user.id
    );
    res.json(paymentMethods);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
```

#### 3. Service Layer Pattern
```typescript
// services/StripeService.ts
import Stripe from 'stripe';
import { PaymentIntent, PaymentMethod } from '../types/payment';

export class StripeService {
  private stripe: Stripe;

  constructor(secretKey: string) {
    this.stripe = new Stripe(secretKey, {
      apiVersion: '2020-08-27'
    });
  }

  async createPaymentIntent(params: {
    amount: number;
    currency: string;
    customerId: string;
  }): Promise<PaymentIntent> {
    const intent = await this.stripe.paymentIntents.create({
      amount: params.amount,
      currency: params.currency,
      customer: params.customerId
    });

    return {
      id: intent.id,
      amount: intent.amount,
      currency: intent.currency,
      status: intent.status as any,
      clientSecret: intent.client_secret || undefined
    };
  }
}
```

### Development Recommendations

#### 1. Error Handling
- Implement centralized error handling middleware
- Use custom error classes with proper HTTP status codes
- Log errors with structured logging (Winston/Bunyan)

#### 2. Validation
- Use Joi or Yup for request validation
- Implement middleware for automatic validation
- Type-safe validation schemas

#### 3. Testing Strategy
- Unit tests for services and utilities
- Integration tests for API endpoints
- Mock external services (Stripe) in tests

#### 4. Security Considerations
- Implement rate limiting
- Use helmet.js for security headers
- Validate webhook signatures
- Sanitize user inputs

#### 5. Deployment
- Use Docker for containerization
- Implement CI/CD pipeline
- Environment-specific configurations
- Health check endpoints

This documentation provides a comprehensive guide for understanding and recreating the ShoutOut Payment Service using TypeScript and Node.js, maintaining the same functionality while leveraging TypeScript's type safety and modern development practices.