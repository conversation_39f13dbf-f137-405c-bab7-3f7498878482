# Message Service REST API Design

## Overview
This document outlines the REST API architecture for the outbound message service, transforming the existing SQS-based system into a RESTful API.

## Core Resources

### 1. Messages
- **POST /api/v1/messages** - Send single message
- **POST /api/v1/messages/bulk** - Send bulk messages
- **GET /api/v1/messages/{messageId}** - Get message status
- **GET /api/v1/messages** - List messages with filters

### 2. Campaigns
- **POST /api/v1/campaigns** - Create campaign
- **GET /api/v1/campaigns/{campaignId}** - Get campaign details
- **GET /api/v1/campaigns** - List campaigns
- **PUT /api/v1/campaigns/{campaignId}** - Update campaign

### 3. Providers
- **GET /api/v1/providers** - List available providers
- **GET /api/v1/providers/{providerId}/status** - Get provider health

## API Endpoints Detail

### Send Single Message
```
POST /api/v1/messages
Content-Type: application/json

{
  "transport": "sms|email|facebook_messenger|push",
  "provider": "ETISALATSL|HUTCH|SENDGRID|...",
  "from": "sender_id",
  "to": "recipient",
  "message": "message content",
  "subject": "subject (email/push only)",
  "ownerId": "owner_id",
  "contactId": "contact_id",
  "campaignId": "campaign_id (optional)"
}

Response:
{
  "success": true,
  "messageId": "unique_message_id",
  "status": "queued|sent|failed",
  "providerId": "provider_message_id"
}
```

### Send Bulk Messages
```
POST /api/v1/messages/bulk
Content-Type: application/json

{
  "messages": [
    {
      "transport": "sms",
      "provider": "ETISALATSL",
      "from": "ShoutOUT",
      "to": "+94771234567",
      "message": "Hello World",
      "ownerId": "owner123",
      "contactId": "contact123"
    }
  ]
}

Response:
{
  "success": true,
  "batchId": "batch_uuid",
  "totalMessages": 100,
  "acceptedMessages": 98,
  "rejectedMessages": 2,
  "messages": [
    {
      "messageId": "msg_id_1",
      "status": "queued",
      "index": 0
    }
  ]
}
```

### Get Message Status
```
GET /api/v1/messages/{messageId}

Response:
{
  "messageId": "msg_id_1",
  "transport": "sms",
  "provider": "ETISALATSL",
  "status": "sent",
  "sentOn": "2024-08-11T15:30:00Z",
  "providerId": "provider_msg_id",
  "providerResponse": {...}
}
```

## Transport Types Supported

### SMS
- **Providers**: ETISALATSL, HUTCH, DIALOGSL, NEXMO, TWILIO, ROUTESMS
- **Required fields**: to (mobile_number), message, from
- **Rate limiting**: Per provider TPS limits

### Email
- **Providers**: SENDGRID, ELASTIC_EMAIL, SES
- **Required fields**: to, subject, message, from
- **Optional fields**: cc, bcc, htmlType

### Push Notifications
- **Providers**: SNS, FCM
- **Required fields**: to, subject, message
- **Platform specific**: iOS/Android configurations

### Facebook Messenger
- **Providers**: FACEBOOK
- **Required fields**: facebook_id (to), message

## Error Handling

### HTTP Status Codes
- **200 OK** - Success
- **201 Created** - Resource created
- **400 Bad Request** - Invalid request data
- **401 Unauthorized** - Authentication required
- **403 Forbidden** - Insufficient permissions
- **404 Not Found** - Resource not found
- **429 Too Many Requests** - Rate limit exceeded
- **500 Internal Server Error** - Server error
- **503 Service Unavailable** - Provider unavailable

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "INVALID_TRANSPORT",
    "message": "Unsupported transport type",
    "details": {
      "field": "transport",
      "value": "invalid_transport"
    }
  }
}
```

## Authentication & Authorization
- **API Key authentication** via Authorization header
- **Owner-based access control** - users can only access their messages
- **Rate limiting** per API key and per provider

## Rate Limiting
- **Global rate limits** per API key
- **Provider-specific limits** (inherited from current system)
- **Transport-specific limits**
- **Headers**: X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset

## Webhook Support
```
POST /api/v1/webhooks
{
  "url": "https://your-app.com/webhook",
  "events": ["message.sent", "message.failed", "campaign.completed"]
}
```

## Message Status Values
- **queued** - Message accepted and queued
- **sent** - Successfully sent to provider
- **delivered** - Delivered to recipient (if supported by provider)
- **failed** - Sending failed
- **invalid_transport** - Unsupported transport
- **invalid_provider** - Provider not available
- **internal_error** - System error

## Monitoring & Analytics
- **GET /api/v1/stats/messages** - Message statistics
- **GET /api/v1/stats/campaigns** - Campaign analytics
- **GET /api/v1/health** - Service health check

## Migration Strategy
1. **Phase 1**: Implement REST API alongside existing SQS consumers
2. **Phase 2**: Route REST API requests through existing message processing pipeline
3. **Phase 3**: Gradually migrate clients from SQS to REST API
4. **Phase 4**: Deprecate SQS interface (optional)