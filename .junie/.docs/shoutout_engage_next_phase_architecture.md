# ShoutOut Engage Next Phase Architecture Plan

## Overview

This document outlines the comprehensive architecture plan for developing the next phase of ShoutOut Engage, including four new microservices and enhancements to the existing core service. The design follows the established patterns from the current `shoutout_engage_core_service` and integrates the requirements from the provided documentation.

## Architecture Principles

- **Microservices Architecture**: Each service handles specific business domains
- **Consistent Patterns**: Follow existing `shoutout_engage_core_service` patterns
- **Redis Bull MQ**: Shared message queuing system between services
- **MongoDB + Supabase**: Hybrid database approach (MongoDB for operational data, Supabase for user management)
- **TypeScript-first**: Strong typing throughout all services
- **Supabase JWT Authentication**: Consistent authentication across services
- **Centralized Error Handling**: Standardized error responses

## Microservices to Develop

### 1. shoutout_engage_campaign_service
**Purpose**: Campaign creation, management, segment targeting, and campaign building process

### 2. shoutout_engage_message_service  
**Purpose**: Outbound message processing, provider management, and message delivery

### 3. shoutout_engage_payment_service
**Purpose**: Payment processing, subscriptions, billing, and financial transactions

### 4. Enhanced shoutout_engage_core_service
**Purpose**: Add sender ID management functionality to existing core service

## Inter-Service Communication

### Message Queue Architecture
- **Shared Redis Instance**: All services connect to the same Redis cluster
- **Dedicated Queues**: Each service has its own queue namespace
- **System Message Queue**: Separate queue for system notifications (email, SMS alerts)
- **Cross-Service Communication**: Services communicate via Redis queues

### Queue Structure
```
Redis Queues:
├── campaign.build.queue          # Campaign building process
├── campaign.send.queue           # Campaign sending initiation
├── message.outbound.queue        # Outbound message processing
├── message.system.queue          # System notifications
├── payment.process.queue         # Payment processing
├── payment.subscription.queue    # Subscription management
└── core.notification.queue       # Core service notifications
```

## Database Architecture

### MongoDB Collections (Operational Data)
```
Campaign Service:
├── campaigns
├── campaign_segments
├── campaign_logs
├── campaign_schedules
└── campaign_templates

Message Service:
├── message_logs
├── message_providers
├── message_templates
├── inbound_logs
└── provider_configurations

Payment Service:
├── subscriptions
├── payment_methods
├── invoices
├── transactions
└── billing_history

Core Service (Enhanced):
├── contacts (existing)
├── contact_activities (existing)
├── jobs (existing)
├── sender_ids (new)
├── sender_artifacts (new)
└── phone_numbers (new)
```

### Supabase Tables (User Management)
```
Existing:
├── users
├── organizations
└── profiles

New/Enhanced:
├── organization_settings
├── user_permissions
└── service_subscriptions
```

## Service-Specific Architecture

### 1. Campaign Service Architecture

#### Core Components
- **Campaign Builder**: Creates and validates campaigns
- **Segment Processor**: Handles contact segmentation
- **Schedule Manager**: Manages campaign scheduling
- **Template Engine**: Processes message templates

#### Key Features
- Contact-based campaigns
- Segment-based campaigns  
- Campaign scheduling
- Template management
- A/B testing support
- Campaign analytics

#### API Endpoints
```
POST   /api/campaigns                    # Create campaign
GET    /api/campaigns                    # List campaigns
GET    /api/campaigns/{id}               # Get campaign details
PUT    /api/campaigns/{id}               # Update campaign
DELETE /api/campaigns/{id}               # Delete campaign
POST   /api/campaigns/{id}/build         # Build campaign
POST   /api/campaigns/{id}/schedule      # Schedule campaign
POST   /api/campaigns/{id}/send          # Send campaign immediately
GET    /api/campaigns/{id}/analytics     # Campaign analytics
POST   /api/segments                     # Create segment
GET    /api/segments                     # List segments
GET    /api/segments/{id}/contacts       # Get segment contacts
```

### 2. Message Service Architecture

#### Core Components
- **Message Router**: Routes messages to appropriate providers
- **Provider Manager**: Manages SMS, Email, Push notification providers
- **Delivery Tracker**: Tracks message delivery status
- **Rate Limiter**: Handles provider rate limits

#### Key Features
- Multi-provider support (SMS, Email, Push, Facebook Messenger)
- Automatic failover between providers
- Rate limiting per provider
- Delivery status tracking
- Message templates
- Bulk message processing

#### API Endpoints
```
POST   /api/messages                     # Send single message
POST   /api/messages/bulk                # Send bulk messages
GET    /api/messages/{id}                # Get message status
GET    /api/messages                     # List messages
GET    /api/providers                    # List providers
GET    /api/providers/{id}/status        # Provider health
POST   /api/webhooks/delivery            # Delivery webhooks
GET    /api/stats/messages               # Message statistics
```

### 3. Payment Service Architecture

#### Core Components
- **Payment Processor**: Handles payment transactions
- **Subscription Manager**: Manages recurring subscriptions
- **Invoice Generator**: Creates and manages invoices
- **Billing Engine**: Handles billing cycles and calculations

#### Key Features
- Stripe integration
- Multiple payment methods
- Subscription management
- Invoice generation
- Payment history
- Webhook handling
- Multi-currency support

#### API Endpoints
```
GET    /api/payment-methods              # Get payment methods
POST   /api/payment-methods              # Add payment method
POST   /api/payment-intents              # Create payment intent
POST   /api/subscriptions                # Create subscription
GET    /api/subscriptions                # List subscriptions
PUT    /api/subscriptions/{id}           # Update subscription
DELETE /api/subscriptions/{id}           # Cancel subscription
GET    /api/invoices                     # List invoices
GET    /api/invoices/{id}                # Get invoice details
POST   /api/webhooks/stripe              # Stripe webhooks
```

### 4. Enhanced Core Service

#### New Features to Add
- **Sender ID Management**: SMS sender ID registration and approval
- **Phone Number Management**: Purchase and manage phone numbers
- **Email Sender Verification**: Email sender verification process
- **Artifact Management**: Handle sender ID documents and approvals

#### New API Endpoints
```
GET    /api/senders                      # Get sender IDs
POST   /api/senders                      # Create sender ID
DELETE /api/senders/{id}                 # Delete sender ID
POST   /api/senders/artifacts/generate   # Generate artifacts
POST   /api/senders/artifacts/upload     # Upload artifacts
GET    /api/numbers/available            # Available phone numbers
GET    /api/numbers                      # Purchased numbers
POST   /api/numbers                      # Purchase number
DELETE /api/numbers/{id}                 # Release number
GET    /api/emails/verify                # Verify email sender
```

## Implementation Phases

### Phase 1: Foundation Setup (Week 1-2)
1. Set up project structures for all four services
2. Configure shared Redis Bull MQ infrastructure
3. Set up MongoDB collections and indexes
4. Implement basic authentication middleware
5. Create shared TypeScript types and utilities

### Phase 2: Core Service Enhancement (Week 3-4)
1. Implement sender ID management functionality
2. Add phone number management features
3. Create email verification system
4. Implement artifact upload and management
5. Add comprehensive testing

### Phase 3: Campaign Service Development (Week 5-7)
1. Implement campaign CRUD operations
2. Build segment management system
3. Create campaign building process
4. Implement scheduling functionality
5. Add campaign analytics
6. Integrate with message service queues

### Phase 4: Message Service Development (Week 8-10)
1. Implement message routing system
2. Add provider management
3. Create delivery tracking
4. Implement rate limiting
5. Add bulk message processing
6. Create webhook handlers

### Phase 5: Payment Service Development (Week 11-13)
1. Implement Stripe integration
2. Add subscription management
3. Create invoice system
4. Implement payment processing
5. Add webhook handling
6. Create billing reports

### Phase 6: Integration & Testing (Week 14-15)
1. End-to-end integration testing
2. Performance optimization
3. Security audit
4. Documentation completion
5. Deployment preparation

## Technology Stack

### Backend Framework
- **Node.js** with **Express.js**
- **TypeScript** for type safety
- **MongoDB** with **Mongoose ODM**
- **Supabase** for user management
- **Redis Bull MQ** for job processing

### Key Dependencies
```json
{
  "express": "^4.18.0",
  "mongoose": "^7.0.0",
  "@supabase/supabase-js": "^2.0.0",
  "bull": "^4.10.0",
  "stripe": "^12.0.0",
  "joi": "^17.9.0",
  "jsonwebtoken": "^9.0.0",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "winston": "^3.8.0"
}
```

### Development Tools
- **Jest** for testing
- **ESLint** + **Prettier** for code quality
- **Swagger** for API documentation
- **Docker** for containerization
- **GitHub Actions** for CI/CD

## Security Considerations

### Authentication & Authorization
- Supabase JWT token validation
- Role-based access control
- API key authentication for service-to-service communication
- Rate limiting per user/organization

### Data Protection
- Input validation using Joi schemas
- SQL injection prevention
- XSS protection with helmet
- Sensitive data encryption
- Audit logging for all operations

### Payment Security
- PCI DSS compliance considerations
- Stripe webhook signature verification
- Secure payment method storage
- Transaction logging and monitoring

## Monitoring & Observability

### Logging Strategy
- Structured logging with Winston
- Correlation IDs for request tracking
- Error tracking and alerting
- Performance metrics collection

### Health Checks
- Service health endpoints
- Database connectivity checks
- Redis queue health monitoring
- External service dependency checks

### Metrics & Analytics
- Campaign performance metrics
- Message delivery rates
- Payment success rates
- System performance monitoring

## Deployment Architecture

### Container Strategy
- Docker containers for each service
- Docker Compose for local development
- Kubernetes for production deployment

### Environment Configuration
```yaml
Development:
  - Local MongoDB instance
  - Local Redis instance
  - Supabase development project
  
Staging:
  - MongoDB Atlas cluster
  - Redis Cloud instance
  - Supabase staging project
  
Production:
  - MongoDB Atlas production cluster
  - Redis Cloud production instance
  - Supabase production project
```

This architecture provides a solid foundation for building a scalable, maintainable, and feature-rich messaging platform while maintaining consistency with existing patterns and ensuring proper separation of concerns across microservices.
