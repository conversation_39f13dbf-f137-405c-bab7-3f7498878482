# Message Service - POST /webhooks (Provider Webhook Handling)

## Task Overview
**Endpoint**: `POST /api/message/v1/webhooks/{provider}`  
**Purpose**: Handle incoming webhooks from messaging providers (Twilio, SendGrid, Firebase) for delivery status updates  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Message service setup, provider integrations, queue processing  

## API Specification

### Twilio SMS Webhook
```http
POST /api/message/v1/webhooks/twilio
Content-Type: application/x-www-form-urlencoded
X-Twilio-Signature: <signature>

AccountSid=ACxxxxx&MessageSid=SMxxxxx&MessageStatus=delivered&To=%2B*********0&From=%2B0987654321&ApiVersion=2010-04-01
```

### SendGrid Email Webhook  
```http
POST /api/message/v1/webhooks/sendgrid
Content-Type: application/json
Authorization: Bearer <webhook_token>

[
  {
    "email": "<EMAIL>",
    "timestamp": **********,
    "smtp-id": "<14c5d75ce93.dfd.64b469@ismtpd-555>",
    "event": "delivered",
    "category": "cat facts",
    "sg_event_id": "sg_event_id",
    "sg_message_id": "sg_message_id"
  }
]
```

### Firebase Push Webhook
```http
POST /api/message/v1/webhooks/firebase
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "message_id": "0:*********0123456%31bd1c9431bd1c94",
  "from": "*********",
  "category": "com.example.app",
  "data": {
    "status": "delivered",
    "timestamp": "2024-08-11T15:30:00Z",
    "error_code": null
  }
}
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "webhook_id": "wh_64a1b2c3d4e5f6789012346",
    "provider": "twilio",
    "events_processed": 1,
    "messages_updated": 1,
    "processing_time": 45
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Implementation Tasks

### 1. Database Models (45 minutes)
```typescript
// src/lib/db/models/webhook-log.model.ts
import { Schema, model } from 'mongoose';

const webhookLogSchema = new Schema({
  webhook_id: { type: String, required: true, unique: true },
  provider: { 
    type: String, 
    enum: ['twilio', 'sendgrid', 'firebase', 'custom'],
    required: true 
  },
  
  // Raw webhook data
  raw_payload: Schema.Types.Mixed,
  headers: Schema.Types.Mixed,
  
  // Processing details
  events_processed: { type: Number, default: 0 },
  messages_updated: { type: Number, default: 0 },
  processing_status: { 
    type: String, 
    enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'],
    default: 'PENDING'
  },
  processing_time: Number, // milliseconds
  
  // Error handling
  error_details: {
    error_type: String,
    error_message: String,
    stack_trace: String,
    retry_count: { type: Number, default: 0 }
  },
  
  // Validation
  signature_valid: { type: Boolean, default: false },
  ip_address: String,
  user_agent: String,
  
  // Webhook events extracted
  webhook_events: [{
    message_id: String,
    external_id: String, // Provider's message ID
    event_type: { 
      type: String, 
      enum: ['sent', 'delivered', 'failed', 'bounced', 'opened', 'clicked', 'unsubscribed']
    },
    timestamp: Date,
    status_details: Schema.Types.Mixed,
    error_code: String,
    error_message: String
  }]
}, { 
  timestamps: true,
  collection: 'webhook_logs'
});

// Indexes for efficient querying
webhookLogSchema.index({ provider: 1, processing_status: 1 });
webhookLogSchema.index({ 'webhook_events.message_id': 1 });
webhookLogSchema.index({ created_at: -1 });

export const WebhookLogModel = model('WebhookLog', webhookLogSchema);
```

### 2. Webhook Processors (1.5 hours)
```typescript
// src/services/webhook-processors/twilio.processor.ts
import crypto from 'crypto';
import { WebhookEvent, ProcessedWebhookResult } from '@/types/webhook.types';

export class TwilioWebhookProcessor {
  static validateSignature(payload: string, signature: string, authToken: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha1', authToken)
      .update(Buffer.from(payload, 'utf-8'))
      .digest('base64');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'base64'),
      Buffer.from(expectedSignature, 'base64')
    );
  }

  static processWebhook(payload: any, headers: any): ProcessedWebhookResult {
    const events: WebhookEvent[] = [];
    
    try {
      // Parse Twilio webhook format
      const event: WebhookEvent = {
        message_id: null, // Will be resolved from MessageSid
        external_id: payload.MessageSid,
        event_type: this.mapTwilioStatus(payload.MessageStatus),
        timestamp: new Date(),
        status_details: {
          to: payload.To,
          from: payload.From,
          account_sid: payload.AccountSid,
          api_version: payload.ApiVersion,
          error_code: payload.ErrorCode,
          error_message: payload.ErrorMessage
        },
        error_code: payload.ErrorCode || null,
        error_message: payload.ErrorMessage || null
      };

      events.push(event);

      return {
        success: true,
        events,
        provider: 'twilio',
        events_count: events.length
      };

    } catch (error) {
      return {
        success: false,
        events: [],
        provider: 'twilio',
        events_count: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private static mapTwilioStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'accepted': 'sent',
      'queued': 'sent',
      'sending': 'sent',
      'sent': 'sent',
      'delivered': 'delivered',
      'failed': 'failed',
      'undelivered': 'failed'
    };

    return statusMap[status.toLowerCase()] || 'failed';
  }
}

// src/services/webhook-processors/sendgrid.processor.ts
export class SendGridWebhookProcessor {
  static validateSignature(payload: string, signature: string, publicKey: string): boolean {
    // Implement SendGrid signature validation
    // This is a simplified version - actual implementation would use SendGrid's verification
    return true; // Placeholder
  }

  static processWebhook(payload: any[], headers: any): ProcessedWebhookResult {
    const events: WebhookEvent[] = [];
    
    try {
      for (const eventData of payload) {
        const event: WebhookEvent = {
          message_id: null, // Will be resolved from sg_message_id
          external_id: eventData.sg_message_id,
          event_type: this.mapSendGridEvent(eventData.event),
          timestamp: new Date(eventData.timestamp * 1000),
          status_details: {
            email: eventData.email,
            smtp_id: eventData['smtp-id'],
            category: eventData.category,
            sg_event_id: eventData.sg_event_id,
            reason: eventData.reason,
            url: eventData.url
          },
          error_code: eventData.reason || null,
          error_message: eventData.reason || null
        };

        events.push(event);
      }

      return {
        success: true,
        events,
        provider: 'sendgrid',
        events_count: events.length
      };

    } catch (error) {
      return {
        success: false,
        events: [],
        provider: 'sendgrid',
        events_count: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private static mapSendGridEvent(event: string): string {
    const eventMap: { [key: string]: string } = {
      'delivered': 'delivered',
      'bounce': 'bounced',
      'dropped': 'failed',
      'deferred': 'failed',
      'processed': 'sent',
      'open': 'opened',
      'click': 'clicked',
      'unsubscribe': 'unsubscribed'
    };

    return eventMap[event.toLowerCase()] || 'failed';
  }
}

// src/services/webhook-processors/firebase.processor.ts
export class FirebaseWebhookProcessor {
  static validateToken(token: string, expectedToken: string): boolean {
    return crypto.timingSafeEqual(
      Buffer.from(token),
      Buffer.from(expectedToken)
    );
  }

  static processWebhook(payload: any, headers: any): ProcessedWebhookResult {
    const events: WebhookEvent[] = [];
    
    try {
      const event: WebhookEvent = {
        message_id: null, // Will be resolved from message_id mapping
        external_id: payload.message_id,
        event_type: this.mapFirebaseStatus(payload.data.status),
        timestamp: new Date(payload.data.timestamp),
        status_details: {
          from: payload.from,
          category: payload.category,
          error_code: payload.data.error_code
        },
        error_code: payload.data.error_code || null,
        error_message: payload.data.error_message || null
      };

      events.push(event);

      return {
        success: true,
        events,
        provider: 'firebase',
        events_count: events.length
      };

    } catch (error) {
      return {
        success: false,
        events: [],
        provider: 'firebase',
        events_count: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private static mapFirebaseStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'delivered': 'delivered',
      'failed': 'failed',
      'sent': 'sent'
    };

    return statusMap[status.toLowerCase()] || 'failed';
  }
}
```

### 3. Main Webhook Handler (1 hour)
```typescript
// src/handlers/webhook.handler.ts
import { Request, Response } from 'express';
import { WebhookLogModel } from '@/lib/db/models/webhook-log.model';
import { MessageDAO } from '@/lib/db/dao/message.dao';
import { TwilioWebhookProcessor } from '@/services/webhook-processors/twilio.processor';
import { SendGridWebhookProcessor } from '@/services/webhook-processors/sendgrid.processor';
import { FirebaseWebhookProcessor } from '@/services/webhook-processors/firebase.processor';
import { QueueService } from '@/services/queue.service';
import { generateWebhookId } from '@/lib/utils/id-generator';
import { handleError } from '@/lib/errors/error-handler';
import { ValidationError } from '@/lib/errors/error-types';

export class WebhookHandler {
  static async handleProviderWebhook(req: Request, res: Response) {
    const startTime = Date.now();
    const { provider } = req.params;
    const webhookId = generateWebhookId();

    try {
      // Create initial webhook log
      const webhookLog = new WebhookLogModel({
        webhook_id: webhookId,
        provider,
        raw_payload: req.body,
        headers: req.headers,
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        processing_status: 'PROCESSING'
      });

      await webhookLog.save();

      // Process webhook based on provider
      let processingResult;
      let signatureValid = false;

      switch (provider.toLowerCase()) {
        case 'twilio':
          signatureValid = TwilioWebhookProcessor.validateSignature(
            JSON.stringify(req.body),
            req.get('X-Twilio-Signature') || '',
            process.env.TWILIO_AUTH_TOKEN || ''
          );
          processingResult = TwilioWebhookProcessor.processWebhook(req.body, req.headers);
          break;

        case 'sendgrid':
          signatureValid = SendGridWebhookProcessor.validateSignature(
            JSON.stringify(req.body),
            req.get('Authorization') || '',
            process.env.SENDGRID_PUBLIC_KEY || ''
          );
          processingResult = SendGridWebhookProcessor.processWebhook(req.body, req.headers);
          break;

        case 'firebase':
          signatureValid = FirebaseWebhookProcessor.validateToken(
            req.get('Authorization')?.replace('Bearer ', '') || '',
            process.env.FIREBASE_WEBHOOK_TOKEN || ''
          );
          processingResult = FirebaseWebhookProcessor.processWebhook(req.body, req.headers);
          break;

        default:
          throw new ValidationError(`Unsupported provider: ${provider}`, 'INVALID_PROVIDER');
      }

      if (!signatureValid) {
        throw new ValidationError('Invalid webhook signature', 'INVALID_SIGNATURE');
      }

      if (!processingResult.success) {
        throw new Error(processingResult.error || 'Webhook processing failed');
      }

      // Update message statuses
      let messagesUpdated = 0;
      for (const event of processingResult.events) {
        try {
          // Resolve message ID from external ID
          const message = await MessageDAO.getMessageByExternalId(event.external_id, provider);
          if (message) {
            await MessageDAO.updateMessageStatus(message.message_id, {
              status: event.event_type,
              status_details: event.status_details,
              error_code: event.error_code,
              error_message: event.error_message,
              updated_at: event.timestamp
            });

            messagesUpdated++;

            // Queue analytics update
            await QueueService.addJob('message-analytics-update', {
              message_id: message.message_id,
              event_type: event.event_type,
              timestamp: event.timestamp
            });
          }
        } catch (error) {
          console.error('Error updating message status:', error);
        }
      }

      // Update webhook log with results
      const processingTime = Date.now() - startTime;
      await WebhookLogModel.findOneAndUpdate(
        { webhook_id: webhookId },
        {
          webhook_events: processingResult.events,
          events_processed: processingResult.events_count,
          messages_updated: messagesUpdated,
          processing_status: 'COMPLETED',
          processing_time: processingTime,
          signature_valid: signatureValid
        }
      );

      // Response
      res.status(200).json({
        success: true,
        data: {
          webhook_id: webhookId,
          provider,
          events_processed: processingResult.events_count,
          messages_updated: messagesUpdated,
          processing_time: processingTime
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      // Update webhook log with error
      const processingTime = Date.now() - startTime;
      await WebhookLogModel.findOneAndUpdate(
        { webhook_id: webhookId },
        {
          processing_status: 'FAILED',
          processing_time: processingTime,
          error_details: {
            error_type: error instanceof Error ? error.constructor.name : 'UnknownError',
            error_message: error instanceof Error ? error.message : 'Unknown error occurred',
            stack_trace: error instanceof Error ? error.stack : undefined,
            retry_count: 0
          }
        }
      );

      handleError(error, res);
    }
  }

  static async getWebhookLogs(req: Request, res: Response) {
    try {
      const { provider, status, page = 1, limit = 20 } = req.query;
      
      const query: any = {};
      if (provider) query.provider = provider;
      if (status) query.processing_status = status;

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      
      const [logs, total] = await Promise.all([
        WebhookLogModel
          .find(query)
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(parseInt(limit as string))
          .select('-raw_payload -headers') // Exclude sensitive data
          .lean(),
        WebhookLogModel.countDocuments(query)
      ]);

      res.status(200).json({
        success: true,
        data: {
          logs,
          pagination: {
            current_page: parseInt(page as string),
            per_page: parseInt(limit as string),
            total_pages: Math.ceil(total / parseInt(limit as string)),
            total_count: total
          }
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (15 minutes)
```typescript
// src/routes/webhook.routes.ts
import { Router } from 'express';
import { WebhookHandler } from '@/handlers/webhook.handler';
import { webhookRateLimitMiddleware } from '@/lib/middlewares/rate-limit.middleware';
import { validateWebhookProvider } from '@/validators/webhook.validator';

const router = Router();

// Provider webhook endpoints (no auth required for incoming webhooks)
router.post(
  '/:provider',
  webhookRateLimitMiddleware,
  validateWebhookProvider,
  WebhookHandler.handleProviderWebhook
);

// Webhook logs (requires authentication)
router.get(
  '/logs',
  supabaseAuthMiddleware,
  WebhookHandler.getWebhookLogs
);

export { router as webhookRoutes };
```

### 5. Validation and Middleware (30 minutes)
```typescript
// src/validators/webhook.validator.ts
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from '@/lib/errors/error-types';

const supportedProviders = ['twilio', 'sendgrid', 'firebase'];

export const validateWebhookProvider = (req: Request, res: Response, next: NextFunction) => {
  const { provider } = req.params;
  
  if (!provider || !supportedProviders.includes(provider.toLowerCase())) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'INVALID_PROVIDER',
        message: `Unsupported provider: ${provider}. Supported providers: ${supportedProviders.join(', ')}`,
        code: 400
      }
    });
  }

  next();
};

// src/lib/middlewares/rate-limit.middleware.ts
import rateLimit from 'express-rate-limit';

export const webhookRateLimitMiddleware = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 1000, // 1000 requests per minute per IP
  message: {
    success: false,
    error: {
      type: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many webhook requests, please try again later',
      code: 429
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});
```

### 6. Queue Integration (30 minutes)
```typescript
// src/services/queue.service.ts (extend existing)
export class QueueService {
  // ... existing methods ...

  static async addWebhookRetryJob(webhookId: string, delay: number = 60000) {
    try {
      await this.webhookRetryQueue.add('retry-webhook', {
        webhook_id: webhookId
      }, {
        delay,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 30000
        }
      });
    } catch (error) {
      console.error('Failed to add webhook retry job:', error);
    }
  }

  static async processWebhookRetry(job: any) {
    const { webhook_id } = job.data;
    
    try {
      const webhookLog = await WebhookLogModel.findOne({ webhook_id });
      if (!webhookLog || webhookLog.processing_status === 'COMPLETED') {
        return;
      }

      // Retry webhook processing
      // Implementation would re-process the webhook events
      console.log(`Retrying webhook: ${webhook_id}`);
      
    } catch (error) {
      console.error(`Webhook retry failed for ${webhook_id}:`, error);
      throw error;
    }
  }
}
```

## Error Handling

### Common Errors
- **400 Invalid Provider**: Unsupported webhook provider
- **401 Invalid Signature**: Webhook signature validation failed
- **422 Invalid Payload**: Malformed webhook payload
- **429 Rate Limited**: Too many webhook requests
- **500 Processing Failed**: Internal error during webhook processing

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "INVALID_SIGNATURE",
    "message": "Webhook signature validation failed",
    "code": 401
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Security Considerations

1. **Signature Validation**: Verify webhook signatures from all providers
2. **Rate Limiting**: Prevent webhook spam and abuse
3. **IP Whitelisting**: Optionally restrict webhooks to provider IPs
4. **Request Size Limits**: Limit webhook payload sizes
5. **Logging**: Comprehensive logging for audit and debugging

## Monitoring and Analytics

### Metrics to Track
- Webhook processing success/failure rates
- Processing times by provider
- Message status update rates
- Provider-specific error patterns

### Alerts
- High webhook failure rates
- Signature validation failures
- Processing time anomalies
- Queue backup alerts