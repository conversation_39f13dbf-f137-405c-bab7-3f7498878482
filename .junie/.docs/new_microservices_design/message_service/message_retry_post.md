# Message Service - POST /messages/{id}/retry (Message Retry Functionality)

## Task Overview
**Endpoint**: `POST /api/message/v1/messages/{message_id}/retry`  
**Purpose**: Retry failed messages with configurable retry policies, exponential backoff, and failure analysis  
**Estimated Time**: 1 day  
**Priority**: High  
**Dependencies**: Message tracking, queue processing, provider management  

## API Specification

### Individual Message Retry Request
```http
POST /api/message/v1/messages/msg_64a1b2c3d4e5f6789012345/retry
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "retry_config": {
    "retry_type": "immediate",
    "max_attempts": 3,
    "override_provider": "twilio_backup"
  }
}
```

### Bulk Message Retry Request
```http
POST /api/message/v1/messages/retry/bulk
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "filters": {
    "status": "failed",
    "provider": "twilio",
    "error_codes": ["21211", "30008"],
    "date_range": {
      "start": "2024-08-10T00:00:00Z",
      "end": "2024-08-11T23:59:59Z"
    }
  },
  "retry_config": {
    "retry_type": "scheduled",
    "scheduled_at": "2024-08-12T09:00:00Z",
    "max_attempts": 2,
    "retry_policy": "exponential_backoff"
  }
}
```

### Response - Individual Retry
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "retry_id": "retry_64a1b2c3d4e5f6789012346",
    "message_id": "msg_64a1b2c3d4e5f6789012345",
    "retry_status": "queued",
    "retry_attempt": 2,
    "scheduled_at": "2024-08-11T15:35:00Z",
    "provider": "twilio_backup",
    "estimated_delivery": "2024-08-11T15:36:00Z"
  }
}
```

### Response - Bulk Retry
```http
HTTP/1.1 202 Accepted
Content-Type: application/json

{
  "success": true,
  "data": {
    "bulk_retry_id": "bulk_retry_64a1b2c3d4e5f6789012347",
    "status": "processing",
    "messages_found": 156,
    "messages_eligible": 134,
    "messages_already_retried": 22,
    "estimated_completion": "2024-08-11T16:00:00Z"
  }
}
```

## Implementation Tasks

### 1. Database Models (20 minutes)
```typescript
// src/lib/db/models/message-retry.model.ts
import { Schema, model } from 'mongoose';

const messageRetrySchema = new Schema({
  org_id: { type: String, required: true, index: true },
  retry_id: { type: String, required: true, unique: true },
  message_id: { type: String, required: true, index: true },
  
  // Retry configuration
  retry_attempt: { type: Number, required: true },
  max_attempts: { type: Number, default: 3 },
  retry_type: { 
    type: String, 
    enum: ['immediate', 'scheduled', 'exponential_backoff'],
    required: true 
  },
  
  // Scheduling
  scheduled_at: { type: Date, required: true },
  executed_at: Date,
  next_retry_at: Date,
  
  // Status tracking
  status: { 
    type: String, 
    enum: ['pending', 'queued', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  
  // Provider configuration
  original_provider: String,
  retry_provider: String,
  provider_override: Boolean,
  
  // Results
  retry_results: {
    success: Boolean,
    new_message_id: String,
    provider_response: Schema.Types.Mixed,
    error_code: String,
    error_message: String,
    delivery_time: Number
  },
  
  // Policy details
  retry_policy: {
    backoff_multiplier: { type: Number, default: 2 },
    base_delay: { type: Number, default: 60000 }, // milliseconds
    max_delay: { type: Number, default: 3600000 } // 1 hour max
  }
}, { 
  timestamps: true,
  collection: 'message_retries'
});

messageRetrySchema.index({ org_id: 1, status: 1 });
messageRetrySchema.index({ scheduled_at: 1, status: 1 });
messageRetrySchema.index({ message_id: 1 });

export const MessageRetryModel = model('MessageRetry', messageRetrySchema);
```

### 2. Retry Service Implementation (45 minutes)
```typescript
// src/services/message-retry.service.ts
import { MessageRetryModel } from '@/lib/db/models/message-retry.model';
import { MessageModel } from '@/lib/db/models/message.model';
import { QueueService } from '@/services/queue.service';
import { MessageService } from '@/services/message.service';
import { generateRetryId } from '@/lib/utils/id-generator';
import { ValidationError, NotFoundError } from '@/lib/errors/error-types';

export class MessageRetryService {
  static async retryMessage(orgId: string, messageId: string, retryConfig: any) {
    // Validate message exists and is eligible for retry
    const message = await MessageModel.findOne({
      org_id: orgId,
      message_id: messageId
    });

    if (!message) {
      throw new NotFoundError('Message not found', 'MESSAGE_NOT_FOUND');
    }

    if (!this.isEligibleForRetry(message)) {
      throw new ValidationError('Message is not eligible for retry', 'RETRY_NOT_ELIGIBLE');
    }

    // Create retry record
    const retryId = generateRetryId();
    const retryAttempt = (message.retry_count || 0) + 1;
    
    const scheduledAt = this.calculateScheduledTime(
      retryConfig.retry_type,
      retryAttempt,
      retryConfig.retry_policy
    );

    const retry = new MessageRetryModel({
      org_id: orgId,
      retry_id: retryId,
      message_id: messageId,
      retry_attempt: retryAttempt,
      max_attempts: retryConfig.max_attempts || 3,
      retry_type: retryConfig.retry_type || 'exponential_backoff',
      scheduled_at: scheduledAt,
      original_provider: message.provider,
      retry_provider: retryConfig.override_provider || message.provider,
      provider_override: Boolean(retryConfig.override_provider),
      status: 'queued'
    });

    await retry.save();

    // Queue retry job
    await QueueService.addJob('message-retry', {
      retry_id: retryId,
      org_id: orgId
    }, {
      delay: scheduledAt.getTime() - Date.now()
    });

    return {
      retry_id: retryId,
      message_id: messageId,
      retry_status: 'queued',
      retry_attempt: retryAttempt,
      scheduled_at: scheduledAt,
      provider: retry.retry_provider,
      estimated_delivery: new Date(scheduledAt.getTime() + 60000) // +1 minute
    };
  }

  static async bulkRetryMessages(orgId: string, filters: any, retryConfig: any) {
    // Find eligible messages
    const query: any = { org_id: orgId };
    
    if (filters.status) query.status = filters.status;
    if (filters.provider) query.provider = filters.provider;
    if (filters.error_codes) query.error_code = { $in: filters.error_codes };
    if (filters.date_range) {
      query.created_at = {
        $gte: new Date(filters.date_range.start),
        $lte: new Date(filters.date_range.end)
      };
    }

    const messages = await MessageModel.find(query);
    const eligibleMessages = messages.filter(msg => this.isEligibleForRetry(msg));
    
    // Create bulk retry operation
    const bulkRetryId = generateRetryId('bulk');
    
    // Process messages in batches
    const batchSize = 100;
    let processed = 0;
    
    for (let i = 0; i < eligibleMessages.length; i += batchSize) {
      const batch = eligibleMessages.slice(i, i + batchSize);
      
      await QueueService.addJob('bulk-message-retry', {
        bulk_retry_id: bulkRetryId,
        org_id: orgId,
        message_ids: batch.map(m => m.message_id),
        retry_config: retryConfig
      });
      
      processed += batch.length;
    }

    return {
      bulk_retry_id: bulkRetryId,
      status: 'processing',
      messages_found: messages.length,
      messages_eligible: eligibleMessages.length,
      messages_already_retried: messages.length - eligibleMessages.length,
      estimated_completion: new Date(Date.now() + (processed * 100)) // rough estimate
    };
  }

  private static isEligibleForRetry(message: any): boolean {
    // Check if message failed
    if (message.status !== 'failed') return false;
    
    // Check retry count
    const maxRetries = 5; // configurable
    if ((message.retry_count || 0) >= maxRetries) return false;
    
    // Check if already being retried
    if (message.retry_status === 'pending') return false;
    
    return true;
  }

  private static calculateScheduledTime(retryType: string, attempt: number, policy?: any): Date {
    const now = new Date();
    
    switch (retryType) {
      case 'immediate':
        return now;
        
      case 'scheduled':
        // Use provided schedule time or default to 5 minutes
        return policy?.scheduled_at ? new Date(policy.scheduled_at) : 
               new Date(now.getTime() + 5 * 60 * 1000);
               
      case 'exponential_backoff':
      default:
        const baseDelay = policy?.base_delay || 60000; // 1 minute
        const multiplier = policy?.backoff_multiplier || 2;
        const maxDelay = policy?.max_delay || 3600000; // 1 hour
        
        const delay = Math.min(baseDelay * Math.pow(multiplier, attempt - 1), maxDelay);
        return new Date(now.getTime() + delay);
    }
  }
}
```

### 3. Handler Implementation (30 minutes)
```typescript
// src/handlers/message-retry.handler.ts
import { Request, Response } from 'express';
import { MessageRetryService } from '@/services/message-retry.service';
import { handleError } from '@/lib/errors/error-handler';
import { OrganizationError } from '@/lib/errors/error-types';

export class MessageRetryHandler {
  static async retryMessage(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { message_id } = req.params;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const result = await MessageRetryService.retryMessage(
        orgId,
        message_id,
        req.body.retry_config || {}
      );

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async bulkRetryMessages(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const result = await MessageRetryService.bulkRetryMessages(
        orgId,
        req.body.filters,
        req.body.retry_config
      );

      res.status(202).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (10 minutes)
```typescript
// src/routes/message-retry.routes.ts
import { Router } from 'express';
import { MessageRetryHandler } from '@/handlers/message-retry.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateRetryMessage, validateBulkRetry } from '@/validators/message-retry.validator';

const router = Router();

// Individual message retry
router.post(
  '/:message_id/retry',
  supabaseAuthMiddleware,
  validateRetryMessage,
  MessageRetryHandler.retryMessage
);

// Bulk message retry
router.post(
  '/retry/bulk',
  supabaseAuthMiddleware,
  validateBulkRetry,
  MessageRetryHandler.bulkRetryMessages
);

export { router as messageRetryRoutes };
```

### 5. Validation Schema (15 minutes)
```typescript
// src/validators/message-retry.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const retryMessageSchema = Joi.object({
  retry_config: Joi.object({
    retry_type: Joi.string().valid('immediate', 'scheduled', 'exponential_backoff').default('exponential_backoff'),
    max_attempts: Joi.number().integer().min(1).max(10).default(3),
    override_provider: Joi.string().optional(),
    retry_policy: Joi.object({
      base_delay: Joi.number().integer().min(1000).max(3600000).optional(),
      backoff_multiplier: Joi.number().min(1).max(10).optional(),
      max_delay: Joi.number().integer().min(60000).max(86400000).optional()
    }).optional()
  }).optional()
});

const bulkRetrySchema = Joi.object({
  filters: Joi.object({
    status: Joi.string().valid('failed', 'pending').optional(),
    provider: Joi.string().optional(),
    error_codes: Joi.array().items(Joi.string()).optional(),
    date_range: Joi.object({
      start: Joi.date().iso().required(),
      end: Joi.date().iso().min(Joi.ref('start')).required()
    }).optional()
  }).min(1).required(),
  
  retry_config: Joi.object({
    retry_type: Joi.string().valid('immediate', 'scheduled', 'exponential_backoff').default('exponential_backoff'),
    scheduled_at: Joi.date().iso().min('now').optional(),
    max_attempts: Joi.number().integer().min(1).max(5).default(2),
    retry_policy: Joi.string().valid('exponential_backoff', 'linear', 'fixed').optional()
  }).optional()
});

export const validateRetryMessage = (req: Request, res: Response, next: NextFunction) => {
  const { error } = retryMessageSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};

export const validateBulkRetry = (req: Request, res: Response, next: NextFunction) => {
  const { error } = bulkRetrySchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};
```

## Error Handling

### Common Errors
- **404 Message Not Found**: Message doesn't exist
- **400 Retry Not Eligible**: Message cannot be retried
- **429 Rate Limited**: Too many retry requests
- **500 Retry Failed**: Retry processing failed

## Security Considerations

1. **Rate Limiting**: Prevent retry abuse
2. **Access Control**: Verify message ownership
3. **Audit Logging**: Track retry operations
4. **Cost Control**: Limit retry attempts
5. **Provider Management**: Secure provider switching