# Message Service - POST /messages/bulk (Send Bulk Messages)

## Task Overview
**Endpoint**: `POST /api/message/v1/messages/bulk`  
**Purpose**: Send multiple messages in a single request for efficient bulk processing  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Message service, provider implementations, queue processors  

## API Specification

### Request
```http
POST /api/message/v1/messages/bulk
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "messages": [
    {
      "transport": "SMS",
      "provider": "TWILIO",
      "from": "STORE",
      "to": "+94771234567",
      "content": "Your order #12345 is ready!",
      "priority": "HIGH",
      "metadata": {"order_id": "12345"}
    },
    {
      "transport": "EMAIL",
      "provider": "SENDGRID",
      "from": "<EMAIL>",
      "to": "<EMAIL>",
      "subject": "Order Update",
      "content": "Your order has been shipped",
      "priority": "NORMAL"
    }
  ],
  "campaign_id": "64a1b2c3d4e5f6789012346",
  "batch_config": {
    "batch_size": 100,
    "delay_between_batches_ms": 1000,
    "fail_fast": false
  }
}
```

### Response
```http
HTTP/1.1 202 Accepted
Content-Type: application/json

{
  "success": true,
  "data": {
    "batch_id": "batch_64a1b2c3d4e5f678901234c",
    "total_messages": 2,
    "queued_messages": 2,
    "failed_messages": 0,
    "estimated_cost": {
      "total": 0.07,
      "currency": "USD",
      "breakdown": {
        "SMS": 0.05,
        "EMAIL": 0.02
      }
    },
    "batch_status": "QUEUED",
    "tracking_url": "/api/message/v1/batches/batch_64a1b2c3d4e5f678901234c"
  }
}
```

## Implementation Tasks

### 1. Service Layer (60 minutes)
```typescript
// src/services/message.service.ts (addition)
export class MessageService {
  static async sendBulkMessages(
    orgId: string,
    bulkRequest: BulkMessageRequest
  ): Promise<BulkMessageResponse> {
    try {
      // Validate bulk request
      this.validateBulkRequest(bulkRequest);
      
      // Generate batch ID
      const batchId = this.generateBatchId();
      
      // Process messages and estimate costs
      const processedMessages = await this.processBulkMessages(
        orgId,
        bulkRequest.messages,
        bulkRequest.campaign_id,
        batchId
      );
      
      // Queue bulk processing job
      await this.queueBulkProcessing(batchId, processedMessages, bulkRequest.batch_config);
      
      return {
        batch_id: batchId,
        total_messages: processedMessages.length,
        queued_messages: processedMessages.filter(m => m.status === 'QUEUED').length,
        failed_messages: processedMessages.filter(m => m.status === 'FAILED').length,
        estimated_cost: this.calculateEstimatedCost(processedMessages),
        batch_status: 'QUEUED',
        tracking_url: `/api/message/v1/batches/${batchId}`
      };
    } catch (error) {
      throw new InternalError(`Failed to process bulk messages: ${error.message}`);
    }
  }

  private static validateBulkRequest(request: BulkMessageRequest): void {
    if (!request.messages || request.messages.length === 0) {
      throw new BadRequestError('Messages array cannot be empty', 'EMPTY_MESSAGES');
    }
    
    if (request.messages.length > 1000) {
      throw new BadRequestError('Maximum 1000 messages per bulk request', 'TOO_MANY_MESSAGES');
    }
  }

  private static generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

### 2. Controller Implementation (30 minutes)
```typescript
// src/controllers/message.controller.ts (addition)
export class MessageController {
  static async sendBulkMessages(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const result = await MessageService.sendBulkMessages(
        req.user.org_id,
        req.body
      );

      res.status(202).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 3. Validation Schema (25 minutes)
```typescript
// src/validators/message.validator.ts (addition)
export const bulkMessageSchema = Joi.object({
  messages: Joi.array().items(
    Joi.object({
      transport: Joi.string().valid('SMS', 'EMAIL', 'PUSH', 'FACEBOOK_MESSENGER').required(),
      provider: Joi.string().required(),
      from: Joi.string().required(),
      to: Joi.string().required(),
      content: Joi.string().max(10000).required(),
      subject: Joi.string().max(200).when('transport', {
        is: 'EMAIL',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH').default('NORMAL'),
      metadata: Joi.object().optional()
    })
  ).min(1).max(1000).required(),
  campaign_id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).optional(),
  batch_config: Joi.object({
    batch_size: Joi.number().min(1).max(100).default(50),
    delay_between_batches_ms: Joi.number().min(0).max(60000).default(1000),
    fail_fast: Joi.boolean().default(false)
  }).optional()
});
```

### 4. Route Definition (10 minutes)
```typescript
// src/routes/message.routes.ts (addition)
router.post(
  '/bulk',
  authenticateRequest,
  validateRequestBody(bulkMessageSchema),
  rateLimitMiddleware,
  MessageController.sendBulkMessages
);
```

## Testing Checklist
- [ ] Unit tests for bulk message processing
- [ ] Integration tests for bulk endpoint
- [ ] Validation tests
- [ ] Queue processing tests
- [ ] Error handling tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Bulk processing documentation
- [ ] Performance guidelines