# Message Service - GET /templates (Message Template Management)

## Task Overview
**Endpoint**: `GET /api/message/v1/templates`  
**Purpose**: Retrieve and manage message templates for SMS, email, and push notifications with variable support and versioning  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Template storage, variable processing, versioning system  

## API Specification

### List Templates Request
```http
GET /api/message/v1/templates?type=sms&category=transactional&page=1&limit=20
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Individual Template Request
```http
GET /api/message/v1/templates/tpl_64a1b2c3d4e5f6789012345
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Template Preview Request
```http
POST /api/message/v1/templates/tpl_64a1b2c3d4e5f6789012345/preview
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "variables": {
    "name": "<PERSON>",
    "code": "SAVE20",
    "amount": "25.99"
  }
}
```

### Query Parameters
- `type` (string): Filter by template type (sms, email, push)
- `category` (string): Filter by category (transactional, promotional, notification)
- `status` (string): Filter by status (active, draft, archived)
- `search` (string): Search in name and content
- `tags` (string): Comma-separated list of tags
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 50)
- `sort_by` (string): Sort field (name, created_at, last_used)
- `sort_order` (string): Sort order (asc, desc)

### Response - List Templates
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "templates": [
      {
        "template_id": "tpl_64a1b2c3d4e5f6789012345",
        "name": "Order Confirmation SMS",
        "type": "sms",
        "category": "transactional",
        "status": "active",
        "version": "1.2.0",
        "content": "Hi {{name}}, your order #{{order_id}} for ${{amount}} has been confirmed. Track: {{tracking_url}}",
        "variables": [
          {
            "name": "name",
            "type": "string",
            "required": true,
            "description": "Customer name"
          },
          {
            "name": "order_id",
            "type": "string",
            "required": true,
            "description": "Order ID"
          },
          {
            "name": "amount",
            "type": "currency",
            "required": true,
            "description": "Order amount"
          },
          {
            "name": "tracking_url",
            "type": "url",
            "required": false,
            "description": "Tracking URL"
          }
        ],
        "tags": ["order", "confirmation", "transactional"],
        "usage_stats": {
          "times_used": 1247,
          "last_used": "2024-08-11T10:30:00Z",
          "success_rate": 98.2
        },
        "validation": {
          "character_count": 89,
          "estimated_sms_parts": 1,
          "compliance_check": "passed"
        },
        "created_by": {
          "user_id": "usr_64a1b2c3d4e5f6789012345",
          "name": "Marketing Team"
        },
        "created_at": "2024-07-15T09:00:00Z",
        "updated_at": "2024-08-01T14:30:00Z"
      },
      {
        "template_id": "tpl_64a1b2c3d4e5f6789012346",
        "name": "Welcome Email",
        "type": "email",
        "category": "notification",
        "status": "active",
        "version": "2.0.0",
        "content": {
          "subject": "Welcome to {{company_name}}, {{name}}!",
          "html": "<!DOCTYPE html><html><body><h1>Welcome {{name}}!</h1><p>Thank you for joining {{company_name}}...</p></body></html>",
          "text": "Welcome {{name}}! Thank you for joining {{company_name}}..."
        },
        "variables": [
          {
            "name": "name",
            "type": "string",
            "required": true,
            "description": "User's name"
          },
          {
            "name": "company_name",
            "type": "string",
            "required": true,
            "description": "Company name"
          }
        ],
        "tags": ["welcome", "onboarding"],
        "usage_stats": {
          "times_used": 892,
          "last_used": "2024-08-11T12:15:00Z",
          "success_rate": 96.8
        },
        "validation": {
          "has_html": true,
          "has_text_fallback": true,
          "compliance_check": "passed"
        },
        "created_by": {
          "user_id": "usr_64a1b2c3d4e5f6789012346",
          "name": "Product Team"
        },
        "created_at": "2024-06-10T11:00:00Z",
        "updated_at": "2024-08-05T16:20:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_pages": 5,
      "total_count": 87
    },
    "filters_applied": {
      "type": "sms",
      "category": "transactional"
    }
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

### Response - Individual Template
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "template_id": "tpl_64a1b2c3d4e5f6789012345",
    "name": "Order Confirmation SMS",
    "type": "sms",
    "category": "transactional",
    "status": "active",
    "version": "1.2.0",
    "content": "Hi {{name}}, your order #{{order_id}} for ${{amount}} has been confirmed. Track: {{tracking_url}}",
    "variables": [
      {
        "name": "name",
        "type": "string",
        "required": true,
        "description": "Customer name",
        "validation": {
          "min_length": 1,
          "max_length": 100
        }
      },
      {
        "name": "order_id",
        "type": "string",
        "required": true,
        "description": "Order ID",
        "validation": {
          "pattern": "^[A-Z0-9]{8,12}$"
        }
      },
      {
        "name": "amount",
        "type": "currency",
        "required": true,
        "description": "Order amount",
        "validation": {
          "min_value": 0,
          "currency": "USD"
        }
      },
      {
        "name": "tracking_url",
        "type": "url",
        "required": false,
        "description": "Tracking URL",
        "default_value": "https://track.example.com"
      }
    ],
    "tags": ["order", "confirmation", "transactional"],
    "version_history": [
      {
        "version": "1.2.0",
        "created_at": "2024-08-01T14:30:00Z",
        "created_by": "usr_64a1b2c3d4e5f6789012345",
        "changes": ["Updated tracking URL variable", "Added currency formatting"]
      },
      {
        "version": "1.1.0",
        "created_at": "2024-07-20T10:15:00Z",
        "created_by": "usr_64a1b2c3d4e5f6789012345",
        "changes": ["Added optional tracking URL"]
      }
    ],
    "usage_stats": {
      "times_used": 1247,
      "last_used": "2024-08-11T10:30:00Z",
      "success_rate": 98.2,
      "total_recipients": 1224,
      "delivery_rate": 97.1
    },
    "validation": {
      "character_count": 89,
      "estimated_sms_parts": 1,
      "compliance_check": "passed",
      "variable_usage": {
        "required_variables": 3,
        "optional_variables": 1,
        "all_variables_defined": true
      }
    },
    "created_by": {
      "user_id": "usr_64a1b2c3d4e5f6789012345",
      "name": "Marketing Team",
      "email": "<EMAIL>"
    },
    "created_at": "2024-07-15T09:00:00Z",
    "updated_at": "2024-08-01T14:30:00Z"
  }
}
```

### Response - Template Preview
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "preview": {
      "rendered_content": "Hi John Doe, your order #ORD12345 for $25.99 has been confirmed. Track: https://track.example.com/ORD12345",
      "character_count": 95,
      "estimated_sms_parts": 1,
      "variables_used": {
        "name": "John Doe",
        "order_id": "ORD12345",
        "amount": "$25.99",
        "tracking_url": "https://track.example.com/ORD12345"
      },
      "validation_results": {
        "all_required_variables_provided": true,
        "character_limit_ok": true,
        "compliance_check": "passed"
      }
    }
  }
}
```

## Implementation Tasks

### 1. Database Model (30 minutes)
```typescript
// src/lib/db/models/message-template.model.ts
import { Schema, model } from 'mongoose';

const templateVariableSchema = new Schema({
  name: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['string', 'number', 'currency', 'url', 'date', 'boolean'],
    required: true 
  },
  required: { type: Boolean, default: false },
  description: String,
  default_value: String,
  validation: {
    min_length: Number,
    max_length: Number,
    pattern: String,
    min_value: Number,
    max_value: Number,
    currency: String
  }
});

const messageTemplateSchema = new Schema({
  org_id: { type: String, required: true, index: true },
  template_id: { type: String, required: true, unique: true },
  name: { type: String, required: true, trim: true },
  
  // Template type and categorization
  type: { 
    type: String, 
    enum: ['sms', 'email', 'push'],
    required: true 
  },
  category: { 
    type: String, 
    enum: ['transactional', 'promotional', 'notification', 'alert'],
    required: true 
  },
  status: { 
    type: String, 
    enum: ['draft', 'active', 'archived'],
    default: 'draft'
  },
  
  // Content
  content: Schema.Types.Mixed, // String for SMS/Push, Object for Email
  variables: [templateVariableSchema],
  tags: [String],
  
  // Versioning
  version: { type: String, default: '1.0.0' },
  version_history: [{
    version: String,
    created_at: Date,
    created_by: String,
    changes: [String],
    content_snapshot: Schema.Types.Mixed
  }],
  
  // Usage tracking
  usage_stats: {
    times_used: { type: Number, default: 0 },
    last_used: Date,
    success_rate: { type: Number, default: 0 },
    total_recipients: { type: Number, default: 0 },
    delivery_rate: { type: Number, default: 0 }
  },
  
  // Validation and compliance
  validation: {
    character_count: Number,
    estimated_sms_parts: Number,
    has_html: Boolean,
    has_text_fallback: Boolean,
    compliance_check: { 
      type: String, 
      enum: ['passed', 'failed', 'pending'] 
    },
    compliance_issues: [String]
  },
  
  // Metadata
  created_by: String,
  updated_by: String
}, { 
  timestamps: true,
  collection: 'message_templates'
});

// Indexes for efficient querying
messageTemplateSchema.index({ org_id: 1, type: 1 });
messageTemplateSchema.index({ org_id: 1, category: 1 });
messageTemplateSchema.index({ org_id: 1, status: 1 });
messageTemplateSchema.index({ org_id: 1, tags: 1 });
messageTemplateSchema.index({ name: 'text' });

export const MessageTemplateModel = model('MessageTemplate', messageTemplateSchema);
```

### 2. DAO Implementation (45 minutes)
```typescript
// src/lib/db/dao/message-template.dao.ts
import { MessageTemplateModel } from '../models/message-template.model';
import { DatabaseError, NotFoundError } from '@/lib/errors/error-types';

export class MessageTemplateDAO {
  static async getTemplates(orgId: string, filters: any = {}) {
    try {
      const query: any = { org_id: orgId };
      
      // Apply filters
      if (filters.type) query.type = filters.type;
      if (filters.category) query.category = filters.category;
      if (filters.status) query.status = filters.status;
      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags };
      }

      // Search functionality
      if (filters.search) {
        query.$or = [
          { name: { $regex: filters.search, $options: 'i' } },
          { 'content.subject': { $regex: filters.search, $options: 'i' } },
          { content: { $regex: filters.search, $options: 'i' } }
        ];
      }

      const sortBy = filters.sort_by || 'created_at';
      const sortOrder = filters.sort_order === 'asc' ? 1 : -1;
      const page = Math.max(1, parseInt(filters.page) || 1);
      const limit = Math.min(50, Math.max(1, parseInt(filters.limit) || 20));
      const skip = (page - 1) * limit;

      const [templates, total] = await Promise.all([
        MessageTemplateModel
          .find(query)
          .sort({ [sortBy]: sortOrder })
          .skip(skip)
          .limit(limit)
          .lean(),
        MessageTemplateModel.countDocuments(query)
      ]);

      const formattedTemplates = templates.map(template => 
        this.formatTemplateResponse(template, false)
      );

      return {
        templates: formattedTemplates,
        pagination: {
          current_page: page,
          per_page: limit,
          total_pages: Math.ceil(total / limit),
          total_count: total
        },
        filters_applied: {
          type: filters.type,
          category: filters.category,
          status: filters.status,
          search: filters.search
        }
      };

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve templates',
        'DB_TEMPLATES_GET_FAILED'
      );
    }
  }

  static async getTemplateById(orgId: string, templateId: string) {
    try {
      const template = await MessageTemplateModel.findOne({
        org_id: orgId,
        template_id: templateId
      }).lean();

      if (!template) {
        throw new NotFoundError(
          `Template ${templateId} not found`,
          'TEMPLATE_NOT_FOUND'
        );
      }

      return this.formatTemplateResponse(template, true);

    } catch (error: unknown) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve template',
        'DB_TEMPLATE_GET_FAILED'
      );
    }
  }

  static async previewTemplate(orgId: string, templateId: string, variables: any) {
    try {
      const template = await MessageTemplateModel.findOne({
        org_id: orgId,
        template_id: templateId
      }).lean();

      if (!template) {
        throw new NotFoundError(
          `Template ${templateId} not found`,
          'TEMPLATE_NOT_FOUND'
        );
      }

      const preview = this.renderTemplate(template, variables);
      return preview;

    } catch (error: unknown) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to preview template',
        'DB_TEMPLATE_PREVIEW_FAILED'
      );
    }
  }

  private static formatTemplateResponse(template: any, includeDetails: boolean = false) {
    const response: any = {
      template_id: template.template_id,
      name: template.name,
      type: template.type,
      category: template.category,
      status: template.status,
      version: template.version,
      content: template.content,
      variables: template.variables,
      tags: template.tags,
      usage_stats: template.usage_stats,
      validation: template.validation,
      created_by: {
        user_id: template.created_by
        // Additional user info would be populated via join
      },
      created_at: template.createdAt,
      updated_at: template.updatedAt
    };

    if (includeDetails) {
      response.version_history = template.version_history;
      response.created_by.name = 'Unknown'; // Would be populated from user service
      response.created_by.email = '<EMAIL>';
    }

    return response;
  }

  private static renderTemplate(template: any, variables: any) {
    let renderedContent: string;
    let characterCount: number;

    if (template.type === 'email' && typeof template.content === 'object') {
      // For email templates, render both subject and body
      const subject = this.replaceVariables(template.content.subject, variables);
      const html = this.replaceVariables(template.content.html, variables);
      
      renderedContent = `Subject: ${subject}\n\n${html}`;
      characterCount = subject.length + html.length;
    } else {
      // For SMS and Push templates
      renderedContent = this.replaceVariables(template.content, variables);
      characterCount = renderedContent.length;
    }

    // Validate required variables
    const requiredVariables = template.variables.filter((v: any) => v.required);
    const allRequiredProvided = requiredVariables.every((v: any) => 
      variables.hasOwnProperty(v.name) && variables[v.name] !== null && variables[v.name] !== ''
    );

    return {
      rendered_content: renderedContent,
      character_count: characterCount,
      estimated_sms_parts: template.type === 'sms' ? Math.ceil(characterCount / 160) : null,
      variables_used: variables,
      validation_results: {
        all_required_variables_provided: allRequiredProvided,
        character_limit_ok: template.type === 'sms' ? characterCount <= 1600 : true,
        compliance_check: template.validation?.compliance_check || 'pending'
      }
    };
  }

  private static replaceVariables(content: string, variables: any): string {
    if (!content) return '';
    
    let result = content;
    
    // Replace variables in {{variable}} format
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value || ''));
    }
    
    return result;
  }
}
```

### 3. Handler Implementation (30 minutes)
```typescript
// src/handlers/message-template.handler.ts
import { Request, Response } from 'express';
import { MessageTemplateDAO } from '@/lib/db/dao/message-template.dao';
import { handleError } from '@/lib/errors/error-handler';
import { OrganizationError } from '@/lib/errors/error-types';

export class MessageTemplateHandler {
  static async getTemplates(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const filters = {
        type: req.query.type as string,
        category: req.query.category as string,
        status: req.query.status as string,
        tags: req.query.tags ? (req.query.tags as string).split(',') : undefined,
        search: req.query.search as string,
        sort_by: req.query.sort_by as string,
        sort_order: req.query.sort_order as string,
        page: req.query.page as string,
        limit: req.query.limit as string
      };

      const result = await MessageTemplateDAO.getTemplates(orgId, filters);

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getTemplateById(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { template_id } = req.params;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const template = await MessageTemplateDAO.getTemplateById(orgId, template_id);

      res.status(200).json({
        success: true,
        data: template,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async previewTemplate(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { template_id } = req.params;
      const { variables } = req.body;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const preview = await MessageTemplateDAO.previewTemplate(
        orgId, 
        template_id, 
        variables || {}
      );

      res.status(200).json({
        success: true,
        data: { preview },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (10 minutes)
```typescript
// src/routes/message-template.routes.ts
import { Router } from 'express';
import { MessageTemplateHandler } from '@/handlers/message-template.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateTemplateQuery, validateTemplatePreview } from '@/validators/message-template.validator';

const router = Router();

// Get templates with filtering
router.get(
  '/',
  supabaseAuthMiddleware,
  validateTemplateQuery,
  MessageTemplateHandler.getTemplates
);

// Get template by ID
router.get(
  '/:template_id',
  supabaseAuthMiddleware,
  MessageTemplateHandler.getTemplateById
);

// Preview template with variables
router.post(
  '/:template_id/preview',
  supabaseAuthMiddleware,
  validateTemplatePreview,
  MessageTemplateHandler.previewTemplate
);

export { router as messageTemplateRoutes };
```

### 5. Validation Schema (15 minutes)
```typescript
// src/validators/message-template.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const templateQuerySchema = Joi.object({
  type: Joi.string().valid('sms', 'email', 'push').optional(),
  category: Joi.string().valid('transactional', 'promotional', 'notification', 'alert').optional(),
  status: Joi.string().valid('draft', 'active', 'archived').optional(),
  tags: Joi.string().optional(),
  search: Joi.string().min(2).max(100).optional(),
  sort_by: Joi.string().valid('name', 'created_at', 'last_used', 'times_used').optional(),
  sort_order: Joi.string().valid('asc', 'desc').optional(),
  page: Joi.number().integer().min(1).max(1000).optional(),
  limit: Joi.number().integer().min(1).max(50).optional()
});

const templatePreviewSchema = Joi.object({
  variables: Joi.object().pattern(
    Joi.string(),
    Joi.alternatives().try(Joi.string(), Joi.number(), Joi.boolean())
  ).optional()
});

export const validateTemplateQuery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = templateQuerySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};

export const validateTemplatePreview = (req: Request, res: Response, next: NextFunction) => {
  const { error } = templatePreviewSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};
```

## Error Handling

### Common Errors
- **404 Template Not Found**: Template doesn't exist or user doesn't have access
- **400 Invalid Query Parameters**: Invalid filter values or search terms
- **422 Preview Failed**: Variable rendering failed
- **429 Rate Limited**: Too many requests

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "TEMPLATE_NOT_FOUND",
    "message": "Template tpl_64a1b2c3d4e5f6789012345 not found",
    "code": 404
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Security Considerations

1. **Access Control**: Ensure users can only access their organization's templates
2. **Input Sanitization**: Sanitize template variables to prevent injection
3. **Content Validation**: Validate template content for compliance
4. **Rate Limiting**: Prevent abuse of preview functionality
5. **Audit Logging**: Track template access and usage