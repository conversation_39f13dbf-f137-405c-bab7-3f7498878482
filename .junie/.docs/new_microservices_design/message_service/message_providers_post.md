# Message Service - POST /providers (Provider Management)

## Task Overview
**Endpoint**: `POST /api/message/v1/providers`  
**Purpose**: Create, configure, and manage messaging providers (Twilio, SendGrid, Firebase) including setup, testing, and activation  
**Estimated Time**: 1.5 days  
**Priority**: Medium  
**Dependencies**: Provider integrations, configuration validation, health monitoring system  

## API Specification

### Create New Provider Request
```http
POST /api/message/v1/providers
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "provider_config": {
    "provider_id": "twilio_primary",
    "name": "Primary Twilio SMS",
    "type": "sms",
    "provider_type": "twilio"
  },
  "configuration": {
    "account_sid": "ACa1b2c3d4e5f67890**********1234",
    "auth_token": "your_auth_token_here",
    "webhook_url": "https://api.example.com/webhooks/twilio",
    "rate_limits": {
      "messages_per_second": 100,
      "daily_limit": 10000
    }
  },
  "setup_options": {
    "auto_activate": true,
    "run_connection_test": true,
    "setup_webhooks": true,
    "import_phone_numbers": true
  }
}
```

### Update Provider Configuration Request
```http
PUT /api/message/v1/providers/twilio_primary
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "configuration_updates": {
    "rate_limits": {
      "messages_per_second": 150,
      "daily_limit": 15000
    },
    "webhook_url": "https://api.example.com/webhooks/twilio/v2"
  },
  "update_options": {
    "test_configuration": true,
    "update_webhooks": true,
    "validate_credentials": true
  }
}
```

### Provider Action Request
```http
POST /api/message/v1/providers/twilio_primary/actions
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "action": "test_connection",
  "action_params": {
    "send_test_message": true,
    "test_recipient": "+**********"
  }
}
```

### Response - Create Provider
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "provider_id": "twilio_primary",
    "name": "Primary Twilio SMS",
    "type": "sms",
    "provider_type": "twilio",
    "status": "active",
    "setup_results": {
      "connection_test": {
        "status": "passed",
        "response_time": 145,
        "test_message_sent": true,
        "test_message_id": "SM**********abcdef"
      },
      "webhook_setup": {
        "status": "configured",
        "webhook_url": "https://api.example.com/webhooks/twilio",
        "events_configured": ["sent", "delivered", "failed"]
      },
      "phone_numbers_imported": {
        "count": 2,
        "numbers": [
          {
            "number": "+**********",
            "status": "active",
            "capabilities": ["sms", "voice"]
          },
          {
            "number": "+**********", 
            "status": "active",
            "capabilities": ["sms"]
          }
        ]
      }
    },
    "configuration": {
      "account_sid": "ACa1b2c3d4e5f67890**********1234",
      "webhook_url": "https://api.example.com/webhooks/twilio",
      "rate_limits": {
        "messages_per_second": 100,
        "daily_limit": 10000
      }
    },
    "health_status": {
      "status": "healthy",
      "last_check": "2024-08-11T15:30:00Z",
      "response_time": 145
    },
    "created_at": "2024-08-11T15:30:00Z"
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

### Response - Update Provider
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "provider_id": "twilio_primary",
    "updated_fields": ["rate_limits", "webhook_url"],
    "validation_results": {
      "credentials_valid": true,
      "webhook_test": {
        "status": "passed",
        "response_code": 200
      },
      "configuration_valid": true
    },
    "configuration": {
      "rate_limits": {
        "messages_per_second": 150,
        "daily_limit": 15000
      },
      "webhook_url": "https://api.example.com/webhooks/twilio/v2"
    },
    "updated_at": "2024-08-11T15:35:00Z"
  }
}
```

### Response - Provider Actions
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "action": "test_connection",
    "action_id": "act_64a1b2c3d4e5f6789012348",
    "status": "completed",
    "results": {
      "connection_test": {
        "status": "passed",
        "response_time": 134,
        "api_accessible": true,
        "credentials_valid": true
      },
      "test_message": {
        "sent": true,
        "message_id": "SM**********abcdef",
        "recipient": "+**********",
        "status": "delivered",
        "delivery_time": 2.1
      }
    },
    "executed_at": "2024-08-11T15:40:00Z"
  }
}
```

## Supported Provider Types

### Twilio SMS Configuration
```json
{
  "provider_type": "twilio",
  "configuration": {
    "account_sid": "ACa1b2c3d4e5f67890**********1234",
    "auth_token": "your_auth_token",
    "webhook_url": "https://api.example.com/webhooks/twilio",
    "phone_numbers": ["auto_import"],
    "rate_limits": {
      "messages_per_second": 100,
      "daily_limit": 10000
    }
  }
}
```

### SendGrid Email Configuration
```json
{
  "provider_type": "sendgrid",
  "configuration": {
    "api_key": "SG.your_api_key_here",
    "webhook_url": "https://api.example.com/webhooks/sendgrid",
    "sender_domains": ["example.com"],
    "default_from_email": "<EMAIL>",
    "rate_limits": {
      "messages_per_second": 200,
      "daily_limit": 50000
    }
  }
}
```

### Firebase Push Configuration
```json
{
  "provider_type": "firebase",
  "configuration": {
    "server_key": "your_server_key",
    "project_id": "your_project_id",
    "webhook_url": "https://api.example.com/webhooks/firebase",
    "rate_limits": {
      "messages_per_second": 500,
      "daily_limit": 100000
    }
  }
}
```

## Implementation Tasks

### 1. Database Models (30 minutes)
```typescript
// src/lib/db/models/provider-action.model.ts
import { Schema, model } from 'mongoose';

const providerActionSchema = new Schema({
  org_id: { type: String, required: true, index: true },
  provider_id: { type: String, required: true, index: true },
  action_id: { type: String, required: true, unique: true },
  action_type: { 
    type: String, 
    enum: ['create', 'update', 'test_connection', 'activate', 'deactivate', 'delete'],
    required: true 
  },
  
  // Action details
  action_params: Schema.Types.Mixed,
  initiated_by: { type: String, required: true },
  
  // Execution details
  status: { 
    type: String, 
    enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  started_at: Date,
  completed_at: Date,
  
  // Results
  results: {
    success: Boolean,
    data: Schema.Types.Mixed,
    error_message: String,
    validation_results: Schema.Types.Mixed,
    performance_metrics: {
      execution_time: Number,
      api_calls_made: Number,
      response_times: [Number]
    }
  },
  
  // Retry information
  retry_count: { type: Number, default: 0 },
  max_retries: { type: Number, default: 3 },
  next_retry_at: Date
}, { 
  timestamps: true,
  collection: 'provider_actions'
});

providerActionSchema.index({ org_id: 1, action_type: 1 });
providerActionSchema.index({ status: 1, next_retry_at: 1 });

export const ProviderActionModel = model('ProviderAction', providerActionSchema);
```

### 2. Provider Service Implementation (1 hour)
```typescript
// src/services/provider-management.service.ts
import { ProviderConfigModel } from '@/lib/db/models/provider-config.model';
import { ProviderActionModel } from '@/lib/db/models/provider-action.model';
import { TwilioService } from './providers/twilio.service';
import { SendGridService } from './providers/sendgrid.service';
import { FirebaseService } from './providers/firebase.service';
import { generateActionId } from '@/lib/utils/id-generator';
import { ValidationError, DatabaseError } from '@/lib/errors/error-types';

export class ProviderManagementService {
  static async createProvider(orgId: string, userId: string, providerData: any) {
    const actionId = generateActionId();
    
    try {
      // Create action record
      const action = new ProviderActionModel({
        org_id: orgId,
        provider_id: providerData.provider_config.provider_id,
        action_id: actionId,
        action_type: 'create',
        action_params: providerData,
        initiated_by: userId,
        status: 'running',
        started_at: new Date()
      });
      await action.save();

      // Validate provider configuration
      const validationResult = await this.validateProviderConfig(
        providerData.provider_config.provider_type,
        providerData.configuration
      );

      if (!validationResult.valid) {
        throw new ValidationError(
          `Provider configuration invalid: ${validationResult.errors.join(', ')}`,
          'PROVIDER_CONFIG_INVALID'
        );
      }

      // Create provider record
      const provider = new ProviderConfigModel({
        org_id: orgId,
        provider_id: providerData.provider_config.provider_id,
        name: providerData.provider_config.name,
        type: providerData.provider_config.type,
        provider_type: providerData.provider_config.provider_type,
        configuration: providerData.configuration,
        status: 'inactive',
        health_status: {
          status: 'unknown',
          last_check: new Date()
        }
      });

      await provider.save();

      // Run setup tasks
      const setupResults = await this.runProviderSetup(provider, providerData.setup_options);

      // Update provider status if setup successful
      if (setupResults.success && providerData.setup_options?.auto_activate) {
        provider.status = 'active';
        await provider.save();
      }

      // Complete action
      await ProviderActionModel.findOneAndUpdate(
        { action_id: actionId },
        {
          status: 'completed',
          completed_at: new Date(),
          results: {
            success: true,
            data: {
              provider_id: provider.provider_id,
              setup_results: setupResults
            }
          }
        }
      );

      return {
        provider_id: provider.provider_id,
        name: provider.name,
        type: provider.type,
        provider_type: provider.provider_type,
        status: provider.status,
        setup_results: setupResults,
        configuration: this.maskSensitiveConfig(provider.configuration),
        health_status: provider.health_status,
        created_at: provider.createdAt
      };

    } catch (error: unknown) {
      // Update action with error
      await ProviderActionModel.findOneAndUpdate(
        { action_id: actionId },
        {
          status: 'failed',
          completed_at: new Date(),
          results: {
            success: false,
            error_message: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      );

      throw error;
    }
  }

  static async updateProvider(orgId: string, providerId: string, updates: any) {
    const actionId = generateActionId();

    try {
      const provider = await ProviderConfigModel.findOne({
        org_id: orgId,
        provider_id: providerId
      });

      if (!provider) {
        throw new ValidationError('Provider not found', 'PROVIDER_NOT_FOUND');
      }

      // Create action record
      const action = new ProviderActionModel({
        org_id: orgId,
        provider_id: providerId,
        action_id: actionId,
        action_type: 'update',
        action_params: updates,
        initiated_by: updates.updated_by || 'system',
        status: 'running',
        started_at: new Date()
      });
      await action.save();

      // Validate configuration updates
      const mergedConfig = { ...provider.configuration, ...updates.configuration_updates };
      const validationResult = await this.validateProviderConfig(
        provider.provider_type,
        mergedConfig
      );

      if (!validationResult.valid) {
        throw new ValidationError(
          `Configuration update invalid: ${validationResult.errors.join(', ')}`,
          'PROVIDER_CONFIG_INVALID'
        );
      }

      // Apply updates
      Object.assign(provider.configuration, updates.configuration_updates);
      provider.updated_at = new Date();

      // Test configuration if requested
      let validationResults = {};
      if (updates.update_options?.test_configuration) {
        validationResults = await this.testProviderConnection(provider);
      }

      await provider.save();

      // Complete action
      await ProviderActionModel.findOneAndUpdate(
        { action_id: actionId },
        {
          status: 'completed',
          completed_at: new Date(),
          results: {
            success: true,
            data: {
              updated_fields: Object.keys(updates.configuration_updates),
              validation_results: validationResults
            }
          }
        }
      );

      return {
        provider_id: providerId,
        updated_fields: Object.keys(updates.configuration_updates),
        validation_results: validationResults,
        configuration: this.maskSensitiveConfig(provider.configuration),
        updated_at: provider.updated_at
      };

    } catch (error: unknown) {
      await ProviderActionModel.findOneAndUpdate(
        { action_id: actionId },
        {
          status: 'failed',
          completed_at: new Date(),
          results: {
            success: false,
            error_message: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      );

      throw error;
    }
  }

  static async executeProviderAction(orgId: string, providerId: string, actionData: any) {
    const actionId = generateActionId();

    try {
      const provider = await ProviderConfigModel.findOne({
        org_id: orgId,
        provider_id: providerId
      });

      if (!provider) {
        throw new ValidationError('Provider not found', 'PROVIDER_NOT_FOUND');
      }

      // Create action record
      const action = new ProviderActionModel({
        org_id: orgId,
        provider_id: providerId,
        action_id: actionId,
        action_type: actionData.action,
        action_params: actionData.action_params,
        initiated_by: actionData.initiated_by || 'system',
        status: 'running',
        started_at: new Date()
      });
      await action.save();

      // Execute action
      let results;
      switch (actionData.action) {
        case 'test_connection':
          results = await this.testProviderConnection(provider, actionData.action_params);
          break;
        case 'activate':
          results = await this.activateProvider(provider);
          break;
        case 'deactivate':
          results = await this.deactivateProvider(provider);
          break;
        default:
          throw new ValidationError(`Unsupported action: ${actionData.action}`, 'INVALID_ACTION');
      }

      // Complete action
      await ProviderActionModel.findOneAndUpdate(
        { action_id: actionId },
        {
          status: 'completed',
          completed_at: new Date(),
          results: {
            success: true,
            data: results
          }
        }
      );

      return {
        action: actionData.action,
        action_id: actionId,
        status: 'completed',
        results: results,
        executed_at: new Date()
      };

    } catch (error: unknown) {
      await ProviderActionModel.findOneAndUpdate(
        { action_id: actionId },
        {
          status: 'failed',
          completed_at: new Date(),
          results: {
            success: false,
            error_message: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      );

      throw error;
    }
  }

  private static async validateProviderConfig(providerType: string, config: any) {
    switch (providerType) {
      case 'twilio':
        return TwilioService.validateConfig(config);
      case 'sendgrid':
        return SendGridService.validateConfig(config);
      case 'firebase':
        return FirebaseService.validateConfig(config);
      default:
        return { valid: false, errors: [`Unsupported provider type: ${providerType}`] };
    }
  }

  private static async runProviderSetup(provider: any, setupOptions: any) {
    const results: any = {
      success: true,
      connection_test: null,
      webhook_setup: null,
      phone_numbers_imported: null
    };

    try {
      // Test connection
      if (setupOptions?.run_connection_test) {
        results.connection_test = await this.testProviderConnection(provider);
        if (!results.connection_test.status === 'passed') {
          results.success = false;
        }
      }

      // Setup webhooks
      if (setupOptions?.setup_webhooks) {
        results.webhook_setup = await this.setupWebhooks(provider);
      }

      // Import phone numbers (for SMS providers)
      if (setupOptions?.import_phone_numbers && provider.type === 'sms') {
        results.phone_numbers_imported = await this.importPhoneNumbers(provider);
      }

    } catch (error) {
      results.success = false;
      results.error = error instanceof Error ? error.message : 'Unknown error';
    }

    return results;
  }

  private static async testProviderConnection(provider: any, params?: any) {
    switch (provider.provider_type) {
      case 'twilio':
        return TwilioService.testConnection(provider.configuration, params);
      case 'sendgrid':
        return SendGridService.testConnection(provider.configuration, params);
      case 'firebase':
        return FirebaseService.testConnection(provider.configuration, params);
      default:
        return { status: 'failed', error: 'Unsupported provider type' };
    }
  }

  private static async setupWebhooks(provider: any) {
    // Implementation for setting up webhooks with the provider
    return {
      status: 'configured',
      webhook_url: provider.configuration.webhook_url,
      events_configured: ['sent', 'delivered', 'failed']
    };
  }

  private static async importPhoneNumbers(provider: any) {
    // Implementation for importing phone numbers from the provider
    return {
      count: 0,
      numbers: []
    };
  }

  private static async activateProvider(provider: any) {
    provider.status = 'active';
    await provider.save();
    return { status: 'activated', activated_at: new Date() };
  }

  private static async deactivateProvider(provider: any) {
    provider.status = 'inactive';
    await provider.save();
    return { status: 'deactivated', deactivated_at: new Date() };
  }

  private static maskSensitiveConfig(config: any) {
    const masked = { ...config };
    
    // Mask sensitive fields
    if (masked.auth_token) masked.auth_token = '***masked***';
    if (masked.api_key) masked.api_key = masked.api_key.substring(0, 3) + '***masked***';
    if (masked.server_key) masked.server_key = '***masked***';
    
    return masked;
  }
}
```

### 3. Handler Implementation (45 minutes)
```typescript
// src/handlers/provider-management.handler.ts
import { Request, Response } from 'express';
import { ProviderManagementService } from '@/services/provider-management.service';
import { handleError } from '@/lib/errors/error-handler';
import { OrganizationError, ValidationError } from '@/lib/errors/error-types';

export class ProviderManagementHandler {
  static async createProvider(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const userId = req.user?.id;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      if (!userId) {
        throw new ValidationError('User context is required', 'USER_001');
      }

      const provider = await ProviderManagementService.createProvider(
        orgId,
        userId,
        req.body
      );

      res.status(201).json({
        success: true,
        data: provider,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async updateProvider(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { provider_id } = req.params;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const updateData = {
        ...req.body,
        updated_by: req.user?.id
      };

      const result = await ProviderManagementService.updateProvider(
        orgId,
        provider_id,
        updateData
      );

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async executeProviderAction(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { provider_id } = req.params;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const actionData = {
        ...req.body,
        initiated_by: req.user?.id
      };

      const result = await ProviderManagementService.executeProviderAction(
        orgId,
        provider_id,
        actionData
      );

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async deleteProvider(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { provider_id } = req.params;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      // Soft delete provider
      await ProviderConfigModel.findOneAndUpdate(
        { org_id: orgId, provider_id },
        { 
          status: 'deleted',
          deleted_at: new Date(),
          deleted_by: req.user?.id
        }
      );

      res.status(200).json({
        success: true,
        data: {
          provider_id,
          deleted_at: new Date()
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (15 minutes)
```typescript
// src/routes/provider-management.routes.ts
import { Router } from 'express';
import { ProviderManagementHandler } from '@/handlers/provider-management.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { 
  validateCreateProvider, 
  validateUpdateProvider, 
  validateProviderAction 
} from '@/validators/provider-management.validator';

const router = Router();

// Create provider
router.post(
  '/',
  supabaseAuthMiddleware,
  validateCreateProvider,
  ProviderManagementHandler.createProvider
);

// Update provider
router.put(
  '/:provider_id',
  supabaseAuthMiddleware,
  validateUpdateProvider,
  ProviderManagementHandler.updateProvider
);

// Execute provider actions
router.post(
  '/:provider_id/actions',
  supabaseAuthMiddleware,
  validateProviderAction,
  ProviderManagementHandler.executeProviderAction
);

// Delete provider
router.delete(
  '/:provider_id',
  supabaseAuthMiddleware,
  ProviderManagementHandler.deleteProvider
);

export { router as providerManagementRoutes };
```

### 5. Validation Schemas (30 minutes)
```typescript
// src/validators/provider-management.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const createProviderSchema = Joi.object({
  provider_config: Joi.object({
    provider_id: Joi.string().required().min(3).max(50),
    name: Joi.string().required().min(1).max(255),
    type: Joi.string().valid('sms', 'email', 'push').required(),
    provider_type: Joi.string().valid('twilio', 'sendgrid', 'firebase').required()
  }).required(),

  configuration: Joi.object({
    // Twilio fields
    account_sid: Joi.when('..provider_config.provider_type', {
      is: 'twilio',
      then: Joi.string().required(),
      otherwise: Joi.string().optional()
    }),
    auth_token: Joi.when('..provider_config.provider_type', {
      is: 'twilio',
      then: Joi.string().required(),
      otherwise: Joi.string().optional()
    }),

    // SendGrid fields
    api_key: Joi.when('..provider_config.provider_type', {
      is: 'sendgrid',
      then: Joi.string().required(),
      otherwise: Joi.string().optional()
    }),

    // Firebase fields
    server_key: Joi.when('..provider_config.provider_type', {
      is: 'firebase',
      then: Joi.string().required(),
      otherwise: Joi.string().optional()
    }),
    project_id: Joi.when('..provider_config.provider_type', {
      is: 'firebase',
      then: Joi.string().required(),
      otherwise: Joi.string().optional()
    }),

    // Common fields
    webhook_url: Joi.string().uri().required(),
    rate_limits: Joi.object({
      messages_per_second: Joi.number().integer().min(1).max(1000).optional(),
      daily_limit: Joi.number().integer().min(1).optional()
    }).optional()
  }).required(),

  setup_options: Joi.object({
    auto_activate: Joi.boolean().default(true),
    run_connection_test: Joi.boolean().default(true),
    setup_webhooks: Joi.boolean().default(true),
    import_phone_numbers: Joi.boolean().default(false)
  }).optional()
});

const updateProviderSchema = Joi.object({
  configuration_updates: Joi.object().min(1).required(),
  update_options: Joi.object({
    test_configuration: Joi.boolean().default(true),
    update_webhooks: Joi.boolean().default(false),
    validate_credentials: Joi.boolean().default(true)
  }).optional()
});

const providerActionSchema = Joi.object({
  action: Joi.string().valid('test_connection', 'activate', 'deactivate').required(),
  action_params: Joi.object().optional()
});

export const validateCreateProvider = (req: Request, res: Response, next: NextFunction) => {
  const { error } = createProviderSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }

  next();
};

export const validateUpdateProvider = (req: Request, res: Response, next: NextFunction) => {
  const { error } = updateProviderSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};

export const validateProviderAction = (req: Request, res: Response, next: NextFunction) => {
  const { error } = providerActionSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};
```

## Error Handling

### Common Errors
- **400 Invalid Configuration**: Provider configuration is invalid or incomplete
- **409 Provider Already Exists**: Provider with same ID already exists
- **422 Connection Test Failed**: Unable to connect to provider
- **424 Setup Failed**: Provider setup or activation failed
- **429 Rate Limited**: Too many requests

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "PROVIDER_CONFIG_INVALID",
    "message": "Provider configuration invalid: auth_token is required",
    "code": 400
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Security Considerations

1. **Credential Encryption**: Encrypt sensitive provider credentials at rest
2. **Access Control**: Restrict provider management to authorized users
3. **Audit Logging**: Log all provider configuration changes
4. **Credential Masking**: Never expose full credentials in responses
5. **Secure Validation**: Validate provider credentials securely

## Testing and Validation

### Connection Testing
- API connectivity tests
- Credential validation
- Rate limit testing  
- Webhook endpoint validation
- Message delivery testing

### Setup Validation
- Configuration completeness
- Required field validation
- Format and type validation
- Provider-specific rules
- Integration compatibility