# Message Service - GET /messages/{id} (Get Message Status)

## Task Overview
**Endpoint**: `GET /api/message/v1/messages/{id}`  
**Purpose**: Retrieve detailed information about a specific message including status, delivery tracking, and provider details  
**Estimated Time**: 1 day  
**Priority**: High  
**Dependencies**: Message creation, authentication middleware, database models  

## API Specification

### Request
```http
GET /api/message/v1/messages/msg_64a1b2c3d4e5f678901234b
Authorization: Bearer <jwt_token>
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "message_id": "msg_64a1b2c3d4e5f678901234b",
    "org_id": "org_64a1b2c3d4e5f6789012345",
    "transport": "SMS",
    "provider": "TWILIO",
    "from": "STORE",
    "to": "+94771234567",
    "content": "Your order #12345 is ready for pickup!",
    "subject": null,
    "html_content": null,
    "status": "DELIVERED",
    "campaign_id": "64a1b2c3d4e5f6789012346",
    "is_campaign_message": true,
    "priority": "HIGH",
    "delivery_info": {
      "sent_at": "2024-08-11T15:30:15Z",
      "delivered_at": "2024-08-11T15:30:18Z",
      "opened_at": null,
      "clicked_at": null,
      "bounced_at": null,
      "unsubscribed_at": null,
      "delivery_duration_ms": 3000,
      "delivery_attempts": 1
    },
    "cost_info": {
      "cost": 0.05,
      "currency": "USD",
      "billing_units": 1
    },
    "provider_info": {
      "provider_message_id": "SM123456789abcdef",
      "provider_response": {
        "account_sid": "AC123456789",
        "message_sid": "SM123456789abcdef",
        "status": "delivered",
        "price": "-0.0075",
        "price_unit": "USD"
      },
      "webhook_received_at": "2024-08-11T15:30:18Z"
    },
    "retry_info": {
      "retry_count": 0,
      "max_retries": 3,
      "next_retry_at": null,
      "last_retry_at": null,
      "retry_reasons": []
    },
    "status_history": [
      {
        "status": "QUEUED",
        "timestamp": "2024-08-11T15:30:00Z",
        "details": "Message queued for processing"
      },
      {
        "status": "SENT",
        "timestamp": "2024-08-11T15:30:15Z",
        "details": "Message sent to provider"
      },
      {
        "status": "DELIVERED",
        "timestamp": "2024-08-11T15:30:18Z",
        "details": "Message delivered successfully"
      }
    ],
    "metadata": {
      "order_id": "12345",
      "customer_id": "cust_789",
      "notification_type": "order_ready",
      "user_agent": null,
      "ip_address": null
    },
    "tracking_urls": {
      "webhook_url": "https://api.example.com/webhooks/message-status",
      "unsubscribe_url": null,
      "click_tracking_enabled": false
    },
    "created_at": "2024-08-11T15:30:00Z",
    "updated_at": "2024-08-11T15:30:18Z"
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 404 Not Found
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "MESSAGE_NOT_FOUND",
    "message": "Message not found or access denied",
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (45 minutes)
```typescript
// src/services/message.service.ts (addition)
export class MessageService {
  static async getMessageById(messageId: string, orgId: string): Promise<MessageDetail> {
    try {
      // Validate message ID format
      if (!messageId || typeof messageId !== 'string') {
        throw new BadRequestError('Invalid message ID format', 'INVALID_MESSAGE_ID');
      }

      // Find message with organization check
      const message = await MessageLogModel.findOne({
        message_id: messageId,
        org_id: orgId
      }).lean();

      if (!message) {
        throw new NotFoundError('Message not found or access denied', 'MESSAGE_NOT_FOUND');
      }

      return this.formatDetailedMessageResponse(message);
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to retrieve message: ${error.message}`);
    }
  }

  private static formatDetailedMessageResponse(message: any): MessageDetail {
    return {
      message_id: message.message_id,
      org_id: message.org_id,
      transport: message.transport,
      provider: message.provider,
      from: message.from,
      to: message.to,
      content: message.content,
      subject: message.subject || null,
      html_content: message.html_content || null,
      status: message.status,
      campaign_id: message.campaign_id?.toString() || null,
      is_campaign_message: message.is_campaign_message || false,
      priority: message.priority || 'NORMAL',
      delivery_info: {
        sent_at: message.sent_at || null,
        delivered_at: message.delivered_at || null,
        opened_at: message.opened_at || null,
        clicked_at: message.clicked_at || null,
        bounced_at: message.bounced_at || null,
        unsubscribed_at: message.unsubscribed_at || null,
        delivery_duration_ms: message.delivery_duration_ms || null,
        delivery_attempts: message.delivery_attempts || 1
      },
      cost_info: {
        cost: message.cost || 0,
        currency: message.currency || 'USD',
        billing_units: message.billing_units || 1
      },
      provider_info: {
        provider_message_id: message.provider_message_id || null,
        provider_response: message.provider_response || {},
        webhook_received_at: message.webhook_received_at || null
      },
      retry_info: {
        retry_count: message.retry_count || 0,
        max_retries: message.max_retries || 3,
        next_retry_at: message.next_retry_at || null,
        last_retry_at: message.last_retry_at || null,
        retry_reasons: message.retry_reasons || []
      },
      status_history: message.status_history || [],
      metadata: message.metadata || {},
      tracking_urls: {
        webhook_url: message.webhook_url || null,
        unsubscribe_url: message.unsubscribe_url || null,
        click_tracking_enabled: message.click_tracking_enabled || false
      },
      created_at: message.created_at,
      updated_at: message.updated_at
    };
  }

  static async getMessageStatusSummary(messageId: string, orgId: string): Promise<MessageStatusSummary> {
    try {
      const message = await this.getMessageById(messageId, orgId);
      
      return {
        message_id: message.message_id,
        status: message.status,
        transport: message.transport,
        provider: message.provider,
        sent_at: message.delivery_info.sent_at,
        delivered_at: message.delivery_info.delivered_at,
        cost: message.cost_info.cost,
        currency: message.cost_info.currency
      };
    } catch (error) {
      throw error;
    }
  }

  static async refreshMessageStatus(messageId: string, orgId: string): Promise<MessageDetail> {
    try {
      const message = await this.getMessageById(messageId, orgId);
      
      // Only refresh if message is in a pending state
      const refreshableStatuses = ['QUEUED', 'SENT'];
      if (!refreshableStatuses.includes(message.status)) {
        return message;
      }

      // Add job to refresh status from provider
      await MessageStatusRefreshQueue.add('refresh-status', {
        messageId: message.message_id,
        provider: message.provider,
        providerMessageId: message.provider_info.provider_message_id
      }, {
        priority: 10,
        removeOnComplete: 5,
        removeOnFail: 5
      });

      return message;
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to refresh message status: ${error.message}`);
    }
  }
}
```

### 2. Controller Implementation (25 minutes)
```typescript
// src/controllers/message.controller.ts (addition)
export class MessageController {
  static async getMessageById(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Message ID is required', 'MESSAGE_ID_REQUIRED');
      }

      const message = await MessageService.getMessageById(id, req.user.org_id);

      res.json({
        success: true,
        data: message,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMessageStatus(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Message ID is required', 'MESSAGE_ID_REQUIRED');
      }

      const statusSummary = await MessageService.getMessageStatusSummary(id, req.user.org_id);

      res.json({
        success: true,
        data: statusSummary,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async refreshMessageStatus(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Message ID is required', 'MESSAGE_ID_REQUIRED');
      }

      const message = await MessageService.refreshMessageStatus(id, req.user.org_id);

      res.json({
        success: true,
        data: {
          message: "Status refresh initiated",
          current_status: message.status,
          message_id: message.message_id
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 3. Route Definition (15 minutes)
```typescript
// src/routes/message.routes.ts (addition)
router.get(
  '/:id',
  authenticateRequest,
  rateLimitMiddleware,
  MessageController.getMessageById
);

router.get(
  '/:id/status',
  authenticateRequest,
  rateLimitMiddleware,
  MessageController.getMessageStatus
);

router.post(
  '/:id/refresh-status',
  authenticateRequest,
  rateLimitMiddleware,
  MessageController.refreshMessageStatus
);
```

### 4. Type Definitions (20 minutes)
```typescript
// src/types/message.types.ts (additions)
export interface MessageDetail {
  message_id: string;
  org_id: string;
  transport: 'SMS' | 'EMAIL' | 'PUSH' | 'FACEBOOK_MESSENGER';
  provider: string;
  from: string;
  to: string;
  content: string;
  subject?: string;
  html_content?: string;
  status: MessageStatus;
  campaign_id?: string;
  is_campaign_message: boolean;
  priority: 'LOW' | 'NORMAL' | 'HIGH';
  delivery_info: {
    sent_at?: Date;
    delivered_at?: Date;
    opened_at?: Date;
    clicked_at?: Date;
    bounced_at?: Date;
    unsubscribed_at?: Date;
    delivery_duration_ms?: number;
    delivery_attempts: number;
  };
  cost_info: {
    cost: number;
    currency: string;
    billing_units: number;
  };
  provider_info: {
    provider_message_id?: string;
    provider_response: Record<string, any>;
    webhook_received_at?: Date;
  };
  retry_info: {
    retry_count: number;
    max_retries: number;
    next_retry_at?: Date;
    last_retry_at?: Date;
    retry_reasons: string[];
  };
  status_history: MessageStatusHistory[];
  metadata: Record<string, any>;
  tracking_urls: {
    webhook_url?: string;
    unsubscribe_url?: string;
    click_tracking_enabled: boolean;
  };
  created_at: Date;
  updated_at: Date;
}

export interface MessageStatusSummary {
  message_id: string;
  status: MessageStatus;
  transport: string;
  provider: string;
  sent_at?: Date;
  delivered_at?: Date;
  cost: number;
  currency: string;
}

export interface MessageStatusHistory {
  status: MessageStatus;
  timestamp: Date;
  details?: string;
}

export type MessageStatus = 
  | 'QUEUED' 
  | 'SENT' 
  | 'DELIVERED' 
  | 'FAILED' 
  | 'BOUNCED' 
  | 'OPENED' 
  | 'CLICKED' 
  | 'UNSUBSCRIBED';
```

### 5. Validation Middleware (15 minutes)
```typescript
// src/middleware/validation.middleware.ts (addition)
export const validateMessageId = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const { id } = req.params;
  
  if (!id) {
    throw new BadRequestError('Message ID parameter is required', 'MESSAGE_ID_REQUIRED');
  }
  
  // Validate message ID format (assuming it starts with 'msg_')
  if (!id.startsWith('msg_') || id.length < 10) {
    throw new BadRequestError('Invalid message ID format', 'INVALID_MESSAGE_ID_FORMAT');
  }
  
  next();
};
```

### 6. Enhanced Route Definition (10 minutes)
```typescript
// src/routes/message.routes.ts (updated)
import { validateMessageId } from '@/middleware/validation.middleware';

router.get(
  '/:id',
  authenticateRequest,
  validateMessageId,
  rateLimitMiddleware,
  MessageController.getMessageById
);

router.get(
  '/:id/status',
  authenticateRequest,
  validateMessageId,
  rateLimitMiddleware,
  MessageController.getMessageStatus
);

router.post(
  '/:id/refresh-status',
  authenticateRequest,
  validateMessageId,
  rateLimitMiddleware,
  MessageController.refreshMessageStatus
);
```

### 7. Caching Implementation (25 minutes)
```typescript
// src/services/message.cache.service.ts (addition)
export class MessageCacheService {
  private static readonly MESSAGE_CACHE_TTL = 600; // 10 minutes
  private static readonly MESSAGE_CACHE_PREFIX = 'message:detail:';

  static async getCachedMessage(messageId: string, orgId: string): Promise<MessageDetail | null> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = `${this.MESSAGE_CACHE_PREFIX}${orgId}:${messageId}`;
      
      const cached = await redis.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Cache retrieval error:', error);
      return null;
    }
  }

  static async setCachedMessage(message: MessageDetail): Promise<void> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = `${this.MESSAGE_CACHE_PREFIX}${message.org_id}:${message.message_id}`;
      
      // Cache for shorter time if message is in pending state
      const ttl = ['QUEUED', 'SENT'].includes(message.status) ? 60 : this.MESSAGE_CACHE_TTL;
      
      await redis.setex(cacheKey, ttl, JSON.stringify(message));
    } catch (error) {
      console.error('Cache storage error:', error);
    }
  }

  static async invalidateMessageCache(messageId: string, orgId: string): Promise<void> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = `${this.MESSAGE_CACHE_PREFIX}${orgId}:${messageId}`;
      
      await redis.del(cacheKey);
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }
}
```

### 8. Status Refresh Queue (30 minutes)
```typescript
// src/queues/message.status.refresh.queue.ts (new file)
import Queue from 'bull';
import { RedisConnector } from '@/lib/db/connectors/RedisConnector';
import { MessageLogModel } from '@/lib/db/models/message.log.model';
import { ProviderService } from '@/services/provider.service';

export const MessageStatusRefreshQueue = new Queue('message-status-refresh', {
  redis: RedisConnector.getConnectionOptions(),
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 10,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000
    }
  }
});

// Process status refresh jobs
MessageStatusRefreshQueue.process('refresh-status', async (job) => {
  const { messageId, provider, providerMessageId } = job.data;
  
  try {
    if (!providerMessageId) {
      console.log(`No provider message ID for ${messageId}, skipping refresh`);
      return;
    }

    // Get current status from provider
    const providerStatus = await ProviderService.getMessageStatus(provider, providerMessageId);
    
    // Map provider status to our status
    const mappedStatus = ProviderService.mapProviderStatus(provider, providerStatus.status);
    
    // Update message in database
    await MessageLogModel.updateOne(
      { message_id: messageId },
      {
        status: mappedStatus,
        provider_response: providerStatus,
        webhook_received_at: new Date(),
        updated_at: new Date(),
        $push: {
          status_history: {
            status: mappedStatus,
            timestamp: new Date(),
            details: `Status refreshed from provider: ${providerStatus.status}`
          }
        }
      }
    );

    // Invalidate cache
    const message = await MessageLogModel.findOne({ message_id: messageId });
    if (message) {
      await MessageCacheService.invalidateMessageCache(messageId, message.org_id);
    }

    console.log(`Status refreshed for message ${messageId}: ${mappedStatus}`);
  } catch (error) {
    console.error(`Failed to refresh status for message ${messageId}:`, error);
    throw error;
  }
});

export { MessageStatusRefreshQueue };
```

### 9. Unit Tests (40 minutes)
```typescript
// tests/unit/controllers/message.controller.test.ts (addition)
describe('MessageController.getMessageById', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = {
      params: { id: 'msg_64a1b2c3d4e5f678901234b' },
      user: { id: 'user123', org_id: 'org123' },
      headers: { 'x-request-id': 'req123' }
    };
    mockRes = {
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should return message details successfully', async () => {
    const mockMessage = {
      message_id: 'msg_64a1b2c3d4e5f678901234b',
      transport: 'SMS',
      provider: 'TWILIO',
      status: 'DELIVERED',
      content: 'Test message',
      delivery_info: {
        sent_at: new Date(),
        delivered_at: new Date()
      },
      cost_info: {
        cost: 0.05,
        currency: 'USD'
      }
    };
    
    (MessageService.getMessageById as jest.Mock).mockResolvedValue(mockMessage);

    await MessageController.getMessageById(mockReq, mockRes, mockNext);

    expect(MessageService.getMessageById).toHaveBeenCalledWith(
      'msg_64a1b2c3d4e5f678901234b',
      'org123'
    );
    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: mockMessage,
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        request_id: 'req123'
      })
    });
  });

  it('should handle missing message ID', async () => {
    mockReq.params.id = undefined;

    await MessageController.getMessageById(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(BadRequestError));
  });

  it('should handle message not found', async () => {
    (MessageService.getMessageById as jest.Mock).mockRejectedValue(
      new NotFoundError('Message not found')
    );

    await MessageController.getMessageById(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(NotFoundError));
  });
});

describe('MessageController.refreshMessageStatus', () => {
  it('should initiate status refresh', async () => {
    const mockMessage = {
      message_id: 'msg_test',
      status: 'SENT'
    };
    
    (MessageService.refreshMessageStatus as jest.Mock).mockResolvedValue(mockMessage);

    await MessageController.refreshMessageStatus(mockReq, mockRes, mockNext);

    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: {
        message: "Status refresh initiated",
        current_status: 'SENT',
        message_id: 'msg_test'
      },
      meta: expect.objectContaining({
        timestamp: expect.any(String)
      })
    });
  });
});
```

### 10. Integration Tests (30 minutes)
```typescript
// tests/integration/message.api.test.ts (addition)
describe('GET /api/message/v1/messages/:id', () => {
  let authToken: string;
  let orgId: string;
  let message: any;

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
    
    message = await MessageLogModel.create({
      org_id: orgId,
      message_id: 'msg_test_detailed',
      transport: 'SMS',
      provider: 'TWILIO',
      from: 'TEST',
      to: '+**********',
      content: 'Test detailed message',
      status: 'DELIVERED',
      cost: 0.05,
      currency: 'USD',
      provider_message_id: 'SM123456789',
      status_history: [
        {
          status: 'QUEUED',
          timestamp: new Date('2024-08-01T10:00:00Z'),
          details: 'Message queued'
        },
        {
          status: 'DELIVERED',
          timestamp: new Date('2024-08-01T10:00:15Z'),
          details: 'Message delivered'
        }
      ],
      created_at: new Date('2024-08-01T10:00:00Z')
    });
  });

  afterAll(async () => {
    await MessageLogModel.deleteMany({ org_id: orgId });
  });

  it('should return message details', async () => {
    const response = await request(app)
      .get(`/api/message/v1/messages/${message.message_id}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.message_id).toBe(message.message_id);
    expect(response.body.data.transport).toBe('SMS');
    expect(response.body.data.status).toBe('DELIVERED');
    expect(response.body.data.delivery_info).toBeDefined();
    expect(response.body.data.cost_info).toBeDefined();
    expect(response.body.data.status_history).toHaveLength(2);
  });

  it('should return 404 for non-existent message', async () => {
    const response = await request(app)
      .get('/api/message/v1/messages/msg_nonexistent')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(404);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('MESSAGE_NOT_FOUND');
  });

  it('should return 400 for invalid message ID', async () => {
    const response = await request(app)
      .get('/api/message/v1/messages/invalid-id')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_MESSAGE_ID_FORMAT');
  });

  it('should return 401 without authentication', async () => {
    await request(app)
      .get(`/api/message/v1/messages/${message.message_id}`)
      .expect(401);
  });
});

describe('GET /api/message/v1/messages/:id/status', () => {
  it('should return message status summary', async () => {
    const response = await request(app)
      .get(`/api/message/v1/messages/${message.message_id}/status`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.message_id).toBe(message.message_id);
    expect(response.body.data.status).toBe('DELIVERED');
    expect(response.body.data.transport).toBe('SMS');
    expect(response.body.data.cost).toBe(0.05);
  });
});
```

## Performance Considerations

### Database Optimization (15 minutes)
```typescript
// src/lib/db/models/message.log.model.ts (index additions)
// Add index for efficient message lookup
messageLogSchema.index({ message_id: 1, org_id: 1 }, { unique: true });
messageLogSchema.index({ org_id: 1, provider_message_id: 1 });
```

## Testing Checklist
- [ ] Unit tests for controller methods
- [ ] Unit tests for service layer retrieval logic
- [ ] Integration tests for GET endpoint
- [ ] Integration tests for status endpoint
- [ ] Integration tests for refresh endpoint
- [ ] Validation tests for message ID format
- [ ] Cache functionality tests
- [ ] Authentication and authorization tests
- [ ] Error handling tests (404, 400, 401)
- [ ] Status refresh queue tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Message status flow documentation
- [ ] Provider integration documentation
- [ ] Cache strategy documentation
- [ ] Queue processing documentation

This task creates a comprehensive message detail retrieval endpoint with status tracking, provider integration, caching, and status refresh capabilities.