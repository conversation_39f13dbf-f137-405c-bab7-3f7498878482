# Message Service - GET /providers (Provider Status and Configuration)

## Task Overview
**Endpoint**: `GET /api/message/v1/providers`  
**Purpose**: Retrieve status, configuration, and performance metrics for messaging providers (Twilio, SendGrid, Firebase)  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Provider integrations, configuration management, monitoring system  

## API Specification

### List All Providers Request
```http
GET /api/message/v1/providers
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Individual Provider Request
```http
GET /api/message/v1/providers/twilio
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Query Parameters
- `type` (string): Filter by provider type (sms, email, push)
- `status` (string): Filter by status (active, inactive, error)
- `include_metrics` (boolean): Include performance metrics (default: true)
- `include_config` (boolean): Include configuration details (default: false)

### Response - All Providers
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "providers": [
      {
        "provider_id": "twilio",
        "name": "Twilio SMS",
        "type": "sms",
        "status": "active",
        "health_status": "healthy",
        "last_health_check": "2024-08-11T15:30:00Z",
        "configuration": {
          "account_sid": "AC***masked***",
          "phone_numbers": [
            {
              "number": "+**********",
              "status": "active",
              "capabilities": ["sms", "voice"]
            }
          ],
          "webhook_url": "https://api.example.com/webhooks/twilio",
          "rate_limits": {
            "messages_per_second": 100,
            "daily_limit": 10000
          }
        },
        "metrics": {
          "last_24h": {
            "messages_sent": 1247,
            "success_rate": 98.2,
            "average_delivery_time": 2.1,
            "error_count": 23
          },
          "last_7d": {
            "messages_sent": 8934,
            "success_rate": 97.8,
            "average_delivery_time": 2.3,
            "error_count": 196
          }
        },
        "cost_metrics": {
          "last_24h_cost": 62.35,
          "last_7d_cost": 446.70,
          "average_cost_per_message": 0.05
        }
      },
      {
        "provider_id": "sendgrid",
        "name": "SendGrid Email",
        "type": "email",
        "status": "active",
        "health_status": "healthy",
        "last_health_check": "2024-08-11T15:29:00Z",
        "configuration": {
          "api_key": "SG.***masked***",
          "sender_domains": [
            {
              "domain": "example.com",
              "status": "verified",
              "dkim_enabled": true,
              "spf_enabled": true
            }
          ],
          "webhook_url": "https://api.example.com/webhooks/sendgrid",
          "rate_limits": {
            "messages_per_second": 200,
            "daily_limit": 50000
          }
        },
        "metrics": {
          "last_24h": {
            "messages_sent": 2156,
            "success_rate": 96.4,
            "average_delivery_time": 1.8,
            "open_rate": 24.3,
            "click_rate": 3.1,
            "bounce_rate": 2.1,
            "error_count": 78
          },
          "last_7d": {
            "messages_sent": 15489,
            "success_rate": 95.9,
            "average_delivery_time": 1.9,
            "open_rate": 25.1,
            "click_rate": 3.4,
            "bounce_rate": 2.3,
            "error_count": 635
          }
        },
        "cost_metrics": {
          "last_24h_cost": 21.56,
          "last_7d_cost": 154.89,
          "average_cost_per_message": 0.01
        }
      }
    ],
    "summary": {
      "total_providers": 3,
      "active_providers": 2,
      "inactive_providers": 1,
      "providers_with_errors": 0,
      "total_messages_24h": 3403,
      "total_cost_24h": 83.91
    }
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

### Response - Individual Provider
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "provider_id": "twilio",
    "name": "Twilio SMS",
    "type": "sms",
    "status": "active",
    "health_status": "healthy",
    "last_health_check": "2024-08-11T15:30:00Z",
    "connection_test": {
      "last_test": "2024-08-11T15:25:00Z",
      "status": "success",
      "response_time": 156,
      "test_message_sent": true
    },
    "configuration": {
      "account_sid": "ACa1b2c3d4e5f67890**********1234",
      "auth_token": "***masked***",
      "phone_numbers": [
        {
          "number": "+**********",
          "status": "active",
          "capabilities": ["sms", "voice"],
          "friendly_name": "Main Business Number",
          "purchased_date": "2024-01-15T10:00:00Z"
        },
        {
          "number": "+**********",
          "status": "active", 
          "capabilities": ["sms"],
          "friendly_name": "Marketing Number",
          "purchased_date": "2024-03-20T14:30:00Z"
        }
      ],
      "webhook_configuration": {
        "webhook_url": "https://api.example.com/webhooks/twilio",
        "status_callback_url": "https://api.example.com/webhooks/twilio/status",
        "events": ["sent", "delivered", "failed", "undelivered"]
      },
      "rate_limits": {
        "messages_per_second": 100,
        "daily_limit": 10000,
        "current_usage": {
          "today": 1247,
          "remaining": 8753
        }
      }
    },
    "detailed_metrics": {
      "hourly_stats": [
        {
          "hour": "2024-08-11T14:00:00Z",
          "messages_sent": 89,
          "delivered": 87,
          "failed": 2,
          "success_rate": 97.8,
          "average_delivery_time": 2.0
        }
      ],
      "error_analysis": [
        {
          "error_code": "30008",
          "error_message": "Unknown error",
          "count": 12,
          "last_occurrence": "2024-08-11T15:20:00Z"
        },
        {
          "error_code": "21211",
          "error_message": "Invalid 'To' Phone Number",
          "count": 8,
          "last_occurrence": "2024-08-11T14:45:00Z"
        }
      ],
      "geographic_distribution": [
        {
          "country": "United States",
          "messages_sent": 1089,
          "success_rate": 98.5
        },
        {
          "country": "Canada", 
          "messages_sent": 158,
          "success_rate": 96.8
        }
      ]
    },
    "billing_info": {
      "current_balance": 456.78,
      "last_charge": {
        "amount": 125.45,
        "date": "2024-08-10T09:00:00Z",
        "description": "SMS usage charges"
      },
      "estimated_monthly_cost": 2150.00
    }
  }
}
```

## Implementation Tasks

### 1. Database Model (30 minutes)
```typescript
// src/lib/db/models/provider-config.model.ts
import { Schema, model } from 'mongoose';

const providerConfigSchema = new Schema({
  org_id: { type: String, required: true, index: true },
  provider_id: { type: String, required: true, index: true },
  name: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['sms', 'email', 'push'],
    required: true 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'error', 'suspended'],
    default: 'active'
  },
  
  // Provider-specific configuration
  configuration: {
    // Twilio
    account_sid: String,
    auth_token: String,
    phone_numbers: [{
      number: String,
      status: String,
      capabilities: [String],
      friendly_name: String,
      purchased_date: Date
    }],
    
    // SendGrid
    api_key: String,
    sender_domains: [{
      domain: String,
      status: String,
      dkim_enabled: Boolean,
      spf_enabled: Boolean,
      verified_date: Date
    }],
    
    // Firebase
    server_key: String,
    project_id: String,
    
    // Common settings
    webhook_url: String,
    status_callback_url: String,
    events: [String],
    rate_limits: {
      messages_per_second: Number,
      daily_limit: Number,
      monthly_limit: Number
    }
  },
  
  // Health monitoring
  health_status: {
    status: { 
      type: String, 
      enum: ['healthy', 'warning', 'error', 'unknown'],
      default: 'unknown'
    },
    last_check: Date,
    response_time: Number,
    error_message: String
  },
  
  // Connection testing
  connection_test: {
    enabled: { type: Boolean, default: true },
    interval_minutes: { type: Number, default: 30 },
    last_test: Date,
    test_status: String,
    test_response_time: Number,
    consecutive_failures: { type: Number, default: 0 }
  },
  
  // Usage tracking
  usage_tracking: {
    enabled: { type: Boolean, default: true },
    daily_usage: Number,
    monthly_usage: Number,
    last_reset_date: Date
  }
}, { 
  timestamps: true,
  collection: 'provider_configurations'
});

// Compound index for efficient querying
providerConfigSchema.index({ org_id: 1, provider_id: 1 }, { unique: true });
providerConfigSchema.index({ org_id: 1, type: 1 });
providerConfigSchema.index({ org_id: 1, status: 1 });

export const ProviderConfigModel = model('ProviderConfig', providerConfigSchema);
```

### 2. DAO Implementation (45 minutes)
```typescript
// src/lib/db/dao/provider-config.dao.ts
import { ProviderConfigModel } from '../models/provider-config.model';
import { MessageAnalyticsModel } from '../models/message-analytics.model';
import { DatabaseError, NotFoundError } from '@/lib/errors/error-types';

export class ProviderConfigDAO {
  static async getProviders(orgId: string, filters: any = {}) {
    try {
      const query: any = { org_id: orgId };
      
      // Apply filters
      if (filters.type) query.type = filters.type;
      if (filters.status) query.status = filters.status;

      const providers = await ProviderConfigModel.find(query).lean();
      
      // Get metrics if requested
      if (filters.include_metrics !== false) {
        for (const provider of providers) {
          provider.metrics = await this.getProviderMetrics(orgId, provider.provider_id);
          provider.cost_metrics = await this.getProviderCostMetrics(orgId, provider.provider_id);
        }
      }

      return providers.map(provider => this.formatProviderResponse(provider, filters.include_config));

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve providers',
        'DB_PROVIDERS_GET_FAILED'
      );
    }
  }

  static async getProviderById(orgId: string, providerId: string, includeConfig: boolean = false) {
    try {
      const provider = await ProviderConfigModel.findOne({
        org_id: orgId,
        provider_id: providerId
      }).lean();

      if (!provider) {
        throw new NotFoundError(
          `Provider ${providerId} not found`,
          'PROVIDER_NOT_FOUND'
        );
      }

      // Get detailed metrics and billing info
      const [metrics, costMetrics, detailedMetrics] = await Promise.all([
        this.getProviderMetrics(orgId, providerId),
        this.getProviderCostMetrics(orgId, providerId),
        this.getDetailedProviderMetrics(orgId, providerId)
      ]);

      return {
        ...this.formatProviderResponse(provider, includeConfig),
        detailed_metrics: detailedMetrics,
        billing_info: await this.getProviderBillingInfo(orgId, providerId)
      };

    } catch (error: unknown) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve provider',
        'DB_PROVIDER_GET_FAILED'
      );
    }
  }

  private static async getProviderMetrics(orgId: string, providerId: string) {
    try {
      const now = new Date();
      const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const [metrics24h, metrics7d] = await Promise.all([
        MessageAnalyticsModel.aggregate([
          {
            $match: {
              org_id: orgId,
              'provider_breakdown.provider': providerId,
              period_start: { $gte: last24h }
            }
          },
          {
            $group: {
              _id: null,
              messages_sent: { $sum: '$metrics.messages_sent' },
              messages_delivered: { $sum: '$metrics.messages_delivered' },
              messages_failed: { $sum: '$metrics.messages_failed' },
              messages_opened: { $sum: '$metrics.messages_opened' },
              messages_clicked: { $sum: '$metrics.messages_clicked' },
              messages_bounced: { $sum: '$metrics.messages_bounced' },
              avg_delivery_time: { $avg: '$performance.average_delivery_time' }
            }
          }
        ]),
        MessageAnalyticsModel.aggregate([
          {
            $match: {
              org_id: orgId,
              'provider_breakdown.provider': providerId,
              period_start: { $gte: last7d }
            }
          },
          {
            $group: {
              _id: null,
              messages_sent: { $sum: '$metrics.messages_sent' },
              messages_delivered: { $sum: '$metrics.messages_delivered' },
              messages_failed: { $sum: '$metrics.messages_failed' },
              messages_opened: { $sum: '$metrics.messages_opened' },
              messages_clicked: { $sum: '$metrics.messages_clicked' },
              messages_bounced: { $sum: '$metrics.messages_bounced' },
              avg_delivery_time: { $avg: '$performance.average_delivery_time' }
            }
          }
        ])
      ]);

      const format = (data: any) => data.length > 0 ? {
        messages_sent: data[0].messages_sent || 0,
        success_rate: data[0].messages_sent > 0 ? 
          ((data[0].messages_delivered || 0) / data[0].messages_sent * 100) : 0,
        average_delivery_time: data[0].avg_delivery_time || 0,
        open_rate: data[0].messages_delivered > 0 ? 
          ((data[0].messages_opened || 0) / data[0].messages_delivered * 100) : 0,
        click_rate: data[0].messages_opened > 0 ? 
          ((data[0].messages_clicked || 0) / data[0].messages_opened * 100) : 0,
        bounce_rate: data[0].messages_sent > 0 ? 
          ((data[0].messages_bounced || 0) / data[0].messages_sent * 100) : 0,
        error_count: data[0].messages_failed || 0
      } : {
        messages_sent: 0,
        success_rate: 0,
        average_delivery_time: 0,
        open_rate: 0,
        click_rate: 0,
        bounce_rate: 0,
        error_count: 0
      };

      return {
        last_24h: format(metrics24h),
        last_7d: format(metrics7d)
      };

    } catch (error: unknown) {
      console.error('Failed to get provider metrics:', error);
      return {
        last_24h: { messages_sent: 0, success_rate: 0, average_delivery_time: 0, error_count: 0 },
        last_7d: { messages_sent: 0, success_rate: 0, average_delivery_time: 0, error_count: 0 }
      };
    }
  }

  private static async getProviderCostMetrics(orgId: string, providerId: string) {
    // Implementation for cost metrics retrieval
    // This would aggregate cost data from message analytics
    return {
      last_24h_cost: 0,
      last_7d_cost: 0,
      average_cost_per_message: 0
    };
  }

  private static async getDetailedProviderMetrics(orgId: string, providerId: string) {
    // Implementation for detailed metrics including hourly stats, error analysis, etc.
    return {
      hourly_stats: [],
      error_analysis: [],
      geographic_distribution: []
    };
  }

  private static async getProviderBillingInfo(orgId: string, providerId: string) {
    // Implementation for provider billing information
    return {
      current_balance: 0,
      last_charge: null,
      estimated_monthly_cost: 0
    };
  }

  private static formatProviderResponse(provider: any, includeConfig: boolean = false) {
    const response: any = {
      provider_id: provider.provider_id,
      name: provider.name,
      type: provider.type,
      status: provider.status,
      health_status: provider.health_status?.status || 'unknown',
      last_health_check: provider.health_status?.last_check,
      metrics: provider.metrics,
      cost_metrics: provider.cost_metrics
    };

    if (includeConfig) {
      response.configuration = this.maskSensitiveConfig(provider.configuration);
    }

    return response;
  }

  private static maskSensitiveConfig(config: any) {
    if (!config) return {};
    
    const masked = { ...config };
    
    // Mask sensitive fields
    if (masked.auth_token) masked.auth_token = '***masked***';
    if (masked.api_key) masked.api_key = masked.api_key.substring(0, 3) + '***masked***';
    if (masked.server_key) masked.server_key = '***masked***';
    if (masked.account_sid) masked.account_sid = masked.account_sid.substring(0, 8) + '***masked***';
    
    return masked;
  }
}
```

### 3. Handler Implementation (30 minutes)
```typescript
// src/handlers/provider-config.handler.ts
import { Request, Response } from 'express';
import { ProviderConfigDAO } from '@/lib/db/dao/provider-config.dao';
import { handleError } from '@/lib/errors/error-handler';
import { OrganizationError } from '@/lib/errors/error-types';

export class ProviderConfigHandler {
  static async getProviders(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const filters = {
        type: req.query.type as string,
        status: req.query.status as string,
        include_metrics: req.query.include_metrics !== 'false',
        include_config: req.query.include_config === 'true'
      };

      const providers = await ProviderConfigDAO.getProviders(orgId, filters);

      // Calculate summary
      const summary = {
        total_providers: providers.length,
        active_providers: providers.filter(p => p.status === 'active').length,
        inactive_providers: providers.filter(p => p.status === 'inactive').length,
        providers_with_errors: providers.filter(p => p.health_status === 'error').length,
        total_messages_24h: providers.reduce((sum, p) => sum + (p.metrics?.last_24h?.messages_sent || 0), 0),
        total_cost_24h: providers.reduce((sum, p) => sum + (p.cost_metrics?.last_24h_cost || 0), 0)
      };

      res.status(200).json({
        success: true,
        data: {
          providers,
          summary
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getProviderById(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { provider_id } = req.params;
      const includeConfig = req.query.include_config === 'true';

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const provider = await ProviderConfigDAO.getProviderById(orgId, provider_id, includeConfig);

      res.status(200).json({
        success: true,
        data: provider,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (10 minutes)
```typescript
// src/routes/provider-config.routes.ts
import { Router } from 'express';
import { ProviderConfigHandler } from '@/handlers/provider-config.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateProviderQuery } from '@/validators/provider-config.validator';

const router = Router();

// Get all providers
router.get(
  '/',
  supabaseAuthMiddleware,
  validateProviderQuery,
  ProviderConfigHandler.getProviders
);

// Get provider by ID
router.get(
  '/:provider_id',
  supabaseAuthMiddleware,
  ProviderConfigHandler.getProviderById
);

export { router as providerConfigRoutes };
```

### 5. Validation Schema (15 minutes)
```typescript
// src/validators/provider-config.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const providerQuerySchema = Joi.object({
  type: Joi.string().valid('sms', 'email', 'push').optional(),
  status: Joi.string().valid('active', 'inactive', 'error', 'suspended').optional(),
  include_metrics: Joi.boolean().default(true),
  include_config: Joi.boolean().default(false)
});

export const validateProviderQuery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = providerQuerySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};
```

## Error Handling

### Common Errors
- **404 Provider Not Found**: Provider doesn't exist or user doesn't have access
- **400 Invalid Parameters**: Invalid query parameters
- **429 Rate Limited**: Too many requests
- **500 Provider Unavailable**: Provider service unavailable
- **503 Health Check Failed**: Provider health check failing

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "PROVIDER_NOT_FOUND",
    "message": "Provider twilio not found",
    "code": 404
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Security Considerations

1. **Configuration Masking**: Mask sensitive configuration data (API keys, tokens)
2. **Access Control**: Ensure users can only access their organization's providers
3. **Rate Limiting**: Prevent abuse of provider endpoints
4. **Health Check Security**: Secure health check endpoints
5. **Audit Logging**: Track provider configuration access

## Monitoring and Health Checks

### Health Check Implementation
- Automatic health checks every 30 minutes
- Connection testing with actual provider APIs
- Response time monitoring
- Error rate tracking
- Alert thresholds for provider failures

### Metrics to Track
- Provider response times
- Success/failure rates by provider
- Cost per provider
- Usage patterns
- Configuration changes