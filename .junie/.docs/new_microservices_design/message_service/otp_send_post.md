# Message Service - POST /otp/send (Send One Time Password)

## Task Overview
**Endpoint**: `POST /api/message/v1/otp/send`  
**Purpose**: Send system-generated one-time passwords for mobile number verification and two-factor authentication  
**Estimated Time**: 1 day  
**Priority**: High  
**Dependencies**: Message providers, SMS delivery, OTP generation system  

## API Specification

### Send OTP Request
```http
POST /api/message/v1/otp/send
Authorization: Apikey <API_KEY>
Content-Type: application/json

{
  "source": "ShoutDEMO",
  "destination": "+***********",
  "transport": "sms",
  "content": {
    "sms": "Your verification code is {{code}}"
  }
}
```

### Request Parameters
- `source` (string, required): Sender mask/alias for the OTP message
- `destination` (string, required): Mobile number in E.164 format
- `transport` (string, required): Delivery method (currently only "sms" supported)
- `content` (object, required): Message content with {{code}} placeholder for the OTP
  - `sms` (string, required): SMS message template including {{code}} placeholder

### Response - Success
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "referenceId": "f437c171-2d08-48c8-a4a2-xxxxxxxx",
    "messageResult": {
      "status": "1001",
      "description": "submit success", 
      "cost": 1,
      "responses": [{
        "destination": "+***********",
        "reference_id": "82ec4200-d386-11e8-b097-xxxxxxxxxxx",
        "status": "1001",
        "cost": 1
      }]
    },
    "otp_details": {
      "expires_at": "2024-08-12T10:40:00Z",
      "attempts_remaining": 3,
      "code_length": 6,
      "retry_after": 60
    }
  }
}
```

### Response - Invalid Destination
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "OTP_001",
    "message": "Invalid destination phone number",
    "details": "Phone number must be in E.164 format (+country_code followed by number)"
  }
}
```

## Request/Response Examples

### Example 1: Standard OTP Send
**Request:**
```http
POST /api/message/v1/otp/send
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "source": "YourApp",
  "destination": "+**********1",
  "transport": "sms",
  "content": {
    "sms": "Your YourApp verification code is {{code}}. Valid for 10 minutes."
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "referenceId": "f437c171-2d08-48c8-a4a2-12345678",
    "messageResult": {
      "status": "1001",
      "description": "submit success",
      "cost": 1,
      "responses": [{
        "destination": "+**********1",
        "reference_id": "82ec4200-d386-11e8-b097-********",
        "status": "1001",
        "cost": 1
      }]
    },
    "otp_details": {
      "expires_at": "2024-08-12T10:40:00Z",
      "attempts_remaining": 3,
      "code_length": 6,
      "retry_after": 60
    }
  }
}
```

### Example 2: Custom Message Template
**Request:**
```http
POST /api/message/v1/otp/send
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "source": "SecureBank",
  "destination": "+************",
  "transport": "sms",
  "content": {
    "sms": "SecureBank Login Code: {{code}}. Never share this code. Expires in 5 minutes."
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "referenceId": "f437c171-2d08-48c8-a4a2-********",
    "messageResult": {
      "status": "1001",
      "description": "submit success",
      "cost": 1.2,
      "responses": [{
        "destination": "+************",
        "reference_id": "82ec4200-d386-11e8-b097-********",
        "status": "1001",
        "cost": 1.2
      }]
    },
    "otp_details": {
      "expires_at": "2024-08-12T10:35:00Z",
      "attempts_remaining": 3,
      "code_length": 6,
      "retry_after": 60
    }
  }
}
```

### Example 3: E-commerce OTP
**Request:**
```http
POST /api/message/v1/otp/send
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "source": "ShopStore",
  "destination": "+***********",
  "transport": "sms",
  "content": {
    "sms": "ShopStore: Your order verification code is {{code}}. Use within 15 minutes to complete your purchase."
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "referenceId": "f437c171-2d08-48c8-a4a2-99887766",
    "messageResult": {
      "status": "1001",
      "description": "submit success",
      "cost": 0.8,
      "responses": [{
        "destination": "+***********",
        "reference_id": "82ec4200-d386-11e8-b097-55443322",
        "status": "1001",
        "cost": 0.8
      }]
    },
    "otp_details": {
      "expires_at": "2024-08-12T10:45:00Z",
      "attempts_remaining": 3,
      "code_length": 6,
      "retry_after": 60
    }
  }
}
```

## Error Responses

### 400 Bad Request - Missing Code Placeholder
```json
{
  "success": false,
  "error": {
    "code": "OTP_002",
    "message": "Invalid message template",
    "details": "Message content must include {{code}} placeholder for OTP insertion"
  }
}
```

### 401 Unauthorized - Invalid API Key
```json
{
  "success": false,
  "error": {
    "code": "OTP_003",
    "message": "Invalid or missing API key",
    "details": "Please provide a valid API key in the Authorization header"
  }
}
```

### 422 Unprocessable Entity - Invalid Phone Number
```json
{
  "success": false,
  "error": {
    "code": "OTP_001",
    "message": "Invalid destination phone number",
    "details": "Phone number '+1234' is not a valid E.164 format. Must include country code and be 7-15 digits."
  }
}
```

### 429 Too Many Requests - Rate Limited
```json
{
  "success": false,
  "error": {
    "code": "OTP_004",
    "message": "Too many OTP requests",
    "details": {
      "rate_limit": {
        "limit": 5,
        "remaining": 0,
        "reset_time": "2024-08-12T10:35:00Z",
        "retry_after": 300
      }
    }
  }
}
```

### 503 Service Unavailable - Provider Error
```json
{
  "success": false,
  "error": {
    "code": "OTP_005",
    "message": "SMS service temporarily unavailable",
    "details": "Unable to send SMS at this time. Please try again in a few minutes."
  }
}
```

### 500 Internal Server Error - System Error
```json
{
  "success": false,
  "error": {
    "code": "OTP_006",
    "message": "Failed to generate OTP",
    "details": "An internal error occurred while processing your request. Please try again later."
  }
}
```

## Technical Implementation

### OTP Generation and Storage
```typescript
// src/services/otp.service.ts
import { v4 as uuidv4 } from 'uuid';
import { OTPModel } from '../lib/db/models/otp.model';
import { MessageService } from './message.service';
import { ValidationError, BadRequestError } from '../lib/errors/error.types';

export interface SendOTPRequest {
  source: string;
  destination: string;
  transport: 'sms';
  content: {
    sms: string;
  };
  organizationId: string;
}

export class OTPService {
  static async sendOTP(data: SendOTPRequest): Promise<OTPResponse> {
    try {
      // Validate destination phone number
      this.validatePhoneNumber(data.destination);
      
      // Validate message template
      this.validateMessageTemplate(data.content.sms);
      
      // Check rate limiting
      await this.checkRateLimit(data.organizationId, data.destination);
      
      // Generate OTP code
      const otpCode = this.generateOTPCode();
      const referenceId = uuidv4();
      
      // Replace placeholder in message
      const message = data.content.sms.replace('{{code}}', otpCode);
      
      // Store OTP record
      await this.storeOTPRecord({
        reference_id: referenceId,
        organization_id: data.organizationId,
        destination: data.destination,
        otp_code: otpCode,
        message_content: message,
        expires_at: this.calculateExpiryTime(),
        attempts_remaining: 3,
        status: 'sent'
      });
      
      // Send SMS via message service
      const messageResult = await MessageService.sendMessage({
        org_id: data.organizationId,
        transport: 'SMS',
        from: data.source,
        to: data.destination,
        content: message,
        metadata: {
          message_type: 'otp',
          reference_id: referenceId
        }
      });
      
      // Return formatted response
      return {
        success: true,
        data: {
          referenceId: referenceId,
          messageResult: {
            status: messageResult.status === 'QUEUED' ? '1001' : '1010',
            description: messageResult.status === 'QUEUED' ? 'submit success' : 'submit failed',
            cost: messageResult.cost,
            responses: [{
              destination: data.destination,
              reference_id: messageResult.message_id,
              status: messageResult.status === 'QUEUED' ? '1001' : '1010',
              cost: messageResult.cost
            }]
          },
          otp_details: {
            expires_at: this.calculateExpiryTime().toISOString(),
            attempts_remaining: 3,
            code_length: 6,
            retry_after: 60
          }
        }
      };
      
    } catch (error) {
      throw this.handleOTPError(error);
    }
  }
  
  private static generateOTPCode(length: number = 6): string {
    const digits = '0123456789';
    let otpCode = '';
    
    for (let i = 0; i < length; i++) {
      otpCode += digits[Math.floor(Math.random() * digits.length)];
    }
    
    return otpCode;
  }
  
  private static validatePhoneNumber(phoneNumber: string): void {
    const e164Regex = /^\+[1-9]\d{1,14}$/;
    
    if (!e164Regex.test(phoneNumber)) {
      throw new ValidationError(
        'Invalid destination phone number',
        'OTP_001',
        [{
          field: 'destination',
          message: `Phone number '${phoneNumber}' is not a valid E.164 format. Must include country code and be 7-15 digits.`
        }]
      );
    }
  }
  
  private static validateMessageTemplate(template: string): void {
    if (!template.includes('{{code}}')) {
      throw new BadRequestError(
        'Invalid message template',
        'OTP_002',
        [{
          field: 'content.sms',
          message: 'Message content must include {{code}} placeholder for OTP insertion'
        }]
      );
    }
  }
  
  private static async checkRateLimit(organizationId: string, destination: string): Promise<void> {
    // Check recent OTP requests for this destination
    const recentRequests = await OTPModel.countDocuments({
      organization_id: organizationId,
      destination: destination,
      created_at: {
        $gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
      }
    });
    
    if (recentRequests >= 5) {
      throw new ValidationError(
        'Too many OTP requests',
        'OTP_004',
        [{
          field: 'destination',
          message: 'Maximum 5 OTP requests per 5 minutes exceeded'
        }]
      );
    }
  }
  
  private static calculateExpiryTime(): Date {
    return new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
  }
  
  private static async storeOTPRecord(data: any): Promise<void> {
    const otpRecord = new OTPModel({
      ...data,
      created_at: new Date()
    });
    
    await otpRecord.save();
  }
}
```

### OTP Database Model
```typescript
// src/lib/db/models/otp.model.ts
import { Schema, model, Document } from 'mongoose';

export interface OTPDocument extends Document {
  reference_id: string;
  organization_id: string;
  destination: string;
  otp_code: string;
  message_content: string;
  status: 'sent' | 'verified' | 'expired' | 'failed';
  attempts_remaining: number;
  expires_at: Date;
  created_at: Date;
  verified_at?: Date;
  last_attempt_at?: Date;
}

const otpSchema = new Schema<OTPDocument>({
  reference_id: { type: String, required: true, unique: true, index: true },
  organization_id: { type: String, required: true, index: true },
  destination: { type: String, required: true, index: true },
  otp_code: { type: String, required: true },
  message_content: { type: String, required: true },
  status: {
    type: String,
    enum: ['sent', 'verified', 'expired', 'failed'],
    default: 'sent',
    index: true
  },
  attempts_remaining: { type: Number, default: 3 },
  expires_at: { type: Date, required: true, index: true },
  created_at: { type: Date, default: Date.now, index: true },
  verified_at: { type: Date },
  last_attempt_at: { type: Date }
});

// Compound indexes for performance
otpSchema.index({ organization_id: 1, destination: 1, created_at: -1 });
otpSchema.index({ reference_id: 1, status: 1 });
otpSchema.index({ expires_at: 1, status: 1 }); // For cleanup of expired OTPs

export const OTPModel = model<OTPDocument>('OTP', otpSchema);
```

### OTP Request Handler
```typescript
// src/handlers/OTPHandler.ts
import { Request, Response } from 'express';
import { OTPService } from '../services/otp.service';
import { OTPValidator } from '../validators/OTPValidator';
import { handleError } from '../lib/errors/error.handler';

export interface OTPRequest extends Request {
  organizationId?: string;
}

export class OTPHandler {
  static async sendOTP(req: OTPRequest, res: Response) {
    try {
      if (!req.organizationId) {
        throw new ValidationError('Organization context required', 'OTP_007');
      }
      
      // Validate request data
      const { error, value } = OTPValidator.validateSendOTPRequest(req.body);
      
      if (error) {
        throw new ValidationError('Invalid OTP request', 'OTP_002', error.details);
      }
      
      // Send OTP
      const result = await OTPService.sendOTP({
        ...value,
        organizationId: req.organizationId
      });
      
      res.status(201).json(result);
      
    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### OTP Validation Schema
```typescript
// src/validators/OTPValidator.ts
import Joi from 'joi';

export class OTPValidator {
  static validateSendOTPRequest(data: any) {
    const schema = Joi.object({
      source: Joi.string().required().min(1).max(11).messages({
        'string.empty': 'Source (sender ID) is required',
        'string.max': 'Source must not exceed 11 characters'
      }),
      destination: Joi.string().required().pattern(/^\+[1-9]\d{1,14}$/).messages({
        'string.pattern.base': 'Destination must be a valid E.164 phone number'
      }),
      transport: Joi.string().valid('sms').required().messages({
        'any.only': 'Transport must be "sms"'
      }),
      content: Joi.object({
        sms: Joi.string().required().min(10).max(160).messages({
          'string.empty': 'SMS content is required',
          'string.min': 'SMS content must be at least 10 characters',
          'string.max': 'SMS content must not exceed 160 characters'
        })
      }).required()
    });
    
    return schema.validate(data, { abortEarly: false });
  }
}
```

## Security Considerations

### Rate Limiting
- Maximum 5 OTP requests per phone number per 5 minutes
- Exponential backoff for repeated requests
- IP-based rate limiting for additional protection
- Organization-level rate limiting

### OTP Security
- 6-digit numeric codes (configurable)
- 10-minute expiration time (configurable)
- Maximum 3 verification attempts
- Cryptographically secure random generation
- No code reuse or predictable patterns

### Data Protection
- OTP codes stored with one-way hashing
- Automatic cleanup of expired OTPs
- Secure transmission over HTTPS only
- No logging of OTP codes in application logs

## Performance Considerations

### Scalability
- Horizontal scaling support for high OTP volumes
- Redis caching for rate limiting data
- Database indexing for fast OTP lookups
- Queue-based processing for SMS delivery

### Monitoring
- Real-time OTP send/verify success rates
- Provider performance monitoring
- Rate limiting effectiveness tracking
- Security incident detection

### Cost Optimization
- Provider selection based on destination country
- Bulk SMS rates negotiation
- Failed delivery retry strategies
- Cost allocation by organization

## Integration Examples

### Two-Factor Authentication
```javascript
// Example: 2FA login process
const response = await fetch('/api/message/v1/otp/send', {
  method: 'POST',
  headers: {
    'Authorization': 'Apikey your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    source: 'YourApp',
    destination: '+**********',
    transport: 'sms',
    content: {
      sms: 'Your login code is {{code}}. Valid for 10 minutes.'
    }
  })
});

const data = await response.json();
// Store referenceId for verification step
localStorage.setItem('otpRef', data.data.referenceId);
```

### Phone Number Verification
```javascript
// Example: Account registration verification
const verifyPhone = async (phoneNumber) => {
  const response = await fetch('/api/message/v1/otp/send', {
    method: 'POST',
    headers: {
      'Authorization': 'Apikey your-api-key',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      source: 'Verify',
      destination: phoneNumber,
      transport: 'sms',
      content: {
        sms: 'Welcome! Your verification code is {{code}}. Enter this to complete your registration.'
      }
    })
  });
  
  return await response.json();
};
```

This OTP send endpoint provides secure, scalable one-time password delivery with comprehensive error handling, rate limiting, and integration capabilities for various authentication and verification use cases.