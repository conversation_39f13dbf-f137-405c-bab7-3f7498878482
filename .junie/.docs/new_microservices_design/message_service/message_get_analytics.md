# Message Service - GET /analytics (Message Analytics)

## Task Overview
**Endpoint**: `GET /api/message/v1/analytics`  
**Purpose**: Retrieve comprehensive message analytics including delivery rates, performance metrics, and channel insights  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Message tracking, webhook processing, analytics aggregation system  

## API Specification

### Overview Analytics Request
```http
GET /api/message/v1/analytics/overview?period=7d&channels=sms,email
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Campaign-Specific Analytics Request
```http
GET /api/message/v1/analytics/campaign/64a1b2c3d4e5f6789012346?period=24h&granularity=hourly
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Channel Performance Request
```http
GET /api/message/v1/analytics/channels?period=30d&compare=previous_period
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Query Parameters
- `period` (string): Time period (1h, 24h, 7d, 30d, 90d, 1y)
- `granularity` (string): Data granularity (hourly, daily, weekly, monthly)
- `channels` (string): Comma-separated list of channels (sms, email, push)
- `campaign_id` (string): Filter by specific campaign
- `compare` (string): Comparison period (previous_period, previous_year)
- `timezone` (string): Timezone for data aggregation (default: UTC)

### Response - Overview Analytics
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "period": {
      "start": "2024-08-04T00:00:00Z",
      "end": "2024-08-11T23:59:59Z",
      "duration_days": 7
    },
    "summary": {
      "total_messages": 45680,
      "messages_sent": 44892,
      "messages_delivered": 42156,
      "messages_failed": 2736,
      "delivery_rate": 93.9,
      "failure_rate": 6.1,
      "average_delivery_time": 2.3,
      "bounce_rate": 2.8,
      "open_rate": 24.6,
      "click_rate": 3.2
    },
    "channels": {
      "sms": {
        "total_messages": 28450,
        "delivered": 27123,
        "failed": 1327,
        "delivery_rate": 95.3,
        "average_cost": 0.048,
        "total_cost": 1365.60
      },
      "email": {
        "total_messages": 15230,
        "delivered": 13890,
        "failed": 1340,
        "delivery_rate": 91.2,
        "opened": 3749,
        "clicked": 487,
        "open_rate": 27.0,
        "click_rate": 3.5,
        "bounce_rate": 8.8,
        "average_cost": 0.012,
        "total_cost": 182.76
      },
      "push": {
        "total_messages": 2000,
        "delivered": 1143,
        "failed": 69,
        "delivery_rate": 94.3,
        "opened": 286,
        "open_rate": 25.0,
        "average_cost": 0.005,
        "total_cost": 10.00
      }
    },
    "trends": [
      {
        "date": "2024-08-05",
        "total_messages": 6523,
        "delivered": 6201,
        "failed": 322,
        "delivery_rate": 95.1
      },
      {
        "date": "2024-08-06", 
        "total_messages": 7124,
        "delivered": 6698,
        "failed": 426,
        "delivery_rate": 94.0
      }
    ],
    "top_campaigns": [
      {
        "campaign_id": "64a1b2c3d4e5f6789012346",
        "campaign_name": "Summer Sale 2024",
        "total_messages": 12450,
        "delivery_rate": 96.2,
        "engagement_rate": 8.4
      }
    ],
    "error_analysis": {
      "common_errors": [
        {
          "error_code": "INVALID_NUMBER",
          "error_message": "Invalid phone number format",
          "count": 892,
          "percentage": 32.6
        },
        {
          "error_code": "BOUNCED_EMAIL",
          "error_message": "Email address bounced",
          "count": 567,
          "percentage": 20.7
        }
      ],
      "provider_errors": {
        "twilio": { "count": 145, "rate": 0.5 },
        "sendgrid": { "count": 234, "rate": 1.5 }
      }
    }
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347",
    "cache_status": "hit",
    "query_time": 156
  }
}
```

### Response - Campaign Analytics
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "campaign_id": "64a1b2c3d4e5f6789012346",
    "campaign_name": "Summer Sale 2024",
    "period": {
      "start": "2024-08-10T00:00:00Z",
      "end": "2024-08-10T23:59:59Z"
    },
    "performance": {
      "total_messages": 12450,
      "messages_sent": 12203,
      "messages_delivered": 11789,
      "messages_failed": 414,
      "delivery_rate": 96.6,
      "total_cost": 623.45,
      "cost_per_message": 0.051,
      "recipients_reached": 11789,
      "unique_recipients": 11456
    },
    "channel_breakdown": {
      "sms": {
        "sent": 7500,
        "delivered": 7312,
        "failed": 188,
        "delivery_rate": 97.5,
        "cost": 375.00
      },
      "email": {
        "sent": 4703,
        "delivered": 4477,
        "failed": 226,
        "opened": 1251,
        "clicked": 178,
        "delivery_rate": 95.2,
        "open_rate": 27.9,
        "click_rate": 4.0,
        "cost": 56.44
      }
    },
    "timeline": [
      {
        "hour": "2024-08-10T09:00:00Z",
        "sent": 1245,
        "delivered": 1198,
        "opened": 89,
        "clicked": 12
      },
      {
        "hour": "2024-08-10T10:00:00Z", 
        "sent": 2156,
        "delivered": 2089,
        "opened": 165,
        "clicked": 23
      }
    ],
    "geographic_distribution": [
      {
        "country": "United States",
        "sent": 8934,
        "delivered": 8623,
        "delivery_rate": 96.5
      },
      {
        "country": "Canada",
        "sent": 2156,
        "delivered": 2089,
        "delivery_rate": 96.9
      }
    ],
    "device_breakdown": {
      "mobile": { "count": 8945, "percentage": 75.9 },
      "desktop": { "count": 2156, "percentage": 18.3 },
      "tablet": { "count": 678, "percentage": 5.8 }
    }
  }
}
```

## Implementation Tasks

### 1. Analytics Database Models (1 hour)
```typescript
// src/lib/db/models/message-analytics.model.ts
import { Schema, model } from 'mongoose';

const messageAnalyticsSchema = new Schema({
  org_id: { type: String, required: true, index: true },
  period_start: { type: Date, required: true, index: true },
  period_end: { type: Date, required: true },
  granularity: { 
    type: String, 
    enum: ['hourly', 'daily', 'weekly', 'monthly'],
    required: true 
  },
  
  // Channel-specific metrics
  channel: { 
    type: String, 
    enum: ['sms', 'email', 'push', 'all'],
    required: true 
  },
  
  // Campaign association (optional)
  campaign_id: { type: String, index: true },
  
  // Core metrics
  metrics: {
    total_messages: { type: Number, default: 0 },
    messages_sent: { type: Number, default: 0 },
    messages_delivered: { type: Number, default: 0 },
    messages_failed: { type: Number, default: 0 },
    messages_pending: { type: Number, default: 0 },
    
    // Email-specific metrics
    messages_opened: { type: Number, default: 0 },
    messages_clicked: { type: Number, default: 0 },
    messages_bounced: { type: Number, default: 0 },
    messages_unsubscribed: { type: Number, default: 0 },
    
    // Calculated rates
    delivery_rate: { type: Number, default: 0 },
    failure_rate: { type: Number, default: 0 },
    open_rate: { type: Number, default: 0 },
    click_rate: { type: Number, default: 0 },
    bounce_rate: { type: Number, default: 0 },
    
    // Cost metrics
    total_cost: { type: Number, default: 0 },
    average_cost_per_message: { type: Number, default: 0 }
  },
  
  // Performance metrics
  performance: {
    average_delivery_time: { type: Number, default: 0 }, // seconds
    processing_time: { type: Number, default: 0 }, // milliseconds
    queue_wait_time: { type: Number, default: 0 } // milliseconds
  },
  
  // Error analysis
  error_breakdown: [{
    error_code: String,
    error_message: String,
    count: Number,
    percentage: Number
  }],
  
  // Provider performance
  provider_breakdown: [{
    provider: String,
    sent: Number,
    delivered: Number,
    failed: Number,
    delivery_rate: Number,
    average_cost: Number
  }],
  
  // Geographic distribution
  geographic_data: [{
    country: String,
    country_code: String,
    sent: Number,
    delivered: Number,
    delivery_rate: Number
  }],
  
  // Additional metadata
  metadata: {
    unique_recipients: { type: Number, default: 0 },
    repeat_recipients: { type: Number, default: 0 },
    first_time_recipients: { type: Number, default: 0 }
  }
}, { 
  timestamps: true,
  collection: 'message_analytics'
});

// Composite indexes for efficient querying
messageAnalyticsSchema.index({ 
  org_id: 1, 
  period_start: 1, 
  granularity: 1, 
  channel: 1 
});
messageAnalyticsSchema.index({ 
  org_id: 1, 
  campaign_id: 1, 
  period_start: 1 
});
messageAnalyticsSchema.index({ period_start: 1, period_end: 1 });

export const MessageAnalyticsModel = model('MessageAnalytics', messageAnalyticsSchema);
```

### 2. Analytics DAO Implementation (1.5 hours)
```typescript
// src/lib/db/dao/message-analytics.dao.ts
import { MessageAnalyticsModel } from '../models/message-analytics.model';
import { MessageModel } from '../models/message.model';
import { DatabaseError } from '@/lib/errors/error-types';

export class MessageAnalyticsDAO {
  static async getOverviewAnalytics(
    orgId: string, 
    period: { start: Date; end: Date }, 
    channels?: string[]
  ) {
    try {
      const matchStage: any = {
        org_id: orgId,
        period_start: { $gte: period.start, $lte: period.end }
      };

      if (channels && channels.length > 0) {
        matchStage.channel = { $in: [...channels, 'all'] };
      }

      const pipeline = [
        { $match: matchStage },
        {
          $group: {
            _id: '$channel',
            total_messages: { $sum: '$metrics.total_messages' },
            messages_sent: { $sum: '$metrics.messages_sent' },
            messages_delivered: { $sum: '$metrics.messages_delivered' },
            messages_failed: { $sum: '$metrics.messages_failed' },
            messages_opened: { $sum: '$metrics.messages_opened' },
            messages_clicked: { $sum: '$metrics.messages_clicked' },
            messages_bounced: { $sum: '$metrics.messages_bounced' },
            total_cost: { $sum: '$metrics.total_cost' },
            avg_delivery_time: { $avg: '$performance.average_delivery_time' }
          }
        }
      ];

      const results = await MessageAnalyticsModel.aggregate(pipeline);
      return this.formatOverviewResults(results, period);

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve analytics overview',
        'DB_ANALYTICS_OVERVIEW_FAILED'
      );
    }
  }

  static async getCampaignAnalytics(
    orgId: string,
    campaignId: string,
    period: { start: Date; end: Date },
    granularity: string = 'daily'
  ) {
    try {
      const analytics = await MessageAnalyticsModel.find({
        org_id: orgId,
        campaign_id: campaignId,
        period_start: { $gte: period.start, $lte: period.end },
        granularity
      }).sort({ period_start: 1 });

      return this.formatCampaignResults(analytics, campaignId);

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve campaign analytics',
        'DB_CAMPAIGN_ANALYTICS_FAILED'
      );
    }
  }

  static async getChannelPerformance(
    orgId: string,
    period: { start: Date; end: Date }
  ) {
    try {
      const pipeline = [
        {
          $match: {
            org_id: orgId,
            period_start: { $gte: period.start, $lte: period.end },
            channel: { $ne: 'all' }
          }
        },
        {
          $group: {
            _id: '$channel',
            total_messages: { $sum: '$metrics.total_messages' },
            delivered: { $sum: '$metrics.messages_delivered' },
            failed: { $sum: '$metrics.messages_failed' },
            opened: { $sum: '$metrics.messages_opened' },
            clicked: { $sum: '$metrics.messages_clicked' },
            total_cost: { $sum: '$metrics.total_cost' },
            avg_delivery_time: { $avg: '$performance.average_delivery_time' }
          }
        },
        {
          $addFields: {
            delivery_rate: {
              $cond: [
                { $eq: ['$total_messages', 0] },
                0,
                { $multiply: [{ $divide: ['$delivered', '$total_messages'] }, 100] }
              ]
            },
            open_rate: {
              $cond: [
                { $eq: ['$delivered', 0] },
                0,
                { $multiply: [{ $divide: ['$opened', '$delivered'] }, 100] }
              ]
            },
            click_rate: {
              $cond: [
                { $eq: ['$opened', 0] },
                0,
                { $multiply: [{ $divide: ['$clicked', '$opened'] }, 100] }
              ]
            },
            average_cost: {
              $cond: [
                { $eq: ['$total_messages', 0] },
                0,
                { $divide: ['$total_cost', '$total_messages'] }
              ]
            }
          }
        }
      ];

      return await MessageAnalyticsModel.aggregate(pipeline);

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve channel performance',
        'DB_CHANNEL_PERFORMANCE_FAILED'
      );
    }
  }

  static async getErrorAnalysis(
    orgId: string,
    period: { start: Date; end: Date },
    limit: number = 10
  ) {
    try {
      const pipeline = [
        {
          $match: {
            org_id: orgId,
            period_start: { $gte: period.start, $lte: period.end }
          }
        },
        { $unwind: '$error_breakdown' },
        {
          $group: {
            _id: {
              error_code: '$error_breakdown.error_code',
              error_message: '$error_breakdown.error_message'
            },
            total_count: { $sum: '$error_breakdown.count' }
          }
        },
        { $sort: { total_count: -1 } },
        { $limit: limit },
        {
          $project: {
            error_code: '$_id.error_code',
            error_message: '$_id.error_message',
            count: '$total_count',
            _id: 0
          }
        }
      ];

      const errors = await MessageAnalyticsModel.aggregate(pipeline);
      
      // Calculate percentages
      const totalErrors = errors.reduce((sum, error) => sum + error.count, 0);
      return errors.map(error => ({
        ...error,
        percentage: totalErrors > 0 ? (error.count / totalErrors) * 100 : 0
      }));

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve error analysis',
        'DB_ERROR_ANALYSIS_FAILED'
      );
    }
  }

  static async getTrendData(
    orgId: string,
    period: { start: Date; end: Date },
    granularity: string = 'daily'
  ) {
    try {
      const analytics = await MessageAnalyticsModel.find({
        org_id: orgId,
        period_start: { $gte: period.start, $lte: period.end },
        granularity,
        channel: 'all'
      }).sort({ period_start: 1 });

      return analytics.map(record => ({
        date: record.period_start,
        total_messages: record.metrics.total_messages,
        delivered: record.metrics.messages_delivered,
        failed: record.metrics.messages_failed,
        delivery_rate: record.metrics.delivery_rate,
        open_rate: record.metrics.open_rate,
        click_rate: record.metrics.click_rate
      }));

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve trend data',
        'DB_TREND_DATA_FAILED'
      );
    }
  }

  private static formatOverviewResults(results: any[], period: any) {
    const channelMap = new Map();
    let summary = {
      total_messages: 0,
      messages_sent: 0,
      messages_delivered: 0,
      messages_failed: 0,
      delivery_rate: 0,
      failure_rate: 0,
      open_rate: 0,
      click_rate: 0,
      bounce_rate: 0
    };

    results.forEach(result => {
      if (result._id === 'all') {
        summary = {
          total_messages: result.total_messages,
          messages_sent: result.messages_sent,
          messages_delivered: result.messages_delivered,
          messages_failed: result.messages_failed,
          delivery_rate: result.total_messages > 0 ? 
            (result.messages_delivered / result.total_messages) * 100 : 0,
          failure_rate: result.total_messages > 0 ? 
            (result.messages_failed / result.total_messages) * 100 : 0,
          open_rate: result.messages_delivered > 0 ? 
            (result.messages_opened / result.messages_delivered) * 100 : 0,
          click_rate: result.messages_opened > 0 ? 
            (result.messages_clicked / result.messages_opened) * 100 : 0,
          bounce_rate: result.messages_sent > 0 ? 
            (result.messages_bounced / result.messages_sent) * 100 : 0
        };
      } else if (result._id !== 'all') {
        channelMap.set(result._id, {
          total_messages: result.total_messages,
          delivered: result.messages_delivered,
          failed: result.messages_failed,
          delivery_rate: result.total_messages > 0 ? 
            (result.messages_delivered / result.total_messages) * 100 : 0,
          opened: result.messages_opened,
          clicked: result.messages_clicked,
          open_rate: result.messages_delivered > 0 ? 
            (result.messages_opened / result.messages_delivered) * 100 : 0,
          click_rate: result.messages_opened > 0 ? 
            (result.messages_clicked / result.messages_opened) * 100 : 0,
          bounce_rate: result.messages_sent > 0 ? 
            (result.messages_bounced / result.messages_sent) * 100 : 0,
          total_cost: result.total_cost,
          average_cost: result.total_messages > 0 ? 
            result.total_cost / result.total_messages : 0
        });
      }
    });

    return {
      period,
      summary,
      channels: Object.fromEntries(channelMap)
    };
  }

  private static formatCampaignResults(analytics: any[], campaignId: string) {
    // Aggregate campaign analytics across time periods
    const aggregated = {
      campaign_id: campaignId,
      performance: {
        total_messages: 0,
        messages_sent: 0,
        messages_delivered: 0,
        messages_failed: 0,
        delivery_rate: 0,
        total_cost: 0
      },
      timeline: analytics.map(record => ({
        period: record.period_start,
        sent: record.metrics.messages_sent,
        delivered: record.metrics.messages_delivered,
        opened: record.metrics.messages_opened,
        clicked: record.metrics.messages_clicked
      }))
    };

    // Calculate aggregated performance
    analytics.forEach(record => {
      aggregated.performance.total_messages += record.metrics.total_messages;
      aggregated.performance.messages_sent += record.metrics.messages_sent;
      aggregated.performance.messages_delivered += record.metrics.messages_delivered;
      aggregated.performance.messages_failed += record.metrics.messages_failed;
      aggregated.performance.total_cost += record.metrics.total_cost;
    });

    aggregated.performance.delivery_rate = aggregated.performance.total_messages > 0 ?
      (aggregated.performance.messages_delivered / aggregated.performance.total_messages) * 100 : 0;

    return aggregated;
  }
}
```

### 3. Analytics Handler Implementation (45 minutes)
```typescript
// src/handlers/analytics.handler.ts
import { Request, Response } from 'express';
import { MessageAnalyticsDAO } from '@/lib/db/dao/message-analytics.dao';
import { CacheService } from '@/services/cache.service';
import { handleError } from '@/lib/errors/error-handler';
import { ValidationError, OrganizationError } from '@/lib/errors/error-types';

export class AnalyticsHandler {
  static async getOverviewAnalytics(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const { period = '7d', channels, timezone = 'UTC' } = req.query;
      
      // Parse period and calculate date range
      const dateRange = AnalyticsHandler.parsePeriod(period as string, timezone as string);
      const channelList = channels ? (channels as string).split(',') : undefined;

      // Check cache first
      const cacheKey = `analytics:overview:${orgId}:${period}:${channels || 'all'}`;
      const cached = await CacheService.get(cacheKey);
      
      if (cached) {
        return res.status(200).json({
          success: true,
          data: cached,
          meta: {
            timestamp: new Date().toISOString(),
            request_id: req.requestId,
            cache_status: 'hit'
          }
        });
      }

      // Fetch analytics data
      const analyticsData = await MessageAnalyticsDAO.getOverviewAnalytics(
        orgId,
        dateRange,
        channelList
      );

      // Get additional data
      const [trendData, errorAnalysis] = await Promise.all([
        MessageAnalyticsDAO.getTrendData(orgId, dateRange, 'daily'),
        MessageAnalyticsDAO.getErrorAnalysis(orgId, dateRange, 5)
      ]);

      const response = {
        ...analyticsData,
        trends: trendData,
        error_analysis: {
          common_errors: errorAnalysis,
          provider_errors: {} // TODO: Implement provider error analysis
        }
      };

      // Cache for 5 minutes
      await CacheService.set(cacheKey, response, 300);

      res.status(200).json({
        success: true,
        data: response,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId,
          cache_status: 'miss'
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getCampaignAnalytics(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { campaign_id } = req.params;
      const { period = '7d', granularity = 'daily' } = req.query;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const dateRange = AnalyticsHandler.parsePeriod(period as string);
      
      const analyticsData = await MessageAnalyticsDAO.getCampaignAnalytics(
        orgId,
        campaign_id,
        dateRange,
        granularity as string
      );

      res.status(200).json({
        success: true,
        data: analyticsData,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getChannelPerformance(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { period = '7d' } = req.query;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const dateRange = AnalyticsHandler.parsePeriod(period as string);
      
      const channelData = await MessageAnalyticsDAO.getChannelPerformance(orgId, dateRange);

      res.status(200).json({
        success: true,
        data: {
          channels: channelData,
          period: dateRange
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  private static parsePeriod(period: string, timezone: string = 'UTC'): { start: Date; end: Date } {
    const now = new Date();
    let start: Date;

    switch (period) {
      case '1h':
        start = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        throw new ValidationError(`Invalid period: ${period}`, 'INVALID_PERIOD');
    }

    return { start, end: now };
  }
}
```

### 4. Route Configuration (10 minutes)
```typescript
// src/routes/analytics.routes.ts
import { Router } from 'express';
import { AnalyticsHandler } from '@/handlers/analytics.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateAnalyticsQuery } from '@/validators/analytics.validator';

const router = Router();

// Overview analytics
router.get(
  '/overview',
  supabaseAuthMiddleware,
  validateAnalyticsQuery,
  AnalyticsHandler.getOverviewAnalytics
);

// Campaign-specific analytics
router.get(
  '/campaign/:campaign_id',
  supabaseAuthMiddleware,
  validateAnalyticsQuery,
  AnalyticsHandler.getCampaignAnalytics
);

// Channel performance analytics
router.get(
  '/channels',
  supabaseAuthMiddleware,
  validateAnalyticsQuery,
  AnalyticsHandler.getChannelPerformance
);

export { router as analyticsRoutes };
```

### 5. Validation Schema (15 minutes)
```typescript
// src/validators/analytics.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const analyticsQuerySchema = Joi.object({
  period: Joi.string().valid('1h', '24h', '7d', '30d', '90d', '1y').optional(),
  granularity: Joi.string().valid('hourly', 'daily', 'weekly', 'monthly').optional(),
  channels: Joi.string().optional(),
  compare: Joi.string().valid('previous_period', 'previous_year').optional(),
  timezone: Joi.string().optional()
});

export const validateAnalyticsQuery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = analyticsQuerySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};
```

## Error Handling

### Common Errors
- **400 Invalid Period**: Invalid time period specified
- **400 Invalid Granularity**: Invalid data granularity requested
- **404 Campaign Not Found**: Campaign analytics not available
- **429 Rate Limited**: Too many analytics requests
- **500 Query Failed**: Database query execution failed

## Performance Considerations

### Caching Strategy
- Cache overview analytics for 5 minutes
- Cache campaign analytics for 10 minutes
- Cache channel performance for 15 minutes
- Implement cache invalidation on new data

### Database Optimization
- Use pre-aggregated analytics data
- Implement proper indexing on time-based queries
- Consider data partitioning for large datasets

## Security Considerations

1. **Access Control**: Ensure users can only access their organization's analytics
2. **Rate Limiting**: Prevent analytics API abuse
3. **Data Privacy**: Anonymize sensitive recipient information
4. **Query Limits**: Restrict date ranges and result sizes