# Message Service - Project Setup

## Base Project Structure

```
shoutout_engage_message_service/
├── src/
│   ├── app.ts                          # Main Express app
│   ├── server.ts                       # Server entry point
│   ├── routes/
│   │   ├── index.ts                    # Route aggregator
│   │   ├── message.routes.ts           # Message endpoints
│   │   ├── provider.routes.ts          # Provider management
│   │   ├── webhook.routes.ts           # Webhook handlers
│   │   └── health.routes.ts            # Health check endpoints
│   ├── controllers/
│   │   ├── message.controller.ts       # Message operations
│   │   ├── provider.controller.ts      # Provider management
│   │   ├── webhook.controller.ts       # Webhook handling
│   │   └── health.controller.ts        # Health checks
│   ├── services/
│   │   ├── message.service.ts          # Core message operations
│   │   ├── provider.service.ts         # Provider management
│   │   ├── delivery.service.ts         # Delivery tracking
│   │   ├── template.service.ts         # Message templates
│   │   └── analytics.service.ts        # Message analytics
│   ├── providers/
│   │   ├── base.provider.ts            # Abstract base provider
│   │   ├── sms/
│   │   │   ├── twilio.sms.provider.ts
│   │   │   ├── nexmo.sms.provider.ts
│   │   │   └── dialog.sms.provider.ts
│   │   ├── email/
│   │   │   ├── sendgrid.email.provider.ts
│   │   │   ├── ses.email.provider.ts
│   │   │   └── mailgun.email.provider.ts
│   │   └── push/
│   │       ├── fcm.push.provider.ts
│   │       └── apns.push.provider.ts
│   ├── middleware/
│   │   ├── auth.middleware.ts          # Supabase JWT auth
│   │   ├── validation.middleware.ts    # Request validation
│   │   ├── error.middleware.ts         # Error handling
│   │   └── rate.limit.middleware.ts    # Rate limiting
│   ├── lib/
│   │   ├── db/
│   │   │   ├── models/
│   │   │   │   ├── message.log.model.ts
│   │   │   │   ├── provider.config.model.ts
│   │   │   │   └── delivery.status.model.ts
│   │   │   ├── dao/
│   │   │   │   ├── message.dao.ts
│   │   │   │   └── provider.dao.ts
│   │   │   └── connectors/
│   │   │       ├── MongooseConnector.ts
│   │   │       └── RedisConnector.ts
│   │   ├── config/
│   │   │   ├── index.ts
│   │   │   ├── database.ts
│   │   │   ├── redis.ts
│   │   │   ├── supabase.ts
│   │   │   └── providers.ts
│   │   ├── constant/
│   │   │   ├── message.constants.ts
│   │   │   ├── provider.constants.ts
│   │   │   └── queue.constants.ts
│   │   ├── errors/
│   │   │   ├── error.types.ts
│   │   │   └── error.handler.ts
│   │   ├── utils/
│   │   │   ├── logger.ts
│   │   │   ├── validator.ts
│   │   │   └── encryption.utils.ts
│   │   └── swagger/
│   │       └── swagger.config.ts
│   ├── types/
│   │   ├── message.types.ts
│   │   ├── provider.types.ts
│   │   ├── webhook.types.ts
│   │   └── api.types.ts
│   ├── workers/
│   │   └── processors/
│   │       ├── message.outbound.processor.ts
│   │       ├── message.retry.processor.ts
│   │       └── delivery.status.processor.ts
│   └── queues/
│       ├── message.outbound.queue.ts
│       ├── message.retry.queue.ts
│       └── delivery.status.queue.ts
├── tests/
│   ├── unit/
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── providers/
│   │   └── utils/
│   ├── integration/
│   │   ├── api/
│   │   ├── providers/
│   │   └── webhooks/
│   └── setup.ts
├── uploads/                            # Temporary file storage
├── logs/                              # Application logs
├── package.json
├── tsconfig.json
├── jest.config.js
├── .env.example
├── .gitignore
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## Package.json Configuration

```json
{
  "name": "shoutout-engage-message-service",
  "version": "1.0.0",
  "description": "Message processing microservice for ShoutOut Engage",
  "main": "dist/server.js",
  "scripts": {
    "dev": "nodemon src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "format": "prettier --write src/**/*.ts"
  },
  "dependencies": {
    "express": "^4.18.0",
    "mongoose": "^7.0.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "joi": "^17.9.0",
    "winston": "^3.8.0",
    "@supabase/supabase-js": "^2.0.0",
    "bull": "^4.10.0",
    "redis": "^4.6.0",
    "jsonwebtoken": "^9.0.0",
    "uuid": "^9.0.0",
    "moment": "^2.29.0",
    "lodash": "^4.17.21",
    "twilio": "^4.19.0",
    "@sendgrid/mail": "^7.7.0",
    "aws-sdk": "^2.1490.0",
    "mailgun-js": "^0.22.0",
    "firebase-admin": "^11.11.0",
    "node-pushnotifications": "^3.0.0",
    "crypto": "^1.0.1",
    "express-rate-limit": "^6.7.0",
    "swagger-jsdoc": "^6.2.0",
    "swagger-ui-express": "^4.6.0"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "@types/express": "^4.17.0",
    "@types/mongoose": "^7.0.0",
    "@types/cors": "^2.8.0",
    "@types/joi": "^17.2.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/uuid": "^9.0.0",
    "@types/lodash": "^4.14.0",
    "ts-node": "^10.9.0",
    "nodemon": "^3.0.0",
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0",
    "ts-jest": "^29.0.0",
    "eslint": "^8.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "prettier": "^3.0.0"
  }
}
```

## Environment Configuration (.env.example)

```env
# Server Configuration
NODE_ENV=development
PORT=3002
BASE_PATH=/api/message/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/shoutout_engage_message
MONGODB_TEST_URI=mongodb://localhost:27017/shoutout_engage_message_test

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# SMS Providers
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
NEXMO_API_KEY=your-nexmo-key
NEXMO_API_SECRET=your-nexmo-secret
DIALOG_API_KEY=your********key

# Email Providers
SENDGRID_API_KEY=your-sendgrid-key
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
MAILGUN_API_KEY=your-mailgun-key
MAILGUN_DOMAIN=your-mailgun-domain

# Push Notification Providers
FCM_SERVER_KEY=your-fcm-key
APNS_KEY_ID=your-apns-key-id
APNS_TEAM_ID=your-apns-team-id
APNS_BUNDLE_ID=your-bundle-id

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret
BASE_WEBHOOK_URL=https://your-domain.com/api/message/v1/webhooks

# External Services
CAMPAIGN_SERVICE_URL=http://localhost:3001
PAYMENT_SERVICE_URL=http://localhost:3003
CORE_SERVICE_URL=http://localhost:3000

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/message-service.log
```

## Base Provider Interface

```typescript
// src/providers/base.provider.ts
export enum MessageTransport {
  SMS = 'SMS',
  EMAIL = 'EMAIL',
  PUSH = 'PUSH',
  FACEBOOK_MESSENGER = 'FACEBOOK_MESSENGER'
}

export interface MessageData {
  messageId: string;
  orgId: string;
  transport: MessageTransport;
  from: string;
  to: string;
  content: string;
  subject?: string;
  htmlContent?: string;
  attachments?: MessageAttachment[];
  metadata?: Record<string, any>;
  campaignId?: string;
}

export interface MessageAttachment {
  filename: string;
  content: Buffer | string;
  contentType: string;
  disposition?: 'attachment' | 'inline';
}

export interface ProviderResult {
  success: boolean;
  providerId?: string;
  providerMessageId?: string;
  cost: number;
  currency: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface DeliveryStatus {
  messageId: string;
  providerMessageId: string;
  status: 'QUEUED' | 'SENT' | 'DELIVERED' | 'FAILED' | 'BOUNCED' | 'OPENED' | 'CLICKED';
  timestamp: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface WebhookResult {
  processed: boolean;
  messageId?: string;
  status?: string;
  error?: string;
}

export abstract class BaseMessageProvider {
  abstract readonly providerName: string;
  abstract readonly supportedTransports: MessageTransport[];
  abstract readonly rateLimits: {
    requestsPerSecond: number;
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };

  abstract async sendMessage(message: MessageData): Promise<ProviderResult>;
  abstract async getDeliveryStatus(providerMessageId: string): Promise<DeliveryStatus>;
  abstract async handleWebhook(payload: any, signature?: string): Promise<WebhookResult>;
  abstract async validateConfiguration(): Promise<boolean>;
  abstract calculateCost(message: MessageData): number;

  protected async logMessage(message: MessageData, result: ProviderResult): Promise<void> {
    // Implementation will be in the concrete service
  }

  protected validateMessage(message: MessageData): void {
    if (!message.messageId) throw new Error('Message ID is required');
    if (!message.orgId) throw new Error('Organization ID is required');
    if (!message.from) throw new Error('From address is required');
    if (!message.to) throw new Error('To address is required');
    if (!message.content) throw new Error('Message content is required');
  }
}
```

## Authentication Middleware (Based on Core Service)

```typescript
// src/middleware/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';
import config from '@/lib/config';

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    org_id: string;
    role: string;
  };
}

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export const authenticateRequest = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Missing or invalid authorization header'
        }
      });
      return;
    }

    const token = authHeader.substring(7);
    
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired token'
        }
      });
      return;
    }

    // Get user profile with organization info
    const { data: profile } = await supabase
      .from('profiles')
      .select('*, organizations(*)')
      .eq('user_id', user.id)
      .single();

    if (!profile) {
      res.status(401).json({
        success: false,
        error: {
          code: 'PROFILE_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      return;
    }

    req.user = {
      id: user.id,
      email: user.email || '',
      org_id: profile.organization_uuid,
      role: profile.user_type
    };

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: 'Authentication error'
      }
    });
  }
};

// Service-to-service authentication for internal API calls
export const authenticateService = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey || apiKey !== process.env.INTERNAL_API_KEY) {
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_API_KEY',
          message: 'Invalid or missing API key'
        }
      });
      return;
    }

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: 'Service authentication error'
      }
    });
  }
};
```

## Docker Configuration

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src ./src

# Build application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S message -u 1001

# Change ownership
RUN chown -R message:nodejs /app
USER message

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3002/api/message/v1/health || exit 1

# Start application
CMD ["npm", "start"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  message-service:
    build: .
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/shoutout_engage_message
      - REDIS_HOST=redis
    depends_on:
      - mongo
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs

  mongo:
    image: mongo:6.0
    ports:
      - "27018:27017"
    volumes:
      - message_mongo_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=shoutout_engage_message

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - message_redis_data:/data

volumes:
  message_mongo_data:
  message_redis_data:
```

This setup provides the foundation for the Message Service with multi-provider support, proper authentication, queue processing, and comprehensive error handling following the existing engage core service patterns.
