# Message Service - POST /otp/verify (Verify One Time Password)

## Task Overview
**Endpoint**: `POST /api/message/v1/otp/verify`  
**Purpose**: Verify one-time passwords sent for mobile number verification and two-factor authentication  
**Estimated Time**: 1 day  
**Priority**: High  
**Dependencies**: OTP storage system, Redis cache, rate limiting  

## API Specification

### Verify OTP Request
```http
POST /api/message/v1/otp/verify
Authorization: Apikey <API_KEY>
Content-Type: application/json

{
  "referenceId": "f437c171-2d08-48c8-a4a2-xxxxxxxx",
  "destination": "+***********",
  "code": "123456"
}
```

### Request Parameters
- `referenceId` (string, required): Reference ID received from OTP send request
- `destination` (string, required): Mobile number in E.164 format that received the OTP
- `code` (string, required): 6-digit OTP code to verify

### Response - Success
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "verified": true,
    "referenceId": "f437c171-2d08-48c8-a4a2-xxxxxxxx",
    "destination": "+***********",
    "verified_at": "2024-08-12T10:35:00Z"
  }
}
```

### Response - Invalid OTP Code
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "OTP_VERIFY_001",
    "message": "Invalid OTP code",
    "details": "The provided OTP code is incorrect or has expired"
  }
}
```

### Response - OTP Expired
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "OTP_VERIFY_002",
    "message": "OTP has expired",
    "details": "The OTP code has exceeded its validity period"
  }
}
```

### Response - Max Attempts Exceeded
```http
HTTP/1.1 429 Too Many Requests
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "OTP_VERIFY_003",
    "message": "Maximum verification attempts exceeded",
    "details": "Too many failed attempts. Please request a new OTP"
  }
}
```

### Response - Invalid Reference ID
```http
HTTP/1.1 404 Not Found
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "OTP_VERIFY_004",
    "message": "Invalid reference ID",
    "details": "The provided reference ID does not exist or is invalid"
  }
}
```

## Request/Response Examples

### Example 1: Successful OTP Verification
**Request:**
```http
POST /api/message/v1/otp/verify
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "referenceId": "f437c171-2d08-48c8-a4a2-12345678",
  "destination": "+***********",
  "code": "456789"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "verified": true,
    "referenceId": "f437c171-2d08-48c8-a4a2-12345678",
    "destination": "+***********",
    "verified_at": "2024-08-12T10:35:15Z"
  }
}
```

### Example 2: Invalid OTP Code
**Request:**
```http
POST /api/message/v1/otp/verify
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "referenceId": "f437c171-2d08-48c8-a4a2-12345678",
  "destination": "+***********",
  "code": "999999"
}
```

**Response:**
```json
{
  "success": false,
  "error": {
    "code": "OTP_VERIFY_001",
    "message": "Invalid OTP code",
    "details": "The provided OTP code is incorrect or has expired"
  }
}
```

### Example 3: OTP Expired
**Request:**
```http
POST /api/message/v1/otp/verify
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "referenceId": "f437c171-2d08-48c8-a4a2-87654321",
  "destination": "+***********",
  "code": "123456"
}
```

**Response:**
```json
{
  "success": false,
  "error": {
    "code": "OTP_VERIFY_002",
    "message": "OTP has expired",
    "details": "The OTP code has exceeded its validity period"
  }
}
```

## Validation Rules

### Input Validation
1. **referenceId**:
   - Must be a valid UUID format
   - Must exist in the OTP storage system
   - Must not be expired or consumed

2. **destination**:
   - Must be a valid E.164 format phone number
   - Must match the destination from the original OTP send request
   - Example: `+***********`, `+***********`

3. **code**:
   - Must be exactly 6 digits
   - Must contain only numeric characters
   - Must not be empty or null

### Business Logic Validation
1. **OTP Expiry**: Default OTP validity is 10 minutes from generation
2. **Max Attempts**: Maximum of 3 verification attempts per OTP
3. **Rate Limiting**: Maximum of 5 verification requests per minute per destination
4. **Single Use**: OTP can only be successfully verified once

## Error Codes Reference

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| OTP_VERIFY_001 | 400 | Invalid OTP code provided |
| OTP_VERIFY_002 | 400 | OTP has expired |
| OTP_VERIFY_003 | 429 | Maximum verification attempts exceeded |
| OTP_VERIFY_004 | 404 | Invalid or non-existent reference ID |
| OTP_VERIFY_005 | 400 | Invalid destination phone number format |
| OTP_VERIFY_006 | 400 | Missing required parameters |
| OTP_VERIFY_007 | 409 | OTP already verified |
| OTP_VERIFY_008 | 429 | Rate limit exceeded for verification attempts |

## Implementation Requirements

### Database Schema Requirements
```sql
-- OTP Storage Table
CREATE TABLE otp_codes (
    id VARCHAR(36) PRIMARY KEY,
    reference_id VARCHAR(36) UNIQUE NOT NULL,
    destination VARCHAR(20) NOT NULL,
    code VARCHAR(6) NOT NULL,
    attempts_count INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_reference_id (reference_id),
    INDEX idx_destination (destination),
    INDEX idx_expires_at (expires_at)
);
```

### Redis Cache Structure
```json
{
  "otp:{referenceId}": {
    "destination": "+***********",
    "code": "456789",
    "attempts": 0,
    "maxAttempts": 3,
    "expiresAt": "2024-08-12T10:40:00Z",
    "verified": false
  }
}
```

### Rate Limiting Configuration
- **Verification attempts**: 5 requests per minute per destination
- **Failed attempts**: 3 failed attempts before blocking
- **Cooldown period**: 1 minute after max attempts exceeded

## Testing Scenarios

### Positive Test Cases
1. **Valid OTP verification**: Verify with correct code within validity period
2. **First-time verification**: Ensure OTP can be verified successfully on first attempt
3. **Edge case timing**: Verify OTP just before expiration

### Negative Test Cases
1. **Invalid OTP code**: Test with wrong 6-digit code
2. **Expired OTP**: Test verification after expiry time
3. **Max attempts exceeded**: Test after 3 failed attempts
4. **Invalid reference ID**: Test with non-existent reference ID
5. **Mismatched destination**: Test with different phone number
6. **Already verified OTP**: Test double verification attempt
7. **Rate limiting**: Test exceeding verification rate limits

### Edge Cases
1. **Malformed input**: Test with invalid JSON, missing fields
2. **Invalid phone format**: Test with non-E.164 phone numbers
3. **Code format validation**: Test with non-numeric, wrong length codes
4. **Concurrent verification**: Test simultaneous verification attempts

## Performance Considerations

### Caching Strategy
- Use Redis for OTP storage with TTL matching OTP expiry
- Cache verification results to prevent replay attacks
- Implement distributed rate limiting using Redis

### Database Optimization
- Index on reference_id for fast lookups
- Index on destination for rate limiting queries
- Automatic cleanup of expired OTP records

### Security Measures
1. **Hash OTP codes**: Store hashed versions in database
2. **Audit logging**: Log all verification attempts
3. **Rate limiting**: Prevent brute force attacks
4. **IP-based tracking**: Monitor suspicious verification patterns

## Dependencies and Integration

### Internal Dependencies
- **OTP Send Service**: Must be called before verification
- **Rate Limiting Service**: For attempt tracking
- **Audit Service**: For security logging

### External Dependencies
- **Redis**: For caching and rate limiting
- **Database**: For persistent OTP storage
- **Logging Service**: For audit trails

### API Integration Points
- Integrates with user authentication flows
- Used by mobile app verification screens
- Required for two-factor authentication setup