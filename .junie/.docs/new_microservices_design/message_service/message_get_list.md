# Message Service - GET /messages (List Messages)

## Task Overview
**Endpoint**: `GET /api/message/v1/messages`  
**Purpose**: Retrieve a paginated list of messages for an organization with filtering and sorting options  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Authentication middleware, database models, pagination utilities  

## API Specification

### Request
```http
GET /api/message/v1/messages?page=1&page_size=20&transport=SMS&status=DELIVERED&from_date=2024-08-01&to_date=2024-08-11&sort_by=created_at&sort_order=desc
Authorization: Bearer <jwt_token>
```

### Query Parameters
- `page` (optional, default: 1): Page number for pagination
- `page_size` (optional, default: 20, max: 100): Number of items per page
- `transport` (optional): Filter by transport type (SMS, EMAIL, PUSH, FACEBOOK_MESSENGER)
- `status` (optional): Filter by message status (QUEUED, SENT, DELIVERED, <PERSON><PERSON>ED, B<PERSON>UNCED, OPENED, CLICKED, UNSUBSCRIBED)
- `provider` (optional): Filter by provider (TWILIO, NEXMO, DIALOG, SENDGRID, SES, MAILGUN)
- `to` (optional): Filter by recipient
- `campaign_id` (optional): Filter by campaign ID
- `from_date` (optional): Filter messages created after this date (ISO format)
- `to_date` (optional): Filter messages created before this date (ISO format)
- `search` (optional): Search in message content
- `sort_by` (optional, default: created_at): Sort field (created_at, updated_at, status)
- `sort_order` (optional, default: desc): Sort order (asc, desc)

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "messages": [
      {
        "message_id": "msg_64a1b2c3d4e5f678901234b",
        "transport": "SMS",
        "provider": "TWILIO",
        "from": "STORE",
        "to": "+94771234567",
        "content": "Your order #12345 is ready for pickup!",
        "subject": null,
        "status": "DELIVERED",
        "campaign_id": "64a1b2c3d4e5f6789012346",
        "is_campaign_message": true,
        "delivery_info": {
          "sent_at": "2024-08-11T15:30:15Z",
          "delivered_at": "2024-08-11T15:30:18Z",
          "delivery_duration_ms": 3000
        },
        "cost_info": {
          "cost": 0.05,
          "currency": "USD"
        },
        "provider_message_id": "SM123456789abcdef",
        "priority": "HIGH",
        "retry_count": 0,
        "metadata": {
          "order_id": "12345",
          "customer_id": "cust_789"
        },
        "created_at": "2024-08-11T15:30:00Z",
        "updated_at": "2024-08-11T15:30:18Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 20,
      "total_items": 150,
      "total_pages": 8,
      "has_next": true,
      "has_previous": false
    },
    "filters": {
      "transport": "SMS",
      "status": "DELIVERED",
      "sort_by": "created_at",
      "sort_order": "desc"
    },
    "summary": {
      "total_messages": 150,
      "by_status": {
        "DELIVERED": 120,
        "SENT": 15,
        "FAILED": 10,
        "QUEUED": 5
      },
      "by_transport": {
        "SMS": 100,
        "EMAIL": 40,
        "PUSH": 10
      },
      "total_cost": {
        "amount": 7.50,
        "currency": "USD"
      }
    }
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (45 minutes)
```typescript
// src/services/message.service.ts (addition)
import { MessageLogModel } from '@/lib/db/models/message.log.model';
import { MessageListFilters, PaginationOptions, MessageListResponse } from '@/types/message.types';

export class MessageService {
  static async getMessages(
    orgId: string,
    filters: MessageListFilters,
    pagination: PaginationOptions
  ): Promise<MessageListResponse> {
    try {
      // Build MongoDB query
      const query = this.buildMessageQuery(orgId, filters);
      
      // Calculate pagination
      const skip = (pagination.page - 1) * pagination.page_size;
      
      // Build sort options
      const sortOptions = this.buildSortOptions(filters.sort_by, filters.sort_order);
      
      // Execute queries in parallel
      const [messages, totalCount, summary] = await Promise.all([
        MessageLogModel.find(query)
          .select(this.getMessageListProjection())
          .sort(sortOptions)
          .skip(skip)
          .limit(pagination.page_size)
          .lean(),
        MessageLogModel.countDocuments(query),
        this.getMessageSummary(orgId, filters)
      ]);
      
      // Format messages
      const formattedMessages = messages.map(this.formatMessageSummary);
      
      // Build pagination metadata
      const paginationMeta = this.buildPaginationMeta(pagination, totalCount);
      
      return {
        messages: formattedMessages,
        pagination: paginationMeta,
        filters: {
          transport: filters.transport,
          status: filters.status,
          provider: filters.provider,
          sort_by: filters.sort_by,
          sort_order: filters.sort_order
        },
        summary
      };
    } catch (error) {
      throw new InternalError(`Failed to retrieve messages: ${error.message}`);
    }
  }

  private static buildMessageQuery(orgId: string, filters: MessageListFilters): any {
    const query: any = { org_id: orgId };
    
    // Transport filter
    if (filters.transport) {
      query.transport = filters.transport;
    }
    
    // Status filter
    if (filters.status) {
      query.status = filters.status;
    }
    
    // Provider filter
    if (filters.provider) {
      query.provider = filters.provider;
    }
    
    // Recipient filter
    if (filters.to) {
      query.to = { $regex: filters.to, $options: 'i' };
    }
    
    // Campaign filter
    if (filters.campaign_id) {
      query.campaign_id = filters.campaign_id;
    }
    
    // Search filter
    if (filters.search) {
      query.$or = [
        { content: { $regex: filters.search, $options: 'i' } },
        { subject: { $regex: filters.search, $options: 'i' } },
        { to: { $regex: filters.search, $options: 'i' } }
      ];
    }
    
    // Date range filters
    if (filters.from_date || filters.to_date) {
      query.created_at = {};
      if (filters.from_date) {
        query.created_at.$gte = new Date(filters.from_date);
      }
      if (filters.to_date) {
        query.created_at.$lte = new Date(filters.to_date);
      }
    }
    
    return query;
  }

  private static buildSortOptions(sortBy: string = 'created_at', sortOrder: string = 'desc'): any {
    const order = sortOrder === 'asc' ? 1 : -1;
    return { [sortBy]: order };
  }

  private static getMessageListProjection(): any {
    return {
      // Exclude large fields for performance
      provider_response: 0,
      status_history: 0,
      html_content: 0
    };
  }

  private static formatMessageSummary(message: any): MessageSummary {
    return {
      message_id: message.message_id,
      transport: message.transport,
      provider: message.provider,
      from: message.from,
      to: message.to,
      content: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : ''),
      subject: message.subject,
      status: message.status,
      campaign_id: message.campaign_id?.toString(),
      is_campaign_message: message.is_campaign_message || false,
      delivery_info: {
        sent_at: message.sent_at,
        delivered_at: message.delivered_at,
        delivery_duration_ms: message.delivery_duration_ms
      },
      cost_info: {
        cost: message.cost || 0,
        currency: message.currency || 'USD'
      },
      provider_message_id: message.provider_message_id,
      priority: message.priority,
      retry_count: message.retry_count || 0,
      metadata: message.metadata || {},
      created_at: message.created_at,
      updated_at: message.updated_at
    };
  }

  private static async getMessageSummary(orgId: string, filters: MessageListFilters): Promise<MessageSummary> {
    const baseQuery = { org_id: orgId };
    
    // Apply date filters to summary as well
    if (filters.from_date || filters.to_date) {
      baseQuery.created_at = {};
      if (filters.from_date) {
        baseQuery.created_at.$gte = new Date(filters.from_date);
      }
      if (filters.to_date) {
        baseQuery.created_at.$lte = new Date(filters.to_date);
      }
    }

    const [statusStats, transportStats, costStats] = await Promise.all([
      MessageLogModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      MessageLogModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$transport', count: { $sum: 1 } } }
      ]),
      MessageLogModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: null, total_cost: { $sum: '$cost' }, count: { $sum: 1 } } }
      ])
    ]);

    return {
      total_messages: costStats[0]?.count || 0,
      by_status: statusStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
      by_transport: transportStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
      total_cost: {
        amount: Math.round((costStats[0]?.total_cost || 0) * 100) / 100,
        currency: 'USD'
      }
    };
  }

  private static buildPaginationMeta(pagination: PaginationOptions, totalCount: number): PaginationMeta {
    const totalPages = Math.ceil(totalCount / pagination.page_size);
    
    return {
      current_page: pagination.page,
      page_size: pagination.page_size,
      total_items: totalCount,
      total_pages: totalPages,
      has_next: pagination.page < totalPages,
      has_previous: pagination.page > 1
    };
  }
}
```

### 2. Controller Implementation (30 minutes)
```typescript
// src/controllers/message.controller.ts (addition)
export class MessageController {
  static async getMessages(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Extract and validate query parameters
      const filters = MessageController.extractListFilters(req.query);
      const pagination = MessageController.extractPaginationOptions(req.query);
      
      // Get messages
      const result = await MessageService.getMessages(
        req.user.org_id,
        filters,
        pagination
      );

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  private static extractListFilters(query: any): MessageListFilters {
    return {
      transport: query.transport,
      status: query.status,
      provider: query.provider,
      to: query.to,
      campaign_id: query.campaign_id,
      from_date: query.from_date,
      to_date: query.to_date,
      search: query.search,
      sort_by: query.sort_by || 'created_at',
      sort_order: query.sort_order || 'desc'
    };
  }

  private static extractPaginationOptions(query: any): PaginationOptions {
    const page = Math.max(1, parseInt(query.page) || 1);
    const pageSize = Math.min(100, Math.max(1, parseInt(query.page_size) || 20));
    
    return {
      page,
      page_size: pageSize
    };
  }
}
```

### 3. Validation Middleware (25 minutes)
```typescript
// src/middleware/validation.middleware.ts (addition)
import Joi from 'joi';

export const validateMessageListQuery = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const schema = Joi.object({
    page: Joi.number().integer().min(1).optional(),
    page_size: Joi.number().integer().min(1).max(100).optional(),
    transport: Joi.string().valid('SMS', 'EMAIL', 'PUSH', 'FACEBOOK_MESSENGER').optional(),
    status: Joi.string().valid('QUEUED', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', 'OPENED', 'CLICKED', 'UNSUBSCRIBED').optional(),
    provider: Joi.string().valid('TWILIO', 'NEXMO', 'DIALOG', 'SENDGRID', 'SES', 'MAILGUN').optional(),
    to: Joi.string().max(50).optional(),
    campaign_id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).optional(),
    from_date: Joi.date().iso().optional(),
    to_date: Joi.date().iso().min(Joi.ref('from_date')).optional(),
    search: Joi.string().max(100).optional(),
    sort_by: Joi.string().valid('created_at', 'updated_at', 'status').optional(),
    sort_order: Joi.string().valid('asc', 'desc').optional()
  });

  const { error } = schema.validate(req.query, { abortEarly: false });
  
  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    throw new BadRequestError('Invalid query parameters provided', 'INVALID_QUERY_PARAMS', details);
  }
  
  next();
};
```

### 4. Route Definition (10 minutes)
```typescript
// src/routes/message.routes.ts (addition)
import { validateMessageListQuery } from '@/middleware/validation.middleware';

router.get(
  '/',
  authenticateRequest,
  validateMessageListQuery,
  rateLimitMiddleware,
  MessageController.getMessages
);
```

### 5. Type Definitions (20 minutes)
```typescript
// src/types/message.types.ts (additions)
export interface MessageListFilters {
  transport?: 'SMS' | 'EMAIL' | 'PUSH' | 'FACEBOOK_MESSENGER';
  status?: 'QUEUED' | 'SENT' | 'DELIVERED' | 'FAILED' | 'BOUNCED' | 'OPENED' | 'CLICKED' | 'UNSUBSCRIBED';
  provider?: string;
  to?: string;
  campaign_id?: string;
  from_date?: string;
  to_date?: string;
  search?: string;
  sort_by?: 'created_at' | 'updated_at' | 'status';
  sort_order?: 'asc' | 'desc';
}

export interface MessageSummary {
  message_id: string;
  transport: string;
  provider: string;
  from: string;
  to: string;
  content: string;
  subject?: string;
  status: string;
  campaign_id?: string;
  is_campaign_message: boolean;
  delivery_info: {
    sent_at?: Date;
    delivered_at?: Date;
    delivery_duration_ms?: number;
  };
  cost_info: {
    cost: number;
    currency: string;
  };
  provider_message_id?: string;
  priority: string;
  retry_count: number;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

export interface MessageListResponse {
  messages: MessageSummary[];
  pagination: PaginationMeta;
  filters: {
    transport?: string;
    status?: string;
    provider?: string;
    sort_by?: string;
    sort_order?: string;
  };
  summary: {
    total_messages: number;
    by_status: Record<string, number>;
    by_transport: Record<string, number>;
    total_cost: {
      amount: number;
      currency: string;
    };
  };
}

export interface PaginationMeta {
  current_page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface PaginationOptions {
  page: number;
  page_size: number;
}
```

### 6. Database Optimization (15 minutes)
```typescript
// src/lib/db/models/message.log.model.ts (index additions)
// Add compound indexes for efficient filtering and sorting
messageLogSchema.index({ org_id: 1, transport: 1, status: 1 });
messageLogSchema.index({ org_id: 1, campaign_id: 1, created_at: -1 });
messageLogSchema.index({ org_id: 1, provider: 1, created_at: -1 });
messageLogSchema.index({ org_id: 1, to: 1, created_at: -1 });
messageLogSchema.index({ org_id: 1, status: 1, created_at: -1 });
messageLogSchema.index({ org_id: 1, created_at: -1 }); // Default sort index
```

### 7. Caching Implementation (25 minutes)
```typescript
// src/services/message.cache.service.ts (new file)
import { RedisConnector } from '@/lib/db/connectors/RedisConnector';

export class MessageCacheService {
  private static readonly CACHE_TTL = 300; // 5 minutes
  private static readonly CACHE_PREFIX = 'messages:';

  static async getCachedMessageList(
    orgId: string,
    filters: MessageListFilters,
    pagination: PaginationOptions
  ): Promise<MessageListResponse | null> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = this.generateCacheKey(orgId, filters, pagination);
      
      const cached = await redis.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Cache retrieval error:', error);
      return null;
    }
  }

  static async setCachedMessageList(
    orgId: string,
    filters: MessageListFilters,
    pagination: PaginationOptions,
    data: MessageListResponse
  ): Promise<void> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = this.generateCacheKey(orgId, filters, pagination);
      
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(data));
    } catch (error) {
      console.error('Cache storage error:', error);
    }
  }

  private static generateCacheKey(
    orgId: string,
    filters: MessageListFilters,
    pagination: PaginationOptions
  ): string {
    const filterString = JSON.stringify(filters);
    const paginationString = JSON.stringify(pagination);
    return `${this.CACHE_PREFIX}${orgId}:${Buffer.from(filterString + paginationString).toString('base64')}`;
  }

  static async invalidateMessageListCache(orgId: string): Promise<void> {
    try {
      const redis = RedisConnector.getClient();
      const pattern = `${this.CACHE_PREFIX}${orgId}:*`;
      
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }
}
```

### 8. Unit Tests (40 minutes)
```typescript
// tests/unit/controllers/message.controller.test.ts (addition)
describe('MessageController.getMessages', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = {
      query: {},
      user: { id: 'user123', org_id: 'org123' },
      headers: { 'x-request-id': 'req123' }
    };
    mockRes = {
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should return paginated messages with default parameters', async () => {
    const mockResult = {
      messages: [
        {
          message_id: 'msg_123',
          transport: 'SMS',
          status: 'DELIVERED',
          content: 'Test message'
        }
      ],
      pagination: {
        current_page: 1,
        page_size: 20,
        total_items: 1,
        total_pages: 1,
        has_next: false,
        has_previous: false
      },
      filters: {
        sort_by: 'created_at',
        sort_order: 'desc'
      },
      summary: {
        total_messages: 1,
        by_status: { DELIVERED: 1 },
        by_transport: { SMS: 1 },
        total_cost: { amount: 0.05, currency: 'USD' }
      }
    };
    
    (MessageService.getMessages as jest.Mock).mockResolvedValue(mockResult);

    await MessageController.getMessages(mockReq, mockRes, mockNext);

    expect(MessageService.getMessages).toHaveBeenCalledWith(
      'org123',
      expect.objectContaining({
        sort_by: 'created_at',
        sort_order: 'desc'
      }),
      expect.objectContaining({
        page: 1,
        page_size: 20
      })
    );
    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: mockResult,
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        request_id: 'req123'
      })
    });
  });

  it('should handle service errors', async () => {
    (MessageService.getMessages as jest.Mock).mockRejectedValue(
      new InternalError('Database connection failed')
    );

    await MessageController.getMessages(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(InternalError));
  });
});
```

### 9. Integration Tests (35 minutes)
```typescript
// tests/integration/message.api.test.ts (addition)
describe('GET /api/message/v1/messages', () => {
  let authToken: string;
  let orgId: string;
  let messages: any[];

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
    
    // Create test messages
    messages = await Promise.all([
      MessageLogModel.create({
        org_id: orgId,
        message_id: 'msg_test_1',
        transport: 'SMS',
        provider: 'TWILIO',
        from: 'TEST',
        to: '+**********',
        content: 'Test SMS message',
        status: 'DELIVERED',
        cost: 0.05,
        created_at: new Date('2024-08-01T10:00:00Z')
      }),
      MessageLogModel.create({
        org_id: orgId,
        message_id: 'msg_test_2',
        transport: 'EMAIL',
        provider: 'SENDGRID',
        from: '<EMAIL>',
        to: '<EMAIL>',
        content: 'Test email message',
        subject: 'Test Subject',
        status: 'OPENED',
        cost: 0.02,
        created_at: new Date('2024-08-02T10:00:00Z')
      })
    ]);
  });

  afterAll(async () => {
    await MessageLogModel.deleteMany({ org_id: orgId });
  });

  it('should return paginated messages', async () => {
    const response = await request(app)
      .get('/api/message/v1/messages?page=1&page_size=10')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.messages).toHaveLength(2);
    expect(response.body.data.pagination).toMatchObject({
      current_page: 1,
      page_size: 10,
      total_items: 2,
      total_pages: 1
    });
    expect(response.body.data.summary).toBeDefined();
  });

  it('should filter messages by transport', async () => {
    const response = await request(app)
      .get('/api/message/v1/messages?transport=SMS')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.messages).toHaveLength(1);
    expect(response.body.data.messages[0].transport).toBe('SMS');
  });

  it('should filter messages by status', async () => {
    const response = await request(app)
      .get('/api/message/v1/messages?status=DELIVERED')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.messages).toHaveLength(1);
    expect(response.body.data.messages[0].status).toBe('DELIVERED');
  });

  it('should validate query parameters', async () => {
    const response = await request(app)
      .get('/api/message/v1/messages?page_size=200')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_QUERY_PARAMS');
  });
});
```

## Testing Checklist
- [ ] Unit tests for controller with various query parameters
- [ ] Unit tests for service layer with filtering and pagination
- [ ] Integration tests for API endpoint with different filters
- [ ] Performance tests with large datasets
- [ ] Cache functionality tests
- [ ] Validation tests for query parameters
- [ ] Authentication tests
- [ ] Error handling tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Query parameter documentation
- [ ] Pagination documentation
- [ ] Performance optimization notes
- [ ] Caching strategy documentation

This task creates a comprehensive message listing endpoint with advanced filtering, pagination, sorting, search capabilities, performance optimizations, and detailed summary statistics.