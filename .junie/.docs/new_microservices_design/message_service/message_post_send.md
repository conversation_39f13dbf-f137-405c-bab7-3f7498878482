# Message Service - POST /messages (Send Single Message)

## Task Overview
**Endpoint**: `POST /api/message/v1/messages`  
**Purpose**: Send a single message via SMS, Email, or Push notification  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Project setup, provider implementations, queue processors  

## API Specification

### Request
```http
POST /api/message/v1/messages
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "transport": "SMS",
  "provider": "TWILIO",
  "from": "STORE",
  "to": "+94771234567",
  "content": "Your order #12345 is ready for pickup!",
  "subject": null,
  "campaign_id": "64a1b2c3d4e5f6789012346",
  "priority": "HIGH",
  "schedule_at": null,
  "metadata": {
    "order_id": "12345",
    "customer_id": "cust_789",
    "notification_type": "order_ready"
  }
}
```

### Response
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "message_id": "msg_64a1b2c3d4e5f678901234b",
    "status": "QUEUED",
    "transport": "SMS",
    "provider": "TWILIO",
    "from": "STORE",
    "to": "+94771234567",
    "estimated_delivery": "2024-08-11T15:31:00Z",
    "cost": 0.05,
    "currency": "USD",
    "tracking_url": "/api/message/v1/messages/msg_64a1b2c3d4e5f678901234b",
    "webhook_url": "https://api.example.com/webhooks/message-status"
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f678901234c"
  }
}
```

## Implementation Tasks

### 1. Database Model (30 minutes)
```typescript
// src/lib/db/models/message.log.model.ts
import { Schema, model } from 'mongoose';
import { MessageLogDocument } from '@/types/message.types';

const messageLogSchema = new Schema<MessageLogDocument>({
  org_id: { type: String, required: true, index: true },
  message_id: { type: String, required: true, unique: true, index: true },
  
  // Message details
  transport: {
    type: String,
    enum: ['SMS', 'EMAIL', 'PUSH', 'FACEBOOK_MESSENGER'],
    required: true,
    index: true
  },
  from: { type: String, required: true },
  to: { type: String, required: true, index: true },
  content: { type: String, required: true },
  subject: String, // For email
  html_content: String, // For email
  
  // Campaign association
  campaign_id: { type: Schema.Types.ObjectId, ref: 'Campaign', index: true },
  is_campaign_message: { type: Boolean, default: false, index: true },
  
  // Provider information
  provider: { type: String, required: true, index: true },
  provider_message_id: String,
  provider_response: Schema.Types.Mixed,
  
  // Status tracking with history
  status: {
    type: String,
    enum: ['QUEUED', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', 'OPENED', 'CLICKED', 'UNSUBSCRIBED'],
    default: 'QUEUED',
    required: true,
    index: true
  },
  status_history: [{
    status: {
      type: String,
      enum: ['QUEUED', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', 'OPENED', 'CLICKED', 'UNSUBSCRIBED']
    },
    timestamp: { type: Date, default: Date.now },
    details: String,
    provider_status: String,
    error_code: String
  }],
  
  // Delivery information
  queued_at: { type: Date, default: Date.now, index: true },
  sent_at: Date,
  delivered_at: Date,
  opened_at: Date,
  clicked_at: Date,
  failed_at: Date,
  bounced_at: Date,
  
  // Error handling
  error_code: String,
  error_message: String,
  retry_count: { type: Number, default: 0 },
  max_retries: { type: Number, default: 3 },
  next_retry_at: Date,
  
  // Cost tracking
  cost: { type: Number, required: true, default: 0 },
  currency: { type: String, default: 'USD' },
  
  // Engagement tracking
  engagement: {
    opened: { type: Boolean, default: false },
    clicked: { type: Boolean, default: false },
    unsubscribed: { type: Boolean, default: false },
    links_clicked: [String],
    user_agent: String,
    ip_address: String,
    click_timestamp: Date,
    open_timestamp: Date
  },
  
  // Priority and scheduling
  priority: {
    type: String,
    enum: ['LOW', 'NORMAL', 'HIGH', 'CRITICAL'],
    default: 'NORMAL'
  },
  scheduled_at: Date,
  
  // Metadata
  metadata: Schema.Types.Mixed,
  tags: [String]
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

// Indexes for performance
messageLogSchema.index({ org_id: 1, transport: 1 });
messageLogSchema.index({ org_id: 1, created_at: -1 });
messageLogSchema.index({ campaign_id: 1, status: 1 });
messageLogSchema.index({ status: 1, next_retry_at: 1 });
messageLogSchema.index({ provider: 1, created_at: -1 });
messageLogSchema.index({ 'engagement.opened': 1, transport: 1 });

export const MessageLogModel = model<MessageLogDocument>('MessageLog', messageLogSchema);
```

### 2. Validation Schema (25 minutes)
```typescript
// src/lib/validation/message.validation.ts
import Joi from 'joi';

export const sendMessageSchema = Joi.object({
  transport: Joi.string().valid('SMS', 'EMAIL', 'PUSH', 'FACEBOOK_MESSENGER').required(),
  provider: Joi.string().optional(), // Auto-select if not provided
  from: Joi.string().required(),
  to: Joi.string().required(),
  content: Joi.string().max(10000).required(),
  subject: Joi.when('transport', {
    is: 'EMAIL',
    then: Joi.string().max(255).required(),
    otherwise: Joi.optional()
  }),
  html_content: Joi.when('transport', {
    is: 'EMAIL',
    then: Joi.string().optional(),
    otherwise: Joi.forbidden()
  }),
  campaign_id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).optional(),
  priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'CRITICAL').default('NORMAL'),
  schedule_at: Joi.date().iso().min('now').optional(),
  metadata: Joi.object().optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  
  // Attachments for email
  attachments: Joi.when('transport', {
    is: 'EMAIL',
    then: Joi.array().items(Joi.object({
      filename: Joi.string().required(),
      content: Joi.string().required(),
      contentType: Joi.string().required(),
      disposition: Joi.string().valid('attachment', 'inline').default('attachment')
    })).max(10).optional(),
    otherwise: Joi.forbidden()
  })
});

// Custom validation for phone numbers
export const validatePhoneNumber = (phone: string, transport: string): boolean => {
  if (transport !== 'SMS') return true;
  
  // E.164 format validation
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
};

// Custom validation for email addresses
export const validateEmailAddress = (email: string, transport: string): boolean => {
  if (transport !== 'EMAIL') return true;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
```

### 3. Service Layer (60 minutes)
```typescript
// src/services/message.service.ts
import { v4 as uuidv4 } from 'uuid';
import { MessageLogModel } from '@/lib/db/models/message.log.model';
import { ProviderService } from './provider.service';
import { MessageOutboundQueue } from '@/queues/message.outbound.queue';
import { SendMessageRequest, MessageResponse } from '@/types/message.types';
import { ValidationError, BadRequestError } from '@/lib/errors/error.types';
import { validatePhoneNumber, validateEmailAddress } from '@/lib/validation/message.validation';

export class MessageService {
  static async sendMessage(data: SendMessageRequest): Promise<MessageResponse> {
    try {
      // Generate unique message ID
      const messageId = `msg_${uuidv4().replace(/-/g, '')}`;
      
      // Validate recipient format based on transport
      this.validateRecipient(data.to, data.transport);
      
      // Auto-select provider if not specified
      const provider = data.provider || await ProviderService.selectBestProvider(
        data.transport,
        data.to,
        data.org_id
      );
      
      // Validate provider supports transport
      await ProviderService.validateProviderForTransport(provider, data.transport);
      
      // Calculate estimated cost
      const cost = await ProviderService.calculateMessageCost(provider, data);
      
      // Create message log entry
      const messageLog = new MessageLogModel({
        org_id: data.org_id,
        message_id: messageId,
        transport: data.transport,
        from: data.from,
        to: data.to,
        content: data.content,
        subject: data.subject,
        html_content: data.html_content,
        campaign_id: data.campaign_id,
        is_campaign_message: !!data.campaign_id,
        provider: provider,
        status: 'QUEUED',
        priority: data.priority || 'NORMAL',
        scheduled_at: data.schedule_at,
        cost: cost,
        currency: 'USD',
        metadata: data.metadata,
        tags: data.tags,
        status_history: [{
          status: 'QUEUED',
          timestamp: new Date(),
          details: 'Message queued for processing'
        }]
      });
      
      await messageLog.save();
      
      // Queue message for processing
      const jobData = {
        jobId: uuidv4(),
        orgId: data.org_id,
        createdBy: data.created_by,
        messageId: messageId,
        transport: data.transport,
        provider: provider,
        from: data.from,
        to: data.to,
        content: data.content,
        subject: data.subject,
        htmlContent: data.html_content,
        campaignId: data.campaign_id,
        priority: this.mapPriorityToQueuePriority(data.priority || 'NORMAL'),
        metadata: data.metadata,
        attachments: data.attachments
      };
      
      // Schedule or queue immediately
      if (data.schedule_at) {
        const delay = new Date(data.schedule_at).getTime() - Date.now();
        await MessageOutboundQueue.addJob(jobData, { delay: Math.max(0, delay) });
      } else {
        await MessageOutboundQueue.addJob(jobData);
      }
      
      // Return response
      return {
        message_id: messageId,
        status: 'QUEUED',
        transport: data.transport,
        provider: provider,
        from: data.from,
        to: data.to,
        estimated_delivery: this.calculateEstimatedDelivery(data.schedule_at),
        cost: cost,
        currency: 'USD',
        tracking_url: `/api/message/v1/messages/${messageId}`,
        webhook_url: data.webhook_url
      };
      
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BadRequestError) {
        throw error;
      }
      throw new Error(`Failed to send message: ${error.message}`);
    }
  }
  
  private static validateRecipient(recipient: string, transport: string): void {
    switch (transport) {
      case 'SMS':
        if (!validatePhoneNumber(recipient, transport)) {
          throw new ValidationError('Invalid phone number format. Use E.164 format (+**********)');
        }
        break;
      case 'EMAIL':
        if (!validateEmailAddress(recipient, transport)) {
          throw new ValidationError('Invalid email address format');
        }
        break;
      case 'PUSH':
        if (!recipient || recipient.length < 10) {
          throw new ValidationError('Invalid push notification token');
        }
        break;
    }
  }
  
  private static mapPriorityToQueuePriority(priority: string): number {
    const priorityMap = {
      'CRITICAL': 10,
      'HIGH': 7,
      'NORMAL': 5,
      'LOW': 3
    };
    return priorityMap[priority] || 5;
  }
  
  private static calculateEstimatedDelivery(scheduledAt?: string): string {
    const baseTime = scheduledAt ? new Date(scheduledAt) : new Date();
    // Add 30 seconds for processing time
    baseTime.setSeconds(baseTime.getSeconds() + 30);
    return baseTime.toISOString();
  }
  
  static async getMessageById(messageId: string, orgId: string): Promise<any> {
    const message = await MessageLogModel.findOne({
      message_id: messageId,
      org_id: orgId
    }).lean();
    
    if (!message) {
      throw new NotFoundError('Message not found');
    }
    
    return this.formatMessageResponse(message);
  }
  
  private static formatMessageResponse(message: any): any {
    return {
      message_id: message.message_id,
      transport: message.transport,
      provider: message.provider,
      status: message.status,
      from: message.from,
      to: message.to,
      content: message.content,
      subject: message.subject,
      cost: message.cost,
      currency: message.currency,
      queued_at: message.queued_at,
      sent_at: message.sent_at,
      delivered_at: message.delivered_at,
      opened_at: message.opened_at,
      clicked_at: message.clicked_at,
      failed_at: message.failed_at,
      provider_message_id: message.provider_message_id,
      status_history: message.status_history,
      engagement: message.engagement,
      metadata: message.metadata,
      created_at: message.created_at,
      updated_at: message.updated_at
    };
  }
}
```

### 4. Controller Implementation (30 minutes)
```typescript
// src/controllers/message.controller.ts
import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '@/middleware/auth.middleware';
import { MessageService } from '@/services/message.service';
import { sendMessageSchema } from '@/lib/validation/message.validation';
import { ValidationError } from '@/lib/errors/error.types';

export class MessageController {
  static async sendMessage(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Validate request body
      const { error, value } = sendMessageSchema.validate(req.body);
      if (error) {
        throw new ValidationError(error.details[0].message);
      }
      
      // Add organization and user context
      const messageData = {
        ...value,
        org_id: req.user.org_id,
        created_by: req.user.id,
        webhook_url: req.body.webhook_url || `${process.env.BASE_WEBHOOK_URL}/message-status`
      };
      
      // Send message
      const result = await MessageService.sendMessage(messageData);
      
      res.status(201).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
  
  static async getMessageById(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Message ID is required');
      }
      
      const message = await MessageService.getMessageById(id, req.user.org_id);
      
      res.json({
        success: true,
        data: message,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 5. Background Processor (45 minutes)
```typescript
// src/workers/processors/message.outbound.processor.ts
import { Job } from 'bull';
import { MessageJobData } from '@/types/queue.types';
import { MessageLogModel } from '@/lib/db/models/message.log.model';
import { ProviderService } from '@/services/provider.service';
import { MessageRetryQueue } from '@/queues/message.retry.queue';
import { PaymentService } from '@/services/payment.service';

export class MessageOutboundProcessor {
  static async processJob(job: Job<MessageJobData>): Promise<void> {
    const { messageId, orgId } = job.data;
    
    try {
      // Get message from database
      const message = await MessageLogModel.findOne({
        message_id: messageId,
        org_id: orgId
      });
      
      if (!message) {
        throw new Error(`Message ${messageId} not found`);
      }
      
      // Check if message is still in queue status
      if (message.status !== 'QUEUED') {
        console.log(`Message ${messageId} already processed with status: ${message.status}`);
        return;
      }
      
      // Update status to sending
      await this.updateMessageStatus(messageId, 'SENDING', 'Message being processed');
      
      // Send message via provider
      const provider = await ProviderService.getProvider(message.provider);
      const result = await provider.sendMessage({
        messageId: message.message_id,
        orgId: message.org_id,
        transport: message.transport as any,
        from: message.from,
        to: message.to,
        content: message.content,
        subject: message.subject,
        htmlContent: message.html_content,
        metadata: message.metadata
      });
      
      if (result.success) {
        // Update message with success
        await MessageLogModel.findOneAndUpdate(
          { message_id: messageId },
          {
            status: 'SENT',
            sent_at: new Date(),
            provider_message_id: result.providerMessageId,
            provider_response: result.metadata,
            cost: result.cost,
            currency: result.currency,
            $push: {
              status_history: {
                status: 'SENT',
                timestamp: new Date(),
                details: 'Message sent successfully',
                provider_status: result.metadata?.status
              }
            }
          }
        );
        
        // Track usage for billing
        await PaymentService.trackUsage({
          orgId: message.org_id,
          usageType: message.transport.toLowerCase(),
          quantity: 1,
          cost: result.cost,
          currency: result.currency,
          referenceId: messageId,
          referenceType: 'MESSAGE'
        });
        
      } else {
        // Handle failure
        await this.handleMessageFailure(messageId, result.error || 'Unknown error', message);
      }
      
    } catch (error) {
      console.error(`Error processing message ${messageId}:`, error);
      
      // Get current message for retry logic
      const message = await MessageLogModel.findOne({ message_id: messageId });
      if (message) {
        await this.handleMessageFailure(messageId, error.message, message);
      }
    }
  }
  
  private static async updateMessageStatus(
    messageId: string,
    status: string,
    details: string
  ): Promise<void> {
    await MessageLogModel.findOneAndUpdate(
      { message_id: messageId },
      {
        status: status,
        $push: {
          status_history: {
            status: status,
            timestamp: new Date(),
            details: details
          }
        }
      }
    );
  }
  
  private static async handleMessageFailure(
    messageId: string,
    error: string,
    message: any
  ): Promise<void> {
    const retryCount = message.retry_count + 1;
    const maxRetries = message.max_retries || 3;
    
    if (retryCount <= maxRetries) {
      // Schedule retry
      const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff
      const nextRetryAt = new Date(Date.now() + retryDelay);
      
      await MessageLogModel.findOneAndUpdate(
        { message_id: messageId },
        {
          retry_count: retryCount,
          next_retry_at: nextRetryAt,
          error_message: error,
          $push: {
            status_history: {
              status: 'RETRY_SCHEDULED',
              timestamp: new Date(),
              details: `Retry ${retryCount}/${maxRetries} scheduled`,
              error_code: 'SEND_FAILED'
            }
          }
        }
      );
      
      // Queue for retry
      await MessageRetryQueue.addJob({
        jobId: `retry_${messageId}_${retryCount}`,
        orgId: message.org_id,
        createdBy: 'SYSTEM',
        messageId: messageId,
        retryCount: retryCount
      }, { delay: retryDelay });
      
    } else {
      // Mark as permanently failed
      await MessageLogModel.findOneAndUpdate(
        { message_id: messageId },
        {
          status: 'FAILED',
          failed_at: new Date(),
          error_message: error,
          $push: {
            status_history: {
              status: 'FAILED',
              timestamp: new Date(),
              details: `Message failed after ${maxRetries} retries`,
              error_code: 'MAX_RETRIES_EXCEEDED'
            }
          }
        }
      );
    }
  }
}
```

### 6. Route Definition (15 minutes)
```typescript
// src/routes/message.routes.ts
import { Router } from 'express';
import { MessageController } from '@/controllers/message.controller';
import { authenticateRequest } from '@/middleware/auth.middleware';
import { rateLimitMiddleware } from '@/middleware/rate.limit.middleware';

const router = Router();

// Send single message
router.post(
  '/',
  authenticateRequest,
  rateLimitMiddleware,
  MessageController.sendMessage
);

// Get message by ID
router.get(
  '/:id',
  authenticateRequest,
  rateLimitMiddleware,
  MessageController.getMessageById
);

export default router;
```

### 7. Unit Tests (40 minutes)
```typescript
// tests/unit/controllers/message.controller.test.ts
import { MessageController } from '@/controllers/message.controller';
import { MessageService } from '@/services/message.service';
import { ValidationError } from '@/lib/errors/error.types';

jest.mock('@/services/message.service');

describe('MessageController.sendMessage', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = {
      body: {
        transport: 'SMS',
        from: 'TEST',
        to: '+**********',
        content: 'Hello World'
      },
      user: { id: 'user123', org_id: 'org123' },
      headers: { 'x-request-id': 'req123' }
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should send message successfully', async () => {
    const mockResult = {
      message_id: 'msg123',
      status: 'QUEUED',
      cost: 0.05
    };
    
    (MessageService.sendMessage as jest.Mock).mockResolvedValue(mockResult);

    await MessageController.sendMessage(mockReq, mockRes, mockNext);

    expect(mockRes.status).toHaveBeenCalledWith(201);
    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: mockResult,
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        request_id: 'req123'
      })
    });
  });

  it('should handle validation errors', async () => {
    mockReq.body.transport = 'INVALID';

    await MessageController.sendMessage(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(ValidationError));
  });
});
```

## Testing Checklist
- [ ] Unit tests for controller
- [ ] Unit tests for service layer
- [ ] Unit tests for background processor
- [ ] Integration tests for API endpoint
- [ ] Provider integration tests
- [ ] Queue processing tests
- [ ] Error handling tests
- [ ] Validation tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Provider integration guide
- [ ] Error codes documentation
- [ ] Rate limiting documentation

This task creates a comprehensive message sending system with multi-provider support, queue processing, retry logic, and proper error handling.
