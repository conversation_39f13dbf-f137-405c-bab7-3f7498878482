# Payment Service - POST /subscriptions (Create Subscription)

## Task Overview
**Endpoint**: `POST /api/payment/v1/subscriptions`  
**Purpose**: Create a recurring subscription for an organization with Stripe integration and billing management  
**Estimated Time**: 2.5 days  
**Priority**: High  
**Dependencies**: Payment service setup, Stripe integration, payment methods management  

## API Specification

### Request
```http
POST /api/payment/v1/subscriptions
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "price_id": "price_1234567890abcdef",
  "payment_method_id": "pm_1234567890abcdef",
  "trial_period_days": 14,
  "billing_cycle_anchor": "2024-09-01T00:00:00Z",
  "prorate": true,
  "collection_method": "charge_automatically",
  "metadata": {
    "plan_name": "Pro Plan",
    "billing_cycle": "monthly",
    "features": "unlimited_campaigns,advanced_analytics"
  },
  "coupon": "DISCOUNT20",
  "tax_rates": ["txr_1234567890abcdef"]
}
```

### Response
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "subscription_id": "sub_1234567890abcdef",
    "customer_id": "cus_1234567890abcdef",
    "status": "active",
    "current_period_start": "2024-08-11T15:30:00Z",
    "current_period_end": "2024-09-11T15:30:00Z",
    "trial_start": "2024-08-11T15:30:00Z",
    "trial_end": "2024-08-25T15:30:00Z",
    "billing_cycle_anchor": "2024-09-01T00:00:00Z",
    "items": [
      {
        "id": "si_1234567890abcdef",
        "price": {
          "id": "price_1234567890abcdef",
          "unit_amount": 2900,
          "currency": "usd",
          "recurring": {
            "interval": "month",
            "interval_count": 1
          }
        },
        "quantity": 1
      }
    ],
    "discount": {
      "coupon": {
        "id": "DISCOUNT20",
        "percent_off": 20,
        "duration": "once"
      }
    },
    "latest_invoice": {
      "id": "in_1234567890abcdef",
      "amount_paid": 2320,
      "amount_due": 0,
      "status": "paid",
      "hosted_invoice_url": "https://invoice.stripe.com/i/abc123"
    },
    "next_invoice_date": "2024-09-11T15:30:00Z",
    "cancel_at_period_end": false,
    "metadata": {
      "plan_name": "Pro Plan",
      "billing_cycle": "monthly",
      "features": "unlimited_campaigns,advanced_analytics"
    },
    "created_at": "2024-08-11T15:30:00Z",
    "updated_at": "2024-08-11T15:30:00Z"
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 402 Payment Required
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "SUBSCRIPTION_CREATION_FAILED",
    "message": "Unable to create subscription due to payment failure",
    "details": [
      {
        "field": "payment_method",
        "message": "Your card was declined"
      }
    ],
    "stripe_error": {
      "type": "card_error",
      "code": "card_declined",
      "decline_code": "insufficient_funds"
    },
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Database Model (35 minutes)
```typescript
// src/lib/db/models/subscription.model.ts
import { Schema, model, Document } from 'mongoose';

export interface SubscriptionDocument extends Document {
  org_id: string;
  stripe_subscription_id: string;
  stripe_customer_id: string;
  stripe_price_id: string;
  status: 'incomplete' | 'incomplete_expired' | 'trialing' | 'active' | 'past_due' | 'canceled' | 'unpaid' | 'paused';
  current_period_start: Date;
  current_period_end: Date;
  trial_start?: Date;
  trial_end?: Date;
  billing_cycle_anchor?: Date;
  cancel_at_period_end: boolean;
  canceled_at?: Date;
  ended_at?: Date;
  collection_method: 'charge_automatically' | 'send_invoice';
  items: [{
    stripe_item_id: string;
    stripe_price_id: string;
    quantity: number;
    unit_amount: number;
    currency: string;
  }];
  discount?: {
    coupon_id: string;
    start: Date;
    end?: Date;
    percent_off?: number;
    amount_off?: number;
  };
  tax_rates: string[];
  latest_invoice_id?: string;
  pending_invoice_id?: string;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

const subscriptionSchema = new Schema<SubscriptionDocument>({
  org_id: { type: String, required: true, index: true },
  stripe_subscription_id: { type: String, required: true, unique: true, index: true },
  stripe_customer_id: { type: String, required: true, index: true },
  stripe_price_id: { type: String, required: true },
  status: {
    type: String,
    enum: ['incomplete', 'incomplete_expired', 'trialing', 'active', 'past_due', 'canceled', 'unpaid', 'paused'],
    required: true,
    index: true
  },
  current_period_start: { type: Date, required: true },
  current_period_end: { type: Date, required: true },
  trial_start: Date,
  trial_end: Date,
  billing_cycle_anchor: Date,
  cancel_at_period_end: { type: Boolean, default: false },
  canceled_at: Date,
  ended_at: Date,
  collection_method: {
    type: String,
    enum: ['charge_automatically', 'send_invoice'],
    default: 'charge_automatically'
  },
  items: [{
    stripe_item_id: { type: String, required: true },
    stripe_price_id: { type: String, required: true },
    quantity: { type: Number, default: 1 },
    unit_amount: { type: Number, required: true },
    currency: { type: String, default: 'usd' }
  }],
  discount: {
    coupon_id: String,
    start: Date,
    end: Date,
    percent_off: Number,
    amount_off: Number
  },
  tax_rates: [String],
  latest_invoice_id: String,
  pending_invoice_id: String,
  metadata: { type: Schema.Types.Mixed, default: {} },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

// Indexes
subscriptionSchema.index({ org_id: 1, status: 1 });
subscriptionSchema.index({ org_id: 1, current_period_end: 1 });
subscriptionSchema.index({ stripe_customer_id: 1, status: 1 });

export const SubscriptionModel = model<SubscriptionDocument>('Subscription', subscriptionSchema);
```

### 2. Service Layer (75 minutes)
```typescript
// src/services/subscription.service.ts
import { SubscriptionModel } from '@/lib/db/models/subscription.model';
import { StripeService } from '@/services/stripe.service';
import { PaymentService } from '@/services/payment.service';
import { CreateSubscriptionRequest, SubscriptionResponse } from '@/types/subscription.types';

export class SubscriptionService {
  static async createSubscription(
    orgId: string,
    subscriptionData: CreateSubscriptionRequest
  ): Promise<SubscriptionResponse> {
    try {
      // Validate price exists
      const stripePrice = await StripeService.retrievePrice(subscriptionData.price_id);
      
      // Get Stripe customer for organization
      const stripeCustomer = await PaymentService.getStripeCustomerForOrg(orgId);

      // Validate payment method if provided
      if (subscriptionData.payment_method_id) {
        await PaymentService.validatePaymentMethodOwnership(orgId, subscriptionData.payment_method_id);
        
        // Set as default payment method for customer
        await StripeService.setDefaultPaymentMethod(stripeCustomer.id, subscriptionData.payment_method_id);
      }

      // Check for existing active subscription
      const existingSubscription = await this.getActiveSubscription(orgId);
      if (existingSubscription) {
        throw new BadRequestError(
          'An active subscription already exists for this organization',
          'SUBSCRIPTION_ALREADY_EXISTS'
        );
      }

      // Prepare subscription parameters
      const subscriptionParams = this.buildSubscriptionParams(
        stripeCustomer.id,
        subscriptionData,
        orgId
      );

      // Create subscription in Stripe
      const stripeSubscription = await StripeService.createSubscription(subscriptionParams);

      // Handle incomplete subscription status
      if (stripeSubscription.status === 'incomplete') {
        await this.handleIncompleteSubscription(stripeSubscription, orgId);
      }

      // Save subscription record
      const subscription = await this.saveSubscriptionRecord(orgId, stripeSubscription);

      // Process trial period setup if applicable
      if (subscriptionData.trial_period_days && subscriptionData.trial_period_days > 0) {
        await this.setupTrialPeriod(subscription, subscriptionData.trial_period_days);
      }

      // Update organization subscription status
      await this.updateOrganizationSubscriptionStatus(orgId, stripeSubscription.status);

      return this.formatSubscriptionResponse(stripeSubscription, subscription);
    } catch (error) {
      if (error.type?.startsWith('Stripe')) {
        throw new BadRequestError(
          error.message,
          'STRIPE_SUBSCRIPTION_ERROR',
          [{ field: 'subscription', message: error.message }]
        );
      }
      if (error instanceof BadRequestError) {
        throw error;
      }
      throw new InternalError(`Failed to create subscription: ${error.message}`);
    }
  }

  private static buildSubscriptionParams(
    customerId: string,
    data: CreateSubscriptionRequest,
    orgId: string
  ): any {
    const params: any = {
      customer: customerId,
      items: [{ price: data.price_id }],
      payment_behavior: 'default_incomplete',
      payment_settings: {
        save_default_payment_method: 'on_subscription'
      },
      expand: ['latest_invoice.payment_intent', 'customer'],
      metadata: {
        org_id: orgId,
        ...data.metadata
      }
    };

    // Add payment method if provided
    if (data.payment_method_id) {
      params.default_payment_method = data.payment_method_id;
    }

    // Add trial period
    if (data.trial_period_days && data.trial_period_days > 0) {
      params.trial_period_days = data.trial_period_days;
    }

    // Add billing cycle anchor
    if (data.billing_cycle_anchor) {
      params.billing_cycle_anchor = Math.floor(new Date(data.billing_cycle_anchor).getTime() / 1000);
    }

    // Add proration behavior
    if (data.prorate !== undefined) {
      params.proration_behavior = data.prorate ? 'create_prorations' : 'none';
    }

    // Add collection method
    if (data.collection_method) {
      params.collection_method = data.collection_method;
    }

    // Add coupon
    if (data.coupon) {
      params.coupon = data.coupon;
    }

    // Add tax rates
    if (data.tax_rates && data.tax_rates.length > 0) {
      params.default_tax_rates = data.tax_rates;
    }

    return params;
  }

  private static async handleIncompleteSubscription(
    subscription: Stripe.Subscription,
    orgId: string
  ): Promise<void> {
    const latestInvoice = subscription.latest_invoice as Stripe.Invoice;
    
    if (latestInvoice?.payment_intent) {
      const paymentIntent = latestInvoice.payment_intent as Stripe.PaymentIntent;
      
      if (paymentIntent.status === 'requires_payment_method') {
        throw new BadRequestError(
          'Payment method is required to complete subscription',
          'PAYMENT_METHOD_REQUIRED'
        );
      }
      
      if (paymentIntent.status === 'requires_action') {
        throw new BadRequestError(
          'Additional authentication is required',
          'AUTHENTICATION_REQUIRED',
          undefined,
          {
            requires_action: true,
            payment_intent: {
              id: paymentIntent.id,
              client_secret: paymentIntent.client_secret
            }
          }
        );
      }
    }
  }

  private static async saveSubscriptionRecord(
    orgId: string,
    stripeSubscription: Stripe.Subscription
  ): Promise<SubscriptionDocument> {
    const subscriptionData = {
      org_id: orgId,
      stripe_subscription_id: stripeSubscription.id,
      stripe_customer_id: stripeSubscription.customer as string,
      stripe_price_id: stripeSubscription.items.data[0].price.id,
      status: stripeSubscription.status,
      current_period_start: new Date(stripeSubscription.current_period_start * 1000),
      current_period_end: new Date(stripeSubscription.current_period_end * 1000),
      trial_start: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : undefined,
      trial_end: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : undefined,
      billing_cycle_anchor: stripeSubscription.billing_cycle_anchor ? 
        new Date(stripeSubscription.billing_cycle_anchor * 1000) : undefined,
      cancel_at_period_end: stripeSubscription.cancel_at_period_end,
      canceled_at: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : undefined,
      ended_at: stripeSubscription.ended_at ? new Date(stripeSubscription.ended_at * 1000) : undefined,
      collection_method: stripeSubscription.collection_method,
      items: stripeSubscription.items.data.map(item => ({
        stripe_item_id: item.id,
        stripe_price_id: item.price.id,
        quantity: item.quantity || 1,
        unit_amount: item.price.unit_amount || 0,
        currency: item.price.currency
      })),
      discount: stripeSubscription.discount ? {
        coupon_id: stripeSubscription.discount.coupon.id,
        start: new Date(stripeSubscription.discount.start * 1000),
        end: stripeSubscription.discount.end ? new Date(stripeSubscription.discount.end * 1000) : undefined,
        percent_off: stripeSubscription.discount.coupon.percent_off,
        amount_off: stripeSubscription.discount.coupon.amount_off
      } : undefined,
      tax_rates: stripeSubscription.default_tax_rates?.map(rate => rate.id) || [],
      latest_invoice_id: typeof stripeSubscription.latest_invoice === 'string' ? 
        stripeSubscription.latest_invoice : stripeSubscription.latest_invoice?.id,
      metadata: stripeSubscription.metadata,
      created_at: new Date(stripeSubscription.created * 1000),
      updated_at: new Date()
    };

    const subscription = new SubscriptionModel(subscriptionData);
    await subscription.save();
    return subscription;
  }

  private static async setupTrialPeriod(
    subscription: SubscriptionDocument,
    trialDays: number
  ): Promise<void> {
    // Add trial period tracking
    await this.createTrialPeriodLog(subscription.org_id, subscription.stripe_subscription_id, trialDays);
    
    // Schedule trial end notification
    await this.scheduleTrialEndNotification(subscription.org_id, subscription.trial_end!);
  }

  private static async updateOrganizationSubscriptionStatus(
    orgId: string,
    status: string
  ): Promise<void> {
    // Update organization record with subscription status
    // This would typically update the organization's billing status
    // Implementation depends on your organization management system
  }

  static async getActiveSubscription(orgId: string): Promise<SubscriptionDocument | null> {
    return await SubscriptionModel.findOne({
      org_id: orgId,
      status: { $in: ['active', 'trialing', 'past_due'] }
    });
  }

  static async cancelSubscription(
    orgId: string,
    subscriptionId: string,
    immediately: boolean = false
  ): Promise<SubscriptionResponse> {
    try {
      // Find subscription
      const subscription = await SubscriptionModel.findOne({
        org_id: orgId,
        stripe_subscription_id: subscriptionId
      });

      if (!subscription) {
        throw new NotFoundError('Subscription not found');
      }

      // Cancel in Stripe
      const canceledSubscription = immediately ?
        await StripeService.cancelSubscriptionImmediately(subscriptionId) :
        await StripeService.cancelSubscriptionAtPeriodEnd(subscriptionId);

      // Update local record
      await SubscriptionModel.updateOne(
        { _id: subscription._id },
        {
          status: canceledSubscription.status,
          cancel_at_period_end: canceledSubscription.cancel_at_period_end,
          canceled_at: canceledSubscription.canceled_at ? 
            new Date(canceledSubscription.canceled_at * 1000) : undefined,
          ended_at: canceledSubscription.ended_at ? 
            new Date(canceledSubscription.ended_at * 1000) : undefined,
          updated_at: new Date()
        }
      );

      return this.formatSubscriptionResponse(canceledSubscription, subscription);
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to cancel subscription: ${error.message}`);
    }
  }

  private static formatSubscriptionResponse(
    stripeSubscription: Stripe.Subscription,
    localSubscription?: SubscriptionDocument
  ): SubscriptionResponse {
    return {
      subscription_id: stripeSubscription.id,
      customer_id: stripeSubscription.customer as string,
      status: stripeSubscription.status,
      current_period_start: new Date(stripeSubscription.current_period_start * 1000),
      current_period_end: new Date(stripeSubscription.current_period_end * 1000),
      trial_start: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : undefined,
      trial_end: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : undefined,
      billing_cycle_anchor: stripeSubscription.billing_cycle_anchor ? 
        new Date(stripeSubscription.billing_cycle_anchor * 1000) : undefined,
      items: stripeSubscription.items.data.map(item => ({
        id: item.id,
        price: {
          id: item.price.id,
          unit_amount: item.price.unit_amount,
          currency: item.price.currency,
          recurring: item.price.recurring
        },
        quantity: item.quantity || 1
      })),
      discount: stripeSubscription.discount ? {
        coupon: {
          id: stripeSubscription.discount.coupon.id,
          percent_off: stripeSubscription.discount.coupon.percent_off,
          amount_off: stripeSubscription.discount.coupon.amount_off,
          duration: stripeSubscription.discount.coupon.duration
        }
      } : undefined,
      latest_invoice: stripeSubscription.latest_invoice ? {
        id: typeof stripeSubscription.latest_invoice === 'string' ? 
          stripeSubscription.latest_invoice : stripeSubscription.latest_invoice.id,
        amount_paid: typeof stripeSubscription.latest_invoice === 'object' ? 
          stripeSubscription.latest_invoice.amount_paid : undefined,
        amount_due: typeof stripeSubscription.latest_invoice === 'object' ? 
          stripeSubscription.latest_invoice.amount_due : undefined,
        status: typeof stripeSubscription.latest_invoice === 'object' ? 
          stripeSubscription.latest_invoice.status : undefined,
        hosted_invoice_url: typeof stripeSubscription.latest_invoice === 'object' ? 
          stripeSubscription.latest_invoice.hosted_invoice_url : undefined
      } : undefined,
      next_invoice_date: new Date(stripeSubscription.current_period_end * 1000),
      cancel_at_period_end: stripeSubscription.cancel_at_period_end,
      metadata: stripeSubscription.metadata,
      created_at: new Date(stripeSubscription.created * 1000),
      updated_at: localSubscription?.updated_at || new Date()
    };
  }
}
```

### 3. Enhanced Stripe Service (30 minutes)
```typescript
// src/services/stripe.service.ts (additions)
export class StripeService {
  static async createSubscription(params: any): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.create(params);
    } catch (error) {
      throw new InternalError(`Failed to create subscription: ${error.message}`);
    }
  }

  static async retrievePrice(priceId: string): Promise<Stripe.Price> {
    try {
      return await this.stripe.prices.retrieve(priceId);
    } catch (error) {
      throw new BadRequestError(`Invalid price ID: ${priceId}`, 'INVALID_PRICE_ID');
    }
  }

  static async cancelSubscriptionAtPeriodEnd(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true
      });
    } catch (error) {
      throw new InternalError(`Failed to cancel subscription: ${error.message}`);
    }
  }

  static async cancelSubscriptionImmediately(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.cancel(subscriptionId);
    } catch (error) {
      throw new InternalError(`Failed to cancel subscription immediately: ${error.message}`);
    }
  }

  static async reactivateSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false
      });
    } catch (error) {
      throw new InternalError(`Failed to reactivate subscription: ${error.message}`);
    }
  }

  static async retrieveSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['latest_invoice', 'customer', 'items.data.price']
      });
    } catch (error) {
      throw new InternalError(`Failed to retrieve subscription: ${error.message}`);
    }
  }
}
```

### 4. Controller Implementation (25 minutes)
```typescript
// src/controllers/subscription.controller.ts
export class SubscriptionController {
  static async createSubscription(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const result = await SubscriptionService.createSubscription(req.user.org_id, req.body);

      res.status(201).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async cancelSubscription(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      const { immediately } = req.body;
      
      if (!id) {
        throw new BadRequestError('Subscription ID is required');
      }

      const result = await SubscriptionService.cancelSubscription(
        req.user.org_id,
        id,
        immediately
      );

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 5. Validation Schema (20 minutes)
```typescript
// src/validators/subscription.validator.ts
import Joi from 'joi';

export const createSubscriptionSchema = Joi.object({
  price_id: Joi.string().pattern(/^price_[a-zA-Z0-9]+$/).required(),
  payment_method_id: Joi.string().pattern(/^pm_[a-zA-Z0-9]+$/).optional(),
  trial_period_days: Joi.number().integer().min(1).max(365).optional(),
  billing_cycle_anchor: Joi.date().iso().optional(),
  prorate: Joi.boolean().optional().default(true),
  collection_method: Joi.string().valid('charge_automatically', 'send_invoice').optional(),
  coupon: Joi.string().optional(),
  tax_rates: Joi.array().items(Joi.string().pattern(/^txr_[a-zA-Z0-9]+$/)).optional(),
  metadata: Joi.object().pattern(
    Joi.string(),
    Joi.alternatives().try(Joi.string(), Joi.number())
  ).optional()
});

export const cancelSubscriptionSchema = Joi.object({
  immediately: Joi.boolean().optional().default(false)
});
```

### 6. Route Definitions (10 minutes)
```typescript
// src/routes/subscription.routes.ts
import { Router } from 'express';
import { SubscriptionController } from '@/controllers/subscription.controller';
import { authenticateRequest } from '@/middleware/auth.middleware';
import { validateRequestBody } from '@/middleware/validation.middleware';
import { rateLimitMiddleware } from '@/middleware/rate.limit.middleware';
import { createSubscriptionSchema, cancelSubscriptionSchema } from '@/validators/subscription.validator';

const router = Router();

router.post(
  '/',
  authenticateRequest,
  validateRequestBody(createSubscriptionSchema),
  rateLimitMiddleware,
  SubscriptionController.createSubscription
);

router.delete(
  '/:id',
  authenticateRequest,
  validateRequestBody(cancelSubscriptionSchema),
  rateLimitMiddleware,
  SubscriptionController.cancelSubscription
);

export default router;
```

### 7. Type Definitions (15 minutes)
```typescript
// src/types/subscription.types.ts
export interface CreateSubscriptionRequest {
  price_id: string;
  payment_method_id?: string;
  trial_period_days?: number;
  billing_cycle_anchor?: string;
  prorate?: boolean;
  collection_method?: 'charge_automatically' | 'send_invoice';
  coupon?: string;
  tax_rates?: string[];
  metadata?: Record<string, any>;
}

export interface SubscriptionResponse {
  subscription_id: string;
  customer_id: string;
  status: string;
  current_period_start: Date;
  current_period_end: Date;
  trial_start?: Date;
  trial_end?: Date;
  billing_cycle_anchor?: Date;
  items: Array<{
    id: string;
    price: {
      id: string;
      unit_amount: number | null;
      currency: string;
      recurring: any;
    };
    quantity: number;
  }>;
  discount?: {
    coupon: {
      id: string;
      percent_off: number | null;
      amount_off: number | null;
      duration: string;
    };
  };
  latest_invoice?: {
    id: string;
    amount_paid?: number;
    amount_due?: number;
    status?: string;
    hosted_invoice_url?: string | null;
  };
  next_invoice_date: Date;
  cancel_at_period_end: boolean;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}
```

### 8. Integration Tests (30 minutes)
```typescript
// tests/integration/subscription.api.test.ts
describe('POST /api/payment/v1/subscriptions', () => {
  let authToken: string;
  let orgId: string;

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
  });

  afterEach(async () => {
    await SubscriptionModel.deleteMany({ org_id: orgId });
  });

  it('should create subscription successfully', async () => {
    // Setup payment method
    await PaymentMethodModel.create({
      org_id: orgId,
      stripe_customer_id: 'cus_test123',
      stripe_payment_method_id: 'pm_test123',
      type: 'card',
      is_default: true,
      status: 'active',
      billing_details: {
        name: 'Test User',
        email: '<EMAIL>'
      }
    });

    // Mock Stripe calls
    jest.spyOn(StripeService, 'retrievePrice').mockResolvedValue({
      id: 'price_test123',
      unit_amount: 2900,
      currency: 'usd',
      recurring: { interval: 'month' }
    } as any);

    jest.spyOn(PaymentService, 'getStripeCustomerForOrg').mockResolvedValue({
      id: 'cus_test123'
    } as any);

    jest.spyOn(StripeService, 'createSubscription').mockResolvedValue({
      id: 'sub_test123',
      customer: 'cus_test123',
      status: 'active',
      current_period_start: 1628690000,
      current_period_end: 1631368400,
      trial_start: 1628690000,
      trial_end: 1629894800,
      items: {
        data: [{
          id: 'si_test123',
          price: {
            id: 'price_test123',
            unit_amount: 2900,
            currency: 'usd'
          },
          quantity: 1
        }]
      },
      created: 1628690000,
      metadata: { org_id: orgId }
    } as any);

    const subscriptionData = {
      price_id: 'price_test123',
      payment_method_id: 'pm_test123',
      trial_period_days: 14,
      metadata: {
        plan_name: 'Pro Plan'
      }
    };

    const response = await request(app)
      .post('/api/payment/v1/subscriptions')
      .set('Authorization', `Bearer ${authToken}`)
      .send(subscriptionData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.subscription_id).toBe('sub_test123');
    expect(response.body.data.status).toBe('active');
    expect(response.body.data.trial_start).toBeDefined();
    expect(response.body.data.trial_end).toBeDefined();

    // Verify database record
    const subscription = await SubscriptionModel.findOne({ org_id: orgId });
    expect(subscription).toBeDefined();
    expect(subscription?.stripe_subscription_id).toBe('sub_test123');
  });

  it('should prevent duplicate active subscriptions', async () => {
    // Create existing active subscription
    await SubscriptionModel.create({
      org_id: orgId,
      stripe_subscription_id: 'sub_existing',
      stripe_customer_id: 'cus_test123',
      stripe_price_id: 'price_test123',
      status: 'active',
      current_period_start: new Date(),
      current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      items: [{
        stripe_item_id: 'si_existing',
        stripe_price_id: 'price_test123',
        quantity: 1,
        unit_amount: 2900,
        currency: 'usd'
      }]
    });

    const subscriptionData = {
      price_id: 'price_test123'
    };

    const response = await request(app)
      .post('/api/payment/v1/subscriptions')
      .set('Authorization', `Bearer ${authToken}`)
      .send(subscriptionData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('SUBSCRIPTION_ALREADY_EXISTS');
  });
});
```

## Testing Checklist
- [ ] Unit tests for service layer subscription creation
- [ ] Unit tests for trial period handling
- [ ] Unit tests for billing cycle management
- [ ] Integration tests for POST endpoint
- [ ] Error handling tests for payment failures
- [ ] Validation tests for subscription parameters
- [ ] Authentication and authorization tests
- [ ] Duplicate subscription prevention tests
- [ ] Stripe integration tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Subscription lifecycle documentation
- [ ] Trial period handling guide
- [ ] Billing cycle documentation
- [ ] Error handling guide

This task creates a comprehensive subscription creation system with proper Stripe integration, trial period management, billing cycle handling, and extensive validation.