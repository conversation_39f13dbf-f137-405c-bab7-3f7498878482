# Payment Service - GET /billing (Billing History and Reports)

## Task Overview
**Endpoint**: `GET /api/payment/v1/billing`  
**Purpose**: Retrieve billing history, generate reports, and manage billing-related data  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Payment processing, subscription management, invoice generation  

## API Specification

### Billing History Request
```http
GET /api/payment/v1/billing/history?period=last_6_months&include_details=true
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Billing Summary Request
```http
GET /api/payment/v1/billing/summary?year=2024
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Billing Report Request
```http
GET /api/payment/v1/billing/reports?report_type=monthly&format=pdf&period=2024-07
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Current Billing Cycle Request
```http
GET /api/payment/v1/billing/current
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Query Parameters
- `period` (string): Time period (current_month, last_month, last_3_months, last_6_months, current_year, custom)
- `start_date` (string): Start date for custom period (ISO 8601)
- `end_date` (string): End date for custom period (ISO 8601)
- `include_details` (boolean): Include detailed line items (default: false)
- `status` (string): Filter by payment status (paid, pending, failed, refunded)
- `subscription_id` (string): Filter by specific subscription
- `report_type` (string): Report format (summary, detailed, monthly, yearly)
- `format` (string): Export format (json, pdf, csv)
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `sort_by` (string): Sort field (date, amount, status)
- `sort_order` (string): Sort direction (asc, desc)

### Response - Billing History
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "period": {
      "start": "2024-01-01T00:00:00Z",
      "end": "2024-07-31T23:59:59Z",
      "description": "Last 6 months"
    },
    "summary": {
      "total_amount": 15847.92,
      "total_invoices": 42,
      "average_monthly": 2641.32,
      "payment_methods": {
        "card": 38,
        "bank_transfer": 3,
        "credits": 1
      },
      "status_breakdown": {
        "paid": 39,
        "pending": 2,
        "failed": 1,
        "refunded": 0
      }
    },
    "billing_records": [
      {
        "billing_id": "bill_64a1b2c3d4e5f6789012345",
        "invoice": {
          "invoice_id": "inv_64a1b2c3d4e5f6789012345",
          "invoice_number": "INV-2024-001234",
          "amount": 299.99,
          "currency": "USD",
          "status": "paid",
          "created_date": "2024-07-01T00:00:00Z",
          "due_date": "2024-07-31T23:59:59Z",
          "paid_date": "2024-07-15T14:30:00Z"
        },
        "subscription": {
          "subscription_id": "sub_64a1b2c3d4e5f6789012345",
          "plan_name": "Business Plan",
          "billing_cycle": "monthly",
          "period": {
            "start": "2024-07-01T00:00:00Z",
            "end": "2024-07-31T23:59:59Z"
          }
        },
        "payment": {
          "payment_id": "pay_64a1b2c3d4e5f6789012345",
          "method": "card",
          "last4": "4242",
          "brand": "visa",
          "amount_paid": 299.99,
          "fees": 8.70,
          "net_amount": 291.29,
          "processed_date": "2024-07-15T14:30:00Z"
        },
        "line_items": [
          {
            "description": "Business Plan - Monthly Subscription",
            "type": "subscription",
            "quantity": 1,
            "unit_amount": 199.99,
            "amount": 199.99,
            "period": {
              "start": "2024-07-01T00:00:00Z",
              "end": "2024-07-31T23:59:59Z"
            }
          },
          {
            "description": "SMS Messages",
            "type": "usage",
            "quantity": 5000,
            "unit_amount": 0.01,
            "amount": 50.00,
            "details": "5,000 SMS messages sent"
          },
          {
            "description": "Email Messages",
            "type": "usage", 
            "quantity": 25000,
            "unit_amount": 0.002,
            "amount": 50.00,
            "details": "25,000 emails sent"
          }
        ],
        "tax": {
          "tax_amount": 24.00,
          "tax_rate": 8.5,
          "tax_description": "Sales Tax",
          "tax_jurisdiction": "California, US"
        },
        "credits_applied": {
          "credit_amount": 0.00,
          "credit_notes": []
        },
        "adjustments": {
          "discount_amount": 0.00,
          "proration_amount": 0.00,
          "adjustment_reason": null
        }
      }
    ],
    "analytics": {
      "monthly_trends": [
        {
          "month": "2024-01",
          "amount": 2456.78,
          "invoices": 6,
          "growth_rate": 5.2
        },
        {
          "month": "2024-02",
          "amount": 2587.43,
          "invoices": 7,
          "growth_rate": 8.1
        }
      ],
      "payment_reliability": {
        "success_rate": 97.6,
        "average_payment_time": 3.2,
        "failed_payments": 1,
        "retry_success_rate": 100.0
      },
      "cost_categories": [
        {
          "category": "subscriptions",
          "amount": 11997.58,
          "percentage": 75.7
        },
        {
          "category": "usage_fees",
          "amount": 3150.42,
          "percentage": 19.9
        },
        {
          "category": "taxes",
          "amount": 699.92,
          "percentage": 4.4
        }
      ]
    }
  },
  "pagination": {
    "current_page": 1,
    "total_pages": 3,
    "total_items": 42,
    "items_per_page": 20,
    "has_next": true,
    "has_previous": false
  }
}
```

### Response - Billing Summary
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "year": 2024,
    "summary": {
      "total_amount": 28456.78,
      "total_invoices": 84,
      "average_monthly": 2371.40,
      "payment_success_rate": 97.6
    },
    "monthly_breakdown": [
      {
        "month": 1,
        "month_name": "January",
        "amount": 2456.78,
        "invoices": 6,
        "status": {
          "paid": 6,
          "pending": 0,
          "failed": 0
        },
        "growth_rate": 5.2
      },
      {
        "month": 2,
        "month_name": "February", 
        "amount": 2587.43,
        "invoices": 7,
        "status": {
          "paid": 7,
          "pending": 0,
          "failed": 0
        },
        "growth_rate": 8.1
      },
      {
        "month": 7,
        "month_name": "July",
        "amount": 3299.99,
        "invoices": 11,
        "status": {
          "paid": 10,
          "pending": 1,
          "failed": 0
        },
        "growth_rate": 15.3
      }
    ],
    "plan_distribution": [
      {
        "plan_name": "Starter",
        "subscribers": 45,
        "revenue": 4455.00,
        "percentage": 15.7
      },
      {
        "plan_name": "Business",
        "subscribers": 28,
        "revenue": 16756.72,
        "percentage": 58.9
      },
      {
        "plan_name": "Enterprise",
        "subscribers": 8,
        "revenue": 7245.06,
        "percentage": 25.4
      }
    ],
    "payment_methods": {
      "card": {
        "count": 78,
        "amount": 26934.56,
        "percentage": 94.6
      },
      "bank_transfer": {
        "count": 5,
        "amount": 1456.78,
        "percentage": 5.1
      },
      "credits": {
        "count": 1,
        "amount": 65.44,
        "percentage": 0.3
      }
    },
    "refunds_and_adjustments": {
      "total_refunds": 234.56,
      "refund_rate": 0.8,
      "total_adjustments": 45.67,
      "net_adjustments": -188.89
    }
  }
}
```

### Response - Current Billing Cycle
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "current_cycle": {
      "cycle_id": "cycle_64a1b2c3d4e5f6789012345",
      "period": {
        "start": "2024-07-01T00:00:00Z",
        "end": "2024-07-31T23:59:59Z"
      },
      "status": "active",
      "days_remaining": 16,
      "progress_percentage": 48.4
    },
    "subscription": {
      "subscription_id": "sub_64a1b2c3d4e5f6789012345",
      "plan_name": "Business Plan",
      "plan_amount": 199.99,
      "billing_cycle": "monthly",
      "next_billing_date": "2024-08-01T00:00:00Z"
    },
    "current_usage": {
      "sms": {
        "used": 7500,
        "included": 10000,
        "remaining": 2500,
        "overage": 0,
        "overage_rate": 0.025
      },
      "email": {
        "used": 45000,
        "included": 50000,
        "remaining": 5000,
        "overage": 0,
        "overage_rate": 0.003
      },
      "push": {
        "used": 2500,
        "included": 5000,
        "remaining": 2500,
        "overage": 0,
        "overage_rate": 0.04
      }
    },
    "estimated_bill": {
      "base_amount": 199.99,
      "usage_charges": 0.00,
      "overage_charges": 0.00,
      "subtotal": 199.99,
      "tax_amount": 17.00,
      "total_estimate": 216.99,
      "confidence": "high"
    },
    "payment_method": {
      "type": "card",
      "last4": "4242",
      "brand": "visa",
      "expires": "12/2026",
      "status": "active"
    },
    "billing_alerts": [
      {
        "type": "info",
        "message": "You're on track for a regular billing cycle",
        "details": "No overages expected based on current usage"
      }
    ]
  }
}
```

## Request/Response Examples

### Example 1: Get Detailed Billing History
**Request:**
```http
GET /api/payment/v1/billing/history?period=last_3_months&include_details=true&status=paid&limit=10
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
X-Organization-ID: org_64a1b2c3d4e5f6789012345
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": {
      "start": "2024-04-01T00:00:00Z",
      "end": "2024-07-31T23:59:59Z",
      "description": "Last 3 months"
    },
    "summary": {
      "total_amount": 8997.65,
      "total_invoices": 21,
      "average_monthly": 2999.22
    },
    "billing_records": [
      {
        "billing_id": "bill_64a1b2c3d4e5f6789012345",
        "invoice": {
          "invoice_id": "inv_64a1b2c3d4e5f6789012345",
          "invoice_number": "INV-2024-001234",
          "amount": 299.99,
          "status": "paid",
          "paid_date": "2024-07-15T14:30:00Z"
        },
        "line_items": [
          {
            "description": "Business Plan - Monthly",
            "amount": 199.99
          },
          {
            "description": "SMS Overage (1,000 messages)",
            "amount": 25.00
          },
          {
            "description": "Email Usage (30,000 emails)",
            "amount": 75.00
          }
        ]
      }
    ]
  }
}
```

### Example 2: Export Billing Report as PDF
**Request:**
```http
GET /api/payment/v1/billing/reports?report_type=yearly&format=pdf&year=2024
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
X-Organization-ID: org_64a1b2c3d4e5f6789012345
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/pdf
Content-Disposition: attachment; filename="billing-report-2024.pdf"

[Binary PDF content]
```

### Example 3: Get CSV Export
**Request:**
```http
GET /api/payment/v1/billing/history?period=current_year&format=csv
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
X-Organization-ID: org_64a1b2c3d4e5f6789012345
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/csv
Content-Disposition: attachment; filename="billing-history-2024.csv"

Date,Invoice Number,Amount,Status,Payment Method,Plan
2024-07-15,INV-2024-001234,299.99,paid,card,Business Plan
2024-06-15,INV-2024-001233,299.99,paid,card,Business Plan
```

## Error Responses

### 400 Bad Request - Invalid Parameters
```json
{
  "success": false,
  "error": {
    "code": "BILLING_001",
    "message": "Invalid billing query parameters",
    "details": [
      {
        "field": "period",
        "message": "Period must be one of: current_month, last_month, last_3_months, last_6_months, current_year, custom"
      },
      {
        "field": "format",
        "message": "Format must be one of: json, pdf, csv"
      }
    ]
  }
}
```

### 401 Unauthorized - Invalid Token
```json
{
  "success": false,
  "error": {
    "code": "BILLING_002",
    "message": "Invalid or expired authentication token",
    "details": "Please provide a valid JWT token in the Authorization header"
  }
}
```

### 403 Forbidden - Insufficient Permissions
```json
{
  "success": false,
  "error": {
    "code": "BILLING_003",
    "message": "Insufficient permissions to access billing data",
    "details": "User does not have permission to view billing information for this organization"
  }
}
```

### 404 Not Found - No Billing Data
```json
{
  "success": false,
  "error": {
    "code": "BILLING_004",
    "message": "No billing data found for the specified period",
    "details": "No billing records exist for the requested time period"
  }
}
```

### 422 Unprocessable Entity - Invalid Date Range
```json
{
  "success": false,
  "error": {
    "code": "BILLING_005",
    "message": "Invalid date range specified",
    "details": [
      {
        "field": "start_date",
        "message": "Start date cannot be in the future"
      },
      {
        "field": "end_date",
        "message": "End date must be after start date and cannot be more than 2 years ago"
      }
    ]
  }
}
```

### 500 Internal Server Error - System Error
```json
{
  "success": false,
  "error": {
    "code": "BILLING_006",
    "message": "Failed to retrieve billing data",
    "details": "An internal error occurred while processing your request. Please try again later."
  }
}
```

## Technical Implementation

### Data Sources
- **Invoice Service**: Invoice generation and management
- **Payment Service**: Payment processing and transactions
- **Subscription Service**: Plan and subscription data
- **Usage Service**: Usage tracking and metering
- **Tax Service**: Tax calculations and compliance

### Caching Strategy
- Cache billing summaries for 1 hour
- Cache historical data for 6 hours
- Real-time data for current billing cycle
- PDF reports cached for 24 hours after generation

### Performance Considerations
- Use database indexes on date ranges and organization_id
- Implement pagination for large billing histories
- Background jobs for report generation
- Materialized views for common aggregations

### Security Requirements
- JWT token validation
- Organization-level data isolation
- Role-based access control (billing admin, viewer)
- Audit logging for billing data access
- Rate limiting: 50 requests per minute per user

### Database Schema References
- **BillingRecords**: Complete billing transaction history
- **Invoices**: Invoice details and status
- **Payments**: Payment transaction records
- **Subscriptions**: Subscription and plan information
- **UsageRecords**: Usage-based billing data

### Queue Integration
- **billing.generate_report**: Background report generation
- **billing.calculate_cycle**: End-of-cycle calculations
- **billing.send_notifications**: Billing alert notifications

## Testing Requirements

### Unit Tests
- Billing data aggregation logic
- Date range validation and filtering
- Report generation functionality
- Permission and access control checks

### Integration Tests
- End-to-end billing history retrieval
- Cross-service data consistency validation
- Export functionality (PDF, CSV)
- Real-time current cycle calculations

### Test Data
- Mock billing data across multiple periods
- Various subscription plans and usage patterns
- Different payment methods and statuses
- Edge cases for date ranges and filtering

## Dependencies

### Internal Services
- **Payment Service**: Transaction and payment data
- **Invoice Service**: Invoice generation and management
- **Subscription Service**: Plan and billing cycle data
- **Usage Service**: Usage tracking and metering
- **Core Service**: Organization and user management

### External Services
- **PDF Generator**: Report generation service
- **Tax Calculator**: Tax calculation service
- **Analytics Database**: Aggregated billing data
- **File Storage**: Report storage and delivery

### Configuration
- Report generation settings
- Cache expiration times
- Export file size limits
- Date range restrictions

## Monitoring and Logging

### Key Metrics
- Billing query response times
- Report generation success rates
- Data export completion times
- Cache hit rates for billing data

### Alerts
- Billing data calculation failures
- High response times (>3 seconds)
- Report generation failures
- Data inconsistencies between services

### Logging
- All billing data access with user details
- Report generation requests and completion
- Export operations and file deliveries
- Performance metrics and optimization opportunities

## Business Intelligence Integration

### Analytics Support
- Revenue trend analysis
- Customer lifetime value calculations
- Plan performance comparisons
- Payment method efficiency metrics

### Reporting Capabilities
- Automated monthly/yearly reports
- Custom date range reports
- Plan-specific revenue analysis
- Tax and compliance reporting

### Data Export Features
- CSV exports for accounting systems
- PDF reports for stakeholders
- API integration for BI tools
- Real-time dashboard data feeds