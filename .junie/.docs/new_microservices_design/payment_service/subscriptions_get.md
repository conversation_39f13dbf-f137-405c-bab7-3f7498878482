# Payment Service - GET /subscriptions (List Subscriptions)

## Task Overview
**Endpoint**: `GET /api/payment/v1/subscriptions`  
**Purpose**: Retrieve a paginated list of subscriptions for an organization with filtering and sorting options  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Subscription creation, authentication middleware, database models  

## API Specification

### Request
```http
GET /api/payment/v1/subscriptions?page=1&page_size=20&status=active&sort_by=created_at&sort_order=desc
Authorization: Bearer <jwt_token>
```

### Query Parameters
- `page` (optional, default: 1): Page number for pagination
- `page_size` (optional, default: 20, max: 100): Number of items per page
- `status` (optional): Filter by subscription status (active, trialing, past_due, canceled, unpaid, incomplete)
- `price_id` (optional): Filter by specific price/plan
- `sort_by` (optional, default: created_at): Sort field (created_at, updated_at, current_period_end, status)
- `sort_order` (optional, default: desc): Sort order (asc, desc)
- `include_canceled` (optional, default: false): Include canceled subscriptions
- `trial_ending_soon` (optional): Filter subscriptions with trial ending in specified days

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "subscription_id": "sub_1234567890abcdef",
        "customer_id": "cus_1234567890abcdef",
        "status": "active",
        "current_period_start": "2024-08-11T15:30:00Z",
        "current_period_end": "2024-09-11T15:30:00Z",
        "trial_start": null,
        "trial_end": null,
        "items": [
          {
            "id": "si_1234567890abcdef",
            "price": {
              "id": "price_1234567890abcdef",
              "unit_amount": 2900,
              "currency": "usd",
              "recurring": {
                "interval": "month",
                "interval_count": 1
              },
              "product": {
                "id": "prod_1234567890abcdef",
                "name": "Pro Plan",
                "description": "Professional features with unlimited campaigns"
              }
            },
            "quantity": 1
          }
        ],
        "discount": null,
        "latest_invoice": {
          "id": "in_1234567890abcdef",
          "amount_paid": 2900,
          "amount_due": 0,
          "status": "paid"
        },
        "next_invoice_date": "2024-09-11T15:30:00Z",
        "cancel_at_period_end": false,
        "days_until_due": null,
        "created_at": "2024-08-11T15:30:00Z",
        "updated_at": "2024-08-11T15:30:00Z"
      },
      {
        "subscription_id": "sub_0987654321fedcba",
        "customer_id": "cus_1234567890abcdef",
        "status": "trialing",
        "current_period_start": "2024-08-01T10:00:00Z",
        "current_period_end": "2024-09-01T10:00:00Z",
        "trial_start": "2024-08-01T10:00:00Z",
        "trial_end": "2024-08-15T10:00:00Z",
        "items": [
          {
            "id": "si_0987654321fedcba",
            "price": {
              "id": "price_0987654321fedcba",
              "unit_amount": 9900,
              "currency": "usd",
              "recurring": {
                "interval": "month",
                "interval_count": 1
              },
              "product": {
                "id": "prod_0987654321fedcba",
                "name": "Enterprise Plan",
                "description": "Enterprise features with advanced analytics"
              }
            },
            "quantity": 1
          }
        ],
        "discount": {
          "coupon": {
            "id": "TRIAL20",
            "percent_off": 20,
            "duration": "once"
          }
        },
        "latest_invoice": null,
        "next_invoice_date": "2024-08-15T10:00:00Z",
        "cancel_at_period_end": false,
        "days_until_due": 3,
        "created_at": "2024-08-01T10:00:00Z",
        "updated_at": "2024-08-01T10:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 20,
      "total_items": 2,
      "total_pages": 1,
      "has_next": false,
      "has_previous": false
    },
    "summary": {
      "total_subscriptions": 2,
      "by_status": {
        "active": 1,
        "trialing": 1,
        "past_due": 0,
        "canceled": 0
      },
      "total_mrr": {
        "amount": 12800,
        "currency": "usd"
      },
      "trial_ending_soon": 1,
      "overdue_count": 0
    }
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (50 minutes)
```typescript
// src/services/subscription.service.ts (addition)
export class SubscriptionService {
  static async getSubscriptions(
    orgId: string,
    filters: SubscriptionListFilters,
    pagination: PaginationOptions
  ): Promise<SubscriptionListResponse> {
    try {
      // Build MongoDB query
      const query = this.buildSubscriptionQuery(orgId, filters);
      
      // Calculate pagination
      const skip = (pagination.page - 1) * pagination.page_size;
      
      // Build sort options
      const sortOptions = this.buildSortOptions(filters.sort_by, filters.sort_order);
      
      // Execute queries in parallel
      const [subscriptions, totalCount, summary] = await Promise.all([
        SubscriptionModel.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(pagination.page_size)
          .lean(),
        SubscriptionModel.countDocuments(query),
        this.getSubscriptionSummary(orgId, filters)
      ]);
      
      // Enrich subscriptions with Stripe data
      const enrichedSubscriptions = await this.enrichSubscriptionsWithStripeData(subscriptions);
      
      // Format subscriptions
      const formattedSubscriptions = enrichedSubscriptions.map(this.formatSubscriptionSummary);
      
      // Build pagination metadata
      const paginationMeta = this.buildPaginationMeta(pagination, totalCount);
      
      return {
        subscriptions: formattedSubscriptions,
        pagination: paginationMeta,
        summary
      };
    } catch (error) {
      throw new InternalError(`Failed to retrieve subscriptions: ${error.message}`);
    }
  }

  private static buildSubscriptionQuery(orgId: string, filters: SubscriptionListFilters): any {
    const query: any = { org_id: orgId };
    
    // Status filter
    if (filters.status) {
      query.status = filters.status;
    } else if (!filters.include_canceled) {
      // Exclude canceled subscriptions by default
      query.status = { $ne: 'canceled' };
    }
    
    // Price ID filter
    if (filters.price_id) {
      query.stripe_price_id = filters.price_id;
    }
    
    // Trial ending soon filter
    if (filters.trial_ending_soon) {
      const daysFromNow = new Date();
      daysFromNow.setDate(daysFromNow.getDate() + filters.trial_ending_soon);
      
      query.status = 'trialing';
      query.trial_end = {
        $gte: new Date(),
        $lte: daysFromNow
      };
    }
    
    return query;
  }

  private static buildSortOptions(sortBy: string = 'created_at', sortOrder: string = 'desc'): any {
    const order = sortOrder === 'asc' ? 1 : -1;
    return { [sortBy]: order };
  }

  private static async enrichSubscriptionsWithStripeData(subscriptions: any[]): Promise<any[]> {
    const enriched = [];
    
    for (const subscription of subscriptions) {
      try {
        // Get latest data from Stripe
        const stripeSubscription = await StripeService.retrieveSubscription(
          subscription.stripe_subscription_id
        );
        
        // Merge local and Stripe data
        enriched.push({
          ...subscription,
          stripe_data: stripeSubscription
        });
      } catch (error) {
        // If Stripe data unavailable, use local data
        console.warn(`Failed to fetch Stripe data for subscription ${subscription.stripe_subscription_id}:`, error);
        enriched.push(subscription);
      }
    }
    
    return enriched;
  }

  private static formatSubscriptionSummary(subscription: any): SubscriptionSummary {
    const stripeData = subscription.stripe_data;
    
    return {
      subscription_id: subscription.stripe_subscription_id,
      customer_id: subscription.stripe_customer_id,
      status: stripeData?.status || subscription.status,
      current_period_start: stripeData ? 
        new Date(stripeData.current_period_start * 1000) : subscription.current_period_start,
      current_period_end: stripeData ? 
        new Date(stripeData.current_period_end * 1000) : subscription.current_period_end,
      trial_start: stripeData?.trial_start ? 
        new Date(stripeData.trial_start * 1000) : subscription.trial_start,
      trial_end: stripeData?.trial_end ? 
        new Date(stripeData.trial_end * 1000) : subscription.trial_end,
      items: stripeData ? stripeData.items.data.map(item => ({
        id: item.id,
        price: {
          id: item.price.id,
          unit_amount: item.price.unit_amount,
          currency: item.price.currency,
          recurring: item.price.recurring,
          product: item.price.product
        },
        quantity: item.quantity || 1
      })) : subscription.items.map(item => ({
        id: item.stripe_item_id,
        price: {
          id: item.stripe_price_id,
          unit_amount: item.unit_amount,
          currency: item.currency,
          recurring: null,
          product: null
        },
        quantity: item.quantity
      })),
      discount: stripeData?.discount ? {
        coupon: {
          id: stripeData.discount.coupon.id,
          percent_off: stripeData.discount.coupon.percent_off,
          amount_off: stripeData.discount.coupon.amount_off,
          duration: stripeData.discount.coupon.duration
        }
      } : subscription.discount,
      latest_invoice: stripeData?.latest_invoice ? {
        id: typeof stripeData.latest_invoice === 'string' ? 
          stripeData.latest_invoice : stripeData.latest_invoice.id,
        amount_paid: typeof stripeData.latest_invoice === 'object' ? 
          stripeData.latest_invoice.amount_paid : undefined,
        amount_due: typeof stripeData.latest_invoice === 'object' ? 
          stripeData.latest_invoice.amount_due : undefined,
        status: typeof stripeData.latest_invoice === 'object' ? 
          stripeData.latest_invoice.status : undefined
      } : null,
      next_invoice_date: stripeData ? 
        new Date(stripeData.current_period_end * 1000) : subscription.current_period_end,
      cancel_at_period_end: stripeData?.cancel_at_period_end || subscription.cancel_at_period_end,
      days_until_due: subscription.status === 'trialing' && subscription.trial_end ? 
        Math.ceil((new Date(subscription.trial_end).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : null,
      created_at: stripeData ? 
        new Date(stripeData.created * 1000) : subscription.created_at,
      updated_at: subscription.updated_at
    };
  }

  private static async getSubscriptionSummary(
    orgId: string,
    filters: SubscriptionListFilters
  ): Promise<SubscriptionSummaryStats> {
    const baseQuery = { org_id: orgId };
    
    // Apply status filter to summary if specified
    if (filters.status) {
      baseQuery.status = filters.status;
    } else if (!filters.include_canceled) {
      baseQuery.status = { $ne: 'canceled' };
    }

    const [statusStats, mrrData, trialEndingSoon, overdueCount] = await Promise.all([
      SubscriptionModel.aggregate([
        { $match: baseQuery },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      this.calculateMRR(orgId),
      SubscriptionModel.countDocuments({
        org_id: orgId,
        status: 'trialing',
        trial_end: {
          $gte: new Date(),
          $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
        }
      }),
      SubscriptionModel.countDocuments({
        org_id: orgId,
        status: 'past_due'
      })
    ]);

    return {
      total_subscriptions: statusStats.reduce((sum, stat) => sum + stat.count, 0),
      by_status: statusStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
      total_mrr: mrrData,
      trial_ending_soon: trialEndingSoon,
      overdue_count: overdueCount
    };
  }

  private static async calculateMRR(orgId: string): Promise<{ amount: number; currency: string }> {
    const activeSubscriptions = await SubscriptionModel.find({
      org_id: orgId,
      status: { $in: ['active', 'trialing'] }
    }).select('items stripe_subscription_id');

    let totalMRR = 0;
    const currency = 'usd'; // Default currency

    for (const subscription of activeSubscriptions) {
      for (const item of subscription.items) {
        // Convert to monthly recurring revenue
        let monthlyAmount = item.unit_amount * item.quantity;
        
        // Note: In a real implementation, you'd need to fetch the price from Stripe
        // to get the exact recurring interval and adjust accordingly
        totalMRR += monthlyAmount;
      }
    }

    return {
      amount: totalMRR,
      currency
    };
  }

  private static buildPaginationMeta(pagination: PaginationOptions, totalCount: number): PaginationMeta {
    const totalPages = Math.ceil(totalCount / pagination.page_size);
    
    return {
      current_page: pagination.page,
      page_size: pagination.page_size,
      total_items: totalCount,
      total_pages: totalPages,
      has_next: pagination.page < totalPages,
      has_previous: pagination.page > 1
    };
  }

  static async getSubscriptionById(
    orgId: string,
    subscriptionId: string
  ): Promise<SubscriptionResponse> {
    try {
      // Find local subscription record
      const localSubscription = await SubscriptionModel.findOne({
        org_id: orgId,
        stripe_subscription_id: subscriptionId
      });

      if (!localSubscription) {
        throw new NotFoundError('Subscription not found');
      }

      // Get latest data from Stripe
      const stripeSubscription = await StripeService.retrieveSubscription(subscriptionId);

      // Update local record if needed
      await this.syncSubscriptionData(localSubscription, stripeSubscription);

      return this.formatSubscriptionResponse(stripeSubscription, localSubscription);
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to retrieve subscription: ${error.message}`);
    }
  }
}
```

### 2. Controller Implementation (30 minutes)
```typescript
// src/controllers/subscription.controller.ts (addition)
export class SubscriptionController {
  static async getSubscriptions(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Extract and validate query parameters
      const filters = SubscriptionController.extractListFilters(req.query);
      const pagination = SubscriptionController.extractPaginationOptions(req.query);
      
      // Get subscriptions
      const result = await SubscriptionService.getSubscriptions(
        req.user.org_id,
        filters,
        pagination
      );

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async getSubscriptionById(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Subscription ID is required');
      }

      const subscription = await SubscriptionService.getSubscriptionById(
        req.user.org_id,
        id
      );

      res.json({
        success: true,
        data: subscription,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  private static extractListFilters(query: any): SubscriptionListFilters {
    return {
      status: query.status,
      price_id: query.price_id,
      sort_by: query.sort_by || 'created_at',
      sort_order: query.sort_order || 'desc',
      include_canceled: query.include_canceled === 'true',
      trial_ending_soon: query.trial_ending_soon ? parseInt(query.trial_ending_soon) : undefined
    };
  }

  private static extractPaginationOptions(query: any): PaginationOptions {
    const page = Math.max(1, parseInt(query.page) || 1);
    const pageSize = Math.min(100, Math.max(1, parseInt(query.page_size) || 20));
    
    return {
      page,
      page_size: pageSize
    };
  }
}
```

### 3. Validation Middleware (20 minutes)
```typescript
// src/middleware/validation.middleware.ts (addition)
export const validateSubscriptionListQuery = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const schema = Joi.object({
    page: Joi.number().integer().min(1).optional(),
    page_size: Joi.number().integer().min(1).max(100).optional(),
    status: Joi.string().valid(
      'incomplete',
      'incomplete_expired',
      'trialing',
      'active',
      'past_due',
      'canceled',
      'unpaid',
      'paused'
    ).optional(),
    price_id: Joi.string().pattern(/^price_[a-zA-Z0-9]+$/).optional(),
    sort_by: Joi.string().valid('created_at', 'updated_at', 'current_period_end', 'status').optional(),
    sort_order: Joi.string().valid('asc', 'desc').optional(),
    include_canceled: Joi.boolean().optional(),
    trial_ending_soon: Joi.number().integer().min(1).max(365).optional()
  });

  const { error } = schema.validate(req.query, { abortEarly: false });
  
  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    throw new BadRequestError('Invalid query parameters provided', 'INVALID_QUERY_PARAMS', details);
  }
  
  next();
};
```

### 4. Route Definitions (10 minutes)
```typescript
// src/routes/subscription.routes.ts (additions)
import { validateSubscriptionListQuery } from '@/middleware/validation.middleware';

router.get(
  '/',
  authenticateRequest,
  validateSubscriptionListQuery,
  rateLimitMiddleware,
  SubscriptionController.getSubscriptions
);

router.get(
  '/:id',
  authenticateRequest,
  rateLimitMiddleware,
  SubscriptionController.getSubscriptionById
);
```

### 5. Type Definitions (15 minutes)
```typescript
// src/types/subscription.types.ts (additions)
export interface SubscriptionListFilters {
  status?: string;
  price_id?: string;
  sort_by?: 'created_at' | 'updated_at' | 'current_period_end' | 'status';
  sort_order?: 'asc' | 'desc';
  include_canceled?: boolean;
  trial_ending_soon?: number;
}

export interface SubscriptionSummary {
  subscription_id: string;
  customer_id: string;
  status: string;
  current_period_start: Date;
  current_period_end: Date;
  trial_start?: Date;
  trial_end?: Date;
  items: Array<{
    id: string;
    price: {
      id: string;
      unit_amount: number | null;
      currency: string;
      recurring: any;
      product: any;
    };
    quantity: number;
  }>;
  discount?: {
    coupon: {
      id: string;
      percent_off: number | null;
      amount_off: number | null;
      duration: string;
    };
  };
  latest_invoice?: {
    id: string;
    amount_paid?: number;
    amount_due?: number;
    status?: string;
  } | null;
  next_invoice_date: Date;
  cancel_at_period_end: boolean;
  days_until_due?: number | null;
  created_at: Date;
  updated_at: Date;
}

export interface SubscriptionListResponse {
  subscriptions: SubscriptionSummary[];
  pagination: PaginationMeta;
  summary: SubscriptionSummaryStats;
}

export interface SubscriptionSummaryStats {
  total_subscriptions: number;
  by_status: Record<string, number>;
  total_mrr: {
    amount: number;
    currency: string;
  };
  trial_ending_soon: number;
  overdue_count: number;
}

export interface PaginationMeta {
  current_page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface PaginationOptions {
  page: number;
  page_size: number;
}
```

### 6. Database Optimization (15 minutes)
```typescript
// src/lib/db/models/subscription.model.ts (index additions)
// Add compound indexes for efficient filtering and sorting
subscriptionSchema.index({ org_id: 1, status: 1, created_at: -1 });
subscriptionSchema.index({ org_id: 1, current_period_end: 1 });
subscriptionSchema.index({ org_id: 1, stripe_price_id: 1 });
subscriptionSchema.index({ org_id: 1, trial_end: 1 });
subscriptionSchema.index({ org_id: 1, status: 1, trial_end: 1 });
```

### 7. Integration Tests (35 minutes)
```typescript
// tests/integration/subscription.list.api.test.ts
describe('GET /api/payment/v1/subscriptions', () => {
  let authToken: string;
  let orgId: string;
  let subscriptions: any[];

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
    
    // Create test subscriptions
    subscriptions = await Promise.all([
      SubscriptionModel.create({
        org_id: orgId,
        stripe_subscription_id: 'sub_active_test',
        stripe_customer_id: 'cus_test123',
        stripe_price_id: 'price_pro',
        status: 'active',
        current_period_start: new Date('2024-08-01T10:00:00Z'),
        current_period_end: new Date('2024-09-01T10:00:00Z'),
        items: [{
          stripe_item_id: 'si_active',
          stripe_price_id: 'price_pro',
          quantity: 1,
          unit_amount: 2900,
          currency: 'usd'
        }],
        created_at: new Date('2024-08-01T10:00:00Z')
      }),
      SubscriptionModel.create({
        org_id: orgId,
        stripe_subscription_id: 'sub_trial_test',
        stripe_customer_id: 'cus_test123',
        stripe_price_id: 'price_enterprise',
        status: 'trialing',
        current_period_start: new Date('2024-08-05T10:00:00Z'),
        current_period_end: new Date('2024-09-05T10:00:00Z'),
        trial_start: new Date('2024-08-05T10:00:00Z'),
        trial_end: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        items: [{
          stripe_item_id: 'si_trial',
          stripe_price_id: 'price_enterprise',
          quantity: 1,
          unit_amount: 9900,
          currency: 'usd'
        }],
        created_at: new Date('2024-08-05T10:00:00Z')
      })
    ]);
  });

  afterAll(async () => {
    await SubscriptionModel.deleteMany({ org_id: orgId });
  });

  it('should return paginated subscriptions', async () => {
    // Mock Stripe service calls
    jest.spyOn(StripeService, 'retrieveSubscription').mockImplementation((subId) => {
      if (subId === 'sub_active_test') {
        return Promise.resolve({
          id: 'sub_active_test',
          status: 'active',
          current_period_start: 1722509200,
          current_period_end: 1725187600,
          items: {
            data: [{
              id: 'si_active',
              price: {
                id: 'price_pro',
                unit_amount: 2900,
                currency: 'usd',
                recurring: { interval: 'month' },
                product: { id: 'prod_pro', name: 'Pro Plan' }
              },
              quantity: 1
            }]
          },
          created: 1722509200,
          cancel_at_period_end: false
        } as any);
      }
      return Promise.resolve({} as any);
    });

    const response = await request(app)
      .get('/api/payment/v1/subscriptions?page=1&page_size=10')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.subscriptions).toHaveLength(2);
    expect(response.body.data.pagination).toMatchObject({
      current_page: 1,
      page_size: 10,
      total_items: 2,
      total_pages: 1,
      has_next: false,
      has_previous: false
    });
    expect(response.body.data.summary).toBeDefined();
    expect(response.body.data.summary.total_subscriptions).toBe(2);
  });

  it('should filter subscriptions by status', async () => {
    const response = await request(app)
      .get('/api/payment/v1/subscriptions?status=active')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.subscriptions).toHaveLength(1);
    expect(response.body.data.subscriptions[0].status).toBe('active');
  });

  it('should filter subscriptions by price_id', async () => {
    const response = await request(app)
      .get('/api/payment/v1/subscriptions?price_id=price_pro')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.subscriptions).toHaveLength(1);
    expect(response.body.data.subscriptions[0].subscription_id).toBe('sub_active_test');
  });

  it('should filter trial ending soon', async () => {
    const response = await request(app)
      .get('/api/payment/v1/subscriptions?trial_ending_soon=7')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.subscriptions).toHaveLength(1);
    expect(response.body.data.subscriptions[0].status).toBe('trialing');
    expect(response.body.data.subscriptions[0].days_until_due).toBeLessThanOrEqual(7);
  });

  it('should validate query parameters', async () => {
    const response = await request(app)
      .get('/api/payment/v1/subscriptions?page_size=200&status=invalid')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_QUERY_PARAMS');
  });

  it('should return 401 without authentication', async () => {
    await request(app)
      .get('/api/payment/v1/subscriptions')
      .expect(401);
  });
});

describe('GET /api/payment/v1/subscriptions/:id', () => {
  it('should return subscription details', async () => {
    // Mock Stripe service
    jest.spyOn(StripeService, 'retrieveSubscription').mockResolvedValue({
      id: 'sub_active_test',
      status: 'active',
      customer: 'cus_test123',
      current_period_start: 1722509200,
      current_period_end: 1725187600,
      items: {
        data: [{
          id: 'si_active',
          price: {
            id: 'price_pro',
            unit_amount: 2900,
            currency: 'usd'
          }
        }]
      },
      created: 1722509200,
      metadata: {}
    } as any);

    const response = await request(app)
      .get(`/api/payment/v1/subscriptions/${subscriptions[0].stripe_subscription_id}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.subscription_id).toBe('sub_active_test');
    expect(response.body.data.status).toBe('active');
  });

  it('should return 404 for non-existent subscription', async () => {
    const response = await request(app)
      .get('/api/payment/v1/subscriptions/sub_nonexistent')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(404);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('NOT_FOUND');
  });
});
```

## Testing Checklist
- [ ] Unit tests for controller with various query parameters
- [ ] Unit tests for service layer with filtering and pagination
- [ ] Integration tests for GET endpoints with different filters
- [ ] Performance tests with large datasets
- [ ] Stripe integration tests
- [ ] Validation tests for query parameters
- [ ] Authentication and authorization tests
- [ ] Error handling tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Query parameter documentation
- [ ] Pagination documentation
- [ ] MRR calculation documentation
- [ ] Subscription status lifecycle documentation

This task creates a comprehensive subscription listing endpoint with advanced filtering, pagination, sorting, MRR calculation, and detailed summary statistics with proper Stripe integration.