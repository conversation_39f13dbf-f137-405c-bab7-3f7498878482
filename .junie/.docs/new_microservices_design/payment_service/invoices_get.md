# Payment Service - GET /invoices (Invoice Listing and Management)

## Task Overview
**Endpoint**: `GET /api/payment/v1/invoices`  
**Purpose**: Retrieve and manage invoices with filtering, search, and detailed invoice information  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Payment processing, Stripe integration, billing system  

## API Specification

### List Invoices Request
```http
GET /api/payment/v1/invoices?status=paid&period=current_month&page=1&limit=20
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Individual Invoice Request
```http
GET /api/payment/v1/invoices/inv_64a1b2c3d4e5f6789012345
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Invoice PDF Download Request
```http
GET /api/payment/v1/invoices/inv_64a1b2c3d4e5f6789012345/download
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Query Parameters
- `status` (string): Filter by status (draft, open, paid, void, uncollectible)
- `period` (string): Filter by period (current_month, last_month, current_year, custom)
- `date_range` (object): Custom date range (start and end dates)
- `customer_id` (string): Filter by customer ID
- `subscription_id` (string): Filter by subscription ID
- `amount_range` (object): Filter by amount range (min and max)
- `search` (string): Search in invoice number, customer name, or description
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 50)
- `sort_by` (string): Sort field (created, due_date, amount, status)
- `sort_order` (string): Sort order (asc, desc)

### Response - List Invoices
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "invoices": [
      {
        "invoice_id": "inv_64a1b2c3d4e5f6789012345",
        "invoice_number": "INV-2024-001234",
        "status": "paid",
        "amount_due": 0,
        "amount_paid": 299.99,
        "amount_total": 299.99,
        "currency": "USD",
        "customer": {
          "customer_id": "cus_64a1b2c3d4e5f6789012345",
          "name": "Acme Corporation",
          "email": "<EMAIL>"
        },
        "subscription": {
          "subscription_id": "sub_64a1b2c3d4e5f6789012345",
          "plan_name": "Business Plan",
          "billing_cycle": "monthly"
        },
        "billing_period": {
          "start": "2024-07-01T00:00:00Z",
          "end": "2024-07-31T23:59:59Z"
        },
        "line_items": [
          {
            "description": "Business Plan - Monthly Subscription",
            "quantity": 1,
            "unit_amount": 199.99,
            "amount": 199.99
          },
          {
            "description": "SMS Messages (10,000 messages)",
            "quantity": 1,
            "unit_amount": 50.00,
            "amount": 50.00
          },
          {
            "description": "Email Messages (50,000 emails)", 
            "quantity": 1,
            "unit_amount": 50.00,
            "amount": 50.00
          }
        ],
        "tax": {
          "tax_amount": 24.00,
          "tax_rate": 8.5,
          "tax_description": "Sales Tax"
        },
        "dates": {
          "created": "2024-07-01T00:00:00Z",
          "due_date": "2024-07-31T23:59:59Z",
          "paid_at": "2024-07-15T14:30:00Z",
          "finalized_at": "2024-07-01T00:00:00Z"
        },
        "payment_method": {
          "type": "card",
          "last4": "4242",
          "brand": "visa",
          "exp_month": 12,
          "exp_year": 2025
        },
        "pdf_url": "https://api.example.com/invoices/inv_64a1b2c3d4e5f6789012345/pdf",
        "hosted_url": "https://invoice.stripe.com/i/acct_xxx/test_xxx"
      },
      {
        "invoice_id": "inv_64a1b2c3d4e5f6789012346",
        "invoice_number": "INV-2024-001235",
        "status": "open",
        "amount_due": 349.99,
        "amount_paid": 0,
        "amount_total": 349.99,
        "currency": "USD",
        "customer": {
          "customer_id": "cus_64a1b2c3d4e5f6789012346",
          "name": "Beta Industries",
          "email": "<EMAIL>"
        },
        "subscription": {
          "subscription_id": "sub_64a1b2c3d4e5f6789012346",
          "plan_name": "Enterprise Plan",
          "billing_cycle": "monthly"
        },
        "billing_period": {
          "start": "2024-08-01T00:00:00Z",
          "end": "2024-08-31T23:59:59Z"
        },
        "line_items": [
          {
            "description": "Enterprise Plan - Monthly Subscription",
            "quantity": 1,
            "unit_amount": 299.99,
            "amount": 299.99
          },
          {
            "description": "Additional SMS Messages",
            "quantity": 1,
            "unit_amount": 50.00,
            "amount": 50.00
          }
        ],
        "tax": {
          "tax_amount": 29.75,
          "tax_rate": 8.5,
          "tax_description": "Sales Tax"
        },
        "dates": {
          "created": "2024-08-01T00:00:00Z",
          "due_date": "2024-08-31T23:59:59Z",
          "paid_at": null,
          "finalized_at": "2024-08-01T00:00:00Z"
        },
        "payment_method": {
          "type": "card",
          "last4": "1234",
          "brand": "mastercard",
          "exp_month": 6,
          "exp_year": 2026
        },
        "pdf_url": "https://api.example.com/invoices/inv_64a1b2c3d4e5f6789012346/pdf",
        "hosted_url": "https://invoice.stripe.com/i/acct_xxx/test_yyy"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_pages": 3,
      "total_count": 47
    },
    "summary": {
      "total_amount": 12450.50,
      "paid_amount": 8300.25,
      "outstanding_amount": 4150.25,
      "count_by_status": {
        "draft": 2,
        "open": 8,
        "paid": 35,
        "void": 1,
        "uncollectible": 1
      }
    },
    "filters_applied": {
      "status": "paid",
      "period": "current_month"
    }
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

### Response - Individual Invoice
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "invoice_id": "inv_64a1b2c3d4e5f6789012345",
    "invoice_number": "INV-2024-001234",
    "status": "paid",
    "amount_due": 0,
    "amount_paid": 299.99,
    "amount_total": 299.99,
    "amount_subtotal": 299.99,
    "amount_tax": 24.00,
    "currency": "USD",
    "customer": {
      "customer_id": "cus_64a1b2c3d4e5f6789012345",
      "name": "Acme Corporation",
      "email": "<EMAIL>",
      "address": {
        "line1": "123 Business St",
        "city": "San Francisco",
        "state": "CA",
        "postal_code": "94105",
        "country": "US"
      },
      "tax_id": "12-3456789"
    },
    "subscription": {
      "subscription_id": "sub_64a1b2c3d4e5f6789012345",
      "plan_name": "Business Plan",
      "billing_cycle": "monthly",
      "current_period_start": "2024-07-01T00:00:00Z",
      "current_period_end": "2024-07-31T23:59:59Z"
    },
    "billing_period": {
      "start": "2024-07-01T00:00:00Z",
      "end": "2024-07-31T23:59:59Z",
      "description": "July 2024"
    },
    "line_items": [
      {
        "id": "li_64a1b2c3d4e5f6789012345",
        "description": "Business Plan - Monthly Subscription",
        "quantity": 1,
        "unit_amount": 199.99,
        "amount": 199.99,
        "currency": "USD",
        "type": "subscription",
        "period": {
          "start": "2024-07-01T00:00:00Z",
          "end": "2024-07-31T23:59:59Z"
        }
      },
      {
        "id": "li_64a1b2c3d4e5f6789012346",
        "description": "SMS Messages",
        "quantity": 10000,
        "unit_amount": 0.005,
        "amount": 50.00,
        "currency": "USD",
        "type": "usage",
        "metadata": {
          "message_type": "sms",
          "usage_period": "2024-07"
        }
      },
      {
        "id": "li_64a1b2c3d4e5f6789012347",
        "description": "Email Messages",
        "quantity": 50000,
        "unit_amount": 0.001,
        "amount": 50.00,
        "currency": "USD",
        "type": "usage",
        "metadata": {
          "message_type": "email",
          "usage_period": "2024-07"
        }
      }
    ],
    "discounts": [
      {
        "coupon": {
          "id": "25OFF",
          "name": "25% Off First Month",
          "percent_off": 25
        },
        "amount": 49.98
      }
    ],
    "tax": {
      "tax_amount": 24.00,
      "tax_rate": 8.5,
      "tax_description": "California Sales Tax",
      "tax_inclusive": false
    },
    "dates": {
      "created": "2024-07-01T00:00:00Z",
      "due_date": "2024-07-31T23:59:59Z",
      "paid_at": "2024-07-15T14:30:00Z",
      "finalized_at": "2024-07-01T00:00:00Z",
      "voided_at": null
    },
    "payment_intent": {
      "payment_intent_id": "pi_64a1b2c3d4e5f6789012345",
      "status": "succeeded",
      "amount": 29999,
      "currency": "USD"
    },
    "payment_method": {
      "payment_method_id": "pm_64a1b2c3d4e5f6789012345",
      "type": "card",
      "card": {
        "last4": "4242",
        "brand": "visa",
        "exp_month": 12,
        "exp_year": 2025,
        "country": "US"
      }
    },
    "payment_history": [
      {
        "payment_id": "py_64a1b2c3d4e5f6789012345",
        "amount": 29999,
        "status": "succeeded",
        "created": "2024-07-15T14:30:00Z",
        "payment_method": "card"
      }
    ],
    "collection_method": "charge_automatically",
    "auto_advance": true,
    "attempt_count": 1,
    "pdf_url": "https://api.example.com/invoices/inv_64a1b2c3d4e5f6789012345/pdf",
    "hosted_invoice_url": "https://invoice.stripe.com/i/acct_xxx/test_xxx",
    "invoice_pdf": "https://pay.stripe.com/invoice/acct_xxx/test_xxx/pdf",
    "metadata": {
      "campaign_id": "campaign_64a1b2c3d4e5f6789012345",
      "billing_source": "subscription"
    },
    "notes": "Thank you for your business!"
  }
}
```

## Implementation Tasks

### 1. Database Model (20 minutes)
```typescript
// src/lib/db/models/invoice.model.ts
import { Schema, model } from 'mongoose';

const invoiceLineItemSchema = new Schema({
  id: String,
  description: String,
  quantity: Number,
  unit_amount: Number,
  amount: Number,
  currency: String,
  type: { type: String, enum: ['subscription', 'usage', 'one_time'] },
  period: {
    start: Date,
    end: Date
  },
  metadata: Schema.Types.Mixed
});

const invoiceSchema = new Schema({
  org_id: { type: String, required: true, index: true },
  invoice_id: { type: String, required: true, unique: true },
  stripe_invoice_id: String,
  invoice_number: String,
  
  // Status and amounts
  status: { 
    type: String, 
    enum: ['draft', 'open', 'paid', 'void', 'uncollectible'],
    required: true 
  },
  amount_due: { type: Number, required: true },
  amount_paid: { type: Number, default: 0 },
  amount_total: { type: Number, required: true },
  amount_subtotal: Number,
  amount_tax: { type: Number, default: 0 },
  currency: { type: String, default: 'USD' },
  
  // Customer information
  customer: {
    customer_id: { type: String, required: true },
    stripe_customer_id: String,
    name: String,
    email: String,
    address: {
      line1: String,
      line2: String,
      city: String,
      state: String,
      postal_code: String,
      country: String
    },
    tax_id: String
  },
  
  // Subscription information
  subscription: {
    subscription_id: String,
    stripe_subscription_id: String,
    plan_name: String,
    billing_cycle: String,
    current_period_start: Date,
    current_period_end: Date
  },
  
  // Billing period
  billing_period: {
    start: Date,
    end: Date,
    description: String
  },
  
  // Line items
  line_items: [invoiceLineItemSchema],
  
  // Discounts
  discounts: [{
    coupon: {
      id: String,
      name: String,
      percent_off: Number,
      amount_off: Number
    },
    amount: Number
  }],
  
  // Tax information
  tax: {
    tax_amount: { type: Number, default: 0 },
    tax_rate: Number,
    tax_description: String,
    tax_inclusive: { type: Boolean, default: false }
  },
  
  // Important dates
  dates: {
    created: Date,
    due_date: Date,
    paid_at: Date,
    finalized_at: Date,
    voided_at: Date
  },
  
  // Payment information
  payment_intent: {
    payment_intent_id: String,
    stripe_payment_intent_id: String,
    status: String,
    amount: Number,
    currency: String
  },
  
  payment_method: {
    payment_method_id: String,
    stripe_payment_method_id: String,
    type: String,
    card: {
      last4: String,
      brand: String,
      exp_month: Number,
      exp_year: Number,
      country: String
    }
  },
  
  // Payment history
  payment_history: [{
    payment_id: String,
    stripe_payment_id: String,
    amount: Number,
    status: String,
    created: Date,
    payment_method: String
  }],
  
  // Collection settings
  collection_method: { 
    type: String, 
    enum: ['charge_automatically', 'send_invoice'],
    default: 'charge_automatically'
  },
  auto_advance: { type: Boolean, default: true },
  attempt_count: { type: Number, default: 0 },
  
  // URLs
  pdf_url: String,
  hosted_invoice_url: String,
  invoice_pdf: String,
  
  // Additional data
  metadata: Schema.Types.Mixed,
  notes: String
}, { 
  timestamps: true,
  collection: 'invoices'
});

// Indexes for efficient querying
invoiceSchema.index({ org_id: 1, status: 1 });
invoiceSchema.index({ org_id: 1, 'customer.customer_id': 1 });
invoiceSchema.index({ org_id: 1, 'subscription.subscription_id': 1 });
invoiceSchema.index({ 'dates.created': -1 });
invoiceSchema.index({ 'dates.due_date': 1 });

export const InvoiceModel = model('Invoice', invoiceSchema);
```

### 2. DAO Implementation (45 minutes)
```typescript
// src/lib/db/dao/invoice.dao.ts
import { InvoiceModel } from '../models/invoice.model';
import { DatabaseError, NotFoundError } from '@/lib/errors/error-types';

export class InvoiceDAO {
  static async getInvoices(orgId: string, filters: any = {}) {
    try {
      const query: any = { org_id: orgId };
      
      // Apply filters
      if (filters.status) query.status = filters.status;
      if (filters.customer_id) query['customer.customer_id'] = filters.customer_id;
      if (filters.subscription_id) query['subscription.subscription_id'] = filters.subscription_id;
      
      // Date range filtering
      if (filters.period || filters.date_range) {
        const dateFilter = this.buildDateFilter(filters.period, filters.date_range);
        if (dateFilter) query['dates.created'] = dateFilter;
      }
      
      // Amount range filtering
      if (filters.amount_range) {
        const amountFilter: any = {};
        if (filters.amount_range.min) amountFilter.$gte = filters.amount_range.min * 100; // Convert to cents
        if (filters.amount_range.max) amountFilter.$lte = filters.amount_range.max * 100;
        if (Object.keys(amountFilter).length > 0) query.amount_total = amountFilter;
      }
      
      // Search functionality
      if (filters.search) {
        query.$or = [
          { invoice_number: { $regex: filters.search, $options: 'i' } },
          { 'customer.name': { $regex: filters.search, $options: 'i' } },
          { 'customer.email': { $regex: filters.search, $options: 'i' } },
          { 'line_items.description': { $regex: filters.search, $options: 'i' } }
        ];
      }

      const sortBy = this.getSortField(filters.sort_by);
      const sortOrder = filters.sort_order === 'asc' ? 1 : -1;
      const page = Math.max(1, parseInt(filters.page) || 1);
      const limit = Math.min(50, Math.max(1, parseInt(filters.limit) || 20));
      const skip = (page - 1) * limit;

      const [invoices, total, summary] = await Promise.all([
        InvoiceModel
          .find(query)
          .sort({ [sortBy]: sortOrder })
          .skip(skip)
          .limit(limit)
          .lean(),
        InvoiceModel.countDocuments(query),
        this.getInvoiceSummary(orgId, filters)
      ]);

      const formattedInvoices = invoices.map(invoice => 
        this.formatInvoiceListResponse(invoice)
      );

      return {
        invoices: formattedInvoices,
        pagination: {
          current_page: page,
          per_page: limit,
          total_pages: Math.ceil(total / limit),
          total_count: total
        },
        summary,
        filters_applied: {
          status: filters.status,
          period: filters.period,
          customer_id: filters.customer_id,
          search: filters.search
        }
      };

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve invoices',
        'DB_INVOICES_GET_FAILED'
      );
    }
  }

  static async getInvoiceById(orgId: string, invoiceId: string) {
    try {
      const invoice = await InvoiceModel.findOne({
        org_id: orgId,
        invoice_id: invoiceId
      }).lean();

      if (!invoice) {
        throw new NotFoundError(
          `Invoice ${invoiceId} not found`,
          'INVOICE_NOT_FOUND'
        );
      }

      return this.formatInvoiceDetailResponse(invoice);

    } catch (error: unknown) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve invoice',
        'DB_INVOICE_GET_FAILED'
      );
    }
  }

  private static buildDateFilter(period: string, dateRange: any) {
    const now = new Date();
    
    if (dateRange && dateRange.start && dateRange.end) {
      return {
        $gte: new Date(dateRange.start),
        $lte: new Date(dateRange.end)
      };
    }
    
    switch (period) {
      case 'current_month':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        return { $gte: startOfMonth, $lte: endOfMonth };
        
      case 'last_month':
        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
        return { $gte: startOfLastMonth, $lte: endOfLastMonth };
        
      case 'current_year':
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        const endOfYear = new Date(now.getFullYear(), 11, 31);
        return { $gte: startOfYear, $lte: endOfYear };
        
      default:
        return null;
    }
  }

  private static getSortField(sortBy: string): string {
    const sortFields: { [key: string]: string } = {
      'created': 'dates.created',
      'due_date': 'dates.due_date',
      'amount': 'amount_total',
      'status': 'status'
    };
    
    return sortFields[sortBy] || 'dates.created';
  }

  private static async getInvoiceSummary(orgId: string, filters: any) {
    const pipeline = [
      { $match: { org_id: orgId } },
      {
        $group: {
          _id: null,
          total_amount: { $sum: '$amount_total' },
          paid_amount: { $sum: { $cond: [{ $eq: ['$status', 'paid'] }, '$amount_paid', 0] } },
          outstanding_amount: { $sum: { $cond: [{ $eq: ['$status', 'open'] }, '$amount_due', 0] } },
          draft_count: { $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] } },
          open_count: { $sum: { $cond: [{ $eq: ['$status', 'open'] }, 1, 0] } },
          paid_count: { $sum: { $cond: [{ $eq: ['$status', 'paid'] }, 1, 0] } },
          void_count: { $sum: { $cond: [{ $eq: ['$status', 'void'] }, 1, 0] } },
          uncollectible_count: { $sum: { $cond: [{ $eq: ['$status', 'uncollectible'] }, 1, 0] } }
        }
      }
    ];

    const result = await InvoiceModel.aggregate(pipeline);
    const summary = result[0] || {};

    return {
      total_amount: (summary.total_amount || 0) / 100, // Convert from cents
      paid_amount: (summary.paid_amount || 0) / 100,
      outstanding_amount: (summary.outstanding_amount || 0) / 100,
      count_by_status: {
        draft: summary.draft_count || 0,
        open: summary.open_count || 0,
        paid: summary.paid_count || 0,
        void: summary.void_count || 0,
        uncollectible: summary.uncollectible_count || 0
      }
    };
  }

  private static formatInvoiceListResponse(invoice: any) {
    return {
      invoice_id: invoice.invoice_id,
      invoice_number: invoice.invoice_number,
      status: invoice.status,
      amount_due: invoice.amount_due / 100,
      amount_paid: invoice.amount_paid / 100,
      amount_total: invoice.amount_total / 100,
      currency: invoice.currency,
      customer: {
        customer_id: invoice.customer.customer_id,
        name: invoice.customer.name,
        email: invoice.customer.email
      },
      subscription: invoice.subscription,
      billing_period: invoice.billing_period,
      line_items: invoice.line_items.map((item: any) => ({
        description: item.description,
        quantity: item.quantity,
        unit_amount: item.unit_amount / 100,
        amount: item.amount / 100
      })),
      tax: {
        tax_amount: invoice.tax.tax_amount / 100,
        tax_rate: invoice.tax.tax_rate,
        tax_description: invoice.tax.tax_description
      },
      dates: invoice.dates,
      payment_method: invoice.payment_method,
      pdf_url: invoice.pdf_url,
      hosted_url: invoice.hosted_invoice_url
    };
  }

  private static formatInvoiceDetailResponse(invoice: any) {
    return {
      ...this.formatInvoiceListResponse(invoice),
      amount_subtotal: invoice.amount_subtotal / 100,
      amount_tax: invoice.amount_tax / 100,
      customer: {
        ...invoice.customer
      },
      discounts: invoice.discounts?.map((discount: any) => ({
        ...discount,
        amount: discount.amount / 100
      })),
      payment_intent: invoice.payment_intent,
      payment_history: invoice.payment_history?.map((payment: any) => ({
        ...payment,
        amount: payment.amount / 100
      })),
      collection_method: invoice.collection_method,
      auto_advance: invoice.auto_advance,
      attempt_count: invoice.attempt_count,
      invoice_pdf: invoice.invoice_pdf,
      metadata: invoice.metadata,
      notes: invoice.notes
    };
  }
}
```

### 3. Handler Implementation (30 minutes)
```typescript
// src/handlers/invoice.handler.ts
import { Request, Response } from 'express';
import { InvoiceDAO } from '@/lib/db/dao/invoice.dao';
import { handleError } from '@/lib/errors/error-handler';
import { OrganizationError } from '@/lib/errors/error-types';

export class InvoiceHandler {
  static async getInvoices(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const filters = {
        status: req.query.status as string,
        period: req.query.period as string,
        date_range: req.query.date_range ? JSON.parse(req.query.date_range as string) : undefined,
        customer_id: req.query.customer_id as string,
        subscription_id: req.query.subscription_id as string,
        amount_range: req.query.amount_range ? JSON.parse(req.query.amount_range as string) : undefined,
        search: req.query.search as string,
        sort_by: req.query.sort_by as string,
        sort_order: req.query.sort_order as string,
        page: req.query.page as string,
        limit: req.query.limit as string
      };

      const result = await InvoiceDAO.getInvoices(orgId, filters);

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getInvoiceById(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { invoice_id } = req.params;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const invoice = await InvoiceDAO.getInvoiceById(orgId, invoice_id);

      res.status(200).json({
        success: true,
        data: invoice,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async downloadInvoicePDF(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const { invoice_id } = req.params;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const invoice = await InvoiceDAO.getInvoiceById(orgId, invoice_id);

      // Redirect to the PDF URL or serve the PDF directly
      if (invoice.pdf_url) {
        res.redirect(invoice.pdf_url);
      } else {
        res.status(404).json({
          success: false,
          error: {
            type: 'PDF_NOT_AVAILABLE',
            message: 'PDF not available for this invoice',
            code: 404
          }
        });
      }

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (10 minutes)
```typescript
// src/routes/invoice.routes.ts
import { Router } from 'express';
import { InvoiceHandler } from '@/handlers/invoice.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateInvoiceQuery } from '@/validators/invoice.validator';

const router = Router();

// Get invoices with filtering
router.get(
  '/',
  supabaseAuthMiddleware,
  validateInvoiceQuery,
  InvoiceHandler.getInvoices
);

// Get invoice by ID
router.get(
  '/:invoice_id',
  supabaseAuthMiddleware,
  InvoiceHandler.getInvoiceById
);

// Download invoice PDF
router.get(
  '/:invoice_id/download',
  supabaseAuthMiddleware,
  InvoiceHandler.downloadInvoicePDF
);

export { router as invoiceRoutes };
```

### 5. Validation Schema (15 minutes)
```typescript
// src/validators/invoice.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const invoiceQuerySchema = Joi.object({
  status: Joi.string().valid('draft', 'open', 'paid', 'void', 'uncollectible').optional(),
  period: Joi.string().valid('current_month', 'last_month', 'current_year', 'custom').optional(),
  date_range: Joi.string().optional(), // JSON string
  customer_id: Joi.string().optional(),
  subscription_id: Joi.string().optional(),
  amount_range: Joi.string().optional(), // JSON string
  search: Joi.string().min(2).max(100).optional(),
  sort_by: Joi.string().valid('created', 'due_date', 'amount', 'status').optional(),
  sort_order: Joi.string().valid('asc', 'desc').optional(),
  page: Joi.number().integer().min(1).max(1000).optional(),
  limit: Joi.number().integer().min(1).max(50).optional()
});

export const validateInvoiceQuery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = invoiceQuerySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }

  next();
};
```

## Error Handling

### Common Errors
- **404 Invoice Not Found**: Invoice doesn't exist or user doesn't have access
- **400 Invalid Query Parameters**: Invalid filter values or date ranges
- **422 Date Range Invalid**: Invalid date range format
- **429 Rate Limited**: Too many requests

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "INVOICE_NOT_FOUND",
    "message": "Invoice inv_64a1b2c3d4e5f6789012345 not found",
    "code": 404
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Security Considerations

1. **Access Control**: Ensure users can only access their organization's invoices
2. **Data Privacy**: Mask sensitive payment information
3. **Rate Limiting**: Prevent abuse of invoice endpoints
4. **PDF Security**: Secure PDF download links with expiration
5. **Audit Logging**: Track invoice access for compliance