# Payment Service - POST /webhooks (Stripe Webhook Handling)

## Task Overview
**Endpoint**: `POST /api/payment/v1/webhooks/stripe`  
**Purpose**: Handle incoming webhooks from Stripe for payment events, subscription updates, and invoice status changes  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Stripe integration, payment processing, subscription management, webhook security  

## API Specification

### Stripe Webhook Endpoint
```http
POST /api/payment/v1/webhooks/stripe
Content-Type: application/json
Stripe-Signature: <webhook_signature>

{
  "id": "evt_1234567890",
  "object": "event",
  "api_version": "2020-08-27",
  "created": 1609459200,
  "data": {
    "object": {
      "id": "pi_1234567890",
      "object": "payment_intent",
      "amount": 2000,
      "currency": "usd",
      "status": "succeeded",
      "customer": "cus_1234567890",
      "invoice": "in_1234567890",
      "metadata": {
        "organization_id": "org_64a1b2c3d4e5f6789012345",
        "campaign_id": "camp_64a1b2c3d4e5f6789012345"
      }
    }
  },
  "livemode": false,
  "pending_webhooks": 1,
  "request": {
    "id": "req_1234567890",
    "idempotency_key": null
  },
  "type": "payment_intent.succeeded"
}
```

### Response - Webhook Processed
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "webhook_id": "wh_64a1b2c3d4e5f6789012345",
    "event_id": "evt_1234567890",
    "event_type": "payment_intent.succeeded",
    "processed_at": "2024-08-11T15:30:00Z",
    "processing_time": 156,
    "actions_performed": [
      "update_payment_intent",
      "update_invoice_status",
      "send_notification"
    ]
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Supported Webhook Events

### Payment Events
- `payment_intent.succeeded` - Payment completed successfully
- `payment_intent.payment_failed` - Payment failed
- `payment_intent.requires_action` - Payment requires customer action
- `payment_method.attached` - Payment method attached to customer

### Invoice Events  
- `invoice.payment_succeeded` - Invoice payment completed
- `invoice.payment_failed` - Invoice payment failed
- `invoice.finalized` - Invoice finalized and ready for payment
- `invoice.voided` - Invoice voided

### Subscription Events
- `customer.subscription.created` - New subscription created
- `customer.subscription.updated` - Subscription modified
- `customer.subscription.deleted` - Subscription cancelled
- `invoice.upcoming` - Upcoming invoice notification

### Customer Events
- `customer.created` - New customer created
- `customer.updated` - Customer information updated
- `customer.deleted` - Customer deleted

## Implementation Tasks

### 1. Database Models (30 minutes)
```typescript
// src/lib/db/models/webhook-event.model.ts
import { Schema, model } from 'mongoose';

const webhookEventSchema = new Schema({
  org_id: { type: String, index: true },
  webhook_id: { type: String, required: true, unique: true },
  stripe_event_id: { type: String, required: true, unique: true },
  event_type: { type: String, required: true, index: true },
  
  // Raw webhook data
  raw_data: {
    stripe_event: Schema.Types.Mixed,
    headers: Schema.Types.Mixed,
    signature: String
  },
  
  // Processing status
  processing_status: { 
    type: String, 
    enum: ['pending', 'processing', 'completed', 'failed', 'skipped'],
    default: 'pending'
  },
  received_at: { type: Date, default: Date.now },
  processed_at: Date,
  processing_time: Number, // milliseconds
  
  // Processing results
  results: {
    actions_performed: [String],
    entities_updated: [{
      entity_type: String, // 'payment_intent', 'invoice', 'subscription', etc.
      entity_id: String,
      updates: Schema.Types.Mixed
    }],
    notifications_sent: [{
      type: String,
      recipient: String,
      sent_at: Date
    }],
    errors: [Schema.Types.Mixed]
  },
  
  // Retry information
  retry_count: { type: Number, default: 0 },
  max_retries: { type: Number, default: 3 },
  next_retry_at: Date,
  
  // Validation
  signature_verified: { type: Boolean, default: false },
  event_duplicate: { type: Boolean, default: false },
  
  // Metadata
  api_version: String,
  livemode: Boolean,
  request_id: String
}, { 
  timestamps: true,
  collection: 'webhook_events'
});

// Indexes for efficient querying
webhookEventSchema.index({ stripe_event_id: 1 }, { unique: true });
webhookEventSchema.index({ event_type: 1, processing_status: 1 });
webhookEventSchema.index({ received_at: -1 });
webhookEventSchema.index({ processing_status: 1, next_retry_at: 1 });

export const WebhookEventModel = model('WebhookEvent', webhookEventSchema);
```

### 2. Webhook Processing Service (1.5 hours)
```typescript
// src/services/webhook-processing.service.ts
import crypto from 'crypto';
import { WebhookEventModel } from '@/lib/db/models/webhook-event.model';
import { PaymentIntentModel } from '@/lib/db/models/payment-intent.model';
import { InvoiceModel } from '@/lib/db/models/invoice.model';
import { SubscriptionModel } from '@/lib/db/models/subscription.model';
import { NotificationService } from '@/services/notification.service';
import { QueueService } from '@/services/queue.service';
import { generateWebhookId } from '@/lib/utils/id-generator';
import { ValidationError, DatabaseError } from '@/lib/errors/error-types';

export class WebhookProcessingService {
  private static readonly STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;

  static async processStripeWebhook(headers: any, body: any, signature: string) {
    const webhookId = generateWebhookId();
    const startTime = Date.now();

    try {
      // Step 1: Verify webhook signature
      const isValidSignature = this.verifyStripeSignature(body, signature);
      if (!isValidSignature) {
        throw new ValidationError('Invalid webhook signature', 'INVALID_SIGNATURE');
      }

      const event = JSON.parse(body);

      // Step 2: Check for duplicate events
      const existingEvent = await WebhookEventModel.findOne({ 
        stripe_event_id: event.id 
      });
      
      if (existingEvent) {
        return {
          webhook_id: existingEvent.webhook_id,
          event_id: event.id,
          event_type: event.type,
          status: 'duplicate',
          processed_at: existingEvent.processed_at,
          processing_time: existingEvent.processing_time
        };
      }

      // Step 3: Create webhook event record
      const webhookEvent = new WebhookEventModel({
        webhook_id: webhookId,
        stripe_event_id: event.id,
        event_type: event.type,
        org_id: this.extractOrgId(event),
        raw_data: {
          stripe_event: event,
          headers: headers,
          signature: signature
        },
        signature_verified: true,
        api_version: event.api_version,
        livemode: event.livemode,
        request_id: event.request?.id,
        processing_status: 'processing',
        received_at: new Date()
      });

      await webhookEvent.save();

      // Step 4: Process the event based on type
      const processingResult = await this.processEventByType(event);

      // Step 5: Update webhook event with results
      const processingTime = Date.now() - startTime;
      
      await WebhookEventModel.findOneAndUpdate(
        { webhook_id: webhookId },
        {
          processing_status: 'completed',
          processed_at: new Date(),
          processing_time: processingTime,
          results: processingResult
        }
      );

      return {
        webhook_id: webhookId,
        event_id: event.id,
        event_type: event.type,
        processed_at: new Date(),
        processing_time: processingTime,
        actions_performed: processingResult.actions_performed
      };

    } catch (error: unknown) {
      // Update webhook event with error
      const processingTime = Date.now() - startTime;
      
      await WebhookEventModel.findOneAndUpdate(
        { webhook_id: webhookId },
        {
          processing_status: 'failed',
          processed_at: new Date(),
          processing_time: processingTime,
          results: {
            actions_performed: [],
            entities_updated: [],
            notifications_sent: [],
            errors: [{
              message: error instanceof Error ? error.message : 'Unknown error',
              timestamp: new Date()
            }]
          }
        }
      );

      throw error;
    }
  }

  private static verifyStripeSignature(payload: string, signature: string): boolean {
    if (!this.STRIPE_WEBHOOK_SECRET) {
      throw new Error('Stripe webhook secret not configured');
    }

    const elements = signature.split(',');
    const signatureElements: { [key: string]: string } = {};

    for (const element of elements) {
      const [key, value] = element.split('=');
      signatureElements[key] = value;
    }

    const timestamp = signatureElements.t;
    const expectedSignature = signatureElements.v1;

    if (!timestamp || !expectedSignature) {
      return false;
    }

    // Check timestamp (prevent replay attacks)
    const timestampSeconds = parseInt(timestamp);
    const now = Math.floor(Date.now() / 1000);
    const tolerance = 300; // 5 minutes

    if (Math.abs(now - timestampSeconds) > tolerance) {
      return false;
    }

    // Verify signature
    const signedPayload = `${timestamp}.${payload}`;
    const computedSignature = crypto
      .createHmac('sha256', this.STRIPE_WEBHOOK_SECRET)
      .update(signedPayload, 'utf8')
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(computedSignature, 'hex')
    );
  }

  private static extractOrgId(event: any): string | null {
    // Try to extract organization ID from various places in the event
    if (event.data?.object?.metadata?.organization_id) {
      return event.data.object.metadata.organization_id;
    }
    
    if (event.data?.object?.customer?.metadata?.organization_id) {
      return event.data.object.customer.metadata.organization_id;
    }

    return null;
  }

  private static async processEventByType(event: any) {
    const actions: string[] = [];
    const entitiesUpdated: any[] = [];
    const notificationsSent: any[] = [];
    const errors: any[] = [];

    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'invoice.finalized':
          await this.handleInvoiceFinalized(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event, actions, entitiesUpdated, notificationsSent);
          break;

        case 'invoice.upcoming':
          await this.handleUpcomingInvoice(event, actions, entitiesUpdated, notificationsSent);
          break;

        default:
          console.log(`Unhandled event type: ${event.type}`);
          actions.push('event_logged');
      }

    } catch (error) {
      console.error(`Error processing ${event.type}:`, error);
      errors.push({
        message: error instanceof Error ? error.message : 'Unknown error',
        event_type: event.type,
        timestamp: new Date()
      });
    }

    return {
      actions_performed: actions,
      entities_updated: entitiesUpdated,
      notifications_sent: notificationsSent,
      errors: errors
    };
  }

  private static async handlePaymentIntentSucceeded(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const paymentIntent = event.data.object;

    // Update payment intent in database
    await PaymentIntentModel.findOneAndUpdate(
      { stripe_payment_intent_id: paymentIntent.id },
      {
        status: 'succeeded',
        amount_received: paymentIntent.amount_received,
        charges: paymentIntent.charges.data,
        updated_at: new Date()
      }
    );

    actions.push('update_payment_intent');
    entitiesUpdated.push({
      entity_type: 'payment_intent',
      entity_id: paymentIntent.id,
      updates: { status: 'succeeded' }
    });

    // Update associated invoice if exists
    if (paymentIntent.invoice) {
      await InvoiceModel.findOneAndUpdate(
        { stripe_invoice_id: paymentIntent.invoice },
        {
          status: 'paid',
          amount_paid: paymentIntent.amount_received,
          'dates.paid_at': new Date(),
          'payment_intent.status': 'succeeded'
        }
      );

      actions.push('update_invoice_status');
      entitiesUpdated.push({
        entity_type: 'invoice',
        entity_id: paymentIntent.invoice,
        updates: { status: 'paid' }
      });
    }

    // Send success notification
    if (paymentIntent.metadata?.organization_id) {
      await NotificationService.sendPaymentSuccessNotification({
        organization_id: paymentIntent.metadata.organization_id,
        payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount_received,
        currency: paymentIntent.currency
      });

      actions.push('send_notification');
      notificationsSent.push({
        type: 'payment_success',
        recipient: paymentIntent.metadata.organization_id,
        sent_at: new Date()
      });
    }
  }

  private static async handlePaymentIntentFailed(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const paymentIntent = event.data.object;

    // Update payment intent in database
    await PaymentIntentModel.findOneAndUpdate(
      { stripe_payment_intent_id: paymentIntent.id },
      {
        status: 'failed',
        last_payment_error: paymentIntent.last_payment_error,
        updated_at: new Date()
      }
    );

    actions.push('update_payment_intent');
    entitiesUpdated.push({
      entity_type: 'payment_intent',
      entity_id: paymentIntent.id,
      updates: { status: 'failed' }
    });

    // Send failure notification
    if (paymentIntent.metadata?.organization_id) {
      await NotificationService.sendPaymentFailedNotification({
        organization_id: paymentIntent.metadata.organization_id,
        payment_intent_id: paymentIntent.id,
        error: paymentIntent.last_payment_error
      });

      actions.push('send_notification');
      notificationsSent.push({
        type: 'payment_failed',
        recipient: paymentIntent.metadata.organization_id,
        sent_at: new Date()
      });
    }
  }

  private static async handleInvoicePaymentSucceeded(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const invoice = event.data.object;

    // Update invoice in database
    await InvoiceModel.findOneAndUpdate(
      { stripe_invoice_id: invoice.id },
      {
        status: 'paid',
        amount_paid: invoice.amount_paid,
        'dates.paid_at': new Date(invoice.status_transitions.paid_at * 1000),
        'payment_intent.status': 'succeeded'
      }
    );

    actions.push('update_invoice_status');
    entitiesUpdated.push({
      entity_type: 'invoice',
      entity_id: invoice.id,
      updates: { status: 'paid' }
    });

    // Update subscription status if this was a subscription invoice
    if (invoice.subscription) {
      await SubscriptionModel.findOneAndUpdate(
        { stripe_subscription_id: invoice.subscription },
        {
          status: 'active',
          current_period_start: new Date(invoice.period_start * 1000),
          current_period_end: new Date(invoice.period_end * 1000),
          updated_at: new Date()
        }
      );

      actions.push('update_subscription_status');
      entitiesUpdated.push({
        entity_type: 'subscription',
        entity_id: invoice.subscription,
        updates: { status: 'active' }
      });
    }
  }

  private static async handleInvoicePaymentFailed(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const invoice = event.data.object;

    // Update invoice in database
    await InvoiceModel.findOneAndUpdate(
      { stripe_invoice_id: invoice.id },
      {
        status: 'open',
        attempt_count: invoice.attempt_count,
        next_payment_attempt: invoice.next_payment_attempt ? 
          new Date(invoice.next_payment_attempt * 1000) : null
      }
    );

    actions.push('update_invoice_status');
    entitiesUpdated.push({
      entity_type: 'invoice',
      entity_id: invoice.id,
      updates: { status: 'open', attempt_count: invoice.attempt_count }
    });

    // Handle subscription status if payment failed
    if (invoice.subscription && invoice.attempt_count >= 3) {
      await SubscriptionModel.findOneAndUpdate(
        { stripe_subscription_id: invoice.subscription },
        {
          status: 'past_due',
          updated_at: new Date()
        }
      );

      actions.push('update_subscription_status');
      entitiesUpdated.push({
        entity_type: 'subscription',
        entity_id: invoice.subscription,
        updates: { status: 'past_due' }
      });
    }
  }

  private static async handleSubscriptionCreated(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const subscription = event.data.object;

    // Create or update subscription in database
    await SubscriptionModel.findOneAndUpdate(
      { stripe_subscription_id: subscription.id },
      {
        org_id: subscription.metadata?.organization_id,
        customer_id: subscription.customer,
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start * 1000),
        current_period_end: new Date(subscription.current_period_end * 1000),
        plan_id: subscription.items.data[0]?.price?.id,
        quantity: subscription.items.data[0]?.quantity || 1,
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        cancel_at_period_end: subscription.cancel_at_period_end,
        updated_at: new Date()
      },
      { upsert: true }
    );

    actions.push('create_subscription');
    entitiesUpdated.push({
      entity_type: 'subscription',
      entity_id: subscription.id,
      updates: { status: subscription.status }
    });
  }

  private static async handleSubscriptionUpdated(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const subscription = event.data.object;

    // Update subscription in database
    await SubscriptionModel.findOneAndUpdate(
      { stripe_subscription_id: subscription.id },
      {
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start * 1000),
        current_period_end: new Date(subscription.current_period_end * 1000),
        plan_id: subscription.items.data[0]?.price?.id,
        quantity: subscription.items.data[0]?.quantity || 1,
        cancel_at_period_end: subscription.cancel_at_period_end,
        canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
        updated_at: new Date()
      }
    );

    actions.push('update_subscription');
    entitiesUpdated.push({
      entity_type: 'subscription',
      entity_id: subscription.id,
      updates: { status: subscription.status }
    });
  }

  private static async handleSubscriptionDeleted(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const subscription = event.data.object;

    // Update subscription status to canceled
    await SubscriptionModel.findOneAndUpdate(
      { stripe_subscription_id: subscription.id },
      {
        status: 'canceled',
        canceled_at: new Date(),
        updated_at: new Date()
      }
    );

    actions.push('cancel_subscription');
    entitiesUpdated.push({
      entity_type: 'subscription',
      entity_id: subscription.id,
      updates: { status: 'canceled' }
    });
  }

  private static async handleUpcomingInvoice(
    event: any, 
    actions: string[], 
    entitiesUpdated: any[], 
    notificationsSent: any[]
  ) {
    const invoice = event.data.object;

    // Send upcoming invoice notification
    if (invoice.customer_email || invoice.metadata?.organization_id) {
      await NotificationService.sendUpcomingInvoiceNotification({
        organization_id: invoice.metadata?.organization_id,
        customer_email: invoice.customer_email,
        invoice_amount: invoice.amount_due,
        due_date: new Date(invoice.period_end * 1000)
      });

      actions.push('send_notification');
      notificationsSent.push({
        type: 'upcoming_invoice',
        recipient: invoice.customer_email || invoice.metadata?.organization_id,
        sent_at: new Date()
      });
    }

    actions.push('process_upcoming_invoice');
  }
}
```

### 3. Handler Implementation (30 minutes)
```typescript
// src/handlers/webhook.handler.ts
import { Request, Response } from 'express';
import { WebhookProcessingService } from '@/services/webhook-processing.service';
import { handleError } from '@/lib/errors/error-handler';

export class WebhookHandler {
  static async handleStripeWebhook(req: Request, res: Response) {
    try {
      const signature = req.get('stripe-signature');
      
      if (!signature) {
        return res.status(400).json({
          success: false,
          error: {
            type: 'MISSING_SIGNATURE',
            message: 'Missing Stripe signature header',
            code: 400
          }
        });
      }

      const result = await WebhookProcessingService.processStripeWebhook(
        req.headers,
        req.body,
        signature
      );

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getWebhookEvents(req: Request, res: Response) {
    try {
      const { event_type, status, page = 1, limit = 20 } = req.query;
      
      const query: any = {};
      if (event_type) query.event_type = event_type;
      if (status) query.processing_status = status;

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      
      const [events, total] = await Promise.all([
        WebhookEventModel
          .find(query)
          .sort({ received_at: -1 })
          .skip(skip)
          .limit(parseInt(limit as string))
          .select('-raw_data') // Exclude sensitive raw data
          .lean(),
        WebhookEventModel.countDocuments(query)
      ]);

      res.status(200).json({
        success: true,
        data: {
          events,
          pagination: {
            current_page: parseInt(page as string),
            per_page: parseInt(limit as string),
            total_pages: Math.ceil(total / parseInt(limit as string)),
            total_count: total
          }
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (15 minutes)
```typescript
// src/routes/webhook.routes.ts
import { Router } from 'express';
import { WebhookHandler } from '@/handlers/webhook.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { webhookRateLimitMiddleware } from '@/lib/middlewares/rate-limit.middleware';

const router = Router();

// Stripe webhook endpoint (no auth required for incoming webhooks)
router.post(
  '/stripe',
  webhookRateLimitMiddleware,
  WebhookHandler.handleStripeWebhook
);

// Get webhook events (requires authentication)
router.get(
  '/events',
  supabaseAuthMiddleware,
  WebhookHandler.getWebhookEvents
);

export { router as webhookRoutes };
```

### 5. Rate Limiting Middleware (15 minutes)
```typescript
// src/lib/middlewares/webhook-rate-limit.middleware.ts
import rateLimit from 'express-rate-limit';

export const webhookRateLimitMiddleware = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 1000, // 1000 requests per minute per IP (generous for Stripe)
  message: {
    success: false,
    error: {
      type: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many webhook requests, please try again later',
      code: 429
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting for known Stripe IPs (optional)
  skip: (req) => {
    const stripeIPs = [
      '**************',
      '**************',
      // Add other Stripe webhook IPs
    ];
    return stripeIPs.includes(req.ip);
  }
});
```

## Error Handling

### Common Errors
- **400 Missing Signature**: Stripe signature header missing
- **401 Invalid Signature**: Webhook signature verification failed
- **409 Duplicate Event**: Event already processed
- **422 Processing Failed**: Error processing webhook event
- **429 Rate Limited**: Too many webhook requests

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "INVALID_SIGNATURE",
    "message": "Webhook signature verification failed",
    "code": 401
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Security Considerations

1. **Signature Verification**: Always verify Stripe webhook signatures
2. **Timestamp Validation**: Check webhook timestamp to prevent replay attacks
3. **IP Whitelisting**: Optionally restrict to known Stripe IPs
4. **Rate Limiting**: Implement appropriate rate limiting
5. **Idempotency**: Handle duplicate events gracefully
6. **Error Logging**: Log all webhook processing errors for monitoring

## Monitoring and Alerting

### Metrics to Track
- Webhook processing success/failure rates
- Processing times by event type
- Duplicate event rates
- Signature verification failures
- Queue processing delays

### Alerts to Configure
- High webhook failure rates (>5%)
- Signature verification failures
- Processing time anomalies (>5 seconds)
- Queue backup (>100 pending events)

## Integration Testing

### Test Webhook Events
```bash
# Use Stripe CLI to forward test events
stripe listen --forward-to localhost:3000/api/payment/v1/webhooks/stripe

# Trigger test events
stripe trigger payment_intent.succeeded
stripe trigger invoice.payment_failed
stripe trigger customer.subscription.created
```

### Webhook Event Validation
- Test signature verification with valid/invalid signatures
- Test duplicate event handling
- Test all supported event types
- Test error handling scenarios
- Test timeout handling for long-running processes