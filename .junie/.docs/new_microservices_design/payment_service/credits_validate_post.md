# Payment Service - POST /credits/validate (Credit Validation for Message Sending)

## Task Overview
**Endpoint**: `POST /api/payment/v1/credits/validate`  
**Purpose**: Validate if user has sufficient credits to send a message based on phone number or email  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Usage tracking, billing system, message cost calculation  

## API Specification

### Validate Credits Request
```http
POST /api/payment/v1/credits/validate
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "message_type": "sms",
  "recipient": "+***********",
  "estimated_cost": 0.05,
  "quantity": 1,
  "campaign_id": "camp_64a1b2c3d4e5f6789012345",
  "metadata": {
    "provider": "twilio",
    "country_code": "LK",
    "message_length": 160
  }
}
```

### Bulk Credit Validation Request
```http
POST /api/payment/v1/credits/validate
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
Content-Type: application/json

{
  "message_type": "email",
  "recipients": [
    {
      "recipient": "<EMAIL>",
      "estimated_cost": 0.002
    },
    {
      "recipient": "<EMAIL>", 
      "estimated_cost": 0.002
    }
  ],
  "quantity": 2,
  "total_estimated_cost": 0.004,
  "campaign_id": "camp_64a1b2c3d4e5f6789012346"
}
```

### Request Parameters
- `message_type` (string): Type of message (sms, email, push, voice)
- `recipient` (string): Single recipient (phone number for SMS, email for Email)
- `recipients` (array): Multiple recipients for bulk validation
- `estimated_cost` (number): Estimated cost per message
- `total_estimated_cost` (number): Total cost for bulk messages
- `quantity` (integer): Number of messages to send
- `campaign_id` (string, optional): Associated campaign ID
- `metadata` (object, optional): Additional metadata (provider, country, etc.)

### Response - Sufficient Credits
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "validation_result": "approved",
    "credit_status": {
      "has_sufficient_credits": true,
      "current_balance": {
        "sms": {
          "included": 10000,
          "used": 7500,
          "remaining": 2500,
          "overage_allowed": true,
          "overage_rate": 0.025
        },
        "email": {
          "included": 50000,
          "used": 45000,
          "remaining": 5000,
          "overage_allowed": true,
          "overage_rate": 0.003
        }
      },
      "estimated_usage": {
        "message_type": "sms",
        "quantity": 1,
        "cost": 0.05,
        "will_trigger_overage": false,
        "remaining_after": 2499
      }
    },
    "billing_info": {
      "current_plan": "Business",
      "billing_cycle": "monthly",
      "cycle_end": "2024-08-31T23:59:59Z",
      "payment_method_valid": true
    },
    "recipient_validation": {
      "recipient": "+***********",
      "is_valid_format": true,
      "country": "Sri Lanka",
      "carrier": "Dialog",
      "risk_score": "low",
      "deliverable": true
    },
    "approval_details": {
      "validation_id": "valid_64a1b2c3d4e5f6789012345",
      "expires_at": "2024-08-11T15:35:00Z",
      "estimated_delivery_time": "2024-08-11T15:31:00Z"
    }
  }
}
```

### Response - Insufficient Credits
```http
HTTP/1.1 402 Payment Required
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "CREDIT_001",
    "message": "Insufficient credits for message sending",
    "details": {
      "validation_result": "rejected",
      "reason": "insufficient_credits",
      "credit_status": {
        "has_sufficient_credits": false,
        "current_balance": {
          "sms": {
            "included": 10000,
            "used": 9999,
            "remaining": 1,
            "overage_allowed": false
          }
        },
        "estimated_usage": {
          "message_type": "sms",
          "quantity": 1,
          "cost": 0.05,
          "required_credits": 1,
          "shortage": 0
        }
      },
      "recommendations": [
        {
          "action": "upgrade_plan",
          "description": "Upgrade to Professional plan for higher SMS limits",
          "url": "/api/payment/v1/subscriptions"
        },
        {
          "action": "enable_overage",
          "description": "Enable overage billing to continue sending",
          "estimated_overage_cost": 0.025
        },
        {
          "action": "add_credits",
          "description": "Purchase additional credits",
          "url": "/api/payment/v1/credits/purchase"
        }
      ]
    }
  }
}
```

### Response - Bulk Validation with Mixed Results
```http
HTTP/1.1 207 Multi-Status
Content-Type: application/json

{
  "success": true,
  "data": {
    "validation_result": "partial",
    "overall_status": {
      "total_requested": 2,
      "approved": 1,
      "rejected": 1,
      "total_approved_cost": 0.002
    },
    "recipient_validations": [
      {
        "recipient": "<EMAIL>",
        "status": "approved",
        "estimated_cost": 0.002,
        "validation_id": "valid_64a1b2c3d4e5f6789012346"
      },
      {
        "recipient": "<EMAIL>",
        "status": "rejected",
        "reason": "invalid_email_format",
        "estimated_cost": 0.002
      }
    ],
    "credit_status": {
      "has_sufficient_credits": true,
      "current_balance": {
        "email": {
          "included": 50000,
          "used": 45000,
          "remaining": 5000,
          "overage_allowed": true
        }
      }
    }
  }
}
```

## Error Responses

### 400 Bad Request - Invalid Parameters
```json
{
  "success": false,
  "error": {
    "code": "CREDIT_002",
    "message": "Invalid credit validation parameters",
    "details": [
      {
        "field": "message_type",
        "message": "Message type must be one of: sms, email, push, voice"
      },
      {
        "field": "recipient",
        "message": "Recipient must be a valid phone number for SMS or email address for Email"
      }
    ]
  }
}
```

### 401 Unauthorized - Invalid Token
```json
{
  "success": false,
  "error": {
    "code": "CREDIT_003",
    "message": "Invalid or expired authentication token",
    "details": "Please provide a valid JWT token in the Authorization header"
  }
}
```

### 402 Payment Required - Payment Method Issue
```json
{
  "success": false,
  "error": {
    "code": "CREDIT_004",
    "message": "Payment method required or invalid",
    "details": {
      "validation_result": "rejected",
      "reason": "payment_method_invalid",
      "payment_status": {
        "has_valid_payment_method": false,
        "last_payment_failed": true,
        "requires_payment_update": true
      },
      "actions_required": [
        {
          "action": "update_payment_method",
          "description": "Please update your payment method",
          "url": "/api/payment/v1/payment-methods"
        }
      ]
    }
  }
}
```

### 403 Forbidden - Account Suspended
```json
{
  "success": false,
  "error": {
    "code": "CREDIT_005",
    "message": "Account suspended - message sending not allowed",
    "details": {
      "validation_result": "rejected",
      "reason": "account_suspended",
      "account_status": {
        "status": "suspended",
        "suspension_reason": "payment_overdue",
        "suspended_since": "2024-08-01T00:00:00Z",
        "outstanding_balance": 299.99
      },
      "actions_required": [
        {
          "action": "settle_outstanding_balance",
          "description": "Please settle your outstanding balance to reactivate account",
          "amount": 299.99,
          "url": "/api/payment/v1/payments/settle"
        }
      ]
    }
  }
}
```

### 422 Unprocessable Entity - Invalid Recipient
```json
{
  "success": false,
  "error": {
    "code": "CREDIT_006",
    "message": "Recipient validation failed",
    "details": {
      "validation_result": "rejected",
      "reason": "invalid_recipient",
      "recipient_validation": {
        "recipient": "+1234567890123456789",
        "is_valid_format": false,
        "validation_errors": [
          "Phone number exceeds maximum length",
          "Invalid country code"
        ]
      }
    }
  }
}
```

### 429 Too Many Requests - Rate Limited
```json
{
  "success": false,
  "error": {
    "code": "CREDIT_007", 
    "message": "Rate limit exceeded for credit validation",
    "details": {
      "rate_limit": {
        "limit": 100,
        "remaining": 0,
        "reset_time": "2024-08-11T16:00:00Z"
      }
    }
  }
}
```

### 500 Internal Server Error - System Error
```json
{
  "success": false,
  "error": {
    "code": "CREDIT_008",
    "message": "Failed to validate credits",
    "details": "An internal error occurred while processing credit validation. Please try again later."
  }
}
```

## Technical Implementation

### Credit Validation Service
```typescript
// src/services/credit.validation.service.ts
import { BadRequestError, ValidationError } from '@/lib/errors/error.types';
import { UsageService } from './usage.service';
import { BillingService } from './billing.service';
import { RecipientValidationService } from './recipient.validation.service';

export class CreditValidationService {
  static async validateCredits(data: CreditValidationRequest): Promise<CreditValidationResponse> {
    try {
      // 1. Validate request format
      await this.validateRequest(data);
      
      // 2. Get current usage and limits
      const currentUsage = await UsageService.getCurrentUsage(data.organizationId);
      
      // 3. Validate recipient(s)
      const recipientValidation = await RecipientValidationService.validateRecipient(
        data.recipient || data.recipients,
        data.message_type
      );
      
      // 4. Check account status
      const accountStatus = await BillingService.getAccountStatus(data.organizationId);
      
      // 5. Calculate if sufficient credits
      const creditCheck = await this.checkSufficientCredits(
        currentUsage,
        data.message_type,
        data.estimated_cost || data.total_estimated_cost,
        data.quantity
      );
      
      // 6. Generate validation response
      return this.generateValidationResponse({
        creditCheck,
        recipientValidation,
        accountStatus,
        requestData: data
      });
      
    } catch (error) {
      throw this.handleValidationError(error);
    }
  }

  private static async checkSufficientCredits(
    currentUsage: UsageData,
    messageType: string,
    estimatedCost: number,
    quantity: number
  ): Promise<CreditCheckResult> {
    const serviceUsage = currentUsage[messageType];
    const remaining = serviceUsage.included - serviceUsage.used;
    
    // Check if within included limits
    if (remaining >= quantity) {
      return {
        hasCredits: true,
        willTriggerOverage: false,
        remainingAfter: remaining - quantity,
        cost: 0 // Included in plan
      };
    }
    
    // Check if overage is allowed
    if (serviceUsage.overage_allowed) {
      const overageQuantity = quantity - remaining;
      const overageCost = overageQuantity * serviceUsage.overage_rate;
      
      return {
        hasCredits: true,
        willTriggerOverage: true,
        remainingAfter: 0,
        overageQuantity,
        overageCost,
        cost: overageCost
      };
    }
    
    // Insufficient credits
    return {
      hasCredits: false,
      shortage: quantity - remaining,
      cost: estimatedCost
    };
  }
}
```

### Integration with Message Service
```typescript
// Enhanced MessageService.sendMessage() method
export class MessageService {
  static async sendMessage(data: SendMessageRequest): Promise<MessageResponse> {
    try {
      // Generate unique message ID
      const messageId = `msg_${uuidv4().replace(/-/g, '')}`;
      
      // Validate recipient format based on transport
      this.validateRecipient(data.to, data.transport);
      
      // Auto-select provider if not specified
      const provider = data.provider || await ProviderService.selectBestProvider(
        data.transport,
        data.to,
        data.org_id
      );
      
      // Calculate estimated cost
      const cost = await ProviderService.calculateMessageCost(provider, data);
      
      // *** NEW: Credit validation before processing ***
      const creditValidation = await CreditValidationService.validateCredits({
        organizationId: data.org_id,
        message_type: data.transport.toLowerCase(),
        recipient: data.to,
        estimated_cost: cost,
        quantity: 1,
        campaign_id: data.campaign_id,
        metadata: {
          provider: provider,
          message_length: data.content.length
        }
      });
      
      // Check if validation passed
      if (creditValidation.validation_result !== 'approved') {
        throw new ValidationError(
          'Credit validation failed',
          'CREDIT_001',
          creditValidation.error?.details
        );
      }
      
      // Continue with existing message processing...
      const messageLog = new MessageLogModel({
        // ... existing fields
        validation_id: creditValidation.data.approval_details.validation_id
      });
      
      // ... rest of existing logic
      
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BadRequestError) {
        throw error;
      }
      throw new Error(`Message sending failed: ${error.message}`);
    }
  }
}
```

### Database Schema Updates
```typescript
// Enhanced message log schema with validation tracking
const messageLogSchema = new Schema<MessageLogDocument>({
  // ... existing fields
  
  // Credit validation tracking
  validation_id: { type: String, index: true },
  credit_validated_at: { type: Date },
  credit_validation_result: {
    type: String,
    enum: ['approved', 'rejected', 'pending'],
    index: true
  },
  pre_send_balance: {
    included: Number,
    used: Number,
    remaining: Number
  },
  triggered_overage: { type: Boolean, default: false },
  overage_cost: { type: Number, default: 0 },
  
  // ... existing fields
});
```

### Queue Integration
```typescript
// Enhanced queue processing with credit re-validation
export class MessageOutboundProcessor {
  static async process(jobData: MessageJobData): Promise<void> {
    try {
      // Re-validate credits before actual sending (in case of delayed processing)
      if (jobData.scheduledAt && jobData.scheduledAt > new Date()) {
        const revalidation = await CreditValidationService.validateCredits({
          organizationId: jobData.orgId,
          message_type: jobData.transport.toLowerCase(),
          recipient: jobData.to,
          estimated_cost: jobData.estimatedCost,
          quantity: 1
        });
        
        if (revalidation.validation_result !== 'approved') {
          await this.handleCreditValidationFailure(jobData, revalidation);
          return;
        }
      }
      
      // Proceed with message sending...
      await ProviderService.sendMessage(jobData);
      
      // Update usage after successful sending
      await UsageService.recordUsage({
        organizationId: jobData.orgId,
        messageType: jobData.transport.toLowerCase(),
        cost: jobData.cost,
        quantity: 1,
        messageId: jobData.messageId
      });
      
    } catch (error) {
      await this.handleSendingFailure(jobData, error);
    }
  }
}
```

## Security Requirements

### Authentication & Authorization
- JWT token validation for all requests
- Organization-level data isolation
- Role-based access control for credit validation
- Rate limiting: 100 validations per minute per organization

### Data Protection
- Sensitive credit information encryption
- Audit logging for all validation requests
- PCI DSS compliance for payment-related data
- GDPR compliance for recipient data processing

### Fraud Prevention
- Recipient validation to prevent abuse
- Rate limiting to prevent credit enumeration
- Suspicious activity monitoring
- Account suspension for policy violations

## Performance Considerations

### Caching Strategy
- Cache usage data for 5 minutes
- Cache account status for 10 minutes  
- Cache recipient validation for 1 hour
- Real-time credit balance updates

### Database Optimization
- Indexes on organization_id, message_type, created_at
- Efficient queries with proper pagination
- Background jobs for usage aggregation
- Connection pooling for high concurrency

### Scalability
- Horizontal scaling with load balancers
- Redis caching for frequently accessed data
- Queue-based processing for bulk validations
- CDN for static validation responses

## Monitoring and Alerting

### Key Metrics
- Credit validation response times (<100ms target)
- Validation success/failure rates
- Account suspension notifications
- Payment method failure alerts

### Business Metrics
- Credit utilization rates by organization
- Overage billing revenue tracking
- Failed validation impact on campaigns
- Customer payment method health

### Operational Alerts
- High validation failure rates (>5%)
- Payment method update requirements
- Account suspension notifications
- System performance degradation

This credit validation system ensures users have sufficient credits before message sending, preventing failed deliveries due to insufficient funds and providing a seamless user experience with clear upgrade paths when limits are reached.