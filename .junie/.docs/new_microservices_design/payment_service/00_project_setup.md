# Payment Service - Project Setup

## Base Project Structure

```
shoutout_engage_payment_service/
├── src/
│   ├── app.ts                          # Main Express app
│   ├── server.ts                       # Server entry point
│   ├── routes/
│   │   ├── index.ts                    # Route aggregator
│   │   ├── payment.routes.ts           # Payment endpoints
│   │   ├── subscription.routes.ts      # Subscription endpoints
│   │   ├── invoice.routes.ts           # Invoice endpoints
│   │   ├── webhook.routes.ts           # Stripe webhook handlers
│   │   └── health.routes.ts            # Health check endpoints
│   ├── controllers/
│   │   ├── payment.controller.ts       # Payment operations
│   │   ├── subscription.controller.ts  # Subscription management
│   │   ├── invoice.controller.ts       # Invoice handling
│   │   ├── webhook.controller.ts       # Webhook processing
│   │   └── health.controller.ts        # Health checks
│   ├── services/
│   │   ├── payment.service.ts          # Core payment operations
│   │   ├── subscription.service.ts     # Subscription management
│   │   ├── invoice.service.ts          # Invoice generation
│   │   ├── stripe.service.ts           # Stripe integration
│   │   └── billing.service.ts          # Billing calculations
│   ├── middleware/
│   │   ├── auth.middleware.ts          # Supabase JWT auth
│   │   ├── validation.middleware.ts    # Request validation
│   │   ├── error.middleware.ts         # Error handling
│   │   └── rate.limit.middleware.ts    # Rate limiting
│   ├── lib/
│   │   ├── db/
│   │   │   ├── models/
│   │   │   │   ├── subscription.model.ts
│   │   │   │   ├── payment.method.model.ts
│   │   │   │   ├── invoice.model.ts
│   │   │   │   ├── transaction.model.ts
│   │   │   │   └── billing.history.model.ts
│   │   │   ├── dao/
│   │   │   │   ├── payment.dao.ts
│   │   │   │   ├── subscription.dao.ts
│   │   │   │   └── invoice.dao.ts
│   │   │   └── connectors/
│   │   │       ├── MongooseConnector.ts
│   │   │       └── RedisConnector.ts
│   │   ├── config/
│   │   │   ├── index.ts
│   │   │   ├── database.ts
│   │   │   ├── redis.ts
│   │   │   ├── supabase.ts
│   │   │   └── stripe.ts
│   │   ├── constant/
│   │   │   ├── payment.constants.ts
│   │   │   ├── subscription.constants.ts
│   │   │   └── billing.constants.ts
│   │   ├── errors/
│   │   │   ├── error.types.ts
│   │   │   └── error.handler.ts
│   │   ├── utils/
│   │   │   ├── logger.ts
│   │   │   ├── validator.ts
│   │   │   └── currency.utils.ts
│   │   └── swagger/
│   │       └── swagger.config.ts
│   ├── types/
│   │   ├── payment.types.ts
│   │   ├── subscription.types.ts
│   │   ├── invoice.types.ts
│   │   └── stripe.types.ts
│   ├── workers/
│   │   └── processors/
│   │       ├── payment.processor.ts
│   │       ├── subscription.processor.ts
│   │       └── invoice.processor.ts
│   └── queues/
│       ├── payment.queue.ts
│       ├── subscription.queue.ts
│       └── webhook.queue.ts
├── tests/
│   ├── unit/
│   │   ├── controllers/
│   │   ├── services/
│   │   └── utils/
│   ├── integration/
│   │   ├── api/
│   │   ├── stripe/
│   │   └── webhooks/
│   └── setup.ts
├── uploads/                            # Temporary file storage
├── logs/                              # Application logs
├── package.json
├── tsconfig.json
├── jest.config.js
├── .env.example
├── .gitignore
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## Package.json Configuration

```json
{
  "name": "shoutout-engage-payment-service",
  "version": "1.0.0",
  "description": "Payment processing microservice for ShoutOut Engage",
  "main": "dist/server.js",
  "scripts": {
    "dev": "nodemon src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "format": "prettier --write src/**/*.ts"
  },
  "dependencies": {
    "express": "^4.18.0",
    "mongoose": "^7.0.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "joi": "^17.9.0",
    "winston": "^3.8.0",
    "@supabase/supabase-js": "^2.0.0",
    "bull": "^4.10.0",
    "redis": "^4.6.0",
    "jsonwebtoken": "^9.0.0",
    "stripe": "^12.0.0",
    "uuid": "^9.0.0",
    "moment": "^2.29.0",
    "lodash": "^4.17.21",
    "swagger-jsdoc": "^6.2.0",
    "swagger-ui-express": "^4.6.0",
    "express-rate-limit": "^6.7.0",
    "multer": "^1.4.5"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "@types/express": "^4.17.0",
    "@types/mongoose": "^7.0.0",
    "@types/cors": "^2.8.0",
    "@types/joi": "^17.2.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/uuid": "^9.0.0",
    "@types/lodash": "^4.14.0",
    "@types/swagger-jsdoc": "^6.0.0",
    "@types/swagger-ui-express": "^4.1.0",
    "@types/multer": "^1.4.0",
    "ts-node": "^10.9.0",
    "nodemon": "^3.0.0",
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0",
    "ts-jest": "^29.0.0",
    "eslint": "^8.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "prettier": "^3.0.0"
  }
}
```

## TypeScript Configuration (tsconfig.json)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/types/*": ["types/*"],
      "@/lib/*": ["lib/*"],
      "@/services/*": ["services/*"],
      "@/controllers/*": ["controllers/*"],
      "@/middleware/*": ["middleware/*"],
      "@/routes/*": ["routes/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

## Environment Configuration (.env.example)

```env
# Server Configuration
NODE_ENV=development
PORT=3003
BASE_PATH=/api/payment/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/shoutout_engage_payment
MONGODB_TEST_URI=mongodb://localhost:27017/shoutout_engage_payment_test

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_API_VERSION=2023-10-16

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/payment-service.log

# External Services
CAMPAIGN_SERVICE_URL=http://localhost:3001
MESSAGE_SERVICE_URL=http://localhost:3002
CORE_SERVICE_URL=http://localhost:3000

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=2

# Billing Configuration
DEFAULT_CURRENCY=USD
BILLING_CYCLE_DAY=1
INVOICE_DUE_DAYS=7
TRIAL_PERIOD_DAYS=14
```

## Authentication Middleware (Based on Core Service)

```typescript
// src/middleware/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';
import config from '@/lib/config';

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    org_id: string;
    role: string;
  };
}

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export const authenticateRequest = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Missing or invalid authorization header'
        }
      });
      return;
    }

    const token = authHeader.substring(7);
    
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired token'
        }
      });
      return;
    }

    // Get user profile with organization info
    const { data: profile } = await supabase
      .from('profiles')
      .select('*, organizations(*)')
      .eq('user_id', user.id)
      .single();

    if (!profile) {
      res.status(401).json({
        success: false,
        error: {
          code: 'PROFILE_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      return;
    }

    req.user = {
      id: user.id,
      email: user.email || '',
      org_id: profile.organization_uuid,
      role: profile.user_type
    };

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: 'Authentication error'
      }
    });
  }
};
```

This setup provides the foundation for the Payment Service with proper Stripe integration, authentication, database connections, and project structure following the existing engage core service patterns.