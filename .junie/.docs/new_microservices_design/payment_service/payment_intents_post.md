# Payment Service - POST /payment-intents (Create Payment Intent)

## Task Overview
**Endpoint**: `POST /api/payment/v1/payment-intents`  
**Purpose**: Create a Stripe Payment Intent for processing one-time payments with confirmation handling  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Payment service setup, Stripe integration, payment methods management  

## API Specification

### Request
```http
POST /api/payment/v1/payment-intents
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "amount": 2500,
  "currency": "usd",
  "payment_method_id": "pm_1234567890abcdef",
  "confirm": true,
  "description": "Payment for ShoutOut Engage services",
  "metadata": {
    "order_id": "order_123",
    "service_type": "messaging_credits",
    "billing_period": "2024-08"
  },
  "receipt_email": "<EMAIL>",
  "return_url": "https://app.example.com/payment/complete"
}
```

### Response
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "payment_intent_id": "pi_1234567890abcdef",
    "client_secret": "pi_1234567890abcdef_secret_abc123",
    "status": "succeeded",
    "amount": 2500,
    "currency": "usd",
    "payment_method": {
      "id": "pm_1234567890abcdef",
      "type": "card",
      "card": {
        "brand": "visa",
        "last4": "4242",
        "exp_month": 12,
        "exp_year": 2025
      }
    },
    "charges": {
      "data": [
        {
          "id": "ch_1234567890abcdef",
          "amount": 2500,
          "status": "succeeded",
          "receipt_url": "https://pay.stripe.com/receipts/abc123"
        }
      ]
    },
    "metadata": {
      "order_id": "order_123",
      "service_type": "messaging_credits",
      "billing_period": "2024-08"
    },
    "created_at": "2024-08-11T15:30:00Z",
    "confirmed_at": "2024-08-11T15:30:05Z"
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 402 Payment Required
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "PAYMENT_FAILED",
    "message": "Your card was declined",
    "payment_intent": {
      "id": "pi_1234567890abcdef",
      "status": "requires_payment_method",
      "last_payment_error": {
        "code": "card_declined",
        "decline_code": "insufficient_funds",
        "message": "Your card has insufficient funds."
      }
    },
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Database Model (30 minutes)
```typescript
// src/lib/db/models/transaction.model.ts
import { Schema, model, Document } from 'mongoose';

export interface TransactionDocument extends Document {
  org_id: string;
  stripe_payment_intent_id: string;
  stripe_customer_id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled';
  payment_method_id?: string;
  description?: string;
  receipt_email?: string;
  receipt_url?: string;
  failure_reason?: string;
  metadata: Record<string, any>;
  stripe_fee?: number;
  net_amount?: number;
  created_at: Date;
  updated_at: Date;
  confirmed_at?: Date;
  failed_at?: Date;
}

const transactionSchema = new Schema<TransactionDocument>({
  org_id: { type: String, required: true, index: true },
  stripe_payment_intent_id: { type: String, required: true, unique: true, index: true },
  stripe_customer_id: { type: String, required: true, index: true },
  amount: { type: Number, required: true },
  currency: { type: String, required: true, default: 'usd' },
  status: {
    type: String,
    enum: ['pending', 'processing', 'succeeded', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  payment_method_id: { type: String, index: true },
  description: String,
  receipt_email: String,
  receipt_url: String,
  failure_reason: String,
  metadata: { type: Schema.Types.Mixed, default: {} },
  stripe_fee: Number,
  net_amount: Number,
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
  confirmed_at: Date,
  failed_at: Date
});

// Indexes
transactionSchema.index({ org_id: 1, status: 1, created_at: -1 });
transactionSchema.index({ org_id: 1, created_at: -1 });
transactionSchema.index({ stripe_customer_id: 1, created_at: -1 });

export const TransactionModel = model<TransactionDocument>('Transaction', transactionSchema);
```

### 2. Service Layer (70 minutes)
```typescript
// src/services/payment.service.ts (addition)
export class PaymentService {
  static async createPaymentIntent(
    orgId: string,
    paymentData: CreatePaymentIntentRequest
  ): Promise<PaymentIntentResponse> {
    try {
      // Validate amount
      this.validatePaymentAmount(paymentData.amount, paymentData.currency);

      // Get Stripe customer for organization
      const stripeCustomer = await this.getStripeCustomerForOrg(orgId);

      // Validate payment method if provided
      if (paymentData.payment_method_id) {
        await this.validatePaymentMethodOwnership(orgId, paymentData.payment_method_id);
      }

      // Create Payment Intent in Stripe
      const paymentIntent = await StripeService.createPaymentIntent({
        amount: paymentData.amount,
        currency: paymentData.currency,
        customer: stripeCustomer.id,
        payment_method: paymentData.payment_method_id,
        confirmation_method: 'manual',
        confirm: paymentData.confirm || false,
        description: paymentData.description,
        receipt_email: paymentData.receipt_email,
        return_url: paymentData.return_url,
        metadata: {
          org_id: orgId,
          ...paymentData.metadata
        }
      });

      // Save transaction record
      const transaction = await this.saveTransactionRecord(orgId, stripeCustomer.id, paymentIntent, paymentData);

      // Process the payment intent based on status
      const processedIntent = await this.processPaymentIntentStatus(paymentIntent, transaction);

      return this.formatPaymentIntentResponse(processedIntent, transaction);
    } catch (error) {
      if (error.type?.startsWith('Stripe')) {
        throw new BadRequestError(error.message, 'STRIPE_ERROR', error.decline_code ? [
          { field: 'payment', message: error.message }
        ] : undefined);
      }
      throw new InternalError(`Failed to create payment intent: ${error.message}`);
    }
  }

  private static validatePaymentAmount(amount: number, currency: string): void {
    // Validate amount is positive
    if (amount <= 0) {
      throw new BadRequestError('Payment amount must be greater than zero', 'INVALID_AMOUNT');
    }

    // Validate minimum amounts per currency (Stripe requirements)
    const minimums = {
      'usd': 50, // $0.50
      'eur': 50, // €0.50
      'gbp': 30, // £0.30
      'cad': 50, // CA$0.50
      'aud': 50  // AU$0.50
    };

    const minimum = minimums[currency.toLowerCase()] || 50;
    if (amount < minimum) {
      throw new BadRequestError(
        `Minimum payment amount for ${currency.toUpperCase()} is ${minimum/100}`,
        'AMOUNT_TOO_SMALL'
      );
    }

    // Validate maximum amount (configurable limit)
    const maxAmount = 999999999; // $9,999,999.99
    if (amount > maxAmount) {
      throw new BadRequestError(
        `Maximum payment amount is ${maxAmount/100}`,
        'AMOUNT_TOO_LARGE'
      );
    }
  }

  private static async validatePaymentMethodOwnership(orgId: string, paymentMethodId: string): Promise<void> {
    const paymentMethod = await PaymentMethodModel.findOne({
      org_id: orgId,
      stripe_payment_method_id: paymentMethodId,
      status: 'active'
    });

    if (!paymentMethod) {
      throw new BadRequestError(
        'Payment method not found or not accessible',
        'INVALID_PAYMENT_METHOD'
      );
    }
  }

  private static async getStripeCustomerForOrg(orgId: string): Promise<Stripe.Customer> {
    const paymentMethod = await PaymentMethodModel.findOne({
      org_id: orgId
    }).select('stripe_customer_id');

    if (!paymentMethod) {
      throw new BadRequestError(
        'No payment methods found for organization. Please add a payment method first.',
        'NO_PAYMENT_METHODS'
      );
    }

    return await StripeService.retrieveCustomer(paymentMethod.stripe_customer_id);
  }

  private static async saveTransactionRecord(
    orgId: string,
    customerId: string,
    paymentIntent: Stripe.PaymentIntent,
    paymentData: CreatePaymentIntentRequest
  ): Promise<TransactionDocument> {
    const transaction = new TransactionModel({
      org_id: orgId,
      stripe_payment_intent_id: paymentIntent.id,
      stripe_customer_id: customerId,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: this.mapStripeStatusToLocal(paymentIntent.status),
      payment_method_id: paymentData.payment_method_id,
      description: paymentData.description,
      receipt_email: paymentData.receipt_email,
      metadata: paymentData.metadata || {},
      created_at: new Date(),
      updated_at: new Date()
    });

    await transaction.save();
    return transaction;
  }

  private static async processPaymentIntentStatus(
    paymentIntent: Stripe.PaymentIntent,
    transaction: TransactionDocument
  ): Promise<Stripe.PaymentIntent> {
    switch (paymentIntent.status) {
      case 'succeeded':
        await this.handleSuccessfulPayment(paymentIntent, transaction);
        break;
      case 'requires_action':
        // Additional authentication required (3D Secure, etc.)
        await this.handlePaymentRequiresAction(paymentIntent, transaction);
        break;
      case 'requires_payment_method':
        // Payment failed, requires new payment method
        await this.handlePaymentFailed(paymentIntent, transaction);
        break;
      case 'processing':
        await this.handlePaymentProcessing(paymentIntent, transaction);
        break;
      default:
        // Update status for other states
        await this.updateTransactionStatus(transaction, paymentIntent.status);
    }

    return paymentIntent;
  }

  private static async handleSuccessfulPayment(
    paymentIntent: Stripe.PaymentIntent,
    transaction: TransactionDocument
  ): Promise<void> {
    // Calculate fees and net amount
    const charge = paymentIntent.charges?.data[0];
    const stripeFee = charge?.balance_transaction ? 
      await StripeService.getBalanceTransaction(charge.balance_transaction as string) : null;

    // Update transaction record
    await TransactionModel.updateOne(
      { _id: transaction._id },
      {
        status: 'succeeded',
        receipt_url: charge?.receipt_url,
        stripe_fee: stripeFee?.fee || 0,
        net_amount: stripeFee?.net || paymentIntent.amount,
        confirmed_at: new Date(),
        updated_at: new Date()
      }
    );

    // Add credits or process service fulfillment
    await this.fulfillPayment(transaction.org_id, paymentIntent.amount, transaction.metadata);

    // Send receipt email if configured
    if (transaction.receipt_email) {
      await this.sendReceiptEmail(transaction, charge?.receipt_url);
    }
  }

  private static async handlePaymentFailed(
    paymentIntent: Stripe.PaymentIntent,
    transaction: TransactionDocument
  ): Promise<void> {
    const failureReason = paymentIntent.last_payment_error?.message || 'Payment failed';
    
    await TransactionModel.updateOne(
      { _id: transaction._id },
      {
        status: 'failed',
        failure_reason: failureReason,
        failed_at: new Date(),
        updated_at: new Date()
      }
    );
  }

  private static mapStripeStatusToLocal(stripeStatus: string): string {
    const statusMap = {
      'requires_payment_method': 'pending',
      'requires_confirmation': 'pending',
      'requires_action': 'processing',
      'processing': 'processing',
      'requires_capture': 'processing',
      'canceled': 'cancelled',
      'succeeded': 'succeeded'
    };

    return statusMap[stripeStatus] || 'pending';
  }

  static async confirmPaymentIntent(
    orgId: string,
    paymentIntentId: string,
    paymentMethodId?: string
  ): Promise<PaymentIntentResponse> {
    try {
      // Find transaction
      const transaction = await TransactionModel.findOne({
        org_id: orgId,
        stripe_payment_intent_id: paymentIntentId
      });

      if (!transaction) {
        throw new NotFoundError('Payment intent not found');
      }

      // Confirm payment intent in Stripe
      const confirmedIntent = await StripeService.confirmPaymentIntent(paymentIntentId, {
        payment_method: paymentMethodId
      });

      // Update transaction based on new status
      await this.processPaymentIntentStatus(confirmedIntent, transaction);

      return this.formatPaymentIntentResponse(confirmedIntent, transaction);
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      if (error.type?.startsWith('Stripe')) {
        throw new BadRequestError(error.message, 'STRIPE_ERROR');
      }
      throw new InternalError(`Failed to confirm payment intent: ${error.message}`);
    }
  }

  private static formatPaymentIntentResponse(
    paymentIntent: Stripe.PaymentIntent,
    transaction?: TransactionDocument
  ): PaymentIntentResponse {
    return {
      payment_intent_id: paymentIntent.id,
      client_secret: paymentIntent.client_secret,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      payment_method: paymentIntent.payment_method ? {
        id: typeof paymentIntent.payment_method === 'string' 
          ? paymentIntent.payment_method 
          : paymentIntent.payment_method.id,
        type: typeof paymentIntent.payment_method === 'object' 
          ? paymentIntent.payment_method.type 
          : undefined,
        card: typeof paymentIntent.payment_method === 'object' && paymentIntent.payment_method.card
          ? paymentIntent.payment_method.card
          : undefined
      } : undefined,
      charges: paymentIntent.charges,
      metadata: paymentIntent.metadata,
      created_at: new Date(paymentIntent.created * 1000),
      confirmed_at: transaction?.confirmed_at
    };
  }
}
```

### 3. Enhanced Stripe Service (40 minutes)
```typescript
// src/services/stripe.service.ts (additions)
export class StripeService {
  static async createPaymentIntent(params: {
    amount: number;
    currency: string;
    customer: string;
    payment_method?: string;
    confirmation_method?: string;
    confirm?: boolean;
    description?: string;
    receipt_email?: string;
    return_url?: string;
    metadata?: Record<string, any>;
  }): Promise<Stripe.PaymentIntent> {
    try {
      return await this.stripe.paymentIntents.create({
        amount: params.amount,
        currency: params.currency,
        customer: params.customer,
        payment_method: params.payment_method,
        confirmation_method: params.confirmation_method || 'manual',
        confirm: params.confirm || false,
        description: params.description,
        receipt_email: params.receipt_email,
        return_url: params.return_url,
        metadata: params.metadata,
        automatic_payment_methods: params.payment_method ? undefined : {
          enabled: true
        }
      });
    } catch (error) {
      throw new InternalError(`Failed to create payment intent: ${error.message}`);
    }
  }

  static async confirmPaymentIntent(
    paymentIntentId: string,
    params?: {
      payment_method?: string;
      return_url?: string;
    }
  ): Promise<Stripe.PaymentIntent> {
    try {
      return await this.stripe.paymentIntents.confirm(paymentIntentId, {
        payment_method: params?.payment_method,
        return_url: params?.return_url
      });
    } catch (error) {
      throw new InternalError(`Failed to confirm payment intent: ${error.message}`);
    }
  }

  static async retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    try {
      return await this.stripe.paymentIntents.retrieve(paymentIntentId, {
        expand: ['payment_method', 'charges.data.balance_transaction']
      });
    } catch (error) {
      throw new InternalError(`Failed to retrieve payment intent: ${error.message}`);
    }
  }

  static async getBalanceTransaction(balanceTransactionId: string): Promise<Stripe.BalanceTransaction> {
    try {
      return await this.stripe.balanceTransactions.retrieve(balanceTransactionId);
    } catch (error) {
      throw new InternalError(`Failed to retrieve balance transaction: ${error.message}`);
    }
  }

  static async cancelPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    try {
      return await this.stripe.paymentIntents.cancel(paymentIntentId);
    } catch (error) {
      throw new InternalError(`Failed to cancel payment intent: ${error.message}`);
    }
  }
}
```

### 4. Controller Implementation (30 minutes)
```typescript
// src/controllers/payment.controller.ts (addition)
export class PaymentController {
  static async createPaymentIntent(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const result = await PaymentService.createPaymentIntent(req.user.org_id, req.body);

      res.status(201).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async confirmPaymentIntent(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      const { payment_method_id } = req.body;
      
      if (!id) {
        throw new BadRequestError('Payment intent ID is required');
      }

      const result = await PaymentService.confirmPaymentIntent(
        req.user.org_id,
        id,
        payment_method_id
      );

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPaymentIntent(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Payment intent ID is required');
      }

      // Find transaction
      const transaction = await TransactionModel.findOne({
        org_id: req.user.org_id,
        stripe_payment_intent_id: id
      });

      if (!transaction) {
        throw new NotFoundError('Payment intent not found');
      }

      // Get latest data from Stripe
      const paymentIntent = await StripeService.retrievePaymentIntent(id);

      const result = PaymentService.formatPaymentIntentResponse(paymentIntent, transaction);

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 5. Validation Schema (25 minutes)
```typescript
// src/validators/payment.validator.ts (addition)
export const createPaymentIntentSchema = Joi.object({
  amount: Joi.number().integer().min(1).max(999999999).required(),
  currency: Joi.string().length(3).lowercase().valid('usd', 'eur', 'gbp', 'cad', 'aud').default('usd'),
  payment_method_id: Joi.string().pattern(/^pm_[a-zA-Z0-9]+$/).optional(),
  confirm: Joi.boolean().optional().default(false),
  description: Joi.string().max(500).optional(),
  receipt_email: Joi.string().email().optional(),
  return_url: Joi.string().uri().optional(),
  metadata: Joi.object().pattern(
    Joi.string(),
    Joi.alternatives().try(Joi.string(), Joi.number())
  ).optional()
});

export const confirmPaymentIntentSchema = Joi.object({
  payment_method_id: Joi.string().pattern(/^pm_[a-zA-Z0-9]+$/).optional()
});
```

### 6. Route Definitions (15 minutes)
```typescript
// src/routes/payment.routes.ts (additions)
router.post(
  '/payment-intents',
  authenticateRequest,
  validateRequestBody(createPaymentIntentSchema),
  rateLimitMiddleware,
  PaymentController.createPaymentIntent
);

router.post(
  '/payment-intents/:id/confirm',
  authenticateRequest,
  validateRequestBody(confirmPaymentIntentSchema),
  rateLimitMiddleware,
  PaymentController.confirmPaymentIntent
);

router.get(
  '/payment-intents/:id',
  authenticateRequest,
  rateLimitMiddleware,
  PaymentController.getPaymentIntent
);
```

### 7. Type Definitions (20 minutes)
```typescript
// src/types/payment.types.ts (additions)
export interface CreatePaymentIntentRequest {
  amount: number;
  currency?: string;
  payment_method_id?: string;
  confirm?: boolean;
  description?: string;
  receipt_email?: string;
  return_url?: string;
  metadata?: Record<string, any>;
}

export interface PaymentIntentResponse {
  payment_intent_id: string;
  client_secret: string | null;
  status: string;
  amount: number;
  currency: string;
  payment_method?: {
    id: string;
    type?: string;
    card?: any;
  };
  charges?: any;
  metadata: Record<string, any>;
  created_at: Date;
  confirmed_at?: Date;
}
```

### 8. Integration Tests (40 minutes)
```typescript
// tests/integration/payment.intent.api.test.ts (new file)
describe('POST /api/payment/v1/payment-intents', () => {
  let authToken: string;
  let orgId: string;

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
  });

  afterEach(async () => {
    await TransactionModel.deleteMany({ org_id: orgId });
  });

  it('should create payment intent successfully', async () => {
    // Setup payment method
    await PaymentMethodModel.create({
      org_id: orgId,
      stripe_customer_id: 'cus_test123',
      stripe_payment_method_id: 'pm_test123',
      type: 'card',
      is_default: true,
      status: 'active',
      billing_details: {
        name: 'Test User',
        email: '<EMAIL>'
      }
    });

    // Mock Stripe calls
    jest.spyOn(StripeService, 'retrieveCustomer').mockResolvedValue({
      id: 'cus_test123'
    } as any);

    jest.spyOn(StripeService, 'createPaymentIntent').mockResolvedValue({
      id: 'pi_test123',
      client_secret: 'pi_test123_secret_abc',
      amount: 2500,
      currency: 'usd',
      status: 'requires_confirmation',
      created: 1628690000,
      metadata: {
        org_id: orgId,
        order_id: 'order_123'
      }
    } as any);

    const paymentData = {
      amount: 2500,
      currency: 'usd',
      description: 'Test payment',
      metadata: {
        order_id: 'order_123'
      }
    };

    const response = await request(app)
      .post('/api/payment/v1/payment-intents')
      .set('Authorization', `Bearer ${authToken}`)
      .send(paymentData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.payment_intent_id).toBe('pi_test123');
    expect(response.body.data.amount).toBe(2500);
    expect(response.body.data.currency).toBe('usd');

    // Verify database record
    const transaction = await TransactionModel.findOne({ org_id: orgId });
    expect(transaction).toBeDefined();
    expect(transaction?.stripe_payment_intent_id).toBe('pi_test123');
  });

  it('should validate payment amount', async () => {
    const invalidData = {
      amount: 0, // Invalid: amount must be greater than 0
      currency: 'usd'
    };

    const response = await request(app)
      .post('/api/payment/v1/payment-intents')
      .set('Authorization', `Bearer ${authToken}`)
      .send(invalidData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });
});
```

## Testing Checklist
- [ ] Unit tests for service layer payment intent creation
- [ ] Unit tests for payment amount validation
- [ ] Unit tests for Stripe integration methods
- [ ] Integration tests for POST endpoint
- [ ] Integration tests for payment confirmation
- [ ] Error handling tests for payment failures
- [ ] Authentication and authorization tests
- [ ] Currency validation tests
- [ ] Payment method ownership validation tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Payment flow documentation
- [ ] Error handling guide
- [ ] Currency support documentation
- [ ] 3D Secure handling guide

This task creates a comprehensive payment intent system with proper Stripe integration, validation, error handling, and transaction tracking.