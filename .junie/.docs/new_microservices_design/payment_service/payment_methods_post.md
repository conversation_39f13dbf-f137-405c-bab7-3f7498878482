# Payment Service - POST /payment-methods (Add Payment Method)

## Task Overview
**Endpoint**: `POST /api/payment/v1/payment-methods`  
**Purpose**: Add a new payment method to an organization with Stripe integration and validation  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Payment service setup, Stripe integration, authentication middleware  

## API Specification

### Request
```http
POST /api/payment/v1/payment-methods
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "payment_method_id": "pm_1234567890abcdef",
  "set_as_default": true,
  "billing_details": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": {
      "city": "San Francisco",
      "country": "US",
      "line1": "123 Main St",
      "line2": "Apt 4B",
      "postal_code": "94102",
      "state": "CA"
    }
  }
}
```

### Response
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012346",
    "stripe_payment_method_id": "pm_1234567890abcdef",
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025,
      "funding": "credit",
      "country": "US"
    },
    "billing_details": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "address": {
        "city": "San Francisco",
        "country": "US",
        "line1": "123 Main St",
        "line2": "Apt 4B",
        "postal_code": "94102",
        "state": "CA"
      }
    },
    "is_default": true,
    "status": "active",
    "created_at": "2024-08-11T15:30:00Z",
    "updated_at": "2024-08-11T15:30:00Z"
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "INVALID_PAYMENT_METHOD",
    "message": "Payment method is invalid or cannot be attached",
    "details": [
      {
        "field": "payment_method_id",
        "message": "This payment method has already been attached to another customer"
      }
    ],
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (60 minutes)
```typescript
// src/services/payment.service.ts (addition)
export class PaymentService {
  static async addPaymentMethod(
    orgId: string,
    paymentMethodData: AddPaymentMethodRequest
  ): Promise<PaymentMethodSummary> {
    try {
      // Validate payment method exists in Stripe
      const stripePaymentMethod = await StripeService.retrievePaymentMethod(
        paymentMethodData.payment_method_id
      );

      // Get or create Stripe customer for organization
      const stripeCustomer = await this.getOrCreateStripeCustomer(orgId, paymentMethodData.billing_details);

      // Attach payment method to customer
      await StripeService.attachPaymentMethodToCustomer(
        paymentMethodData.payment_method_id,
        stripeCustomer.id
      );

      // Check if this should be the default payment method
      if (paymentMethodData.set_as_default) {
        // Remove default flag from other payment methods
        await PaymentMethodModel.updateMany(
          { org_id: orgId, is_default: true },
          { is_default: false, updated_at: new Date() }
        );
      }

      // Create payment method record
      const paymentMethod = new PaymentMethodModel({
        org_id: orgId,
        stripe_customer_id: stripeCustomer.id,
        stripe_payment_method_id: paymentMethodData.payment_method_id,
        type: stripePaymentMethod.type,
        card: stripePaymentMethod.card ? {
          brand: stripePaymentMethod.card.brand,
          last4: stripePaymentMethod.card.last4,
          exp_month: stripePaymentMethod.card.exp_month,
          exp_year: stripePaymentMethod.card.exp_year,
          funding: stripePaymentMethod.card.funding,
          country: stripePaymentMethod.card.country
        } : undefined,
        billing_details: paymentMethodData.billing_details,
        is_default: paymentMethodData.set_as_default || false,
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      });

      await paymentMethod.save();

      // Update Stripe customer's default payment method if needed
      if (paymentMethodData.set_as_default) {
        await StripeService.setDefaultPaymentMethod(
          stripeCustomer.id,
          paymentMethodData.payment_method_id
        );
      }

      return this.formatPaymentMethod(paymentMethod.toObject());
    } catch (error) {
      if (error.type === 'StripeCardError') {
        throw new BadRequestError(error.message, 'INVALID_PAYMENT_METHOD');
      }
      if (error.code === 'resource_already_exists') {
        throw new BadRequestError(
          'This payment method has already been attached to another customer',
          'PAYMENT_METHOD_ALREADY_ATTACHED'
        );
      }
      throw new InternalError(`Failed to add payment method: ${error.message}`);
    }
  }

  private static async getOrCreateStripeCustomer(
    orgId: string,
    billingDetails: BillingDetails
  ): Promise<Stripe.Customer> {
    try {
      // Check if customer already exists for this organization
      let existingMethod = await PaymentMethodModel.findOne({
        org_id: orgId
      }).select('stripe_customer_id');

      if (existingMethod) {
        return await StripeService.retrieveCustomer(existingMethod.stripe_customer_id);
      }

      // Create new customer
      return await StripeService.createCustomer(
        orgId,
        billingDetails.email,
        billingDetails.name
      );
    } catch (error) {
      throw new InternalError(`Failed to get or create Stripe customer: ${error.message}`);
    }
  }

  static async removePaymentMethod(orgId: string, paymentMethodId: string): Promise<void> {
    try {
      // Find payment method
      const paymentMethod = await PaymentMethodModel.findOne({
        _id: paymentMethodId,
        org_id: orgId
      });

      if (!paymentMethod) {
        throw new NotFoundError('Payment method not found');
      }

      // Check if this is the default payment method
      if (paymentMethod.is_default) {
        // Check if there are other payment methods
        const otherMethods = await PaymentMethodModel.find({
          org_id: orgId,
          _id: { $ne: paymentMethodId },
          status: 'active'
        });

        if (otherMethods.length > 0) {
          // Set the first other method as default
          await PaymentMethodModel.updateOne(
            { _id: otherMethods[0]._id },
            { is_default: true, updated_at: new Date() }
          );

          // Update Stripe customer's default payment method
          await StripeService.setDefaultPaymentMethod(
            paymentMethod.stripe_customer_id,
            otherMethods[0].stripe_payment_method_id
          );
        }
      }

      // Detach from Stripe customer
      await StripeService.detachPaymentMethod(paymentMethod.stripe_payment_method_id);

      // Mark as inactive instead of deleting for audit trail
      await PaymentMethodModel.updateOne(
        { _id: paymentMethodId },
        { status: 'inactive', updated_at: new Date() }
      );
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to remove payment method: ${error.message}`);
    }
  }

  static async setDefaultPaymentMethod(orgId: string, paymentMethodId: string): Promise<void> {
    try {
      // Find payment method
      const paymentMethod = await PaymentMethodModel.findOne({
        _id: paymentMethodId,
        org_id: orgId,
        status: 'active'
      });

      if (!paymentMethod) {
        throw new NotFoundError('Payment method not found');
      }

      // Remove default flag from other payment methods
      await PaymentMethodModel.updateMany(
        { org_id: orgId, is_default: true },
        { is_default: false, updated_at: new Date() }
      );

      // Set this payment method as default
      await PaymentMethodModel.updateOne(
        { _id: paymentMethodId },
        { is_default: true, updated_at: new Date() }
      );

      // Update Stripe customer's default payment method
      await StripeService.setDefaultPaymentMethod(
        paymentMethod.stripe_customer_id,
        paymentMethod.stripe_payment_method_id
      );
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to set default payment method: ${error.message}`);
    }
  }
}
```

### 2. Enhanced Stripe Service (30 minutes)
```typescript
// src/services/stripe.service.ts (additions)
export class StripeService {
  static async attachPaymentMethodToCustomer(
    paymentMethodId: string,
    customerId: string
  ): Promise<Stripe.PaymentMethod> {
    try {
      return await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId
      });
    } catch (error) {
      throw new InternalError(`Failed to attach payment method to customer: ${error.message}`);
    }
  }

  static async detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    try {
      return await this.stripe.paymentMethods.detach(paymentMethodId);
    } catch (error) {
      throw new InternalError(`Failed to detach payment method: ${error.message}`);
    }
  }

  static async setDefaultPaymentMethod(
    customerId: string,
    paymentMethodId: string
  ): Promise<Stripe.Customer> {
    try {
      return await this.stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId
        }
      });
    } catch (error) {
      throw new InternalError(`Failed to set default payment method: ${error.message}`);
    }
  }

  static async retrieveCustomer(customerId: string): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);
      if (customer.deleted) {
        throw new NotFoundError('Customer has been deleted');
      }
      return customer as Stripe.Customer;
    } catch (error) {
      throw new InternalError(`Failed to retrieve customer: ${error.message}`);
    }
  }

  static async validatePaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.retrieve(paymentMethodId);
      return paymentMethod && !paymentMethod.customer;
    } catch (error) {
      return false;
    }
  }
}
```

### 3. Controller Implementation (25 minutes)
```typescript
// src/controllers/payment.controller.ts (addition)
export class PaymentController {
  static async addPaymentMethod(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const result = await PaymentService.addPaymentMethod(req.user.org_id, req.body);

      res.status(201).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async removePaymentMethod(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Payment method ID is required');
      }

      await PaymentService.removePaymentMethod(req.user.org_id, id);

      res.json({
        success: true,
        data: {
          message: 'Payment method removed successfully'
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async setDefaultPaymentMethod(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Payment method ID is required');
      }

      await PaymentService.setDefaultPaymentMethod(req.user.org_id, id);

      res.json({
        success: true,
        data: {
          message: 'Default payment method updated successfully'
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 4. Validation Schema (20 minutes)
```typescript
// src/validators/payment.validator.ts
import Joi from 'joi';

export const addPaymentMethodSchema = Joi.object({
  payment_method_id: Joi.string().required().pattern(/^pm_[a-zA-Z0-9]+$/),
  set_as_default: Joi.boolean().optional().default(false),
  billing_details: Joi.object({
    name: Joi.string().required().min(1).max(100),
    email: Joi.string().email().required(),
    phone: Joi.string().optional().pattern(/^\+?[1-9]\d{1,14}$/),
    address: Joi.object({
      city: Joi.string().optional().max(50),
      country: Joi.string().optional().length(2).uppercase(),
      line1: Joi.string().optional().max(100),
      line2: Joi.string().optional().max(100),
      postal_code: Joi.string().optional().max(20),
      state: Joi.string().optional().max(50)
    }).optional()
  }).required()
});
```

### 5. Route Definitions (15 minutes)
```typescript
// src/routes/payment.routes.ts (additions)
import { validateRequestBody } from '@/middleware/validation.middleware';
import { addPaymentMethodSchema } from '@/validators/payment.validator';

router.post(
  '/payment-methods',
  authenticateRequest,
  validateRequestBody(addPaymentMethodSchema),
  rateLimitMiddleware,
  PaymentController.addPaymentMethod
);

router.delete(
  '/payment-methods/:id',
  authenticateRequest,
  validateObjectId('id'),
  rateLimitMiddleware,
  PaymentController.removePaymentMethod
);

router.put(
  '/payment-methods/:id/default',
  authenticateRequest,
  validateObjectId('id'),
  rateLimitMiddleware,
  PaymentController.setDefaultPaymentMethod
);
```

### 6. Type Definitions (15 minutes)
```typescript
// src/types/payment.types.ts (additions)
export interface AddPaymentMethodRequest {
  payment_method_id: string;
  set_as_default?: boolean;
  billing_details: BillingDetails;
}

export interface BillingDetails {
  name: string;
  email: string;
  phone?: string;
  address?: {
    city?: string;
    country?: string;
    line1?: string;
    line2?: string;
    postal_code?: string;
    state?: string;
  };
}
```

### 7. Unit Tests (45 minutes)
```typescript
// tests/unit/services/payment.service.test.ts (addition)
describe('PaymentService.addPaymentMethod', () => {
  let mockStripePaymentMethod: any;
  let mockStripeCustomer: any;

  beforeEach(() => {
    mockStripePaymentMethod = {
      id: 'pm_test123',
      type: 'card',
      card: {
        brand: 'visa',
        last4: '4242',
        exp_month: 12,
        exp_year: 2025,
        funding: 'credit',
        country: 'US'
      }
    };

    mockStripeCustomer = {
      id: 'cus_test123',
      email: '<EMAIL>'
    };
  });

  it('should add payment method successfully', async () => {
    (StripeService.retrievePaymentMethod as jest.Mock).mockResolvedValue(mockStripePaymentMethod);
    (PaymentService.getOrCreateStripeCustomer as any).mockResolvedValue(mockStripeCustomer);
    (StripeService.attachPaymentMethodToCustomer as jest.Mock).mockResolvedValue(mockStripePaymentMethod);
    (PaymentMethodModel.prototype.save as jest.Mock).mockResolvedValue({
      toObject: () => ({
        _id: 'test-id',
        stripe_payment_method_id: 'pm_test123',
        type: 'card',
        card: mockStripePaymentMethod.card,
        is_default: true
      })
    });

    const paymentMethodData = {
      payment_method_id: 'pm_test123',
      set_as_default: true,
      billing_details: {
        name: 'John Doe',
        email: '<EMAIL>'
      }
    };

    const result = await PaymentService.addPaymentMethod('org123', paymentMethodData);

    expect(result).toBeDefined();
    expect(result.stripe_payment_method_id).toBe('pm_test123');
    expect(result.is_default).toBe(true);
    expect(StripeService.retrievePaymentMethod).toHaveBeenCalledWith('pm_test123');
    expect(StripeService.attachPaymentMethodToCustomer).toHaveBeenCalledWith('pm_test123', 'cus_test123');
  });

  it('should handle Stripe card errors', async () => {
    const stripeError = new Error('Your card was declined');
    stripeError.type = 'StripeCardError';
    
    (StripeService.retrievePaymentMethod as jest.Mock).mockRejectedValue(stripeError);

    const paymentMethodData = {
      payment_method_id: 'pm_test123',
      billing_details: {
        name: 'John Doe',
        email: '<EMAIL>'
      }
    };

    await expect(
      PaymentService.addPaymentMethod('org123', paymentMethodData)
    ).rejects.toThrow('Your card was declined');
  });

  it('should handle already attached payment method', async () => {
    const stripeError = new Error('Already attached');
    stripeError.code = 'resource_already_exists';
    
    (StripeService.retrievePaymentMethod as jest.Mock).mockResolvedValue(mockStripePaymentMethod);
    (PaymentService.getOrCreateStripeCustomer as any).mockResolvedValue(mockStripeCustomer);
    (StripeService.attachPaymentMethodToCustomer as jest.Mock).mockRejectedValue(stripeError);

    const paymentMethodData = {
      payment_method_id: 'pm_test123',
      billing_details: {
        name: 'John Doe',
        email: '<EMAIL>'
      }
    };

    await expect(
      PaymentService.addPaymentMethod('org123', paymentMethodData)
    ).rejects.toThrow('This payment method has already been attached to another customer');
  });
});
```

### 8. Integration Tests (35 minutes)
```typescript
// tests/integration/payment.api.test.ts (addition)
describe('POST /api/payment/v1/payment-methods', () => {
  let authToken: string;
  let orgId: string;

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
  });

  afterEach(async () => {
    await PaymentMethodModel.deleteMany({ org_id: orgId });
  });

  it('should add payment method successfully', async () => {
    const paymentMethodData = {
      payment_method_id: 'pm_test123',
      set_as_default: true,
      billing_details: {
        name: 'John Doe',
        email: '<EMAIL>',
        address: {
          city: 'San Francisco',
          country: 'US',
          line1: '123 Main St',
          postal_code: '94102',
          state: 'CA'
        }
      }
    };

    // Mock Stripe calls
    jest.spyOn(StripeService, 'retrievePaymentMethod').mockResolvedValue({
      id: 'pm_test123',
      type: 'card',
      card: {
        brand: 'visa',
        last4: '4242',
        exp_month: 12,
        exp_year: 2025,
        funding: 'credit',
        country: 'US'
      }
    } as any);

    jest.spyOn(StripeService, 'createCustomer').mockResolvedValue({
      id: 'cus_test123'
    } as any);

    jest.spyOn(StripeService, 'attachPaymentMethodToCustomer').mockResolvedValue({} as any);
    jest.spyOn(StripeService, 'setDefaultPaymentMethod').mockResolvedValue({} as any);

    const response = await request(app)
      .post('/api/payment/v1/payment-methods')
      .set('Authorization', `Bearer ${authToken}`)
      .send(paymentMethodData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.stripe_payment_method_id).toBe('pm_test123');
    expect(response.body.data.is_default).toBe(true);
    expect(response.body.data.billing_details.name).toBe('John Doe');

    // Verify database record
    const savedMethod = await PaymentMethodModel.findOne({ org_id: orgId });
    expect(savedMethod).toBeDefined();
    expect(savedMethod?.stripe_payment_method_id).toBe('pm_test123');
    expect(savedMethod?.is_default).toBe(true);
  });

  it('should validate request body', async () => {
    const invalidData = {
      payment_method_id: 'invalid-id', // Should start with pm_
      billing_details: {
        name: '', // Required and should not be empty
        email: 'invalid-email' // Should be valid email
      }
    };

    const response = await request(app)
      .post('/api/payment/v1/payment-methods')
      .set('Authorization', `Bearer ${authToken}`)
      .send(invalidData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('VALIDATION_ERROR');
    expect(response.body.error.details).toHaveLength(3);
  });

  it('should return 401 without authentication', async () => {
    await request(app)
      .post('/api/payment/v1/payment-methods')
      .send({})
      .expect(401);
  });
});
```

## Error Handling Scenarios

### 1. Payment Method Validation
- Invalid payment method ID format
- Payment method doesn't exist in Stripe
- Payment method already attached to another customer
- Card declined or invalid

### 2. Customer Management
- Failed to create Stripe customer
- Customer deletion/deactivation conflicts
- Multiple customers for same organization

### 3. Default Payment Method Logic
- Setting default when no other methods exist
- Removing default method and reassigning
- Stripe sync failures

## Security Considerations

### 1. Payment Method Security
- Validate payment method ownership
- Secure Stripe API key handling
- PCI compliance considerations
- Audit trail for payment method changes

### 2. Organization Isolation
- Ensure payment methods are isolated by org_id
- Prevent cross-organization access
- Validate user permissions for payment operations

## Testing Checklist
- [ ] Unit tests for service layer payment method addition
- [ ] Unit tests for Stripe integration methods
- [ ] Integration tests for POST endpoint
- [ ] Validation tests for request schema
- [ ] Error handling tests for Stripe errors
- [ ] Authentication and authorization tests
- [ ] Default payment method logic tests
- [ ] Payment method removal tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Stripe integration guide
- [ ] Payment method lifecycle documentation
- [ ] Error handling guide
- [ ] Security considerations documentation

This task creates a comprehensive payment method addition system with proper Stripe integration, validation, error handling, and security considerations.