# Payment Service - GET /usage (Usage Tracking and Analytics)

## Task Overview
**Endpoint**: `GET /api/payment/v1/usage`  
**Purpose**: Track and retrieve usage analytics for billing, monitoring, and optimization  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Message tracking, campaign analytics, billing system  

## API Specification

### List Usage Metrics Request
```http
GET /api/payment/v1/usage?service=sms&period=current_month&granularity=daily
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Service-specific Usage Request
```http
GET /api/payment/v1/usage/sms?start_date=2024-07-01&end_date=2024-07-31
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Usage Summary Request
```http
GET /api/payment/v1/usage/summary?period=current_month
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Query Parameters
- `service` (string): Filter by service type (sms, email, push, voice, all)
- `period` (string): Time period (current_month, last_month, current_year, custom)
- `start_date` (string): Start date for custom period (ISO 8601)
- `end_date` (string): End date for custom period (ISO 8601)
- `granularity` (string): Data granularity (hourly, daily, weekly, monthly)
- `campaign_id` (string): Filter by specific campaign
- `message_type` (string): Filter by message type (promotional, transactional, otp)
- `status` (string): Filter by delivery status (sent, delivered, failed, pending)
- `breakdown` (string): Group by field (campaign, message_type, destination_country)
- `include_cost` (boolean): Include cost calculations (default: false)

### Response - Usage Metrics
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "period": {
      "start": "2024-07-01T00:00:00Z",
      "end": "2024-07-31T23:59:59Z",
      "granularity": "daily"
    },
    "summary": {
      "total_messages": 125000,
      "total_cost": 2750.50,
      "services": {
        "sms": {
          "count": 75000,
          "cost": 1875.00,
          "success_rate": 98.5
        },
        "email": {
          "count": 45000,
          "cost": 675.00,
          "success_rate": 99.2
        },
        "push": {
          "count": 5000,
          "cost": 200.50,
          "success_rate": 95.8
        }
      }
    },
    "usage_data": [
      {
        "date": "2024-07-01",
        "services": {
          "sms": {
            "sent": 2500,
            "delivered": 2463,
            "failed": 37,
            "cost": 62.50,
            "breakdown": {
              "promotional": 1800,
              "transactional": 650,
              "otp": 50
            },
            "destinations": {
              "US": 1200,
              "CA": 300,
              "UK": 500,
              "others": 500
            }
          },
          "email": {
            "sent": 1500,
            "delivered": 1488,
            "failed": 12,
            "cost": 22.50,
            "breakdown": {
              "promotional": 1200,
              "transactional": 300
            }
          },
          "push": {
            "sent": 200,
            "delivered": 192,
            "failed": 8,
            "cost": 8.00
          }
        },
        "total": {
          "sent": 4200,
          "delivered": 4143,
          "failed": 57,
          "cost": 93.00,
          "success_rate": 98.6
        }
      }
    ],
    "trends": {
      "growth_rate": 12.5,
      "cost_efficiency": 95.2,
      "peak_usage_hour": 14,
      "peak_usage_day": "tuesday"
    },
    "limits": {
      "current_plan": "Business",
      "monthly_limit": 200000,
      "used": 125000,
      "remaining": 75000,
      "usage_percentage": 62.5,
      "overage_cost": 0.00
    }
  },
  "pagination": {
    "current_page": 1,
    "total_pages": 31,
    "total_items": 31,
    "items_per_page": 1
  }
}
```

### Response - Usage Summary
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "period": {
      "start": "2024-07-01T00:00:00Z",
      "end": "2024-07-31T23:59:59Z"
    },
    "summary": {
      "total_messages": 125000,
      "total_cost": 2750.50,
      "average_cost_per_message": 0.022,
      "success_rate": 98.3
    },
    "by_service": [
      {
        "service": "sms",
        "count": 75000,
        "cost": 1875.00,
        "percentage": 60.0,
        "success_rate": 98.5
      },
      {
        "service": "email", 
        "count": 45000,
        "cost": 675.00,
        "percentage": 36.0,
        "success_rate": 99.2
      },
      {
        "service": "push",
        "count": 5000,
        "cost": 200.50,
        "percentage": 4.0,
        "success_rate": 95.8
      }
    ],
    "by_message_type": [
      {
        "type": "promotional",
        "count": 85000,
        "cost": 1912.50,
        "percentage": 68.0
      },
      {
        "type": "transactional",
        "count": 35000,
        "cost": 787.50,
        "percentage": 28.0
      },
      {
        "type": "otp",
        "count": 5000,
        "cost": 50.50,
        "percentage": 4.0
      }
    ],
    "top_campaigns": [
      {
        "campaign_id": "camp_64a1b2c3d4e5f6789012345",
        "campaign_name": "Summer Sale 2024",
        "messages_sent": 25000,
        "cost": 562.50,
        "success_rate": 98.8
      },
      {
        "campaign_id": "camp_64a1b2c3d4e5f6789012346",
        "campaign_name": "Newsletter July",
        "messages_sent": 20000,
        "cost": 300.00,
        "success_rate": 99.1
      }
    ],
    "cost_breakdown": {
      "base_subscription": 199.99,
      "message_costs": 2550.51,
      "overage_charges": 0.00,
      "taxes": 204.04,
      "total": 2954.54
    },
    "projections": {
      "end_of_month_estimate": {
        "messages": 130000,
        "cost": 2860.00
      },
      "monthly_trend": "increasing",
      "cost_optimization_suggestions": [
        "Consider upgrading to Professional plan for better rates",
        "Optimize SMS sending times to reduce failures",
        "Use email for non-urgent promotional content"
      ]
    }
  }
}
```

## Request/Response Examples

### Example 1: Get Current Month SMS Usage
**Request:**
```http
GET /api/payment/v1/usage?service=sms&period=current_month&granularity=weekly&include_cost=true
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
X-Organization-ID: org_64a1b2c3d4e5f6789012345
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": {
      "start": "2024-07-01T00:00:00Z",
      "end": "2024-07-31T23:59:59Z",
      "granularity": "weekly"
    },
    "usage_data": [
      {
        "week": "2024-W27",
        "date_range": {
          "start": "2024-07-01T00:00:00Z",
          "end": "2024-07-07T23:59:59Z"
        },
        "sms": {
          "sent": 18750,
          "delivered": 18469,
          "failed": 281,
          "cost": 468.75,
          "success_rate": 98.5
        }
      },
      {
        "week": "2024-W28",
        "date_range": {
          "start": "2024-07-08T00:00:00Z",
          "end": "2024-07-14T23:59:59Z"
        },
        "sms": {
          "sent": 19200,
          "delivered": 18912,
          "failed": 288,
          "cost": 480.00,
          "success_rate": 98.5
        }
      }
    ]
  }
}
```

### Example 2: Get Usage with Campaign Breakdown
**Request:**
```http
GET /api/payment/v1/usage?breakdown=campaign&period=last_month&limit=5
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
X-Organization-ID: org_64a1b2c3d4e5f6789012345
```

**Response:**
```json
{
  "success": true,
  "data": {
    "breakdown_by": "campaign",
    "period": {
      "start": "2024-06-01T00:00:00Z",
      "end": "2024-06-30T23:59:59Z"
    },
    "campaigns": [
      {
        "campaign_id": "camp_64a1b2c3d4e5f6789012345",
        "campaign_name": "Summer Sale Launch",
        "services": {
          "sms": {
            "sent": 15000,
            "cost": 375.00
          },
          "email": {
            "sent": 10000,
            "cost": 150.00
          }
        },
        "total_cost": 525.00,
        "success_rate": 98.7
      }
    ]
  }
}
```

## Error Responses

### 400 Bad Request - Invalid Parameters
```json
{
  "success": false,
  "error": {
    "code": "USAGE_001",
    "message": "Invalid usage query parameters",
    "details": [
      {
        "field": "period",
        "message": "Period must be one of: current_month, last_month, current_year, custom"
      },
      {
        "field": "granularity",
        "message": "Granularity must be one of: hourly, daily, weekly, monthly"
      }
    ]
  }
}
```

### 401 Unauthorized - Invalid Token
```json
{
  "success": false,
  "error": {
    "code": "USAGE_002",
    "message": "Invalid or expired authentication token",
    "details": "Please provide a valid JWT token in the Authorization header"
  }
}
```

### 403 Forbidden - Insufficient Permissions
```json
{
  "success": false,
  "error": {
    "code": "USAGE_003",
    "message": "Insufficient permissions to access usage data",
    "details": "User does not have permission to view usage analytics for this organization"
  }
}
```

### 422 Unprocessable Entity - Invalid Date Range
```json
{
  "success": false,
  "error": {
    "code": "USAGE_004",
    "message": "Invalid date range specified",
    "details": [
      {
        "field": "start_date",
        "message": "Start date cannot be in the future"
      },
      {
        "field": "end_date", 
        "message": "End date must be after start date"
      }
    ]
  }
}
```

### 500 Internal Server Error - System Error
```json
{
  "success": false,
  "error": {
    "code": "USAGE_005",
    "message": "Failed to retrieve usage data",
    "details": "An internal error occurred while processing your request. Please try again later."
  }
}
```

## Technical Implementation

### Data Sources
- **Message Service**: Real-time message delivery status
- **Campaign Service**: Campaign performance metrics
- **Billing System**: Cost calculations and pricing tiers
- **Analytics Database**: Aggregated usage statistics

### Caching Strategy
- Cache usage summaries for 15 minutes
- Cache detailed breakdowns for 5 minutes
- Real-time data for current day usage
- Historical data cached for 1 hour

### Performance Considerations
- Use materialized views for common queries
- Implement query result pagination
- Background jobs for complex aggregations
- Redis caching for frequently accessed data

### Security Requirements
- JWT token validation
- Organization-level data isolation
- Role-based access control for cost data
- Rate limiting: 100 requests per minute per user

### Database Schema References
- **UsageMetrics**: Message delivery and cost tracking
- **CampaignAnalytics**: Campaign performance data
- **BillingEvents**: Cost calculation events
- **ServiceLimits**: Plan limits and overages

### Queue Integration
- **usage.calculate**: Background usage calculation
- **billing.update**: Real-time billing updates
- **analytics.aggregate**: Daily/weekly aggregations

## Testing Requirements

### Unit Tests
- Usage data aggregation logic
- Cost calculation accuracy
- Date range validation
- Permission checks

### Integration Tests
- End-to-end usage tracking flow
- Cross-service data consistency
- Real-time vs cached data accuracy
- Performance under load

### Test Data
- Mock usage data for different services
- Various date ranges and granularities
- Different organization sizes and plans
- Edge cases for data aggregation

## Dependencies

### Internal Services
- **Message Service**: Delivery status and metrics
- **Campaign Service**: Campaign performance data
- **Core Service**: Organization and user data
- **Payment Service**: Billing and subscription data

### External Services
- **Analytics Database**: Usage data storage
- **Redis**: Caching and real-time data
- **Background Jobs**: Data aggregation tasks

### Configuration
- Usage tracking intervals
- Cache TTL settings
- Rate limiting configurations
- Cost calculation parameters

## Monitoring and Logging

### Key Metrics
- Query response times
- Data freshness accuracy
- Cache hit rates
- Error rates by endpoint

### Alerts
- Usage data calculation failures
- High query response times (>2 seconds)
- Cache miss rates >20%
- Data inconsistencies between services

### Logging
- All usage queries with parameters
- Cost calculation processes
- Data aggregation job results
- Performance metrics and timings