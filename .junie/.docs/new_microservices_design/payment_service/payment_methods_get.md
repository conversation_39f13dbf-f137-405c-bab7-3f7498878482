# Payment Service - GET /payment-methods (Get Payment Methods)

## Task Overview
**Endpoint**: `GET /api/payment/v1/payment-methods`  
**Purpose**: Retrieve all payment methods for an organization with status and metadata  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Payment service setup, Stripe integration, authentication middleware  

## API Specification

### Request
```http
GET /api/payment/v1/payment-methods
Authorization: Bearer <jwt_token>
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "payment_methods": [
      {
        "_id": "64a1b2c3d4e5f6789012346",
        "stripe_payment_method_id": "pm_1234567890abcdef",
        "type": "card",
        "card": {
          "brand": "visa",
          "last4": "4242",
          "exp_month": 12,
          "exp_year": 2025,
          "funding": "credit",
          "country": "US"
        },
        "billing_details": {
          "name": "<PERSON>",
          "email": "<EMAIL>",
          "address": {
            "city": "San Francisco",
            "country": "US",
            "line1": "123 Main St",
            "postal_code": "94102",
            "state": "CA"
          }
        },
        "is_default": true,
        "status": "active",
        "created_at": "2024-08-11T15:30:00Z",
        "updated_at": "2024-08-11T15:30:00Z"
      }
    ],
    "default_payment_method": "64a1b2c3d4e5f6789012346"
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 404 Not Found
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "NO_PAYMENT_METHODS",
    "message": "No payment methods found for this organization",
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Database Model (25 minutes)
```typescript
// src/lib/db/models/payment.method.model.ts
import { Schema, model, Document } from 'mongoose';

export interface PaymentMethodDocument extends Document {
  org_id: string;
  stripe_customer_id: string;
  stripe_payment_method_id: string;
  type: 'card' | 'bank_account' | 'sepa_debit';
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
    funding: string;
    country: string;
  };
  billing_details: {
    name: string;
    email: string;
    phone?: string;
    address: {
      city?: string;
      country?: string;
      line1?: string;
      line2?: string;
      postal_code?: string;
      state?: string;
    };
  };
  is_default: boolean;
  status: 'active' | 'inactive' | 'expired';
  created_at: Date;
  updated_at: Date;
}

const paymentMethodSchema = new Schema<PaymentMethodDocument>({
  org_id: { type: String, required: true, index: true },
  stripe_customer_id: { type: String, required: true, index: true },
  stripe_payment_method_id: { type: String, required: true, unique: true },
  type: {
    type: String,
    enum: ['card', 'bank_account', 'sepa_debit'],
    required: true
  },
  card: {
    brand: String,
    last4: String,
    exp_month: Number,
    exp_year: Number,
    funding: String,
    country: String
  },
  billing_details: {
    name: { type: String, required: true },
    email: { type: String, required: true },
    phone: String,
    address: {
      city: String,
      country: String,
      line1: String,
      line2: String,
      postal_code: String,
      state: String
    }
  },
  is_default: { type: Boolean, default: false },
  status: {
    type: String,
    enum: ['active', 'inactive', 'expired'],
    default: 'active'
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

// Indexes
paymentMethodSchema.index({ org_id: 1, is_default: 1 });
paymentMethodSchema.index({ stripe_customer_id: 1 });
paymentMethodSchema.index({ status: 1, created_at: -1 });

export const PaymentMethodModel = model<PaymentMethodDocument>('PaymentMethod', paymentMethodSchema);
```

### 2. Service Layer (40 minutes)
```typescript
// src/services/payment.service.ts
import { PaymentMethodModel } from '@/lib/db/models/payment.method.model';
import { StripeService } from '@/services/stripe.service';
import { PaymentMethodResponse } from '@/types/payment.types';

export class PaymentService {
  static async getPaymentMethods(orgId: string): Promise<PaymentMethodResponse> {
    try {
      // Get payment methods from database
      const paymentMethods = await PaymentMethodModel.find({
        org_id: orgId,
        status: 'active'
      }).sort({ created_at: -1 }).lean();

      if (!paymentMethods || paymentMethods.length === 0) {
        return {
          payment_methods: [],
          default_payment_method: null
        };
      }

      // Sync with Stripe to ensure data is up to date
      const syncedMethods = await this.syncWithStripe(paymentMethods);

      // Find default payment method
      const defaultMethod = syncedMethods.find(method => method.is_default);

      return {
        payment_methods: syncedMethods.map(this.formatPaymentMethod),
        default_payment_method: defaultMethod?._id.toString() || null
      };
    } catch (error) {
      throw new InternalError(`Failed to retrieve payment methods: ${error.message}`);
    }
  }

  private static async syncWithStripe(paymentMethods: any[]): Promise<any[]> {
    const syncedMethods = [];
    
    for (const method of paymentMethods) {
      try {
        // Get latest data from Stripe
        const stripeMethod = await StripeService.retrievePaymentMethod(
          method.stripe_payment_method_id
        );
        
        // Update local data if needed
        if (this.needsUpdate(method, stripeMethod)) {
          const updated = await PaymentMethodModel.findByIdAndUpdate(
            method._id,
            {
              card: stripeMethod.card,
              billing_details: stripeMethod.billing_details,
              updated_at: new Date()
            },
            { new: true }
          ).lean();
          
          syncedMethods.push(updated);
        } else {
          syncedMethods.push(method);
        }
      } catch (stripeError) {
        // If payment method doesn't exist in Stripe, mark as inactive
        await PaymentMethodModel.updateOne(
          { _id: method._id },
          { status: 'inactive', updated_at: new Date() }
        );
        console.warn(`Payment method ${method.stripe_payment_method_id} not found in Stripe`);
      }
    }
    
    return syncedMethods;
  }

  private static needsUpdate(localMethod: any, stripeMethod: any): boolean {
    if (!localMethod.card || !stripeMethod.card) return false;
    
    return (
      localMethod.card.exp_month !== stripeMethod.card.exp_month ||
      localMethod.card.exp_year !== stripeMethod.card.exp_year ||
      localMethod.billing_details.name !== stripeMethod.billing_details.name
    );
  }

  private static formatPaymentMethod(method: any): any {
    return {
      _id: method._id.toString(),
      stripe_payment_method_id: method.stripe_payment_method_id,
      type: method.type,
      card: method.card,
      billing_details: method.billing_details,
      is_default: method.is_default,
      status: method.status,
      created_at: method.created_at,
      updated_at: method.updated_at
    };
  }
}
```

### 3. Controller Implementation (20 minutes)
```typescript
// src/controllers/payment.controller.ts
export class PaymentController {
  static async getPaymentMethods(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const result = await PaymentService.getPaymentMethods(req.user.org_id);

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 4. Stripe Service Integration (35 minutes)
```typescript
// src/services/stripe.service.ts
import Stripe from 'stripe';
import config from '@/lib/config';

export class StripeService {
  private static stripe = new Stripe(config.stripe.secretKey, {
    apiVersion: '2023-10-16'
  });

  static async retrievePaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    try {
      return await this.stripe.paymentMethods.retrieve(paymentMethodId);
    } catch (error) {
      throw new InternalError(`Failed to retrieve payment method from Stripe: ${error.message}`);
    }
  }

  static async listCustomerPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
    try {
      const result = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: 'card'
      });
      
      return result.data;
    } catch (error) {
      throw new InternalError(`Failed to list customer payment methods: ${error.message}`);
    }
  }

  static async createCustomer(orgId: string, email: string, name: string): Promise<Stripe.Customer> {
    try {
      return await this.stripe.customers.create({
        email,
        name,
        metadata: {
          org_id: orgId
        }
      });
    } catch (error) {
      throw new InternalError(`Failed to create Stripe customer: ${error.message}`);
    }
  }
}
```

### 5. Route Definition (10 minutes)
```typescript
// src/routes/payment.routes.ts
import { Router } from 'express';
import { PaymentController } from '@/controllers/payment.controller';
import { authenticateRequest } from '@/middleware/auth.middleware';
import { rateLimitMiddleware } from '@/middleware/rate.limit.middleware';

const router = Router();

router.get(
  '/payment-methods',
  authenticateRequest,
  rateLimitMiddleware,
  PaymentController.getPaymentMethods
);

export default router;
```

### 6. Type Definitions (15 minutes)
```typescript
// src/types/payment.types.ts
export interface PaymentMethodResponse {
  payment_methods: PaymentMethodSummary[];
  default_payment_method: string | null;
}

export interface PaymentMethodSummary {
  _id: string;
  stripe_payment_method_id: string;
  type: 'card' | 'bank_account' | 'sepa_debit';
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
    funding: string;
    country: string;
  };
  billing_details: {
    name: string;
    email: string;
    phone?: string;
    address: {
      city?: string;
      country?: string;
      line1?: string;
      line2?: string;
      postal_code?: string;
      state?: string;
    };
  };
  is_default: boolean;
  status: 'active' | 'inactive' | 'expired';
  created_at: Date;
  updated_at: Date;
}
```

## Testing Checklist
- [ ] Unit tests for service layer payment method retrieval
- [ ] Unit tests for Stripe integration
- [ ] Integration tests for GET endpoint
- [ ] Authentication tests
- [ ] Error handling tests
- [ ] Stripe webhook validation tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Stripe integration documentation
- [ ] Payment method lifecycle documentation