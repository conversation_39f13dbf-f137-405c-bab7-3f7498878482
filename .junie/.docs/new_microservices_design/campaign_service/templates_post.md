# Campaign Service - POST /templates (Create Template)

## Task Overview
**Endpoint**: `POST /api/campaign/v1/templates`  
**Purpose**: Create reusable campaign templates for quick campaign creation and standardized messaging  
**Estimated Time**: 1.5 days  
**Priority**: Medium  
**Dependencies**: Campaign service setup, validation schemas, file storage integration  

## API Specification

### Request
```http
POST /api/campaign/v1/templates
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>

{
  "template_data": {
    "name": "Black Friday Sale Template",
    "description": "Standard template for Black Friday promotional campaigns",
    "category": "PROMOTIONAL",
    "tags": ["sale", "promotion", "seasonal"],
    "is_public": false,
    "template_type": "CAMPAIGN"
  },
  "campaign_config": {
    "type": "BROADCAST",
    "channels": {
      "sms": {
        "enabled": true,
        "content": "🔥 BLACK FRIDAY: {{discount}}% OFF everything! Use code {{code}}. Shop now: {{shop_url}}",
        "template_variables": [
          {
            "name": "discount",
            "type": "number",
            "description": "Discount percentage",
            "default_value": "50",
            "required": true
          },
          {
            "name": "code",
            "type": "string",
            "description": "Promo code",
            "default_value": "BLACKFRIDAY",
            "required": true
          },
          {
            "name": "shop_url",
            "type": "url",
            "description": "Shopping website URL",
            "required": true
          }
        ],
        "character_count": 89,
        "estimated_sms_parts": 1
      },
      "email": {
        "enabled": true,
        "subject": "🔥 BLACK FRIDAY: {{discount}}% OFF Everything!",
        "content": "<!DOCTYPE html><html><body><h1>Black Friday Sale</h1><p>Don't miss our biggest sale of the year...</p></body></html>",
        "template_variables": [
          {
            "name": "discount",
            "type": "number",
            "description": "Discount percentage",
            "default_value": "50",
            "required": true
          }
        ],
        "content_type": "html",
        "preview_text": "Biggest sale of the year - {{discount}}% off everything!"
      }
    },
    "target_config": {
      "type": "SEGMENTS",
      "default_filters": {
        "exclude_unsubscribed": true,
        "exclude_bounced": true,
        "last_activity_days": 90
      }
    },
    "schedule_config": {
      "default_timezone": "UTC",
      "recommended_send_times": ["09:00", "12:00", "15:00"]
    }
  },
  "design_assets": {
    "thumbnail": "data:image/jpeg;base64,/9j/4AAQSkZJRgABA...",
    "email_header_image": "https://cdn.example.com/templates/blackfriday/header.jpg",
    "social_preview": "https://cdn.example.com/templates/blackfriday/social.jpg"
  },
  "usage_settings": {
    "usage_limit": 100,
    "expiry_date": "2024-12-01T00:00:00Z",
    "allowed_modifications": ["content", "variables", "schedule"],
    "restrict_to_teams": []
  }
}
```

### Response
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "template_id": "tpl_64a1b2c3d4e5f6789012346",
    "name": "Black Friday Sale Template",
    "description": "Standard template for Black Friday promotional campaigns",
    "category": "PROMOTIONAL",
    "template_type": "CAMPAIGN",
    "status": "ACTIVE",
    "version": "1.0.0",
    "usage_stats": {
      "times_used": 0,
      "campaigns_created": 0,
      "last_used": null
    },
    "channels": {
      "sms": {
        "enabled": true,
        "character_count": 89,
        "estimated_sms_parts": 1,
        "variables_count": 3
      },
      "email": {
        "enabled": true,
        "content_type": "html",
        "variables_count": 1,
        "has_images": true
      }
    },
    "asset_urls": {
      "thumbnail": "https://cdn.example.com/templates/tpl_64a1b2c3d4e5f6789012346/thumbnail.jpg",
      "email_header": "https://cdn.example.com/templates/tpl_64a1b2c3d4e5f6789012346/header.jpg",
      "social_preview": "https://cdn.example.com/templates/tpl_64a1b2c3d4e5f6789012346/social.jpg"
    },
    "estimated_cost_per_use": {
      "sms_per_recipient": 0.05,
      "email_per_recipient": 0.01,
      "setup_cost": 0.00
    },
    "created_by": {
      "user_id": "usr_64a1b2c3d4e5f6789012345",
      "name": "Marketing Team",
      "email": "<EMAIL>"
    },
    "created_at": "2024-08-11T15:30:00Z",
    "updated_at": "2024-08-11T15:30:00Z"
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Implementation Tasks

### 1. Database Model (45 minutes)
```typescript
// src/lib/db/models/template.model.ts
import { Schema, model } from 'mongoose';

const templateVariableSchema = new Schema({
  name: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['string', 'number', 'url', 'date', 'boolean'],
    required: true 
  },
  description: { type: String, required: true },
  default_value: String,
  required: { type: Boolean, default: false },
  validation_rules: {
    min_length: Number,
    max_length: Number,
    pattern: String,
    min_value: Number,
    max_value: Number
  }
});

const templateSchema = new Schema({
  org_id: { type: String, required: true, index: true },
  template_id: { type: String, required: true, unique: true },
  created_by: { type: String, required: true, index: true },
  
  // Template metadata
  name: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 255
  },
  description: { 
    type: String, 
    trim: true,
    maxlength: 1000
  },
  category: { 
    type: String, 
    enum: ['PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'REMINDER', 'SEASONAL', 'OTHER'],
    required: true
  },
  tags: [{ type: String, trim: true }],
  template_type: { 
    type: String, 
    enum: ['CAMPAIGN', 'MESSAGE', 'AUTOMATION'],
    default: 'CAMPAIGN'
  },
  status: { 
    type: String, 
    enum: ['DRAFT', 'ACTIVE', 'ARCHIVED', 'DEPRECATED'],
    default: 'DRAFT'
  },
  version: { type: String, default: '1.0.0' },
  is_public: { type: Boolean, default: false },
  
  // Campaign configuration
  campaign_config: {
    type: { 
      type: String, 
      enum: ['BROADCAST', 'AUTOMATED', 'TRIGGERED'],
      required: true 
    },
    channels: {
      sms: {
        enabled: { type: Boolean, default: false },
        content: String,
        template_variables: [templateVariableSchema],
        character_count: Number,
        estimated_sms_parts: Number,
        sender_id_template: String
      },
      email: {
        enabled: { type: Boolean, default: false },
        subject: String,
        content: String,
        template_variables: [templateVariableSchema],
        content_type: { 
          type: String, 
          enum: ['html', 'text'],
          default: 'html'
        },
        preview_text: String,
        sender_email_template: String
      },
      push: {
        enabled: { type: Boolean, default: false },
        title: String,
        content: String,
        template_variables: [templateVariableSchema],
        action_url_template: String
      }
    },
    target_config: {
      type: { 
        type: String, 
        enum: ['ALL_CONTACTS', 'SEGMENTS', 'INDIVIDUAL'],
        default: 'SEGMENTS'
      },
      default_filters: Schema.Types.Mixed
    },
    schedule_config: {
      default_timezone: { type: String, default: 'UTC' },
      recommended_send_times: [String],
      blackout_periods: [{
        start_time: String,
        end_time: String,
        days: [String]
      }]
    }
  },
  
  // Design assets
  design_assets: {
    thumbnail: String,
    email_header_image: String,
    social_preview: String,
    custom_assets: [String]
  },
  
  // Usage settings
  usage_settings: {
    usage_limit: { type: Number, default: -1 }, // -1 for unlimited
    expiry_date: Date,
    allowed_modifications: [{ 
      type: String, 
      enum: ['content', 'variables', 'schedule', 'targeting', 'design']
    }],
    restrict_to_teams: [String],
    cost_per_use: Number
  },
  
  // Usage statistics
  usage_stats: {
    times_used: { type: Number, default: 0 },
    campaigns_created: { type: Number, default: 0 },
    total_recipients_reached: { type: Number, default: 0 },
    average_success_rate: { type: Number, default: 0 },
    last_used: Date,
    last_used_by: String
  },
  
  // Approval workflow
  approval_status: { 
    type: String, 
    enum: ['PENDING', 'APPROVED', 'REJECTED', 'NEEDS_REVIEW'],
    default: 'PENDING'
  },
  approved_by: String,
  approved_at: Date,
  rejection_reason: String
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient querying
templateSchema.index({ org_id: 1, status: 1 });
templateSchema.index({ org_id: 1, category: 1 });
templateSchema.index({ org_id: 1, tags: 1 });
templateSchema.index({ created_by: 1 });
templateSchema.index({ 'usage_stats.times_used': -1 });

export const TemplateModel = model('Template', templateSchema);
```

### 2. DAO Implementation (1 hour)
```typescript
// src/lib/db/dao/template.dao.ts
import { TemplateModel } from '../models/template.model';
import { generateTemplateId } from '@/lib/utils/id-generator';
import { ValidationError, DuplicateError, DatabaseError } from '@/lib/errors/error-types';

export class TemplateDAO {
  static async createTemplate(orgId: string, userId: string, templateData: any) {
    try {
      // Check for duplicate template name
      const existingTemplate = await TemplateModel.findOne({
        org_id: orgId,
        name: templateData.template_data.name,
        status: { $ne: 'ARCHIVED' }
      });

      if (existingTemplate) {
        throw new DuplicateError(
          `Template with name "${templateData.template_data.name}" already exists`,
          'TEMPLATE_NAME_EXISTS'
        );
      }

      const templateId = generateTemplateId();
      
      const template = new TemplateModel({
        org_id: orgId,
        template_id: templateId,
        created_by: userId,
        name: templateData.template_data.name,
        description: templateData.template_data.description,
        category: templateData.template_data.category,
        tags: templateData.template_data.tags || [],
        template_type: templateData.template_data.template_type || 'CAMPAIGN',
        is_public: templateData.template_data.is_public || false,
        campaign_config: templateData.campaign_config,
        design_assets: templateData.design_assets || {},
        usage_settings: {
          usage_limit: templateData.usage_settings?.usage_limit || -1,
          expiry_date: templateData.usage_settings?.expiry_date,
          allowed_modifications: templateData.usage_settings?.allowed_modifications || ['content', 'variables', 'schedule'],
          restrict_to_teams: templateData.usage_settings?.restrict_to_teams || [],
          cost_per_use: templateData.usage_settings?.cost_per_use || 0
        }
      });

      const savedTemplate = await template.save();
      return this.formatTemplateResponse(savedTemplate);

    } catch (error: unknown) {
      if (error instanceof DuplicateError || error instanceof ValidationError) {
        throw error;
      }

      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to create template',
        'DB_TEMPLATE_CREATE_FAILED',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  static async getTemplateById(templateId: string, orgId: string) {
    try {
      const template = await TemplateModel.findOne({
        template_id: templateId,
        org_id: orgId,
        status: { $ne: 'ARCHIVED' }
      });

      return template ? this.formatTemplateResponse(template) : null;

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve template',
        'DB_TEMPLATE_GET_FAILED'
      );
    }
  }

  static async getTemplates(orgId: string, filters: any = {}) {
    try {
      const query: any = {
        org_id: orgId,
        status: { $ne: 'ARCHIVED' }
      };

      // Apply filters
      if (filters.category) query.category = filters.category;
      if (filters.template_type) query.template_type = filters.template_type;
      if (filters.status) query.status = filters.status;
      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags };
      }
      if (filters.created_by) query.created_by = filters.created_by;

      // Search functionality
      if (filters.search) {
        query.$or = [
          { name: { $regex: filters.search, $options: 'i' } },
          { description: { $regex: filters.search, $options: 'i' } },
          { tags: { $regex: filters.search, $options: 'i' } }
        ];
      }

      const sortBy = filters.sort_by || 'created_at';
      const sortOrder = filters.sort_order === 'asc' ? 1 : -1;
      const page = Math.max(1, parseInt(filters.page) || 1);
      const limit = Math.min(50, Math.max(1, parseInt(filters.limit) || 20));
      const skip = (page - 1) * limit;

      const [templates, total] = await Promise.all([
        TemplateModel
          .find(query)
          .sort({ [sortBy]: sortOrder })
          .skip(skip)
          .limit(limit)
          .lean(),
        TemplateModel.countDocuments(query)
      ]);

      const formattedTemplates = templates.map(template => 
        this.formatTemplateResponse(template)
      );

      return {
        templates: formattedTemplates,
        pagination: {
          current_page: page,
          per_page: limit,
          total_pages: Math.ceil(total / limit),
          total_count: total
        }
      };

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve templates',
        'DB_TEMPLATES_GET_FAILED'
      );
    }
  }

  static async updateTemplateUsage(templateId: string, orgId: string) {
    try {
      await TemplateModel.findOneAndUpdate(
        { template_id: templateId, org_id: orgId },
        {
          $inc: {
            'usage_stats.times_used': 1,
            'usage_stats.campaigns_created': 1
          },
          $set: {
            'usage_stats.last_used': new Date()
          }
        }
      );
    } catch (error: unknown) {
      // Don't throw error for usage tracking to avoid blocking template usage
      console.error('Failed to update template usage:', error);
    }
  }

  static formatTemplateResponse(template: any) {
    const templateObj = template.toObject ? template.toObject() : template;
    
    return {
      template_id: templateObj.template_id,
      name: templateObj.name,
      description: templateObj.description,
      category: templateObj.category,
      template_type: templateObj.template_type,
      status: templateObj.status,
      version: templateObj.version,
      tags: templateObj.tags,
      is_public: templateObj.is_public,
      
      usage_stats: templateObj.usage_stats,
      
      channels: {
        sms: templateObj.campaign_config?.channels?.sms ? {
          enabled: templateObj.campaign_config.channels.sms.enabled,
          character_count: templateObj.campaign_config.channels.sms.character_count,
          estimated_sms_parts: templateObj.campaign_config.channels.sms.estimated_sms_parts,
          variables_count: templateObj.campaign_config.channels.sms.template_variables?.length || 0
        } : { enabled: false },
        
        email: templateObj.campaign_config?.channels?.email ? {
          enabled: templateObj.campaign_config.channels.email.enabled,
          content_type: templateObj.campaign_config.channels.email.content_type,
          variables_count: templateObj.campaign_config.channels.email.template_variables?.length || 0,
          has_images: Boolean(templateObj.design_assets?.email_header_image)
        } : { enabled: false }
      },
      
      asset_urls: this.generateAssetUrls(templateObj.template_id, templateObj.design_assets),
      
      estimated_cost_per_use: {
        sms_per_recipient: 0.05,
        email_per_recipient: 0.01,
        setup_cost: templateObj.usage_settings?.cost_per_use || 0.00
      },
      
      created_by: {
        user_id: templateObj.created_by,
        // Additional user info would be populated via join or separate query
      },
      
      created_at: templateObj.createdAt,
      updated_at: templateObj.updatedAt
    };
  }

  private static generateAssetUrls(templateId: string, designAssets: any) {
    const baseUrl = process.env.CDN_BASE_URL || 'https://cdn.example.com';
    
    return {
      thumbnail: designAssets?.thumbnail ? 
        `${baseUrl}/templates/${templateId}/thumbnail.jpg` : null,
      email_header: designAssets?.email_header_image ? 
        `${baseUrl}/templates/${templateId}/header.jpg` : null,
      social_preview: designAssets?.social_preview ? 
        `${baseUrl}/templates/${templateId}/social.jpg` : null
    };
  }
}
```

### 3. Handler Implementation (45 minutes)
```typescript
// src/handlers/template.handler.ts
import { Request, Response } from 'express';
import { TemplateDAO } from '@/lib/db/dao/template.dao';
import { FileUploadService } from '@/services/file-upload.service';
import { handleError } from '@/lib/errors/error-handler';
import { ValidationError, OrganizationError } from '@/lib/errors/error-types';

export class TemplateHandler {
  static async createTemplate(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const userId = req.user?.id;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      if (!userId) {
        throw new ValidationError('User context is required', 'USER_001');
      }

      // Process design assets if provided
      let processedTemplateData = { ...req.body };
      if (req.body.design_assets) {
        processedTemplateData.design_assets = await FileUploadService.processTemplateAssets(
          req.body.design_assets,
          orgId
        );
      }

      // Calculate content statistics
      processedTemplateData = await TemplateHandler.calculateContentStats(processedTemplateData);

      const template = await TemplateDAO.createTemplate(orgId, userId, processedTemplateData);

      res.status(201).json({
        success: true,
        data: template,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getTemplates(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const filters = {
        category: req.query.category as string,
        template_type: req.query.template_type as string,
        status: req.query.status as string,
        tags: req.query.tags ? (req.query.tags as string).split(',') : undefined,
        created_by: req.query.created_by as string,
        search: req.query.search as string,
        sort_by: req.query.sort_by as string,
        sort_order: req.query.sort_order as string,
        page: req.query.page as string,
        limit: req.query.limit as string
      };

      const result = await TemplateDAO.getTemplates(orgId, filters);

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getTemplateById(req: Request, res: Response) {
    try {
      const { template_id } = req.params;
      const orgId = req.organizationId;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const template = await TemplateDAO.getTemplateById(template_id, orgId);

      if (!template) {
        return res.status(404).json({
          success: false,
          error: {
            type: 'TEMPLATE_NOT_FOUND',
            message: `Template with ID ${template_id} not found`,
            code: 404
          },
          meta: {
            timestamp: new Date().toISOString(),
            request_id: req.requestId
          }
        });
      }

      res.status(200).json({
        success: true,
        data: template,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  private static async calculateContentStats(templateData: any) {
    // Calculate SMS character count and parts
    if (templateData.campaign_config?.channels?.sms?.content) {
      const smsContent = templateData.campaign_config.channels.sms.content;
      const charCount = smsContent.length;
      const estimatedParts = Math.ceil(charCount / 160);
      
      templateData.campaign_config.channels.sms.character_count = charCount;
      templateData.campaign_config.channels.sms.estimated_sms_parts = estimatedParts;
    }

    return templateData;
  }
}
```

### 4. Route Configuration (15 minutes)
```typescript
// src/routes/template.routes.ts
import { Router } from 'express';
import { TemplateHandler } from '@/handlers/template.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateCreateTemplate } from '@/validators/template.validator';

const router = Router();

// Create template
router.post(
  '/',
  supabaseAuthMiddleware,
  validateCreateTemplate,
  TemplateHandler.createTemplate
);

// Get templates with filtering
router.get(
  '/',
  supabaseAuthMiddleware,
  TemplateHandler.getTemplates
);

// Get template by ID
router.get(
  '/:template_id',
  supabaseAuthMiddleware,
  TemplateHandler.getTemplateById
);

export { router as templateRoutes };
```

### 5. Validation Schema (30 minutes)
```typescript
// src/validators/template.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const templateVariableSchema = Joi.object({
  name: Joi.string().required().max(50),
  type: Joi.string().valid('string', 'number', 'url', 'date', 'boolean').required(),
  description: Joi.string().required().max(200),
  default_value: Joi.string().allow('').optional(),
  required: Joi.boolean().default(false),
  validation_rules: Joi.object({
    min_length: Joi.number().min(0),
    max_length: Joi.number().min(1),
    pattern: Joi.string(),
    min_value: Joi.number(),
    max_value: Joi.number()
  }).optional()
});

const createTemplateSchema = Joi.object({
  template_data: Joi.object({
    name: Joi.string().required().min(1).max(255),
    description: Joi.string().max(1000).optional(),
    category: Joi.string().valid(
      'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 
      'REMINDER', 'SEASONAL', 'OTHER'
    ).required(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    is_public: Joi.boolean().default(false),
    template_type: Joi.string().valid('CAMPAIGN', 'MESSAGE', 'AUTOMATION').default('CAMPAIGN')
  }).required(),

  campaign_config: Joi.object({
    type: Joi.string().valid('BROADCAST', 'AUTOMATED', 'TRIGGERED').required(),
    
    channels: Joi.object({
      sms: Joi.object({
        enabled: Joi.boolean().default(false),
        content: Joi.when('enabled', {
          is: true,
          then: Joi.string().required().max(1600),
          otherwise: Joi.string().optional()
        }),
        template_variables: Joi.array().items(templateVariableSchema).optional(),
        sender_id_template: Joi.string().max(11).optional()
      }).optional(),
      
      email: Joi.object({
        enabled: Joi.boolean().default(false),
        subject: Joi.when('enabled', {
          is: true,
          then: Joi.string().required().max(200),
          otherwise: Joi.string().optional()
        }),
        content: Joi.when('enabled', {
          is: true,
          then: Joi.string().required(),
          otherwise: Joi.string().optional()
        }),
        template_variables: Joi.array().items(templateVariableSchema).optional(),
        content_type: Joi.string().valid('html', 'text').default('html'),
        preview_text: Joi.string().max(150).optional(),
        sender_email_template: Joi.string().email().optional()
      }).optional(),
      
      push: Joi.object({
        enabled: Joi.boolean().default(false),
        title: Joi.when('enabled', {
          is: true,
          then: Joi.string().required().max(50),
          otherwise: Joi.string().optional()
        }),
        content: Joi.when('enabled', {
          is: true,
          then: Joi.string().required().max(200),
          otherwise: Joi.string().optional()
        }),
        template_variables: Joi.array().items(templateVariableSchema).optional(),
        action_url_template: Joi.string().uri().optional()
      }).optional()
    }).min(1).required(),

    target_config: Joi.object({
      type: Joi.string().valid('ALL_CONTACTS', 'SEGMENTS', 'INDIVIDUAL').default('SEGMENTS'),
      default_filters: Joi.object().optional()
    }).optional(),

    schedule_config: Joi.object({
      default_timezone: Joi.string().default('UTC'),
      recommended_send_times: Joi.array().items(
        Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
      ).optional(),
      blackout_periods: Joi.array().items(
        Joi.object({
          start_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
          end_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
          days: Joi.array().items(
            Joi.string().valid('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
          ).min(1).required()
        })
      ).optional()
    }).optional()
  }).required(),

  design_assets: Joi.object({
    thumbnail: Joi.string().optional(),
    email_header_image: Joi.string().uri().optional(),
    social_preview: Joi.string().uri().optional(),
    custom_assets: Joi.array().items(Joi.string().uri()).optional()
  }).optional(),

  usage_settings: Joi.object({
    usage_limit: Joi.number().integer().min(-1).default(-1),
    expiry_date: Joi.date().iso().min('now').optional(),
    allowed_modifications: Joi.array().items(
      Joi.string().valid('content', 'variables', 'schedule', 'targeting', 'design')
    ).default(['content', 'variables', 'schedule']),
    restrict_to_teams: Joi.array().items(Joi.string()).optional(),
    cost_per_use: Joi.number().min(0).optional()
  }).optional()
});

export const validateCreateTemplate = (req: Request, res: Response, next: NextFunction) => {
  const { error, value } = createTemplateSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }

  // Check that at least one channel is enabled
  const channels = value.campaign_config.channels;
  const hasEnabledChannel = Object.values(channels).some((channel: any) => channel?.enabled);
  
  if (!hasEnabledChannel) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: 'At least one channel must be enabled',
        details: [{ field: 'campaign_config.channels', message: 'At least one channel must be enabled' }]
      }
    });
  }

  req.body = value;
  next();
};
```

## Error Handling

### Common Errors
- **400 Validation Error**: Invalid template data or missing required fields
- **409 Duplicate Template**: Template name already exists in organization
- **413 Payload Too Large**: Template content or assets exceed size limits
- **429 Rate Limited**: Too many template creation requests
- **500 Storage Error**: Failed to save template assets

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "TEMPLATE_NAME_EXISTS",
    "message": "Template with name 'Black Friday Sale Template' already exists",
    "code": 409
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Testing Strategy

### Unit Tests
- Test template creation with various configurations
- Test validation schema edge cases
- Test content statistics calculation
- Test asset URL generation

### Integration Tests
- Test complete template creation flow
- Test file upload integration
- Test database operations
- Test error handling scenarios

## Security Considerations

1. **Input Validation**: Sanitize all template content and variables
2. **File Upload Security**: Validate and scan uploaded assets
3. **Content Filtering**: Check for malicious content in templates
4. **Access Control**: Ensure proper organization isolation
5. **Rate Limiting**: Prevent template spam and abuse