# Campaign Service - POST /campaigns/{id}/build (Build Campaign)

## Task Overview
**Endpoint**: `POST /api/campaign/v1/campaigns/{id}/build`  
**Purpose**: Build campaign by resolving contacts, generating messages, and preparing for sending  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Campaign creation, segment service, queue processors  

## API Specification

### Request
```http
POST /api/campaign/v1/campaigns/64a1b2c3d4e5f6789012346/build
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "force_rebuild": false,
  "validate_only": false
}
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "job_id": "build_job_64a1b2c3d4e5f6789012348",
    "campaign_id": "64a1b2c3d4e5f6789012346",
    "status": "BUILDING",
    "estimated_recipients": 1250,
    "estimated_completion": "2024-08-11T15:35:00Z",
    "progress_url": "/api/campaign/v1/campaigns/64a1b2c3d4e5f6789012346/build-status",
    "channels_to_build": ["SMS", "EMAIL"]
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012349"
  }
}
```

## Implementation Tasks

### 1. Queue Job Data Types (15 minutes)
```typescript
// src/types/queue.types.ts
export interface CampaignBuildJobData extends BaseJobData {
  campaignId: string;
  forceRebuild: boolean;
  validateOnly: boolean;
  channels: string[];
  estimatedRecipients: number;
}

export interface CampaignBuildProgress {
  jobId: string;
  campaignId: string;
  status: 'QUEUED' | 'BUILDING' | 'COMPLETED' | 'FAILED';
  progress: {
    current: number;
    total: number;
    percentage: number;
  };
  steps: {
    resolveContacts: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    generateMessages: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    validateContent: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    calculateCosts: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  };
  error?: string;
  startedAt: Date;
  completedAt?: Date;
}
```

### 2. Service Layer (45 minutes)
```typescript
// src/services/campaign.service.ts (addition)
import { CampaignBuildQueue } from '@/queues/campaign.build.queue';
import { v4 as uuidv4 } from 'uuid';

export class CampaignService {
  static async buildCampaign(
    campaignId: string, 
    orgId: string, 
    options: { forceRebuild?: boolean; validateOnly?: boolean } = {}
  ): Promise<any> {
    try {
      // Validate campaign exists and is in correct state
      const campaign = await this.validateCampaignForBuild(campaignId, orgId);
      
      // Check if already building
      if (campaign.status === 'BUILDING') {
        throw new ConflictError('Campaign is already being built');
      }

      // Get enabled channels
      const enabledChannels = this.getEnabledChannels(campaign.channels);
      if (enabledChannels.length === 0) {
        throw new ValidationError('No channels enabled for campaign');
      }

      // Estimate recipients
      const estimatedRecipients = await this.estimateRecipients(
        campaign.target_config, 
        orgId
      );

      if (estimatedRecipients === 0) {
        throw new ValidationError('No recipients found for campaign');
      }

      // Update campaign status
      await CampaignModel.findByIdAndUpdate(campaignId, {
        status: 'BUILDING',
        updated_at: new Date()
      });

      // Create build job
      const jobId = uuidv4();
      const jobData: CampaignBuildJobData = {
        jobId,
        orgId,
        createdBy: campaign.created_by,
        campaignId,
        forceRebuild: options.forceRebuild || false,
        validateOnly: options.validateOnly || false,
        channels: enabledChannels,
        estimatedRecipients,
        priority: QUEUE_PRIORITIES.HIGH
      };

      // Queue the build job
      await CampaignBuildQueue.addJob(jobData);

      return {
        job_id: jobId,
        campaign_id: campaignId,
        status: 'BUILDING',
        estimated_recipients: estimatedRecipients,
        estimated_completion: this.calculateEstimatedCompletion(estimatedRecipients),
        progress_url: `/api/campaign/v1/campaigns/${campaignId}/build-status`,
        channels_to_build: enabledChannels
      };
    } catch (error) {
      // Revert campaign status if it was changed
      await CampaignModel.findByIdAndUpdate(campaignId, {
        status: 'DRAFT'
      });
      
      if (error instanceof ValidationError || error instanceof ConflictError) {
        throw error;
      }
      throw new InternalError(`Failed to build campaign: ${error.message}`);
    }
  }

  private static async validateCampaignForBuild(campaignId: string, orgId: string): Promise<any> {
    const campaign = await CampaignModel.findOne({
      _id: campaignId,
      org_id: orgId
    });

    if (!campaign) {
      throw new NotFoundError('Campaign not found');
    }

    if (!['DRAFT', 'FAILED'].includes(campaign.status)) {
      throw new ConflictError(`Cannot build campaign in ${campaign.status} status`);
    }

    return campaign;
  }

  private static getEnabledChannels(channels: any): string[] {
    return Object.entries(channels)
      .filter(([_, config]: [string, any]) => config?.enabled)
      .map(([channel, _]) => channel.toUpperCase());
  }

  private static calculateEstimatedCompletion(recipientCount: number): string {
    // Estimate 1000 recipients per minute processing time
    const estimatedMinutes = Math.ceil(recipientCount / 1000);
    const completionTime = new Date();
    completionTime.setMinutes(completionTime.getMinutes() + estimatedMinutes);
    return completionTime.toISOString();
  }
}
```

### 3. Controller Implementation (25 minutes)
```typescript
// src/controllers/campaign.controller.ts (addition)
import Joi from 'joi';

const buildCampaignSchema = Joi.object({
  force_rebuild: Joi.boolean().default(false),
  validate_only: Joi.boolean().default(false)
});

export class CampaignController {
  static async buildCampaign(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      // Validate request body
      const { error, value } = buildCampaignSchema.validate(req.body);
      if (error) {
        throw new ValidationError(error.details[0].message);
      }

      const buildResult = await CampaignService.buildCampaign(
        id,
        req.user.org_id,
        {
          forceRebuild: value.force_rebuild,
          validateOnly: value.validate_only
        }
      );

      res.json({
        success: true,
        data: buildResult,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 4. Background Processor (60 minutes)
```typescript
// src/workers/processors/campaign.build.processor.ts
import { Job } from 'bull';
import { CampaignBuildJobData, CampaignBuildProgress } from '@/types/queue.types';
import { CampaignModel } from '@/lib/db/models/campaign.model';
import { ContactService } from '@/services/contact.service';
import { TemplateService } from '@/services/template.service';
import { CostEstimationService } from '@/services/cost.estimation.service';
import { RedisConnector } from '@/lib/db/connectors/RedisConnector';

export class CampaignBuildProcessor {
  static async processJob(job: Job<CampaignBuildJobData>): Promise<void> {
    const { campaignId, orgId, forceRebuild, validateOnly, channels } = job.data;
    
    try {
      // Initialize progress tracking
      const progress: CampaignBuildProgress = {
        jobId: job.data.jobId,
        campaignId,
        status: 'BUILDING',
        progress: { current: 0, total: 4, percentage: 0 },
        steps: {
          resolveContacts: 'PENDING',
          generateMessages: 'PENDING',
          validateContent: 'PENDING',
          calculateCosts: 'PENDING'
        },
        startedAt: new Date()
      };

      await this.updateProgress(progress);

      // Step 1: Resolve contacts
      progress.steps.resolveContacts = 'IN_PROGRESS';
      await this.updateProgress(progress);
      
      const contacts = await this.resolveContacts(campaignId, orgId);
      
      progress.steps.resolveContacts = 'COMPLETED';
      progress.progress.current = 1;
      progress.progress.percentage = 25;
      await this.updateProgress(progress);

      // Step 2: Generate messages
      progress.steps.generateMessages = 'IN_PROGRESS';
      await this.updateProgress(progress);
      
      const messages = await this.generateMessages(campaignId, contacts, channels);
      
      progress.steps.generateMessages = 'COMPLETED';
      progress.progress.current = 2;
      progress.progress.percentage = 50;
      await this.updateProgress(progress);

      // Step 3: Validate content
      progress.steps.validateContent = 'IN_PROGRESS';
      await this.updateProgress(progress);
      
      await this.validateContent(messages);
      
      progress.steps.validateContent = 'COMPLETED';
      progress.progress.current = 3;
      progress.progress.percentage = 75;
      await this.updateProgress(progress);

      // Step 4: Calculate actual costs
      progress.steps.calculateCosts = 'IN_PROGRESS';
      await this.updateProgress(progress);
      
      const actualCosts = await this.calculateActualCosts(messages);
      
      progress.steps.calculateCosts = 'COMPLETED';
      progress.progress.current = 4;
      progress.progress.percentage = 100;
      progress.status = 'COMPLETED';
      progress.completedAt = new Date();
      await this.updateProgress(progress);

      // Update campaign with build results
      await this.updateCampaignWithResults(campaignId, {
        contacts,
        messages,
        actualCosts,
        validateOnly
      });

    } catch (error) {
      // Handle build failure
      await this.handleBuildFailure(campaignId, job.data.jobId, error);
      throw error;
    }
  }

  private static async resolveContacts(campaignId: string, orgId: string): Promise<any[]> {
    const campaign = await CampaignModel.findById(campaignId);
    if (!campaign) throw new Error('Campaign not found');

    switch (campaign.target_config.type) {
      case 'ALL':
        return await ContactService.getAllContacts(orgId, campaign.target_config.filters);
      case 'SEGMENTS':
        return await ContactService.getContactsBySegments(
          campaign.target_config.segment_ids,
          orgId
        );
      case 'CONTACTS':
        return await ContactService.getContactsByIds(
          campaign.target_config.contact_ids,
          orgId
        );
      default:
        throw new Error('Invalid target configuration');
    }
  }

  private static async generateMessages(
    campaignId: string,
    contacts: any[],
    channels: string[]
  ): Promise<any[]> {
    const campaign = await CampaignModel.findById(campaignId);
    const messages: any[] = [];

    for (const contact of contacts) {
      for (const channel of channels) {
        const channelConfig = campaign.channels[channel.toLowerCase()];
        if (!channelConfig?.enabled) continue;

        const message = await TemplateService.generateMessage({
          contact,
          channel: channel.toLowerCase(),
          config: channelConfig,
          campaignId
        });

        messages.push(message);
      }
    }

    return messages;
  }

  private static async validateContent(messages: any[]): Promise<void> {
    for (const message of messages) {
      await TemplateService.validateMessage(message);
    }
  }

  private static async calculateActualCosts(messages: any[]): Promise<any> {
    const costsByChannel = messages.reduce((acc, message) => {
      const channel = message.transport.toLowerCase();
      acc[channel] = (acc[channel] || 0) + 1;
      return acc;
    }, {});

    return await CostEstimationService.calculateActualCosts(costsByChannel);
  }

  private static async updateProgress(progress: CampaignBuildProgress): Promise<void> {
    const redis = RedisConnector.getClient();
    const key = `campaign:build:progress:${progress.jobId}`;
    await redis.setex(key, 3600, JSON.stringify(progress)); // 1 hour TTL
  }

  private static async updateCampaignWithResults(
    campaignId: string,
    results: any
  ): Promise<void> {
    const updateData: any = {
      status: results.validateOnly ? 'DRAFT' : 'READY',
      'stats.total_recipients': results.contacts.length,
      'cost_analysis.actual_cost': results.actualCosts,
      updated_at: new Date()
    };

    if (!results.validateOnly) {
      updateData.built_at = new Date();
    }

    await CampaignModel.findByIdAndUpdate(campaignId, updateData);
  }

  private static async handleBuildFailure(
    campaignId: string,
    jobId: string,
    error: Error
  ): Promise<void> {
    // Update campaign status
    await CampaignModel.findByIdAndUpdate(campaignId, {
      status: 'FAILED',
      updated_at: new Date()
    });

    // Update progress with error
    const progress: CampaignBuildProgress = {
      jobId,
      campaignId,
      status: 'FAILED',
      progress: { current: 0, total: 4, percentage: 0 },
      steps: {
        resolveContacts: 'FAILED',
        generateMessages: 'FAILED',
        validateContent: 'FAILED',
        calculateCosts: 'FAILED'
      },
      error: error.message,
      startedAt: new Date(),
      completedAt: new Date()
    };

    await this.updateProgress(progress);
  }
}
```

### 5. Queue Configuration (20 minutes)
```typescript
// src/queues/campaign.build.queue.ts
import Bull from 'bull';
import { CampaignBuildJobData } from '@/types/queue.types';
import { CampaignBuildProcessor } from '@/workers/processors/campaign.build.processor';
import { MICROSERVICE_QUEUES, QUEUE_CONFIGURATIONS } from '@/lib/constant/queue.constants';

export class CampaignBuildQueue {
  private static queue: Bull.Queue<CampaignBuildJobData>;

  static initialize(): void {
    this.queue = new Bull(MICROSERVICE_QUEUES.CAMPAIGN_BUILD, {
      redis: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD
      }
    });

    // Process jobs
    this.queue.process(5, CampaignBuildProcessor.processJob);

    // Event handlers
    this.queue.on('completed', (job) => {
      console.log(`Campaign build job ${job.id} completed`);
    });

    this.queue.on('failed', (job, err) => {
      console.error(`Campaign build job ${job.id} failed:`, err);
    });
  }

  static async addJob(data: CampaignBuildJobData): Promise<Bull.Job<CampaignBuildJobData>> {
    return await this.queue.add(data, {
      ...QUEUE_CONFIGURATIONS.MESSAGE_PROCESSING,
      jobId: data.jobId,
      removeOnComplete: 10,
      removeOnFail: 5
    });
  }

  static getQueue(): Bull.Queue<CampaignBuildJobData> {
    return this.queue;
  }
}
```

### 6. Build Status Endpoint (25 minutes)
```typescript
// src/controllers/campaign.controller.ts (addition)
export class CampaignController {
  static async getBuildStatus(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      const { job_id } = req.query;

      if (!job_id) {
        throw new BadRequestError('job_id query parameter is required');
      }

      const progress = await CampaignService.getBuildProgress(
        id,
        job_id as string,
        req.user.org_id
      );

      res.json({
        success: true,
        data: progress,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}

// src/services/campaign.service.ts (addition)
export class CampaignService {
  static async getBuildProgress(
    campaignId: string,
    jobId: string,
    orgId: string
  ): Promise<CampaignBuildProgress> {
    // Verify campaign belongs to organization
    const campaign = await CampaignModel.findOne({
      _id: campaignId,
      org_id: orgId
    });

    if (!campaign) {
      throw new NotFoundError('Campaign not found');
    }

    // Get progress from Redis
    const redis = RedisConnector.getClient();
    const key = `campaign:build:progress:${jobId}`;
    const progressData = await redis.get(key);

    if (!progressData) {
      throw new NotFoundError('Build progress not found');
    }

    return JSON.parse(progressData);
  }
}
```

### 7. Route Definitions (10 minutes)
```typescript
// src/routes/campaign.routes.ts (additions)
router.post(
  '/:id/build',
  authenticateRequest,
  validateObjectId('id'),
  rateLimitMiddleware,
  CampaignController.buildCampaign
);

router.get(
  '/:id/build-status',
  authenticateRequest,
  validateObjectId('id'),
  rateLimitMiddleware,
  CampaignController.getBuildStatus
);
```

## Testing Checklist
- [ ] Unit tests for controller
- [ ] Unit tests for service layer
- [ ] Unit tests for build processor
- [ ] Integration tests for build endpoint
- [ ] Integration tests for build status endpoint
- [ ] Queue processing tests
- [ ] Error handling tests
- [ ] Progress tracking tests

## Documentation
- [ ] API documentation for build endpoint
- [ ] API documentation for build status endpoint
- [ ] Background job documentation
- [ ] Progress tracking documentation

This task creates a comprehensive campaign building system with background processing, progress tracking, and proper error handling.
