# Campaign Service - GET /campaigns (List Campaigns)

## Task Overview
**Endpoint**: `GET /api/campaign/v1/campaigns`  
**Purpose**: Retrieve a paginated list of campaigns for an organization with filtering and sorting options  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Authentication middleware, database models, pagination utilities  

## API Specification

### Request
```http
GET /api/campaign/v1/campaigns?page=1&page_size=20&status=ACTIVE&type=BROADCAST&sort_by=created_at&sort_order=desc
Authorization: Bearer <jwt_token>
```

### Query Parameters
- `page` (optional, default: 1): Page number for pagination
- `page_size` (optional, default: 20, max: 100): Number of items per page
- `status` (optional): Filter by campaign status (DRAFT, ACTIVE, PAUSED, COMPLETED, CANCELLED)
- `type` (optional): Filter by campaign type (BROADCAST, TRIGGERED)
- `search` (optional): Search campaigns by name or description
- `sort_by` (optional, default: created_at): Sort field (created_at, updated_at, name, status)
- `sort_order` (optional, default: desc): Sort order (asc, desc)
- `created_from` (optional): Filter campaigns created after this date (ISO format)
- `created_to` (optional): Filter campaigns created before this date (ISO format)

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "campaigns": [
      {
        "_id": "64a1b2c3d4e5f6789012346",
        "name": "Summer Sale 2024",
        "description": "Promotional campaign for summer collection",
        "type": "BROADCAST",
        "status": "ACTIVE",
        "target_config": {
          "type": "SEGMENTS",
          "segment_ids": ["64a1b2c3d4e5f6789012345"]
        },
        "channels": {
          "sms": {
            "enabled": true,
            "sender_id": "SUMMER24"
          },
          "email": {
            "enabled": true,
            "sender_email": "<EMAIL>"
          }
        },
        "schedule_config": {
          "is_scheduled": false,
          "scheduled_at": null
        },
        "stats": {
          "total_recipients": 1250,
          "messages_sent": 1200,
          "messages_delivered": 1150,
          "messages_failed": 50,
          "open_rate": 0.25,
          "click_rate": 0.12
        },
        "cost_analysis": {
          "actual_cost": {
            "total": 87.50,
            "currency": "USD"
          }
        },
        "tags": ["summer", "promotional"],
        "created_at": "2024-08-11T15:30:00Z",
        "updated_at": "2024-08-11T16:45:00Z",
        "started_at": "2024-08-11T17:00:00Z",
        "completed_at": null
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 20,
      "total_items": 45,
      "total_pages": 3,
      "has_next": true,
      "has_previous": false
    },
    "filters": {
      "status": "ACTIVE",
      "type": "BROADCAST",
      "sort_by": "created_at",
      "sort_order": "desc"
    }
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "INVALID_QUERY_PARAMS",
    "message": "Invalid query parameters provided",
    "details": [
      {
        "field": "page_size",
        "message": "Page size must be between 1 and 100"
      }
    ],
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (45 minutes)
```typescript
// src/services/campaign.service.ts (addition)
export class CampaignService {
  static async getCampaigns(
    orgId: string,
    filters: CampaignListFilters,
    pagination: PaginationOptions
  ): Promise<CampaignListResponse> {
    try {
      // Build MongoDB query
      const query = this.buildCampaignQuery(orgId, filters);
      
      // Calculate pagination
      const skip = (pagination.page - 1) * pagination.page_size;
      
      // Build sort options
      const sortOptions = this.buildSortOptions(filters.sort_by, filters.sort_order);
      
      // Execute query with pagination
      const [campaigns, totalCount] = await Promise.all([
        CampaignModel.find(query)
          .select(this.getCampaignListProjection())
          .sort(sortOptions)
          .skip(skip)
          .limit(pagination.page_size)
          .lean(),
        CampaignModel.countDocuments(query)
      ]);
      
      // Format campaigns
      const formattedCampaigns = campaigns.map(this.formatCampaignSummary);
      
      // Build pagination metadata
      const paginationMeta = this.buildPaginationMeta(pagination, totalCount);
      
      return {
        campaigns: formattedCampaigns,
        pagination: paginationMeta,
        filters: {
          status: filters.status,
          type: filters.type,
          sort_by: filters.sort_by,
          sort_order: filters.sort_order
        }
      };
    } catch (error) {
      throw new InternalError(`Failed to retrieve campaigns: ${error.message}`);
    }
  }

  private static buildCampaignQuery(orgId: string, filters: CampaignListFilters): any {
    const query: any = { org_id: orgId };
    
    // Status filter
    if (filters.status) {
      query.status = filters.status;
    }
    
    // Type filter
    if (filters.type) {
      query.type = filters.type;
    }
    
    // Search filter
    if (filters.search) {
      query.$or = [
        { name: { $regex: filters.search, $options: 'i' } },
        { description: { $regex: filters.search, $options: 'i' } }
      ];
    }
    
    // Date range filters
    if (filters.created_from || filters.created_to) {
      query.created_at = {};
      if (filters.created_from) {
        query.created_at.$gte = new Date(filters.created_from);
      }
      if (filters.created_to) {
        query.created_at.$lte = new Date(filters.created_to);
      }
    }
    
    return query;
  }

  private static buildSortOptions(sortBy: string = 'created_at', sortOrder: string = 'desc'): any {
    const order = sortOrder === 'asc' ? 1 : -1;
    return { [sortBy]: order };
  }

  private static getCampaignListProjection(): any {
    return {
      // Exclude large fields for list view performance
      'channels.sms.content': 0,
      'channels.email.content': 0,
      'channels.email.html_content': 0,
      'metadata.large_data': 0
    };
  }

  private static formatCampaignSummary(campaign: any): CampaignSummary {
    return {
      _id: campaign._id.toString(),
      name: campaign.name,
      description: campaign.description,
      type: campaign.type,
      status: campaign.status,
      target_config: {
        type: campaign.target_config.type,
        segment_ids: campaign.target_config.segment_ids || []
      },
      channels: {
        sms: {
          enabled: campaign.channels?.sms?.enabled || false,
          sender_id: campaign.channels?.sms?.sender_id
        },
        email: {
          enabled: campaign.channels?.email?.enabled || false,
          sender_email: campaign.channels?.email?.sender_email
        }
      },
      schedule_config: campaign.schedule_config,
      stats: campaign.stats || this.getDefaultStats(),
      cost_analysis: {
        actual_cost: campaign.cost_analysis?.actual_cost || { total: 0, currency: 'USD' }
      },
      tags: campaign.tags || [],
      created_at: campaign.created_at,
      updated_at: campaign.updated_at,
      started_at: campaign.started_at,
      completed_at: campaign.completed_at
    };
  }

  private static buildPaginationMeta(pagination: PaginationOptions, totalCount: number): PaginationMeta {
    const totalPages = Math.ceil(totalCount / pagination.page_size);
    
    return {
      current_page: pagination.page,
      page_size: pagination.page_size,
      total_items: totalCount,
      total_pages: totalPages,
      has_next: pagination.page < totalPages,
      has_previous: pagination.page > 1
    };
  }

  private static getDefaultStats() {
    return {
      total_recipients: 0,
      messages_sent: 0,
      messages_delivered: 0,
      messages_failed: 0,
      open_rate: null,
      click_rate: null
    };
  }
}
```

### 2. Controller Implementation (30 minutes)
```typescript
// src/controllers/campaign.controller.ts (addition)
export class CampaignController {
  static async getCampaigns(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Extract and validate query parameters
      const filters = CampaignController.extractListFilters(req.query);
      const pagination = CampaignController.extractPaginationOptions(req.query);
      
      // Get campaigns
      const result = await CampaignService.getCampaigns(
        req.user.org_id,
        filters,
        pagination
      );

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  private static extractListFilters(query: any): CampaignListFilters {
    return {
      status: query.status,
      type: query.type,
      search: query.search,
      sort_by: query.sort_by || 'created_at',
      sort_order: query.sort_order || 'desc',
      created_from: query.created_from,
      created_to: query.created_to
    };
  }

  private static extractPaginationOptions(query: any): PaginationOptions {
    const page = Math.max(1, parseInt(query.page) || 1);
    const pageSize = Math.min(100, Math.max(1, parseInt(query.page_size) || 20));
    
    return {
      page,
      page_size: pageSize
    };
  }
}
```

### 3. Validation Middleware (20 minutes)
```typescript
// src/middleware/validation.middleware.ts (addition)
import Joi from 'joi';

export const validateCampaignListQuery = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const schema = Joi.object({
    page: Joi.number().integer().min(1).optional(),
    page_size: Joi.number().integer().min(1).max(100).optional(),
    status: Joi.string().valid('DRAFT', 'ACTIVE', 'PAUSED', 'COMPLETED', 'CANCELLED').optional(),
    type: Joi.string().valid('BROADCAST', 'TRIGGERED').optional(),
    search: Joi.string().max(100).optional(),
    sort_by: Joi.string().valid('created_at', 'updated_at', 'name', 'status').optional(),
    sort_order: Joi.string().valid('asc', 'desc').optional(),
    created_from: Joi.date().iso().optional(),
    created_to: Joi.date().iso().min(Joi.ref('created_from')).optional()
  });

  const { error } = schema.validate(req.query, { abortEarly: false });
  
  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    throw new BadRequestError('Invalid query parameters provided', 'INVALID_QUERY_PARAMS', details);
  }
  
  next();
};
```

### 4. Route Definition (10 minutes)
```typescript
// src/routes/campaign.routes.ts (addition)
import { validateCampaignListQuery } from '@/middleware/validation.middleware';

router.get(
  '/',
  authenticateRequest,
  validateCampaignListQuery,
  rateLimitMiddleware,
  CampaignController.getCampaigns
);
```

### 5. Type Definitions (15 minutes)
```typescript
// src/types/campaign.types.ts (additions)
export interface CampaignListFilters {
  status?: CampaignStatus;
  type?: CampaignType;
  search?: string;
  sort_by?: 'created_at' | 'updated_at' | 'name' | 'status';
  sort_order?: 'asc' | 'desc';
  created_from?: string;
  created_to?: string;
}

export interface PaginationOptions {
  page: number;
  page_size: number;
}

export interface PaginationMeta {
  current_page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface CampaignSummary {
  _id: string;
  name: string;
  description: string;
  type: CampaignType;
  status: CampaignStatus;
  target_config: {
    type: string;
    segment_ids: string[];
  };
  channels: {
    sms: {
      enabled: boolean;
      sender_id?: string;
    };
    email: {
      enabled: boolean;
      sender_email?: string;
    };
  };
  schedule_config: any;
  stats: CampaignStats;
  cost_analysis: {
    actual_cost: {
      total: number;
      currency: string;
    };
  };
  tags: string[];
  created_at: Date;
  updated_at: Date;
  started_at?: Date;
  completed_at?: Date;
}

export interface CampaignListResponse {
  campaigns: CampaignSummary[];
  pagination: PaginationMeta;
  filters: {
    status?: CampaignStatus;
    type?: CampaignType;
    sort_by?: string;
    sort_order?: string;
  };
}
```

### 6. Database Optimization (20 minutes)
```typescript
// src/lib/db/models/campaign.model.ts (addition)
// Add indexes for list query performance
campaignSchema.index({ org_id: 1, status: 1 });
campaignSchema.index({ org_id: 1, type: 1 });
campaignSchema.index({ org_id: 1, created_at: -1 });
campaignSchema.index({ org_id: 1, updated_at: -1 });
campaignSchema.index({ org_id: 1, name: 'text', description: 'text' });
```

### 7. Unit Tests (40 minutes)
```typescript
// tests/unit/controllers/campaign.controller.test.ts (addition)
describe('CampaignController.getCampaigns', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = {
      query: {},
      user: { id: 'user123', org_id: 'org123' },
      headers: { 'x-request-id': 'req123' }
    };
    mockRes = {
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should return paginated campaigns with default parameters', async () => {
    const mockResult = {
      campaigns: [
        {
          _id: '64a1b2c3d4e5f6789012346',
          name: 'Test Campaign',
          type: 'BROADCAST',
          status: 'ACTIVE'
        }
      ],
      pagination: {
        current_page: 1,
        page_size: 20,
        total_items: 1,
        total_pages: 1,
        has_next: false,
        has_previous: false
      },
      filters: {
        sort_by: 'created_at',
        sort_order: 'desc'
      }
    };
    
    (CampaignService.getCampaigns as jest.Mock).mockResolvedValue(mockResult);

    await CampaignController.getCampaigns(mockReq, mockRes, mockNext);

    expect(CampaignService.getCampaigns).toHaveBeenCalledWith(
      'org123',
      expect.objectContaining({
        sort_by: 'created_at',
        sort_order: 'desc'
      }),
      expect.objectContaining({
        page: 1,
        page_size: 20
      })
    );
    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: mockResult,
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        request_id: 'req123'
      })
    });
  });

  it('should handle query parameters correctly', async () => {
    mockReq.query = {
      page: '2',
      page_size: '10',
      status: 'ACTIVE',
      type: 'BROADCAST',
      search: 'summer',
      sort_by: 'name',
      sort_order: 'asc'
    };

    const mockResult = {
      campaigns: [],
      pagination: {
        current_page: 2,
        page_size: 10,
        total_items: 0,
        total_pages: 0,
        has_next: false,
        has_previous: true
      },
      filters: {
        status: 'ACTIVE',
        type: 'BROADCAST',
        sort_by: 'name',
        sort_order: 'asc'
      }
    };
    
    (CampaignService.getCampaigns as jest.Mock).mockResolvedValue(mockResult);

    await CampaignController.getCampaigns(mockReq, mockRes, mockNext);

    expect(CampaignService.getCampaigns).toHaveBeenCalledWith(
      'org123',
      expect.objectContaining({
        status: 'ACTIVE',
        type: 'BROADCAST',
        search: 'summer',
        sort_by: 'name',
        sort_order: 'asc'
      }),
      expect.objectContaining({
        page: 2,
        page_size: 10
      })
    );
  });

  it('should handle service errors', async () => {
    (CampaignService.getCampaigns as jest.Mock).mockRejectedValue(
      new InternalError('Database connection failed')
    );

    await CampaignController.getCampaigns(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(InternalError));
  });
});
```

### 8. Integration Tests (30 minutes)
```typescript
// tests/integration/campaign.api.test.ts (addition)
describe('GET /api/campaign/v1/campaigns', () => {
  let authToken: string;
  let orgId: string;
  let campaigns: any[];

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
    
    // Create test campaigns
    campaigns = await Promise.all([
      CampaignModel.create({
        org_id: orgId,
        created_by: 'user123',
        name: 'Summer Campaign',
        type: 'BROADCAST',
        status: 'ACTIVE',
        target_config: { type: 'ALL' },
        channels: { sms: { enabled: true, sender_id: 'TEST', content: 'Hello' } },
        created_at: new Date('2024-08-01T10:00:00Z')
      }),
      CampaignModel.create({
        org_id: orgId,
        created_by: 'user123',
        name: 'Winter Campaign',
        type: 'BROADCAST',
        status: 'DRAFT',
        target_config: { type: 'ALL' },
        channels: { sms: { enabled: true, sender_id: 'TEST', content: 'Hi' } },
        created_at: new Date('2024-08-02T10:00:00Z')
      })
    ]);
  });

  afterAll(async () => {
    await CampaignModel.deleteMany({ org_id: orgId });
  });

  it('should return paginated campaigns', async () => {
    const response = await request(app)
      .get('/api/campaign/v1/campaigns?page=1&page_size=10')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.campaigns).toHaveLength(2);
    expect(response.body.data.pagination).toMatchObject({
      current_page: 1,
      page_size: 10,
      total_items: 2,
      total_pages: 1,
      has_next: false,
      has_previous: false
    });
  });

  it('should filter campaigns by status', async () => {
    const response = await request(app)
      .get('/api/campaign/v1/campaigns?status=ACTIVE')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.campaigns).toHaveLength(1);
    expect(response.body.data.campaigns[0].status).toBe('ACTIVE');
  });

  it('should search campaigns by name', async () => {
    const response = await request(app)
      .get('/api/campaign/v1/campaigns?search=summer')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.campaigns).toHaveLength(1);
    expect(response.body.data.campaigns[0].name).toContain('Summer');
  });

  it('should sort campaigns correctly', async () => {
    const response = await request(app)
      .get('/api/campaign/v1/campaigns?sort_by=name&sort_order=asc')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.campaigns[0].name).toBe('Summer Campaign');
    expect(response.body.data.campaigns[1].name).toBe('Winter Campaign');
  });

  it('should validate query parameters', async () => {
    const response = await request(app)
      .get('/api/campaign/v1/campaigns?page_size=200')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_QUERY_PARAMS');
  });

  it('should return 401 without authentication', async () => {
    await request(app)
      .get('/api/campaign/v1/campaigns')
      .expect(401);
  });
});
```

### 9. Performance Considerations (15 minutes)
```typescript
// src/services/campaign.service.ts (optimization additions)
export class CampaignService {
  // Add caching for frequently accessed campaign lists
  static async getCampaignsWithCache(
    orgId: string,
    filters: CampaignListFilters,
    pagination: PaginationOptions
  ): Promise<CampaignListResponse> {
    // Generate cache key
    const cacheKey = `campaigns:${orgId}:${JSON.stringify(filters)}:${pagination.page}:${pagination.page_size}`;
    
    try {
      // Try to get from cache first
      const cached = await RedisConnector.getClient().get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
      
      // Get from database
      const result = await this.getCampaigns(orgId, filters, pagination);
      
      // Cache for 2 minutes
      await RedisConnector.getClient().setex(cacheKey, 120, JSON.stringify(result));
      
      return result;
    } catch (cacheError) {
      // If cache fails, fallback to direct database query
      console.error('Cache error:', cacheError);
      return this.getCampaigns(orgId, filters, pagination);
    }
  }
}
```

## Testing Checklist
- [ ] Unit tests for controller with various query parameters
- [ ] Unit tests for service layer with filtering and pagination
- [ ] Integration tests for API endpoint with different filters
- [ ] Performance tests with large datasets
- [ ] Cache functionality tests
- [ ] Validation tests for query parameters
- [ ] Authentication tests
- [ ] Error handling tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Query parameter documentation
- [ ] Pagination documentation
- [ ] Performance optimization notes
- [ ] Caching strategy documentation

This task creates a comprehensive campaign listing endpoint with advanced filtering, pagination, sorting, search capabilities, and performance optimizations including caching and database indexing.