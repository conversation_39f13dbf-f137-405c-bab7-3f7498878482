# Campaign Service - PUT /campaigns/{id} (Update Campaign)

## Task Overview
**Endpoint**: `PUT /api/campaign/v1/campaigns/{id}`  
**Purpose**: Update an existing campaign with new configuration, content, or settings  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Campaign creation, validation middleware, database models  

## API Specification

### Request
```http
PUT /api/campaign/v1/campaigns/64a1b2c3d4e5f6789012346
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Updated Summer Sale 2024",
  "description": "Updated promotional campaign for summer collection",
  "target_config": {
    "type": "SEGMENTS",
    "segment_ids": ["64a1b2c3d4e5f6789012345", "64a1b2c3d4e5f6789012347"]
  },
  "channels": {
    "sms": {
      "enabled": true,
      "sender_id": "SUMMER24",
      "content": "🌞 Updated Summer Sale! Get {{discount}}% off. Use: {{code}}",
      "template_variables": {
        "discount": "40",
        "code": "SUMMER40"
      }
    }
  },
  "tags": ["summer", "promotional", "updated"]
}
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012346",
    "name": "Updated Summer Sale 2024",
    "description": "Updated promotional campaign for summer collection",
    "type": "BROADCAST",
    "status": "DRAFT",
    "updated_at": "2024-08-11T17:30:00Z"
  }
}
```

## Implementation Tasks

### 1. Service Layer (60 minutes)
```typescript
// src/services/campaign.service.ts (addition)
static async updateCampaign(
  campaignId: string,
  orgId: string,
  updateData: CampaignUpdateRequest
): Promise<Campaign> {
  try {
    // Validate campaign exists and belongs to org
    const existingCampaign = await this.getCampaignById(campaignId, orgId);
    
    // Check if campaign can be updated
    this.validateCampaignUpdateability(existingCampaign);
    
    // Prepare update data
    const sanitizedData = this.sanitizeUpdateData(updateData);
    
    // Update campaign
    const updatedCampaign = await CampaignModel.findOneAndUpdate(
      { _id: campaignId, org_id: orgId },
      { 
        ...sanitizedData,
        updated_at: new Date()
      },
      { new: true, runValidators: true }
    ).lean();

    if (!updatedCampaign) {
      throw new NotFoundError('Campaign not found');
    }

    // Invalidate cache
    await CampaignCacheService.invalidateCampaignCache(campaignId, orgId);
    
    return this.formatDetailedCampaignResponse(updatedCampaign);
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof BadRequestError) {
      throw error;
    }
    throw new InternalError(`Failed to update campaign: ${error.message}`);
  }
}
```

### 2. Controller Implementation (30 minutes)
```typescript
// src/controllers/campaign.controller.ts (addition)
static async updateCampaign(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const { id } = req.params;
    
    if (!id) {
      throw new BadRequestError('Campaign ID is required');
    }

    const updatedCampaign = await CampaignService.updateCampaign(
      id,
      req.user.org_id,
      req.body
    );

    res.json({
      success: true,
      data: updatedCampaign,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] || 'unknown'
      }
    });
  } catch (error) {
    next(error);
  }
}
```

### 3. Validation Schema (25 minutes)
```typescript
// src/validators/campaign.validator.ts (addition)
export const campaignUpdateSchema = Joi.object({
  name: Joi.string().min(1).max(100).optional(),
  description: Joi.string().max(500).optional(),
  target_config: Joi.object({
    type: Joi.string().valid('ALL', 'SEGMENTS', 'FILTERS').optional(),
    segment_ids: Joi.array().items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/)).optional()
  }).optional(),
  channels: Joi.object({
    sms: Joi.object({
      enabled: Joi.boolean().optional(),
      sender_id: Joi.string().max(11).optional(),
      content: Joi.string().max(1000).optional(),
      template_variables: Joi.object().pattern(
        Joi.string(),
        Joi.string()
      ).optional()
    }).optional(),
    email: Joi.object({
      enabled: Joi.boolean().optional(),
      sender_email: Joi.string().email().optional(),
      subject: Joi.string().max(100).optional(),
      content: Joi.string().max(10000).optional()
    }).optional()
  }).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
  metadata: Joi.object().optional()
});
```

### 4. Route Definition (10 minutes)
```typescript
// src/routes/campaign.routes.ts (addition)
router.put(
  '/:id',
  authenticateRequest,
  validateObjectId('id'),
  validateRequestBody(campaignUpdateSchema),
  rateLimitMiddleware,
  CampaignController.updateCampaign
);
```

## Testing Checklist
- [ ] Unit tests for service layer update logic
- [ ] Integration tests for PUT endpoint
- [ ] Validation tests for update schema
- [ ] Permission and ownership tests
- [ ] Error handling tests

## Documentation
- [ ] API documentation update
- [ ] Update validation rules
- [ ] Error response documentation