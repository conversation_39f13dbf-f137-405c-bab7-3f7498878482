# Campaign Service - GET /templates (Template Management)

## Task Overview
**Endpoint**: `GET /api/campaign/v1/templates`  
**Purpose**: Retrieve and manage campaign templates with filtering, search, and pagination  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Template creation, database models, search indexing  

## API Specification

### Request
```http
GET /api/campaign/v1/templates?category=PROMOTIONAL&status=ACTIVE&page=1&limit=20&search=sale
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

### Query Parameters
- `category` (string): Filter by template category (PROMOTIONAL, TRANSACTIONAL, etc.)
- `template_type` (string): Filter by template type (CAMPAIGN, MESSAGE, AUTOMATION)
- `status` (string): Filter by status (ACTIVE, DRAFT, ARCHIVED)
- `tags` (string): Comma-separated list of tags to filter by
- `created_by` (string): Filter by creator user ID
- `search` (string): Search in name, description, and tags
- `sort_by` (string): Sort field (created_at, name, usage_stats.times_used)
- `sort_order` (string): Sort order (asc, desc)
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 50)

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "templates": [
      {
        "template_id": "tpl_64a1b2c3d4e5f6789012346",
        "name": "Black Friday Sale Template",
        "description": "Standard template for Black Friday promotional campaigns",
        "category": "PROMOTIONAL",
        "template_type": "CAMPAIGN",
        "status": "ACTIVE",
        "version": "1.0.0",
        "tags": ["sale", "promotion", "seasonal"],
        "usage_stats": {
          "times_used": 25,
          "campaigns_created": 20,
          "last_used": "2024-08-10T14:30:00Z",
          "average_success_rate": 85.6
        },
        "channels": {
          "sms": {
            "enabled": true,
            "character_count": 89,
            "estimated_sms_parts": 1,
            "variables_count": 3
          },
          "email": {
            "enabled": true,
            "content_type": "html",
            "variables_count": 1,
            "has_images": true
          }
        },
        "asset_urls": {
          "thumbnail": "https://cdn.example.com/templates/tpl_64a1b2c3d4e5f6789012346/thumbnail.jpg",
          "email_header": "https://cdn.example.com/templates/tpl_64a1b2c3d4e5f6789012346/header.jpg"
        },
        "estimated_cost_per_use": {
          "sms_per_recipient": 0.05,
          "email_per_recipient": 0.01,
          "setup_cost": 0.00
        },
        "created_by": {
          "user_id": "usr_64a1b2c3d4e5f6789012345",
          "name": "Marketing Team"
        },
        "created_at": "2024-08-05T09:00:00Z",
        "updated_at": "2024-08-10T14:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_pages": 3,
      "total_count": 52
    },
    "filters_applied": {
      "category": "PROMOTIONAL",
      "status": "ACTIVE",
      "search": "sale"
    }
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Individual Template Retrieval

### Request
```http
GET /api/campaign/v1/templates/tpl_64a1b2c3d4e5f6789012346
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "template_id": "tpl_64a1b2c3d4e5f6789012346",
    "name": "Black Friday Sale Template",
    "description": "Standard template for Black Friday promotional campaigns",
    "category": "PROMOTIONAL",
    "template_type": "CAMPAIGN",
    "status": "ACTIVE",
    "version": "1.0.0",
    "tags": ["sale", "promotion", "seasonal"],
    "is_public": false,
    "campaign_config": {
      "type": "BROADCAST",
      "channels": {
        "sms": {
          "enabled": true,
          "content": "🔥 BLACK FRIDAY: {{discount}}% OFF everything! Use code {{code}}. Shop now: {{shop_url}}",
          "template_variables": [
            {
              "name": "discount",
              "type": "number",
              "description": "Discount percentage",
              "default_value": "50",
              "required": true
            }
          ],
          "character_count": 89,
          "estimated_sms_parts": 1,
          "sender_id_template": "COMPANY"
        },
        "email": {
          "enabled": true,
          "subject": "🔥 BLACK FRIDAY: {{discount}}% OFF Everything!",
          "content": "<!DOCTYPE html><html>...</html>",
          "template_variables": [
            {
              "name": "discount",
              "type": "number",
              "description": "Discount percentage",
              "default_value": "50",
              "required": true
            }
          ],
          "content_type": "html",
          "preview_text": "Biggest sale of the year!"
        }
      },
      "target_config": {
        "type": "SEGMENTS",
        "default_filters": {
          "exclude_unsubscribed": true,
          "exclude_bounced": true,
          "last_activity_days": 90
        }
      },
      "schedule_config": {
        "default_timezone": "UTC",
        "recommended_send_times": ["09:00", "12:00", "15:00"]
      }
    },
    "usage_settings": {
      "usage_limit": 100,
      "expiry_date": "2024-12-01T00:00:00Z",
      "allowed_modifications": ["content", "variables", "schedule"],
      "restrict_to_teams": []
    },
    "usage_stats": {
      "times_used": 25,
      "campaigns_created": 20,
      "total_recipients_reached": 15000,
      "average_success_rate": 85.6,
      "last_used": "2024-08-10T14:30:00Z",
      "last_used_by": "usr_64a1b2c3d4e5f6789012348"
    },
    "created_by": {
      "user_id": "usr_64a1b2c3d4e5f6789012345",
      "name": "Marketing Team",
      "email": "<EMAIL>"
    },
    "created_at": "2024-08-05T09:00:00Z",
    "updated_at": "2024-08-10T14:30:00Z"
  }
}
```

## Implementation Tasks

### 1. Extended DAO Methods (30 minutes)
```typescript
// src/lib/db/dao/template.dao.ts (extend existing)
export class TemplateDAO {
  // ... existing methods ...

  static async getTemplatesByUsage(orgId: string, limit: number = 10) {
    try {
      const templates = await TemplateModel
        .find({
          org_id: orgId,
          status: 'ACTIVE'
        })
        .sort({ 'usage_stats.times_used': -1 })
        .limit(limit)
        .lean();

      return templates.map(template => this.formatTemplateResponse(template));

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve popular templates',
        'DB_TEMPLATES_POPULAR_FAILED'
      );
    }
  }

  static async getTemplateCategories(orgId: string) {
    try {
      const categories = await TemplateModel.aggregate([
        { $match: { org_id: orgId, status: { $ne: 'ARCHIVED' } } },
        { 
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            last_used: { $max: '$usage_stats.last_used' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      return categories.map(cat => ({
        category: cat._id,
        template_count: cat.count,
        last_used: cat.last_used
      }));

    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve template categories',
        'DB_TEMPLATE_CATEGORIES_FAILED'
      );
    }
  }
}
```

### 2. Enhanced Handler Methods (30 minutes)
```typescript
// src/handlers/template.handler.ts (extend existing)
export class TemplateHandler {
  // ... existing methods from templates_post.md ...

  static async getPopularTemplates(req: Request, res: Response) {
    try {
      const orgId = req.organizationId;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const popularTemplates = await TemplateDAO.getTemplatesByUsage(orgId, Math.min(limit, 50));

      res.status(200).json({
        success: true,
        data: {
          templates: popularTemplates,
          count: popularTemplates.length
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async updateTemplateStatus(req: Request, res: Response) {
    try {
      const { template_id } = req.params;
      const { status, reason } = req.body;
      const orgId = req.organizationId;

      if (!orgId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      const validStatuses = ['ACTIVE', 'ARCHIVED', 'DRAFT'];
      if (!validStatuses.includes(status)) {
        throw new ValidationError('Invalid status value', 'VALIDATION_ERROR');
      }

      const template = await TemplateDAO.getTemplateById(template_id, orgId);
      if (!template) {
        throw new NotFoundError(`Template with ID ${template_id} not found`, 'TEMPLATE_NOT_FOUND');
      }

      await TemplateModel.findOneAndUpdate(
        { template_id, org_id: orgId },
        { 
          status,
          ...(reason && { status_reason: reason }),
          updated_at: new Date()
        }
      );

      res.status(200).json({
        success: true,
        data: {
          template_id,
          status,
          updated_at: new Date().toISOString()
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 3. Extended Route Configuration (15 minutes)
```typescript
// src/routes/template.routes.ts (extend existing)
import { Router } from 'express';
import { TemplateHandler } from '@/handlers/template.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateCreateTemplate, validateTemplateStatusUpdate } from '@/validators/template.validator';

const router = Router();

// Template CRUD operations
router.post('/', supabaseAuthMiddleware, validateCreateTemplate, TemplateHandler.createTemplate);
router.get('/', supabaseAuthMiddleware, TemplateHandler.getTemplates);
router.get('/:template_id', supabaseAuthMiddleware, TemplateHandler.getTemplateById);

// Template management endpoints
router.get('/popular/list', supabaseAuthMiddleware, TemplateHandler.getPopularTemplates);
router.patch('/:template_id/status', supabaseAuthMiddleware, validateTemplateStatusUpdate, TemplateHandler.updateTemplateStatus);

export { router as templateRoutes };
```

### 4. Additional Validation (15 minutes)
```typescript
// src/validators/template.validator.ts (extend existing)
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const templateStatusUpdateSchema = Joi.object({
  status: Joi.string().valid('ACTIVE', 'ARCHIVED', 'DRAFT').required(),
  reason: Joi.string().max(500).optional()
});

export const validateTemplateStatusUpdate = (req: Request, res: Response, next: NextFunction) => {
  const { error } = templateStatusUpdateSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }

  next();
};

export const validateTemplateQuery = (req: Request, res: Response, next: NextFunction) => {
  const querySchema = Joi.object({
    category: Joi.string().valid(
      'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'REMINDER', 'SEASONAL', 'OTHER'
    ).optional(),
    template_type: Joi.string().valid('CAMPAIGN', 'MESSAGE', 'AUTOMATION').optional(),
    status: Joi.string().valid('ACTIVE', 'DRAFT', 'ARCHIVED').optional(),
    tags: Joi.string().optional(),
    search: Joi.string().min(2).max(100).optional(),
    sort_by: Joi.string().valid('created_at', 'name', 'usage_stats.times_used').optional(),
    sort_order: Joi.string().valid('asc', 'desc').optional(),
    page: Joi.number().integer().min(1).max(1000).optional(),
    limit: Joi.number().integer().min(1).max(50).optional()
  });

  const { error } = querySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message
      }
    });
  }

  next();
};
```

## Error Handling

### Common Errors
- **400 Invalid Query Parameters**: Invalid filter values or malformed queries
- **404 Template Not Found**: Template doesn't exist or user doesn't have access
- **422 Invalid Search Query**: Search query too short or contains invalid characters
- **429 Rate Limited**: Too many requests
- **500 Database Error**: Issues with template retrieval

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "TEMPLATE_NOT_FOUND",
    "message": "Template with ID tpl_64a1b2c3d4e5f6789012346 not found",
    "code": 404
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Advanced Features

### 1. Template Filtering and Search
- **Category-based filtering**: Filter by promotional, transactional, etc.
- **Status-based filtering**: Active, draft, archived templates
- **Usage-based sorting**: Most used, recently used, highest success rate
- **Full-text search**: Search in names, descriptions, content, tags
- **Tag-based filtering**: Multiple tag support

### 2. Template Management
- **Status management**: Activate, archive, or draft templates
- **Usage tracking**: Monitor template usage patterns
- **Popular templates**: Most frequently used templates
- **Category insights**: Performance by template category

## Testing Strategy

### Unit Tests
- Test template filtering logic with various parameter combinations
- Test pagination edge cases
- Test error handling for invalid parameters
- Test status update functionality

### Integration Tests
- Test complete template retrieval flow with authentication
- Test template management operations
- Test database operations
- Test error scenarios

### Performance Tests
- Test query performance with large numbers of templates
- Test concurrent template access scenarios
- Test database index effectiveness

## Security Considerations

1. **Access Control**: Ensure users can only access their organization's templates
2. **Query Injection Prevention**: Sanitize all search and filter parameters  
3. **Rate Limiting**: Prevent abuse of template endpoints
4. **Data Privacy**: Mask sensitive template content in list views
5. **Audit Logging**: Track template access and usage patterns

## Monitoring and Performance

### Metrics to Track
- Template retrieval response times
- Filter usage patterns
- Most accessed templates
- Error rates by endpoint

### Caching Strategy
- Cache popular template lists (5-minute TTL)
- Cache template categories and counts (15-minute TTL)
- Invalidate cache on template status changes

### Database Optimization
- Ensure proper indexes on frequently queried fields
- Monitor slow queries and optimize as needed
- Implement query result pagination for large datasets