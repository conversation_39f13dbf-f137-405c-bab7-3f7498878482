# Campaign Service - POST /campaigns/{id}/test (Test Campaign)

## Task Overview
**Endpoint**: `POST /api/campaign/v1/campaigns/{campaign_id}/test`  
**Purpose**: Send test messages to validate campaign content and configuration before full deployment  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Campaign creation, message service integration, contact validation  

## API Specification

### Request
```http
POST /api/campaign/v1/campaigns/{campaign_id}/test
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>

{
  "test_config": {
    "test_type": "PREVIEW",
    "recipients": [
      {
        "contact_id": "64a1b2c3d4e5f6789012345",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "name": "Test User"
      },
      {
        "contact_id": "64a1b2c3d4e5f6789012346",
        "email": "<EMAIL>",
        "phone": "+1234567891",
        "name": "Admin User"
      }
    ],
    "channels": ["sms", "email"],
    "override_variables": {
      "discount": "25",
      "code": "TEST25",
      "shop_url": "https://test.example.com"
    },
    "test_mode": true,
    "validate_only": false
  },
  "delivery_options": {
    "immediate": true,
    "scheduled_at": null,
    "max_delivery_time": 300
  }
}
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "test_id": "test_64a1b2c3d4e5f6789012347",
    "campaign_id": "64a1b2c3d4e5f6789012346",
    "test_status": "SENT",
    "recipients_count": 2,
    "messages_sent": {
      "sms": 2,
      "email": 2,
      "total": 4
    },
    "test_results": [
      {
        "recipient": {
          "contact_id": "64a1b2c3d4e5f6789012345",
          "email": "<EMAIL>",
          "phone": "+1234567890"
        },
        "channels": {
          "sms": {
            "status": "DELIVERED",
            "message_id": "msg_64a1b2c3d4e5f6789012348",
            "sent_at": "2024-08-11T15:30:00Z",
            "content_preview": "🌞 Summer Sale! Get 25% off. Use: TEST25"
          },
          "email": {
            "status": "DELIVERED",
            "message_id": "msg_64a1b2c3d4e5f6789012349",
            "sent_at": "2024-08-11T15:30:01Z",
            "subject": "Summer Sale - 25% Off!",
            "content_preview": "Dear Test User, don't miss our summer sale..."
          }
        }
      }
    ],
    "validation_results": {
      "content_valid": true,
      "variables_resolved": true,
      "sender_ids_valid": true,
      "templates_valid": true,
      "issues": []
    },
    "cost_estimate": {
      "sms": 0.10,
      "email": 0.02,
      "total": 0.12,
      "currency": "USD"
    },
    "created_at": "2024-08-11T15:30:00Z"
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012350"
  }
}
```

## Implementation Tasks

### 1. Database Model (20 minutes)
```typescript
// src/lib/db/models/campaign-test.model.ts
import { Schema, model } from 'mongoose';

const campaignTestSchema = new Schema({
  org_id: { type: String, required: true, index: true },
  campaign_id: { type: String, required: true, index: true },
  test_id: { type: String, required: true, unique: true },
  test_config: {
    test_type: { 
      type: String, 
      enum: ['PREVIEW', 'VALIDATION', 'DELIVERY_TEST'],
      required: true 
    },
    recipients: [{
      contact_id: String,
      email: String,
      phone: String,
      name: String
    }],
    channels: [{ type: String, enum: ['sms', 'email', 'push'] }],
    override_variables: Schema.Types.Mixed,
    test_mode: { type: Boolean, default: true },
    validate_only: { type: Boolean, default: false }
  },
  test_results: [{
    recipient: {
      contact_id: String,
      email: String,
      phone: String
    },
    channels: Schema.Types.Mixed,
    delivery_time: Date,
    status: { 
      type: String, 
      enum: ['SENT', 'DELIVERED', 'FAILED', 'PENDING'] 
    }
  }],
  validation_results: {
    content_valid: Boolean,
    variables_resolved: Boolean,
    sender_ids_valid: Boolean,
    templates_valid: Boolean,
    issues: [String]
  },
  cost_estimate: {
    sms: Number,
    email: Number,
    total: Number,
    currency: { type: String, default: 'USD' }
  },
  status: { 
    type: String, 
    enum: ['PENDING', 'RUNNING', 'COMPLETED', 'FAILED'],
    default: 'PENDING'
  }
}, { timestamps: true });

export const CampaignTestModel = model('CampaignTest', campaignTestSchema);
```

### 2. DAO Implementation (45 minutes)
```typescript
// src/lib/db/dao/campaign-test.dao.ts
import { CampaignTestModel } from '../models/campaign-test.model';
import { generateTestId } from '@/lib/utils/id-generator';

export class CampaignTestDAO {
  static async createTest(orgId: string, campaignId: string, testData: any) {
    const testId = generateTestId();
    
    const campaignTest = new CampaignTestModel({
      org_id: orgId,
      campaign_id: campaignId,
      test_id: testId,
      ...testData
    });

    return await campaignTest.save();
  }

  static async getTestById(testId: string, orgId: string) {
    return await CampaignTestModel.findOne({
      test_id: testId,
      org_id: orgId
    });
  }

  static async getTestsByCampaign(campaignId: string, orgId: string) {
    return await CampaignTestModel.find({
      campaign_id: campaignId,
      org_id: orgId
    }).sort({ created_at: -1 });
  }

  static async updateTestStatus(testId: string, status: string, results?: any) {
    const updateData: any = { status };
    if (results) {
      updateData.test_results = results;
    }

    return await CampaignTestModel.findOneAndUpdate(
      { test_id: testId },
      updateData,
      { new: true }
    );
  }
}
```

### 3. Handler Implementation (1 hour)
```typescript
// src/handlers/campaign-test.handler.ts
import { Request, Response } from 'express';
import { CampaignTestDAO } from '@/lib/db/dao/campaign-test.dao';
import { CampaignDAO } from '@/lib/db/dao/campaign.dao';
import { MessageService } from '@/services/message.service';
import { handleError } from '@/lib/errors/error-handler';
import { NotFoundError, ValidationError } from '@/lib/errors/error-types';

export class CampaignTestHandler {
  static async testCampaign(req: Request, res: Response) {
    try {
      const { campaign_id } = req.params;
      const { test_config, delivery_options } = req.body;
      const orgId = req.organizationId;

      // Validate campaign exists
      const campaign = await CampaignDAO.getCampaignById(campaign_id, orgId);
      if (!campaign) {
        throw new NotFoundError('Campaign not found', 'CAMPAIGN_NOT_FOUND');
      }

      // Validate test configuration
      if (!test_config?.recipients?.length) {
        throw new ValidationError('Test recipients are required', 'VALIDATION_ERROR');
      }

      // Create test record
      const testRecord = await CampaignTestDAO.createTest(orgId, campaign_id, {
        test_config,
        delivery_options,
        status: 'PENDING'
      });

      // Send test messages
      const testResults = await MessageService.sendTestMessages({
        campaign,
        test_config,
        test_id: testRecord.test_id
      });

      // Update test record with results
      await CampaignTestDAO.updateTestStatus(
        testRecord.test_id,
        'COMPLETED',
        testResults
      );

      // Format response
      const response = {
        test_id: testRecord.test_id,
        campaign_id,
        test_status: 'SENT',
        recipients_count: test_config.recipients.length,
        messages_sent: testResults.messages_sent,
        test_results: testResults.results,
        validation_results: testResults.validation,
        cost_estimate: testResults.cost_estimate,
        created_at: testRecord.createdAt
      };

      res.status(200).json({
        success: true,
        data: response,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }

  static async getTestResults(req: Request, res: Response) {
    try {
      const { test_id } = req.params;
      const orgId = req.organizationId;

      const testRecord = await CampaignTestDAO.getTestById(test_id, orgId);
      if (!testRecord) {
        throw new NotFoundError('Test record not found', 'TEST_NOT_FOUND');
      }

      res.status(200).json({
        success: true,
        data: testRecord,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.requestId
        }
      });

    } catch (error) {
      handleError(error, res);
    }
  }
}
```

### 4. Route Configuration (15 minutes)
```typescript
// src/routes/campaign-test.routes.ts
import { Router } from 'express';
import { CampaignTestHandler } from '@/handlers/campaign-test.handler';
import { supabaseAuthMiddleware } from '@/lib/middlewares/auth.middleware';
import { validateTestCampaign } from '@/validators/campaign-test.validator';

const router = Router();

// Test campaign
router.post(
  '/:campaign_id/test',
  supabaseAuthMiddleware,
  validateTestCampaign,
  CampaignTestHandler.testCampaign
);

// Get test results
router.get(
  '/test/:test_id',
  supabaseAuthMiddleware,
  CampaignTestHandler.getTestResults
);

export { router as campaignTestRoutes };
```

### 5. Validation Schema (20 minutes)
```typescript
// src/validators/campaign-test.validator.ts
import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

const testCampaignSchema = Joi.object({
  test_config: Joi.object({
    test_type: Joi.string().valid('PREVIEW', 'VALIDATION', 'DELIVERY_TEST').required(),
    recipients: Joi.array().items(
      Joi.object({
        contact_id: Joi.string(),
        email: Joi.string().email(),
        phone: Joi.string(),
        name: Joi.string()
      }).or('email', 'phone')
    ).min(1).max(10).required(),
    channels: Joi.array().items(Joi.string().valid('sms', 'email', 'push')).required(),
    override_variables: Joi.object().optional(),
    test_mode: Joi.boolean().default(true),
    validate_only: Joi.boolean().default(false)
  }).required(),
  delivery_options: Joi.object({
    immediate: Joi.boolean().default(true),
    scheduled_at: Joi.date().iso().optional(),
    max_delivery_time: Joi.number().min(60).max(3600).default(300)
  }).optional()
});

export const validateTestCampaign = (req: Request, res: Response, next: NextFunction) => {
  const { error } = testCampaignSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        type: 'VALIDATION_ERROR',
        message: error.details[0].message,
        details: error.details
      }
    });
  }
  next();
};
```

### 6. Service Integration (30 minutes)
```typescript
// src/services/message.service.ts (extend existing)
export class MessageService {
  static async sendTestMessages(params: {
    campaign: any;
    test_config: any;
    test_id: string;
  }) {
    const { campaign, test_config, test_id } = params;
    const results = [];
    let totalCost = 0;
    const messagesSent = { sms: 0, email: 0, total: 0 };

    for (const recipient of test_config.recipients) {
      const recipientResult = {
        recipient,
        channels: {}
      };

      for (const channel of test_config.channels) {
        try {
          const message = await this.buildTestMessage(campaign, channel, recipient, test_config.override_variables);
          const sendResult = await this.sendSingleTestMessage(message, channel, test_id);
          
          recipientResult.channels[channel] = sendResult;
          messagesSent[channel]++;
          messagesSent.total++;
          totalCost += sendResult.cost || 0;

        } catch (error) {
          recipientResult.channels[channel] = {
            status: 'FAILED',
            error: error.message
          };
        }
      }

      results.push(recipientResult);
    }

    // Validate campaign content
    const validation = await this.validateCampaignContent(campaign, test_config);

    return {
      results,
      messages_sent: messagesSent,
      validation,
      cost_estimate: {
        sms: messagesSent.sms * 0.05,
        email: messagesSent.email * 0.01,
        total: totalCost,
        currency: 'USD'
      }
    };
  }

  private static async buildTestMessage(campaign: any, channel: string, recipient: any, overrideVars: any) {
    const channelConfig = campaign.channels[channel];
    if (!channelConfig?.enabled) {
      throw new Error(`Channel ${channel} is not enabled for this campaign`);
    }

    // Merge variables
    const variables = {
      ...channelConfig.template_variables,
      ...overrideVars,
      contact: recipient
    };

    // Replace template variables in content
    let content = channelConfig.content;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      content = content.replace(regex, String(value));
    }

    return {
      channel,
      recipient,
      content,
      subject: channel === 'email' ? channelConfig.subject : undefined,
      sender_id: channelConfig.sender_id,
      sender_email: channelConfig.sender_email
    };
  }

  private static async sendSingleTestMessage(message: any, channel: string, testId: string) {
    // Implementation for sending actual test message
    // This would integrate with your messaging providers
    
    return {
      status: 'DELIVERED',
      message_id: `test_msg_${Date.now()}`,
      sent_at: new Date().toISOString(),
      content_preview: message.content.substring(0, 100),
      cost: channel === 'sms' ? 0.05 : 0.01
    };
  }

  private static async validateCampaignContent(campaign: any, testConfig: any) {
    const issues = [];
    
    // Validate sender IDs
    for (const [channel, config] of Object.entries(campaign.channels)) {
      if (config.enabled && channel === 'sms' && !config.sender_id) {
        issues.push(`SMS sender ID is required for channel ${channel}`);
      }
    }

    // Validate template variables
    // Add more validation logic as needed

    return {
      content_valid: issues.length === 0,
      variables_resolved: true,
      sender_ids_valid: !issues.some(issue => issue.includes('sender ID')),
      templates_valid: true,
      issues
    };
  }
}
```

## Error Handling

### Common Errors
- **404 Campaign Not Found**: Campaign doesn't exist or user doesn't have access
- **400 Invalid Recipients**: No valid recipients provided or recipients exceed limit
- **400 Invalid Channels**: Unsupported or disabled channels specified
- **429 Rate Limited**: Too many test requests in short period
- **500 Delivery Failed**: Message delivery service unavailable

### Error Response Format
```json
{
  "success": false,
  "error": {
    "type": "CAMPAIGN_NOT_FOUND",
    "message": "Campaign with ID 64a1b2c3d4e5f6789012346 not found",
    "code": 404
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012350"
  }
}
```

## Testing Strategy

### Unit Tests
- Test campaign validation logic
- Test message building with variable substitution
- Test cost calculation
- Test error handling scenarios

### Integration Tests
- Test end-to-end campaign testing flow
- Test message delivery integration
- Test database operations
- Test authentication and authorization

### Load Testing
- Test with maximum recipients (10 recipients)
- Test concurrent test requests
- Test message delivery performance

## Security Considerations

1. **Authentication**: Verify JWT token and organization access
2. **Rate Limiting**: Limit test requests per organization (50/hour)
3. **Recipient Validation**: Validate recipient contact information
4. **Cost Control**: Implement daily test cost limits per organization
5. **Data Privacy**: Ensure test messages comply with privacy regulations

## Monitoring and Analytics

### Metrics to Track
- Test success/failure rates
- Average test delivery time
- Most tested campaigns
- Test cost per organization
- Channel-specific test performance

### Logs to Capture
- Test request details
- Message delivery status
- Validation failures
- Cost calculations
- Performance metrics