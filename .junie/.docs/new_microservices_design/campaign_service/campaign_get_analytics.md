# GET /api/v1/campaigns/{id}/analytics - Campaign Performance Analytics

## Endpoint Overview
**Method**: GET  
**URL**: `/api/v1/campaigns/{campaign_id}/analytics`  
**Service**: Campaign Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 200 requests per minute

## Purpose
Retrieves comprehensive performance analytics for a specific campaign including delivery metrics, engagement rates, cost analysis, and trend data.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Path Parameters
- `campaign_id` (string): MongoDB ObjectId of the campaign

## Query Parameters
```
time_range=7d                    # Time range: 1h, 24h, 7d, 30d, 90d (default: 7d)
include_breakdown=true           # Include detailed breakdowns (default: false)
include_comparisons=false        # Include comparison with previous periods (default: false)
segment_by=channel              # Segment data by: channel, time, audience (optional)
export_format=json              # Export format: json, csv, xlsx (optional)
timezone=America/New_York       # Timezone for date calculations (default: UTC)
refresh_cache=false             # Force refresh of cached analytics (default: false)
```

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "campaign_id": "64a1b2c3d4e5f6789012345",
    "campaign_name": "Black Friday Sale 2024",
    "campaign_type": "BROADCAST",
    "campaign_status": "COMPLETED",
    "analytics_period": {
      "start_date": "2024-12-05T00:00:00Z",
      "end_date": "2024-12-12T23:59:59Z",
      "duration_hours": 191,
      "timezone": "America/New_York"
    },
    "overview_metrics": {
      "total_recipients": 25430,
      "messages_sent": 25430,
      "messages_delivered": 24789,
      "messages_failed": 641,
      "delivery_rate": 97.48,
      "total_cost": 345.67,
      "cost_per_recipient": 0.0136,
      "roi": {
        "revenue_generated": 12450.00,
        "cost": 345.67,
        "roi_percentage": 3502.3,
        "profit": 12104.33
      }
    },
    "channel_breakdown": {
      "sms": {
        "recipients": 15230,
        "sent": 15230,
        "delivered": 14892,
        "failed": 338,
        "delivery_rate": 97.78,
        "cost": 186.45,
        "engagement_metrics": {
          "click_through_rate": 8.5,
          "response_rate": 3.2,
          "opt_out_rate": 0.8,
          "unique_clicks": 1265,
          "total_clicks": 1847
        }
      },
      "email": {
        "recipients": 10200,
        "sent": 10200,
        "delivered": 9897,
        "failed": 303,
        "delivery_rate": 97.03,
        "cost": 159.22,
        "engagement_metrics": {
          "open_rate": 24.3,
          "click_through_rate": 4.7,
          "unsubscribe_rate": 0.2,
          "bounce_rate": 2.97,
          "unique_opens": 2405,
          "unique_clicks": 465,
          "total_opens": 3124,
          "total_clicks": 612
        }
      }
    },
    "time_series_data": {
      "granularity": "hourly",
      "data_points": [
        {
          "timestamp": "2024-12-05T09:00:00Z",
          "sent": 1247,
          "delivered": 1198,
          "failed": 49,
          "opens": 289,
          "clicks": 67,
          "cost": 15.24
        },
        {
          "timestamp": "2024-12-05T10:00:00Z",
          "sent": 1356,
          "delivered": 1322,
          "failed": 34,
          "opens": 312,
          "clicks": 78,
          "cost": 16.58
        }
      ]
    },
    "audience_segmentation": {
      "demographics": {
        "age_groups": {
          "18-24": { "recipients": 3847, "engagement_rate": 12.4 },
          "25-34": { "recipients": 8965, "engagement_rate": 15.7 },
          "35-44": { "recipients": 7234, "engagement_rate": 18.2 },
          "45-54": { "recipients": 3892, "engagement_rate": 14.8 },
          "55+": { "recipients": 1492, "engagement_rate": 11.3 }
        },
        "locations": {
          "US": { "recipients": 18234, "engagement_rate": 16.2 },
          "CA": { "recipients": 4567, "engagement_rate": 14.8 },
          "GB": { "recipients": 2629, "engagement_rate": 13.5 }
        }
      },
      "behavioral": {
        "new_customers": { "recipients": 8965, "conversion_rate": 8.7 },
        "returning_customers": { "recipients": 12845, "conversion_rate": 12.4 },
        "vip_customers": { "recipients": 3620, "conversion_rate": 24.6 }
      }
    },
    "performance_benchmarks": {
      "industry_averages": {
        "delivery_rate": 94.5,
        "open_rate": 21.8,
        "click_rate": 3.9,
        "unsubscribe_rate": 0.4
      },
      "your_performance": {
        "delivery_rate": 97.48,
        "open_rate": 24.3,
        "click_rate": 6.1,
        "unsubscribe_rate": 0.5
      },
      "performance_score": 92
    },
    "conversion_tracking": {
      "total_conversions": 892,
      "conversion_rate": 3.51,
      "revenue_per_conversion": 13.95,
      "conversion_funnel": {
        "message_delivered": 24789,
        "message_opened": 3124,
        "link_clicked": 612,
        "landing_page_visited": 547,
        "converted": 892
      }
    },
    "device_breakdown": {
      "mobile": {
        "percentage": 68.4,
        "engagement_rate": 16.7
      },
      "desktop": {
        "percentage": 24.8,
        "engagement_rate": 14.2
      },
      "tablet": {
        "percentage": 6.8,
        "engagement_rate": 12.9
      }
    },
    "top_performing_content": {
      "subject_lines": [
        {
          "content": "🔥 BLACK FRIDAY: 50% OFF Everything!",
          "sent": 8965,
          "open_rate": 28.7
        }
      ],
      "call_to_actions": [
        {
          "text": "Shop Now",
          "clicks": 423,
          "click_rate": 15.2
        }
      ]
    },
    "errors_and_issues": {
      "total_errors": 641,
      "error_breakdown": {
        "invalid_phone_number": 234,
        "email_bounce": 187,
        "carrier_blocked": 125,
        "spam_filter": 95
      },
      "resolution_suggestions": [
        "Update contact list to remove invalid phone numbers",
        "Implement email validation before sending",
        "Review message content for spam triggers"
      ]
    },
    "comparison_data": {
      "previous_campaign": {
        "campaign_name": "October Flash Sale",
        "delivery_rate": 95.2,
        "engagement_rate": 12.4,
        "cost": 298.45,
        "roi_percentage": 2876.3,
        "improvement": {
          "delivery_rate": "*****%",
          "engagement_rate": "****%",
          "cost": "+15.8%",
          "roi": "+21.8%"
        }
      }
    },
    "metadata": {
      "generated_at": "2024-12-12T15:30:00Z",
      "data_freshness": "real-time",
      "processing_time_ms": 2341,
      "cache_expires_at": "2024-12-12T16:30:00Z",
      "export_available": true
    }
  },
  "message": "Campaign analytics retrieved successfully"
}
```

## Error Responses

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "CAMPAIGN_004",
    "message": "Campaign not found",
    "details": [
      {
        "field": "campaign_id",
        "message": "No campaign found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "CAMPAIGN_007",
    "message": "Access denied to campaign analytics",
    "details": [
      {
        "field": "permissions",
        "message": "User does not have analytics access for this campaign"
      }
    ]
  }
}
```

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "CAMPAIGN_008",
    "message": "Invalid analytics request parameters",
    "details": [
      {
        "field": "time_range",
        "message": "Invalid time range. Allowed values: 1h, 24h, 7d, 30d, 90d"
      }
    ]
  }
}
```

## Implementation Details

### Analytics Processing Pipeline
```typescript
interface CampaignAnalyticsProcessor {
  async generateAnalytics(campaignId: string, options: AnalyticsOptions): Promise<CampaignAnalytics> {
    const campaign = await this.getCampaignDetails(campaignId);
    const messageData = await this.getMessageData(campaignId, options.timeRange);
    const engagementData = await this.getEngagementData(campaignId, options.timeRange);
    const conversionData = await this.getConversionData(campaignId, options.timeRange);
    
    return {
      overview: this.calculateOverviewMetrics(messageData, engagementData),
      channelBreakdown: this.calculateChannelMetrics(messageData, engagementData),
      timeSeries: this.generateTimeSeriesData(messageData, options.granularity),
      audienceSegmentation: this.calculateAudienceMetrics(messageData, engagementData),
      conversions: this.calculateConversionMetrics(conversionData)
    };
  }
}
```

### Performance Optimizations
- Analytics data cached for 1 hour for completed campaigns
- Real-time data for active campaigns
- Pre-computed metrics for large campaigns
- Pagination for time series data with >1000 points

### Business Logic

#### Engagement Rate Calculations
```typescript
interface EngagementCalculator {
  calculateDeliveryRate(sent: number, delivered: number): number {
    return sent > 0 ? (delivered / sent) * 100 : 0;
  }
  
  calculateOpenRate(delivered: number, opened: number): number {
    return delivered > 0 ? (opened / delivered) * 100 : 0;
  }
  
  calculateClickRate(delivered: number, clicked: number): number {
    return delivered > 0 ? (clicked / delivered) * 100 : 0;
  }
  
  calculateConversionRate(delivered: number, conversions: number): number {
    return delivered > 0 ? (conversions / delivered) * 100 : 0;
  }
}
```

#### ROI Calculations
```typescript
interface ROICalculator {
  calculateROI(revenue: number, cost: number): ROIMetrics {
    const profit = revenue - cost;
    const roiPercentage = cost > 0 ? (profit / cost) * 100 : 0;
    
    return {
      revenue,
      cost,
      profit,
      roi_percentage: roiPercentage,
      cost_per_dollar_earned: revenue > 0 ? cost / revenue : 0
    };
  }
}
```

### Data Sources Integration
- **Message Service**: Delivery and engagement data
- **Payment Service**: Revenue and conversion tracking
- **Core Service**: Contact and segment information
- **External Analytics**: Third-party tracking pixels

## Usage Examples

### Basic Campaign Analytics
```bash
GET /api/v1/campaigns/64a1b2c3d4e5f6789012345/analytics
```

### Detailed Analytics with Breakdown
```bash
GET /api/v1/campaigns/64a1b2c3d4e5f6789012345/analytics?time_range=30d&include_breakdown=true&segment_by=channel
```

### Analytics with Comparisons
```bash
GET /api/v1/campaigns/64a1b2c3d4e5f6789012345/analytics?include_comparisons=true&timezone=America/New_York
```

### Export Analytics Data
```bash
GET /api/v1/campaigns/64a1b2c3d4e5f6789012345/analytics?export_format=csv&time_range=90d
```

### Real-time Analytics
```bash
GET /api/v1/campaigns/64a1b2c3d4e5f6789012345/analytics?refresh_cache=true&time_range=24h
```