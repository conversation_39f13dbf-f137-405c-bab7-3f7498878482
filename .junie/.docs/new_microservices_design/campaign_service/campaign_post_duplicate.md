# POST /api/v1/campaigns/{id}/duplicate - Duplicate Campaign

## Endpoint Overview
**Method**: POST  
**URL**: `/api/v1/campaigns/{campaign_id}/duplicate`  
**Service**: Campaign Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 50 requests per minute

## Purpose
Creates a duplicate copy of an existing campaign with optional modifications. This allows users to quickly create new campaigns based on successful templates or previous campaigns.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Path Parameters
- `campaign_id` (string): MongoDB ObjectId of the campaign to duplicate

## Request Body
```json
{
  "duplicate_config": {
    "new_name": "Black Friday Sale 2025 (Copy)",
    "copy_schedule": false,
    "copy_segments": true,
    "copy_content": true,
    "copy_settings": true,
    "reset_analytics": true,
    "duplicate_type": "FULL"
  },
  "modifications": {
    "name": "Black Friday Sale 2025",
    "description": "Updated campaign for 2025 Black Friday sales event",
    "status": "DRAFT",
    "channels": {
      "sms": {
        "enabled": true,
        "content": "🎉 BLACK FRIDAY 2025: Save 60% on everything! Limited time offer. Shop now: {{shop_url}}"
      },
      "email": {
        "enabled": true,
        "subject": "BLACK FRIDAY 2025: 60% OFF Everything!",
        "content": "Don't miss our biggest sale of 2025..."
      }
    },
    "schedule_config": {
      "is_scheduled": true,
      "scheduled_at": "2025-11-28T09:00:00Z",
      "timezone": "America/New_York"
    },
    "target_config": {
      "type": "SEGMENTS",
      "segment_ids": ["64a1b2c3d4e5f6789012346", "64a1b2c3d4e5f6789012347"],
      "filters": {
        "exclude_unsubscribed": true,
        "exclude_bounced": true,
        "last_activity_days": 60
      }
    }
  },
  "copy_options": {
    "include_ab_tests": false,
    "include_analytics": false,
    "include_triggers": true,
    "include_attachments": true,
    "preserve_sender_ids": true
  },
  "metadata": {
    "duplicated_reason": "Creating 2025 version of successful campaign",
    "original_campaign_performance": {
      "delivery_rate": 97.5,
      "engagement_rate": 18.2,
      "roi": 3502.3
    },
    "expected_improvements": [
      "Updated discount percentage",
      "Enhanced targeting segments",
      "Improved call-to-action"
    ]
  }
}
```

## Request Body Schema

### Required Fields
- `duplicate_config` (object): Configuration for duplication process

### Optional Fields
- `modifications` (object): Fields to modify in the duplicate
- `copy_options` (object): Specific elements to include/exclude
- `metadata` (object): Additional information about the duplication

### Duplicate Config Object
```typescript
interface DuplicateConfig {
  new_name?: string;              // New name for duplicate (default: "{original_name} (Copy)")
  copy_schedule?: boolean;        // Copy scheduling configuration (default: false)
  copy_segments?: boolean;        // Copy segment targeting (default: true)
  copy_content?: boolean;         // Copy message content (default: true)
  copy_settings?: boolean;        // Copy campaign settings (default: true)
  reset_analytics?: boolean;      // Reset analytics data (default: true)
  duplicate_type?: 'FULL' | 'TEMPLATE' | 'CONTENT_ONLY'; // Type of duplication (default: FULL)
}
```

### Duplicate Types
- **FULL**: Complete copy including all settings, content, and configurations
- **TEMPLATE**: Copy structure and settings but reset content for editing
- **CONTENT_ONLY**: Copy only message content and basic settings

## Response

### Success Response (201 Created)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012999",
    "org_id": "org_123456789",
    "created_by": "user_123456789",
    "original_campaign_id": "64a1b2c3d4e5f6789012345",
    "name": "Black Friday Sale 2025",
    "description": "Updated campaign for 2025 Black Friday sales event",
    "type": "BROADCAST",
    "status": "DRAFT",
    "target_config": {
      "type": "SEGMENTS",
      "segment_ids": ["64a1b2c3d4e5f6789012346", "64a1b2c3d4e5f6789012347"],
      "estimated_recipients": 28450,
      "filters": {
        "exclude_unsubscribed": true,
        "exclude_bounced": true,
        "last_activity_days": 60
      }
    },
    "channels": {
      "sms": {
        "enabled": true,
        "sender_id": "STORE25",
        "content": "🎉 BLACK FRIDAY 2025: Save 60% on everything! Limited time offer. Shop now: {{shop_url}}",
        "template_variables": {
          "shop_url": "https://shop.example.com/bf2025",
          "discount": "60",
          "year": "2025"
        }
      },
      "email": {
        "enabled": true,
        "sender_email": "<EMAIL>",
        "sender_name": "Example Store",
        "subject": "BLACK FRIDAY 2025: 60% OFF Everything!",
        "content": "Don't miss our biggest sale of 2025...",
        "html_content": "<html>...</html>",
        "template_variables": {
          "discount": "60",
          "year": "2025",
          "shop_url": "https://shop.example.com/bf2025"
        }
      }
    },
    "schedule_config": {
      "is_scheduled": true,
      "scheduled_at": "2025-11-28T09:00:00Z",
      "timezone": "America/New_York",
      "recurring": {
        "enabled": false
      }
    },
    "cost_analysis": {
      "estimated_cost": {
        "sms": 567.89,
        "email": 234.56,
        "total": 802.45,
        "currency": "USD"
      },
      "budget_allocation": {
        "approved": false,
        "requires_approval": true,
        "approval_threshold": 500.00
      }
    },
    "duplication_details": {
      "original_campaign": {
        "id": "64a1b2c3d4e5f6789012345",
        "name": "Black Friday Sale 2024",
        "created_at": "2024-11-15T10:30:00Z",
        "performance_summary": {
          "delivery_rate": 97.5,
          "engagement_rate": 18.2,
          "roi": 3502.3
        }
      },
      "duplication_type": "FULL",
      "elements_copied": [
        "campaign_structure",
        "message_content", 
        "targeting_config",
        "sender_settings",
        "template_variables"
      ],
      "elements_modified": [
        "campaign_name",
        "message_content",
        "schedule_config",
        "discount_percentage"
      ],
      "elements_excluded": [
        "campaign_analytics",
        "send_history",
        "delivery_logs"
      ],
      "copy_processing_time_ms": 1847
    },
    "validation_results": {
      "status": "VALID",
      "warnings": [
        {
          "code": "SCHEDULE_FUTURE_DATE",
          "message": "Campaign scheduled for future date (2025-11-28). Verify date is correct."
        },
        {
          "code": "CONTENT_SIMILARITY",
          "message": "Content is very similar to original campaign. Consider updating for uniqueness."
        }
      ],
      "required_approvals": [
        {
          "type": "BUDGET_APPROVAL",
          "reason": "Estimated cost ($802.45) exceeds auto-approval limit",
          "approver_role": "campaign_manager"
        }
      ]
    },
    "ab_test_config": {
      "enabled": false,
      "note": "A/B tests not copied as per copy_options.include_ab_tests=false"
    },
    "tags": ["black-friday", "2025", "promotional", "duplicated"],
    "metadata": {
      "duplicated_reason": "Creating 2025 version of successful campaign",
      "original_campaign_performance": {
        "delivery_rate": 97.5,
        "engagement_rate": 18.2,
        "roi": 3502.3
      },
      "expected_improvements": [
        "Updated discount percentage",
        "Enhanced targeting segments", 
        "Improved call-to-action"
      ]
    },
    "created_at": "2024-12-16T16:45:00Z",
    "updated_at": "2024-12-16T16:45:00Z"
  },
  "message": "Campaign duplicated successfully. Review and approve before scheduling.",
  "warnings": [
    {
      "code": "BUDGET_APPROVAL_REQUIRED",
      "message": "Campaign requires budget approval before it can be sent due to estimated cost."
    }
  ]
}
```

### Error Responses

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "CAMPAIGN_004",
    "message": "Original campaign not found",
    "details": [
      {
        "field": "campaign_id",
        "message": "No campaign found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "CAMPAIGN_009",
    "message": "Invalid duplication request",
    "details": [
      {
        "field": "duplicate_config.new_name",
        "message": "Campaign name already exists in organization"
      },
      {
        "field": "modifications.channels.sms.content",
        "message": "SMS content exceeds maximum length of 1600 characters"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "CAMPAIGN_010",
    "message": "Access denied for campaign duplication",
    "details": [
      {
        "field": "permissions",
        "message": "User does not have permission to duplicate campaigns in this organization"
      }
    ]
  }
}
```

### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "CAMPAIGN_011",
    "message": "Campaign duplication validation failed",
    "details": [
      {
        "field": "target_config.segment_ids",
        "message": "One or more segments no longer exist or are inactive"
      },
      {
        "field": "cost_analysis.estimated_cost",
        "message": "Estimated cost exceeds organization budget limits"
      }
    ]
  }
}
```

## Implementation Details

### Duplication Processing Pipeline
```typescript
interface CampaignDuplicationProcessor {
  async duplicateCampaign(
    originalId: string,
    config: DuplicateConfig,
    modifications?: CampaignModifications
  ): Promise<Campaign> {
    
    // Step 1: Validate source campaign
    const originalCampaign = await this.validateSourceCampaign(originalId);
    
    // Step 2: Create base duplicate
    const duplicateCampaign = await this.createBaseDuplicate(originalCampaign, config);
    
    // Step 3: Apply modifications
    if (modifications) {
      await this.applyModifications(duplicateCampaign, modifications);
    }
    
    // Step 4: Validate duplicate
    const validationResult = await this.validateDuplicate(duplicateCampaign);
    
    // Step 5: Save and return
    return await this.saveDuplicate(duplicateCampaign, validationResult);
  }
}
```

### Business Logic

#### Duplication Rules
1. **Content Copying**: All message content, templates, and variables are copied
2. **Segment Validation**: Target segments are validated for existence and accessibility
3. **Sender ID Validation**: Sender IDs are checked for approval status
4. **Cost Calculation**: New cost estimates are calculated for the duplicate
5. **Analytics Reset**: All performance metrics are reset for the new campaign

#### Smart Defaults
```typescript
interface SmartDefaults {
  generateDefaultName(originalName: string): string {
    const timestamp = new Date().toISOString().split('T')[0];
    return `${originalName} (Copy ${timestamp})`;
  }
  
  adjustScheduleDate(originalDate: Date): Date {
    // If original date is in the past, suggest next business day
    const now = new Date();
    return originalDate < now ? this.getNextBusinessDay(now) : originalDate;
  }
  
  updateTemplateVariables(variables: Record<string, string>): Record<string, string> {
    // Update year references automatically
    const currentYear = new Date().getFullYear().toString();
    const updatedVariables = { ...variables };
    
    Object.keys(updatedVariables).forEach(key => {
      if (updatedVariables[key].includes('2024')) {
        updatedVariables[key] = updatedVariables[key].replace('2024', currentYear);
      }
    });
    
    return updatedVariables;
  }
}
```

#### Validation Logic
```typescript
interface DuplicationValidator {
  async validateDuplicate(campaign: Campaign): Promise<ValidationResult> {
    const issues: ValidationIssue[] = [];
    
    // Check campaign name uniqueness
    if (await this.campaignNameExists(campaign.name, campaign.org_id)) {
      issues.push({
        field: 'name',
        severity: 'ERROR',
        message: 'Campaign name already exists'
      });
    }
    
    // Validate segments still exist
    const invalidSegments = await this.validateSegments(campaign.target_config.segment_ids);
    if (invalidSegments.length > 0) {
      issues.push({
        field: 'target_config.segment_ids',
        severity: 'ERROR',
        message: `Invalid segments: ${invalidSegments.join(', ')}`
      });
    }
    
    // Check budget limits
    if (campaign.cost_analysis.estimated_cost.total > await this.getBudgetLimit(campaign.org_id)) {
      issues.push({
        field: 'cost_analysis',
        severity: 'WARNING',
        message: 'Estimated cost exceeds budget limit, approval required'
      });
    }
    
    return {
      isValid: !issues.some(i => i.severity === 'ERROR'),
      issues
    };
  }
}
```

### Performance Optimizations
- Bulk operations for copying large campaigns with many segments
- Async processing for complex duplications with extensive modifications
- Caching of validation results for repeated duplication requests
- Optimized queries to fetch only necessary data from original campaign

### Security Considerations
1. **Access Control**: Users can only duplicate campaigns they have access to
2. **Organization Isolation**: Duplicates are created within the same organization
3. **Content Sanitization**: All copied content is sanitized to prevent XSS
4. **Rate Limiting**: Prevents abuse of duplication functionality
5. **Audit Trail**: All duplications are logged with user and timestamp information

## Usage Examples

### Basic Campaign Duplication
```json
{
  "duplicate_config": {
    "new_name": "Holiday Sale 2025",
    "duplicate_type": "FULL"
  }
}
```

### Template-Only Duplication
```json
{
  "duplicate_config": {
    "new_name": "New Campaign from Template",
    "copy_schedule": false,
    "copy_segments": false,
    "duplicate_type": "TEMPLATE",
    "reset_analytics": true
  },
  "modifications": {
    "description": "Fresh campaign based on proven template",
    "status": "DRAFT"
  }
}
```

### Content Update Duplication
```json
{
  "duplicate_config": {
    "duplicate_type": "FULL"
  },
  "modifications": {
    "name": "Updated Black Friday Campaign",
    "channels": {
      "sms": {
        "content": "🎄 EXTENDED: Black Friday deals continue! 70% off everything!"
      },
      "email": {
        "subject": "EXTENDED: Black Friday Deals Continue!",
        "content": "By popular demand, we're extending our Black Friday sale..."
      }
    }
  },
  "copy_options": {
    "include_ab_tests": false,
    "include_analytics": false,
    "preserve_sender_ids": true
  }
}
```

### Scheduled Duplicate with Targeting Changes
```json
{
  "duplicate_config": {
    "new_name": "Q1 2025 Reactivation Campaign",
    "copy_schedule": false,
    "duplicate_type": "FULL"
  },
  "modifications": {
    "schedule_config": {
      "is_scheduled": true,
      "scheduled_at": "2025-01-15T10:00:00Z",
      "timezone": "America/New_York"
    },
    "target_config": {
      "type": "SEGMENTS",
      "segment_ids": ["64a1b2c3d4e5f6789012999"],
      "filters": {
        "exclude_unsubscribed": true,
        "last_activity_days": 180
      }
    }
  }
}
```