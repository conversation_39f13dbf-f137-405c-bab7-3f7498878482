# Campaign Service - POST /campaigns/{id}/schedule (Schedule Campaign)

## Task Overview
**Endpoint**: `POST /api/campaign/v1/campaigns/{id}/schedule`  
**Purpose**: Schedule a campaign for future execution with timezone support and validation  
**Estimated Time**: 1.5 days  
**Priority**: High  
**Dependencies**: Campaign retrieval, Bull MQ queues, timezone handling  

## API Specification

### Request
```http
POST /api/campaign/v1/campaigns/64a1b2c3d4e5f6789012346/schedule
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "scheduled_at": "2024-08-15T10:30:00Z",
  "timezone": "America/New_York",
  "schedule_config": {
    "send_immediately_if_past": false,
    "cancel_if_past": true,
    "max_delay_minutes": 60
  }
}
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "message": "Campaign scheduled successfully",
    "campaign": {
      "_id": "64a1b2c3d4e5f6789012346",
      "name": "Summer Sale 2024",
      "status": "SCHEDULED",
      "schedule_config": {
        "is_scheduled": true,
        "scheduled_at": "2024-08-15T10:30:00Z",
        "timezone": "America/New_York",
        "local_scheduled_at": "2024-08-15T06:30:00-04:00",
        "job_id": "campaign-send-64a1b2c3d4e5f6789012346-1692096600000"
      }
    }
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "INVALID_SCHEDULE_TIME",
    "message": "Scheduled time must be at least 5 minutes in the future",
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (60 minutes)
```typescript
// src/services/campaign.service.ts (addition)
import { CampaignSendQueue } from '@/queues/campaign.send.queue';
import moment from 'moment-timezone';

export class CampaignService {
  static async scheduleCampaign(
    campaignId: string,
    orgId: string,
    scheduleData: CampaignScheduleRequest
  ): Promise<CampaignScheduleResponse> {
    try {
      // Validate campaign exists and can be scheduled
      const campaign = await this.getCampaignById(campaignId, orgId);
      this.validateCampaignScheduling(campaign);

      // Process and validate schedule time
      const processedSchedule = this.processScheduleTime(scheduleData);

      // Update campaign with schedule information
      const updatedCampaign = await CampaignModel.findOneAndUpdate(
        { _id: campaignId, org_id: orgId },
        {
          status: 'SCHEDULED',
          schedule_config: processedSchedule,
          updated_at: new Date()
        },
        { new: true, runValidators: true }
      ).lean();

      if (!updatedCampaign) {
        throw new NotFoundError('Campaign not found');
      }

      // Schedule the campaign send job
      const jobId = await this.scheduleInQueue(campaignId, processedSchedule);

      // Update campaign with job ID
      await CampaignModel.updateOne(
        { _id: campaignId },
        { 'schedule_config.job_id': jobId }
      );

      // Invalidate cache
      await CampaignCacheService.invalidateCampaignCache(campaignId, orgId);

      return {
        message: 'Campaign scheduled successfully',
        campaign: {
          _id: updatedCampaign._id.toString(),
          name: updatedCampaign.name,
          status: updatedCampaign.status,
          schedule_config: {
            ...processedSchedule,
            job_id: jobId
          }
        }
      };
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to schedule campaign: ${error.message}`);
    }
  }

  private static validateCampaignScheduling(campaign: any): void {
    // Only DRAFT and PAUSED campaigns can be scheduled
    const allowedStatuses = ['DRAFT', 'PAUSED'];
    
    if (!allowedStatuses.includes(campaign.status)) {
      throw new BadRequestError(
        `Cannot schedule campaign in ${campaign.status} status`,
        'CAMPAIGN_SCHEDULE_NOT_ALLOWED'
      );
    }

    // Campaign must be built before scheduling
    if (!campaign.stats?.total_recipients || campaign.stats.total_recipients === 0) {
      throw new BadRequestError(
        'Campaign must be built before scheduling',
        'CAMPAIGN_NOT_BUILT'
      );
    }

    // Check if campaign is already scheduled
    if (campaign.schedule_config?.is_scheduled) {
      throw new BadRequestError(
        'Campaign is already scheduled. Cancel existing schedule first.',
        'CAMPAIGN_ALREADY_SCHEDULED'
      );
    }
  }

  private static processScheduleTime(scheduleData: CampaignScheduleRequest): ScheduleConfig {
    const { scheduled_at, timezone, schedule_config } = scheduleData;
    
    // Convert scheduled time to UTC if timezone is provided
    let utcScheduledTime: Date;
    let localScheduledTime: string;

    if (timezone) {
      // Validate timezone
      if (!moment.tz.zone(timezone)) {
        throw new BadRequestError(`Invalid timezone: ${timezone}`, 'INVALID_TIMEZONE');
      }

      // Convert to UTC
      const momentTime = moment.tz(scheduled_at, timezone);
      utcScheduledTime = momentTime.utc().toDate();
      localScheduledTime = momentTime.format();
    } else {
      utcScheduledTime = new Date(scheduled_at);
      localScheduledTime = scheduled_at;
    }

    // Validate schedule time is in the future
    const now = new Date();
    const minScheduleTime = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now

    if (utcScheduledTime < minScheduleTime) {
      if (schedule_config?.send_immediately_if_past) {
        utcScheduledTime = new Date(now.getTime() + 1 * 60 * 1000); // 1 minute from now
      } else if (schedule_config?.cancel_if_past) {
        throw new BadRequestError(
          'Scheduled time is in the past and cancel_if_past is enabled',
          'SCHEDULE_TIME_IN_PAST'
        );
      } else {
        throw new BadRequestError(
          'Scheduled time must be at least 5 minutes in the future',
          'INVALID_SCHEDULE_TIME'
        );
      }
    }

    // Validate max delay if specified
    if (schedule_config?.max_delay_minutes) {
      const maxDelay = schedule_config.max_delay_minutes * 60 * 1000;
      const maxScheduleTime = new Date(now.getTime() + maxDelay);
      
      if (utcScheduledTime > maxScheduleTime) {
        throw new BadRequestError(
          `Scheduled time cannot be more than ${schedule_config.max_delay_minutes} minutes in the future`,
          'SCHEDULE_TIME_TOO_FAR'
        );
      }
    }

    return {
      is_scheduled: true,
      scheduled_at: utcScheduledTime,
      timezone: timezone || 'UTC',
      local_scheduled_at: localScheduledTime,
      schedule_config: schedule_config || {}
    };
  }

  private static async scheduleInQueue(campaignId: string, scheduleConfig: ScheduleConfig): Promise<string> {
    try {
      const delay = scheduleConfig.scheduled_at.getTime() - Date.now();
      
      const job = await CampaignSendQueue.add(
        'send-campaign',
        {
          campaignId,
          scheduledAt: scheduleConfig.scheduled_at.toISOString(),
          timezone: scheduleConfig.timezone
        },
        {
          delay: delay,
          jobId: `campaign-send-${campaignId}-${scheduleConfig.scheduled_at.getTime()}`,
          removeOnComplete: 10,
          removeOnFail: 10,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 30000
          }
        }
      );

      return job.id as string;
    } catch (error) {
      throw new InternalError(`Failed to schedule campaign in queue: ${error.message}`);
    }
  }

  static async cancelCampaignSchedule(campaignId: string, orgId: string): Promise<void> {
    try {
      const campaign = await this.getCampaignById(campaignId, orgId);
      
      if (!campaign.schedule_config?.is_scheduled) {
        throw new BadRequestError('Campaign is not scheduled', 'CAMPAIGN_NOT_SCHEDULED');
      }

      // Cancel the queue job
      if (campaign.schedule_config.job_id) {
        await CampaignSendQueue.removeJob(campaign.schedule_config.job_id);
      }

      // Update campaign status
      await CampaignModel.updateOne(
        { _id: campaignId, org_id: orgId },
        {
          status: 'DRAFT',
          schedule_config: {
            is_scheduled: false,
            scheduled_at: null,
            timezone: null,
            job_id: null
          },
          updated_at: new Date()
        }
      );

      // Invalidate cache
      await CampaignCacheService.invalidateCampaignCache(campaignId, orgId);
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to cancel campaign schedule: ${error.message}`);
    }
  }
}
```

### 2. Controller Implementation (30 minutes)
```typescript
// src/controllers/campaign.controller.ts (addition)
export class CampaignController {
  static async scheduleCampaign(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Campaign ID is required');
      }

      const result = await CampaignService.scheduleCampaign(
        id,
        req.user.org_id,
        req.body
      );

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }

  static async cancelCampaignSchedule(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Campaign ID is required');
      }

      await CampaignService.cancelCampaignSchedule(id, req.user.org_id);

      res.json({
        success: true,
        data: {
          message: 'Campaign schedule cancelled successfully'
        },
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 3. Validation Schema (25 minutes)
```typescript
// src/validators/campaign.validator.ts (addition)
import Joi from 'joi';
import moment from 'moment-timezone';

const timezoneValidator = (value: string, helpers: any) => {
  if (!moment.tz.zone(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

export const campaignScheduleSchema = Joi.object({
  scheduled_at: Joi.string().isoDate().required(),
  timezone: Joi.string().custom(timezoneValidator, 'timezone validation').optional(),
  schedule_config: Joi.object({
    send_immediately_if_past: Joi.boolean().optional().default(false),
    cancel_if_past: Joi.boolean().optional().default(true),
    max_delay_minutes: Joi.number().integer().min(5).max(43200).optional() // Max 30 days
  }).optional()
});
```

### 4. Route Definition (15 minutes)
```typescript
// src/routes/campaign.routes.ts (addition)
import { validateRequestBody } from '@/middleware/validation.middleware';
import { campaignScheduleSchema } from '@/validators/campaign.validator';

router.post(
  '/:id/schedule',
  authenticateRequest,
  validateObjectId('id'),
  validateRequestBody(campaignScheduleSchema),
  rateLimitMiddleware,
  CampaignController.scheduleCampaign
);

router.delete(
  '/:id/schedule',
  authenticateRequest,
  validateObjectId('id'),
  rateLimitMiddleware,
  CampaignController.cancelCampaignSchedule
);
```

### 5. Type Definitions (20 minutes)
```typescript
// src/types/campaign.types.ts (additions)
export interface CampaignScheduleRequest {
  scheduled_at: string;
  timezone?: string;
  schedule_config?: {
    send_immediately_if_past?: boolean;
    cancel_if_past?: boolean;
    max_delay_minutes?: number;
  };
}

export interface ScheduleConfig {
  is_scheduled: boolean;
  scheduled_at: Date;
  timezone: string;
  local_scheduled_at: string;
  job_id?: string;
  schedule_config?: {
    send_immediately_if_past?: boolean;
    cancel_if_past?: boolean;
    max_delay_minutes?: number;
  };
}

export interface CampaignScheduleResponse {
  message: string;
  campaign: {
    _id: string;
    name: string;
    status: string;
    schedule_config: ScheduleConfig;
  };
}
```

### 6. Queue Implementation (40 minutes)
```typescript
// src/queues/campaign.send.queue.ts (enhancement)
import Queue from 'bull';
import { RedisConnector } from '@/lib/db/connectors/RedisConnector';

export const CampaignSendQueue = new Queue('campaign-send', {
  redis: RedisConnector.getConnectionOptions(),
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 10,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 30000
    }
  }
});

// Process scheduled campaign sends
CampaignSendQueue.process('send-campaign', async (job) => {
  const { campaignId, scheduledAt, timezone } = job.data;
  
  try {
    // Validate the campaign is still scheduled and ready
    const campaign = await CampaignModel.findById(campaignId);
    
    if (!campaign) {
      throw new Error(`Campaign ${campaignId} not found`);
    }
    
    if (campaign.status !== 'SCHEDULED') {
      throw new Error(`Campaign ${campaignId} is no longer scheduled (status: ${campaign.status})`);
    }
    
    // Update campaign status to SENDING
    await CampaignModel.updateOne(
      { _id: campaignId },
      {
        status: 'SENDING',
        started_at: new Date(),
        updated_at: new Date()
      }
    );
    
    // Trigger the actual campaign sending process
    // This would typically add jobs to the message service queue
    await CampaignService.executeCampaignSend(campaignId);
    
    console.log(`Campaign ${campaignId} scheduled send completed`);
  } catch (error) {
    console.error(`Campaign ${campaignId} scheduled send failed:`, error);
    
    // Update campaign status to FAILED
    await CampaignModel.updateOne(
      { _id: campaignId },
      {
        status: 'FAILED',
        updated_at: new Date()
      }
    );
    
    throw error;
  }
});

// Handle failed jobs
CampaignSendQueue.on('failed', (job, error) => {
  console.error(`Campaign send job ${job.id} failed:`, error);
});

// Handle completed jobs
CampaignSendQueue.on('completed', (job) => {
  console.log(`Campaign send job ${job.id} completed`);
});

export { CampaignSendQueue };
```

### 7. Database Model Updates (15 minutes)
```typescript
// src/lib/db/models/campaign.model.ts (updates)
const campaignSchema = new Schema({
  // ... existing fields
  schedule_config: {
    is_scheduled: {
      type: Boolean,
      default: false
    },
    scheduled_at: {
      type: Date,
      default: null
    },
    timezone: {
      type: String,
      default: null
    },
    local_scheduled_at: {
      type: String,
      default: null
    },
    job_id: {
      type: String,
      default: null
    },
    schedule_config: {
      send_immediately_if_past: {
        type: Boolean,
        default: false
      },
      cancel_if_past: {
        type: Boolean,
        default: true
      },
      max_delay_minutes: {
        type: Number,
        default: null
      }
    }
  }
});

// Add index for scheduled campaigns
campaignSchema.index({ 'schedule_config.is_scheduled': 1, 'schedule_config.scheduled_at': 1 });
```

### 8. Unit Tests (45 minutes)
```typescript
// tests/unit/services/campaign.service.test.ts (addition)
describe('CampaignService.scheduleCampaign', () => {
  let mockCampaign: any;

  beforeEach(() => {
    mockCampaign = {
      _id: '64a1b2c3d4e5f6789012346',
      name: 'Test Campaign',
      status: 'DRAFT',
      stats: { total_recipients: 100 },
      schedule_config: { is_scheduled: false }
    };
  });

  it('should schedule campaign successfully', async () => {
    (CampaignService.getCampaignById as jest.Mock).mockResolvedValue(mockCampaign);
    (CampaignModel.findOneAndUpdate as jest.Mock).mockResolvedValue({
      ...mockCampaign,
      status: 'SCHEDULED'
    });
    (CampaignSendQueue.add as jest.Mock).mockResolvedValue({ id: 'job-123' });

    const scheduleData = {
      scheduled_at: '2024-08-15T10:30:00Z',
      timezone: 'America/New_York'
    };

    const result = await CampaignService.scheduleCampaign('campaign-id', 'org-id', scheduleData);

    expect(result.message).toBe('Campaign scheduled successfully');
    expect(result.campaign.status).toBe('SCHEDULED');
    expect(CampaignSendQueue.add).toHaveBeenCalled();
  });

  it('should reject scheduling for active campaign', async () => {
    mockCampaign.status = 'ACTIVE';
    (CampaignService.getCampaignById as jest.Mock).mockResolvedValue(mockCampaign);

    const scheduleData = {
      scheduled_at: '2024-08-15T10:30:00Z'
    };

    await expect(
      CampaignService.scheduleCampaign('campaign-id', 'org-id', scheduleData)
    ).rejects.toThrow('Cannot schedule campaign in ACTIVE status');
  });

  it('should reject past schedule time', async () => {
    (CampaignService.getCampaignById as jest.Mock).mockResolvedValue(mockCampaign);

    const scheduleData = {
      scheduled_at: '2024-01-01T10:30:00Z' // Past date
    };

    await expect(
      CampaignService.scheduleCampaign('campaign-id', 'org-id', scheduleData)
    ).rejects.toThrow('Scheduled time must be at least 5 minutes in the future');
  });

  it('should handle timezone conversion', async () => {
    (CampaignService.getCampaignById as jest.Mock).mockResolvedValue(mockCampaign);
    (CampaignModel.findOneAndUpdate as jest.Mock).mockResolvedValue({
      ...mockCampaign,
      status: 'SCHEDULED'
    });
    (CampaignSendQueue.add as jest.Mock).mockResolvedValue({ id: 'job-123' });

    const scheduleData = {
      scheduled_at: '2024-08-15T14:30:00',
      timezone: 'America/New_York'
    };

    const result = await CampaignService.scheduleCampaign('campaign-id', 'org-id', scheduleData);

    expect(result.campaign.schedule_config.timezone).toBe('America/New_York');
    expect(result.campaign.schedule_config.local_scheduled_at).toBeDefined();
  });
});
```

### 9. Integration Tests (35 minutes)
```typescript
// tests/integration/campaign.schedule.api.test.ts (new file)
describe('POST /api/campaign/v1/campaigns/:id/schedule', () => {
  let authToken: string;
  let orgId: string;
  let campaign: any;

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
    
    campaign = await CampaignModel.create({
      org_id: orgId,
      created_by: 'user123',
      name: 'Test Campaign',
      type: 'BROADCAST',
      status: 'DRAFT',
      target_config: { type: 'ALL' },
      channels: { sms: { enabled: true, sender_id: 'TEST', content: 'Hello' } },
      stats: { total_recipients: 100 }
    });
  });

  afterAll(async () => {
    await CampaignModel.deleteMany({ org_id: orgId });
  });

  it('should schedule campaign successfully', async () => {
    const futureTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
    
    const response = await request(app)
      .post(`/api/campaign/v1/campaigns/${campaign._id}/schedule`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        scheduled_at: futureTime.toISOString(),
        timezone: 'UTC'
      })
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.message).toBe('Campaign scheduled successfully');
    expect(response.body.data.campaign.status).toBe('SCHEDULED');

    // Verify database update
    const updatedCampaign = await CampaignModel.findById(campaign._id);
    expect(updatedCampaign?.status).toBe('SCHEDULED');
    expect(updatedCampaign?.schedule_config.is_scheduled).toBe(true);
  });

  it('should reject past schedule time', async () => {
    const pastTime = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
    
    const response = await request(app)
      .post(`/api/campaign/v1/campaigns/${campaign._id}/schedule`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        scheduled_at: pastTime.toISOString()
      })
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_SCHEDULE_TIME');
  });

  it('should validate timezone', async () => {
    const futureTime = new Date(Date.now() + 10 * 60 * 1000);
    
    const response = await request(app)
      .post(`/api/campaign/v1/campaigns/${campaign._id}/schedule`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        scheduled_at: futureTime.toISOString(),
        timezone: 'Invalid/Timezone'
      })
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_TIMEZONE');
  });
});
```

## Testing Checklist
- [ ] Unit tests for service layer scheduling logic
- [ ] Unit tests for timezone handling
- [ ] Unit tests for validation rules
- [ ] Integration tests for schedule endpoint
- [ ] Queue job testing
- [ ] Schedule cancellation tests
- [ ] Error handling tests
- [ ] Authentication tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Timezone handling documentation
- [ ] Queue job documentation
- [ ] Business rules documentation

This task implements comprehensive campaign scheduling functionality with timezone support, queue management, validation, and proper error handling.