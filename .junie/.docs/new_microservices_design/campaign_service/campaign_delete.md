# Campaign Service - DELETE /campaigns/{id} (Delete Campaign)

## Task Overview
**Endpoint**: `DELETE /api/campaign/v1/campaigns/{id}`  
**Purpose**: Delete a campaign permanently with proper validation and cleanup  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Campaign retrieval, authentication middleware, database models  

## API Specification

### Request
```http
DELETE /api/campaign/v1/campaigns/64a1b2c3d4e5f6789012346
Authorization: Bearer <jwt_token>
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "message": "Campaign deleted successfully",
    "deleted_campaign": {
      "_id": "64a1b2c3d4e5f6789012346",
      "name": "Summer Sale 2024",
      "status": "DRAFT"
    }
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "CAMPAIGN_DELETE_NOT_ALLOWED",
    "message": "Cannot delete campaign in ACTIVE status",
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (45 minutes)
```typescript
// src/services/campaign.service.ts (addition)
export class CampaignService {
  static async deleteCampaign(campaignId: string, orgId: string): Promise<CampaignDeleteResponse> {
    try {
      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(campaignId)) {
        throw new BadRequestError('Invalid campaign ID format');
      }

      // Find campaign with organization check
      const campaign = await CampaignModel.findOne({
        _id: campaignId,
        org_id: orgId
      }).lean();

      if (!campaign) {
        throw new NotFoundError('Campaign not found or access denied');
      }

      // Validate deletion is allowed
      this.validateCampaignDeletion(campaign);

      // Perform soft delete (mark as deleted instead of physical delete)
      const deletedCampaign = await CampaignModel.findOneAndUpdate(
        { _id: campaignId, org_id: orgId },
        { 
          status: 'DELETED',
          deleted_at: new Date(),
          updated_at: new Date()
        },
        { new: true }
      ).lean();

      // Clean up related data
      await this.cleanupCampaignData(campaignId);

      // Invalidate cache
      await CampaignCacheService.invalidateCampaignCache(campaignId, orgId);

      return {
        message: 'Campaign deleted successfully',
        deleted_campaign: {
          _id: campaign._id.toString(),
          name: campaign.name,
          status: campaign.status
        }
      };
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to delete campaign: ${error.message}`);
    }
  }

  private static validateCampaignDeletion(campaign: any): void {
    // Cannot delete active or completed campaigns
    const restrictedStatuses = ['ACTIVE', 'SENDING', 'COMPLETED'];
    
    if (restrictedStatuses.includes(campaign.status)) {
      throw new BadRequestError(
        `Cannot delete campaign in ${campaign.status} status`,
        'CAMPAIGN_DELETE_NOT_ALLOWED'
      );
    }

    // Cannot delete campaigns with scheduled sends
    if (campaign.schedule_config?.is_scheduled && campaign.schedule_config?.scheduled_at > new Date()) {
      throw new BadRequestError(
        'Cannot delete scheduled campaign. Cancel the schedule first.',
        'CAMPAIGN_SCHEDULED_DELETE_NOT_ALLOWED'
      );
    }
  }

  private static async cleanupCampaignData(campaignId: string): Promise<void> {
    try {
      // Clean up campaign logs
      await CampaignLogModel.deleteMany({ campaign_id: campaignId });
      
      // Cancel any pending queue jobs
      await this.cancelCampaignJobs(campaignId);
      
      // Additional cleanup can be added here
    } catch (error) {
      // Log cleanup errors but don't fail the deletion
      console.error(`Cleanup error for campaign ${campaignId}:`, error);
    }
  }

  private static async cancelCampaignJobs(campaignId: string): Promise<void> {
    // Implementation to cancel Bull MQ jobs related to this campaign
    // This would interact with the campaign build and send queues
  }
}
```

### 2. Controller Implementation (20 minutes)
```typescript
// src/controllers/campaign.controller.ts (addition)
export class CampaignController {
  static async deleteCampaign(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Campaign ID is required');
      }

      const result = await CampaignService.deleteCampaign(id, req.user.org_id);

      res.json({
        success: true,
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 3. Route Definition (10 minutes)
```typescript
// src/routes/campaign.routes.ts (addition)
import { validateObjectId } from '@/middleware/validation.middleware';

router.delete(
  '/:id',
  authenticateRequest,
  validateObjectId('id'),
  rateLimitMiddleware,
  CampaignController.deleteCampaign
);
```

### 4. Type Definitions (15 minutes)
```typescript
// src/types/campaign.types.ts (additions)
export interface CampaignDeleteResponse {
  message: string;
  deleted_campaign: {
    _id: string;
    name: string;
    status: string;
  };
}
```

### 5. Database Model Enhancement (20 minutes)
```typescript
// src/lib/db/models/campaign.model.ts (addition)
// Add deleted_at field to schema
const campaignSchema = new Schema({
  // ... existing fields
  deleted_at: {
    type: Date,
    default: null
  }
});

// Add compound index for soft delete queries
campaignSchema.index({ org_id: 1, status: 1, deleted_at: 1 });

// Add query helpers to exclude deleted campaigns by default
campaignSchema.query.active = function() {
  return this.where({ status: { $ne: 'DELETED' }, deleted_at: null });
};
```

### 6. Unit Tests (35 minutes)
```typescript
// tests/unit/controllers/campaign.controller.test.ts (addition)
describe('CampaignController.deleteCampaign', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = {
      params: { id: '64a1b2c3d4e5f6789012346' },
      user: { id: 'user123', org_id: 'org123' },
      headers: { 'x-request-id': 'req123' }
    };
    mockRes = {
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should delete campaign successfully', async () => {
    const mockResult = {
      message: 'Campaign deleted successfully',
      deleted_campaign: {
        _id: '64a1b2c3d4e5f6789012346',
        name: 'Test Campaign',
        status: 'DRAFT'
      }
    };
    
    (CampaignService.deleteCampaign as jest.Mock).mockResolvedValue(mockResult);

    await CampaignController.deleteCampaign(mockReq, mockRes, mockNext);

    expect(CampaignService.deleteCampaign).toHaveBeenCalledWith(
      '64a1b2c3d4e5f6789012346',
      'org123'
    );
    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: mockResult,
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        request_id: 'req123'
      })
    });
  });

  it('should handle missing campaign ID', async () => {
    mockReq.params.id = undefined;

    await CampaignController.deleteCampaign(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(BadRequestError));
  });

  it('should handle campaign not found', async () => {
    (CampaignService.deleteCampaign as jest.Mock).mockRejectedValue(
      new NotFoundError('Campaign not found')
    );

    await CampaignController.deleteCampaign(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(NotFoundError));
  });

  it('should handle deletion of active campaign', async () => {
    (CampaignService.deleteCampaign as jest.Mock).mockRejectedValue(
      new BadRequestError('Cannot delete campaign in ACTIVE status', 'CAMPAIGN_DELETE_NOT_ALLOWED')
    );

    await CampaignController.deleteCampaign(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(BadRequestError));
  });
});
```

### 7. Integration Tests (25 minutes)
```typescript
// tests/integration/campaign.api.test.ts (addition)
describe('DELETE /api/campaign/v1/campaigns/:id', () => {
  let authToken: string;
  let orgId: string;
  let draftCampaign: any;
  let activeCampaign: any;

  beforeAll(async () => {
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
    
    // Create test campaigns
    draftCampaign = await CampaignModel.create({
      org_id: orgId,
      created_by: 'user123',
      name: 'Draft Campaign',
      type: 'BROADCAST',
      status: 'DRAFT',
      target_config: { type: 'ALL' },
      channels: { sms: { enabled: true, sender_id: 'TEST', content: 'Hello' } }
    });

    activeCampaign = await CampaignModel.create({
      org_id: orgId,
      created_by: 'user123',
      name: 'Active Campaign',
      type: 'BROADCAST',
      status: 'ACTIVE',
      target_config: { type: 'ALL' },
      channels: { sms: { enabled: true, sender_id: 'TEST', content: 'Hello' } }
    });
  });

  afterAll(async () => {
    await CampaignModel.deleteMany({ org_id: orgId });
  });

  it('should delete draft campaign successfully', async () => {
    const response = await request(app)
      .delete(`/api/campaign/v1/campaigns/${draftCampaign._id}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.message).toBe('Campaign deleted successfully');
    expect(response.body.data.deleted_campaign.name).toBe('Draft Campaign');

    // Verify campaign is soft deleted
    const deletedCampaign = await CampaignModel.findById(draftCampaign._id);
    expect(deletedCampaign?.status).toBe('DELETED');
    expect(deletedCampaign?.deleted_at).toBeDefined();
  });

  it('should not delete active campaign', async () => {
    const response = await request(app)
      .delete(`/api/campaign/v1/campaigns/${activeCampaign._id}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('CAMPAIGN_DELETE_NOT_ALLOWED');
  });

  it('should return 404 for non-existent campaign', async () => {
    const nonExistentId = '64a1b2c3d4e5f6789012999';
    
    const response = await request(app)
      .delete(`/api/campaign/v1/campaigns/${nonExistentId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(404);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('CAMPAIGN_NOT_FOUND');
  });

  it('should return 400 for invalid campaign ID', async () => {
    const response = await request(app)
      .delete('/api/campaign/v1/campaigns/invalid-id')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_ID_FORMAT');
  });

  it('should return 401 without authentication', async () => {
    await request(app)
      .delete(`/api/campaign/v1/campaigns/${draftCampaign._id}`)
      .expect(401);
  });
});
```

### 8. Additional Services Integration (20 minutes)
```typescript
// src/services/campaign.cleanup.service.ts (new file)
export class CampaignCleanupService {
  static async hardDeleteExpiredCampaigns(): Promise<void> {
    // Find campaigns deleted more than 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const expiredCampaigns = await CampaignModel.find({
      status: 'DELETED',
      deleted_at: { $lt: thirtyDaysAgo }
    }).select('_id');

    for (const campaign of expiredCampaigns) {
      await this.hardDeleteCampaign(campaign._id.toString());
    }
  }

  private static async hardDeleteCampaign(campaignId: string): Promise<void> {
    try {
      // Delete campaign logs
      await CampaignLogModel.deleteMany({ campaign_id: campaignId });
      
      // Delete the campaign permanently
      await CampaignModel.deleteOne({ _id: campaignId });
      
      console.log(`Hard deleted campaign: ${campaignId}`);
    } catch (error) {
      console.error(`Failed to hard delete campaign ${campaignId}:`, error);
    }
  }
}
```

### 9. Background Job for Cleanup (15 minutes)
```typescript
// src/workers/processors/campaign.cleanup.processor.ts (new file)
import { Job } from 'bull';
import { CampaignCleanupService } from '@/services/campaign.cleanup.service';

export const processCampaignCleanup = async (job: Job): Promise<void> => {
  try {
    await CampaignCleanupService.hardDeleteExpiredCampaigns();
    console.log('Campaign cleanup completed');
  } catch (error) {
    console.error('Campaign cleanup failed:', error);
    throw error;
  }
};
```

## Business Rules & Constraints

### Deletion Rules
- Only campaigns with status `DRAFT`, `PAUSED`, or `CANCELLED` can be deleted
- Cannot delete `ACTIVE`, `SENDING`, or `COMPLETED` campaigns
- Cannot delete campaigns with future scheduled sends
- Perform soft delete by default (mark as DELETED)
- Hard delete after 30 days via background job

### Data Cleanup
- Remove campaign logs
- Cancel pending queue jobs
- Invalidate related caches
- Preserve audit trail for compliance

## Testing Checklist
- [ ] Unit tests for service layer deletion logic
- [ ] Unit tests for validation rules
- [ ] Integration tests for DELETE endpoint
- [ ] Test deletion restrictions (active campaigns)
- [ ] Test soft delete functionality
- [ ] Test cleanup service
- [ ] Authentication and authorization tests
- [ ] Error handling tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Business rules documentation
- [ ] Data retention policy documentation
- [ ] Cleanup process documentation

This task implements a comprehensive campaign deletion system with proper validation, soft delete functionality, data cleanup, and background processing for permanent deletion.