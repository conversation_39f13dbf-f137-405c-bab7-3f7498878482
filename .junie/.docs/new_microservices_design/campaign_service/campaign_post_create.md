# Campaign Service - POST /campaigns (Create Campaign)

## Task Overview
**Endpoint**: `POST /api/campaign/v1/campaigns`  
**Purpose**: Create a new campaign with validation, cost estimation, and initial setup  
**Estimated Time**: 2 days  
**Priority**: High  
**Dependencies**: Project setup, authentication middleware, database models  

## API Specification

### Request
```http
POST /api/campaign/v1/campaigns
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Summer Sale 2024",
  "description": "Promotional campaign for summer collection",
  "type": "BROADCAST",
  "target_config": {
    "type": "SEGMENTS",
    "segment_ids": ["64a1b2c3d4e5f6789012345"],
    "filters": {
      "exclude_unsubscribed": true,
      "exclude_bounced": true,
      "last_activity_days": 30
    }
  },
  "channels": {
    "sms": {
      "enabled": true,
      "sender_id": "SUMMER24",
      "content": "🌞 Summer Sale! Get {{discount}}% off. Use: {{code}}",
      "template_variables": {
        "discount": "30",
        "code": "SUMMER30"
      }
    },
    "email": {
      "enabled": true,
      "sender_email": "<EMAIL>",
      "subject": "Summer Sale - {{discount}}% Off!",
      "content": "Dear {{contact.name}}, don't miss our summer sale...",
      "template_variables": {
        "discount": "30"
      }
    }
  },
  "schedule_config": {
    "is_scheduled": false
  },
  "tags": ["summer", "promotional"],
  "metadata": {
    "budget_limit": 2000
  }
}
```

### Response
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012346",
    "name": "Summer Sale 2024",
    "type": "BROADCAST",
    "status": "DRAFT",
    "estimated_recipients": 1250,
    "estimated_cost": {
      "sms": 62.50,
      "email": 12.50,
      "total": 75.00,
      "currency": "USD"
    },
    "created_at": "2024-08-11T15:30:00Z",
    "updated_at": "2024-08-11T15:30:00Z"
  },
  "meta": {
    "timestamp": "2024-08-11T15:30:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012347"
  }
}
```

## Implementation Tasks

### 1. Database Model (30 minutes)
```typescript
// src/lib/db/models/campaign.model.ts
import { Schema, model } from 'mongoose';
import { CampaignDocument } from '@/types/campaign.types';

const campaignSchema = new Schema<CampaignDocument>({
  org_id: { type: String, required: true, index: true },
  created_by: { type: String, required: true, index: true },
  name: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 255
  },
  description: { 
    type: String, 
    trim: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: ['BROADCAST', 'TRIGGERED', 'DRIP', 'AB_TEST'],
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: ['DRAFT', 'BUILDING', 'SCHEDULED', 'SENDING', 'COMPLETED', 'CANCELLED', 'FAILED'],
    default: 'DRAFT',
    required: true,
    index: true
  },
  target_config: {
    type: {
      type: String,
      enum: ['CONTACTS', 'SEGMENTS', 'ALL'],
      required: true
    },
    contact_ids: [{ type: Schema.Types.ObjectId, ref: 'Contact' }],
    segment_ids: [{ type: Schema.Types.ObjectId, ref: 'CampaignSegment' }],
    filters: {
      exclude_unsubscribed: { type: Boolean, default: true },
      exclude_bounced: { type: Boolean, default: true },
      last_activity_days: Number
    }
  },
  channels: {
    sms: {
      enabled: { type: Boolean, default: false },
      sender_id: String,
      content: String,
      template_variables: Schema.Types.Mixed
    },
    email: {
      enabled: { type: Boolean, default: false },
      sender_email: String,
      sender_name: String,
      subject: String,
      content: String,
      html_content: String,
      template_variables: Schema.Types.Mixed
    },
    push: {
      enabled: { type: Boolean, default: false },
      title: String,
      content: String,
      template_variables: Schema.Types.Mixed
    }
  },
  schedule_config: {
    is_scheduled: { type: Boolean, default: false },
    scheduled_at: Date,
    timezone: String,
    recurring: {
      enabled: { type: Boolean, default: false },
      frequency: { type: String, enum: ['DAILY', 'WEEKLY', 'MONTHLY'] },
      interval: Number,
      end_date: Date
    }
  },
  stats: {
    total_recipients: { type: Number, default: 0 },
    messages_sent: { type: Number, default: 0 },
    messages_delivered: { type: Number, default: 0 },
    messages_failed: { type: Number, default: 0 },
    open_rate: Number,
    click_rate: Number,
    unsubscribe_rate: Number
  },
  cost_analysis: {
    estimated_cost: {
      sms: { type: Number, default: 0 },
      email: { type: Number, default: 0 },
      push: { type: Number, default: 0 },
      total: { type: Number, default: 0 },
      currency: { type: String, default: 'USD' }
    },
    actual_cost: {
      sms: { type: Number, default: 0 },
      email: { type: Number, default: 0 },
      push: { type: Number, default: 0 },
      total: { type: Number, default: 0 },
      currency: { type: String, default: 'USD' }
    }
  },
  tags: [String],
  metadata: Schema.Types.Mixed
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

// Indexes
campaignSchema.index({ org_id: 1, status: 1 });
campaignSchema.index({ org_id: 1, created_at: -1 });
campaignSchema.index({ org_id: 1, type: 1 });
campaignSchema.index({ 'schedule_config.scheduled_at': 1 }, { sparse: true });

export const CampaignModel = model<CampaignDocument>('Campaign', campaignSchema);
```

### 2. Validation Schema (20 minutes)
```typescript
// src/lib/validation/campaign.validation.ts
import Joi from 'joi';

export const createCampaignSchema = Joi.object({
  name: Joi.string().trim().min(1).max(255).required(),
  description: Joi.string().trim().max(1000).optional(),
  type: Joi.string().valid('BROADCAST', 'TRIGGERED', 'DRIP', 'AB_TEST').required(),
  target_config: Joi.object({
    type: Joi.string().valid('CONTACTS', 'SEGMENTS', 'ALL').required(),
    contact_ids: Joi.array().items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/)).optional(),
    segment_ids: Joi.array().items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/)).optional(),
    filters: Joi.object({
      exclude_unsubscribed: Joi.boolean().default(true),
      exclude_bounced: Joi.boolean().default(true),
      last_activity_days: Joi.number().min(1).max(365).optional()
    }).optional()
  }).required(),
  channels: Joi.object({
    sms: Joi.object({
      enabled: Joi.boolean().required(),
      sender_id: Joi.when('enabled', {
        is: true,
        then: Joi.string().required(),
        otherwise: Joi.optional()
      }),
      content: Joi.when('enabled', {
        is: true,
        then: Joi.string().max(1600).required(),
        otherwise: Joi.optional()
      }),
      template_variables: Joi.object().optional()
    }).optional(),
    email: Joi.object({
      enabled: Joi.boolean().required(),
      sender_email: Joi.when('enabled', {
        is: true,
        then: Joi.string().email().required(),
        otherwise: Joi.optional()
      }),
      sender_name: Joi.string().optional(),
      subject: Joi.when('enabled', {
        is: true,
        then: Joi.string().max(255).required(),
        otherwise: Joi.optional()
      }),
      content: Joi.when('enabled', {
        is: true,
        then: Joi.string().required(),
        otherwise: Joi.optional()
      }),
      html_content: Joi.string().optional(),
      template_variables: Joi.object().optional()
    }).optional(),
    push: Joi.object({
      enabled: Joi.boolean().required(),
      title: Joi.when('enabled', {
        is: true,
        then: Joi.string().max(100).required(),
        otherwise: Joi.optional()
      }),
      content: Joi.when('enabled', {
        is: true,
        then: Joi.string().max(500).required(),
        otherwise: Joi.optional()
      }),
      template_variables: Joi.object().optional()
    }).optional()
  }).required(),
  schedule_config: Joi.object({
    is_scheduled: Joi.boolean().default(false),
    scheduled_at: Joi.when('is_scheduled', {
      is: true,
      then: Joi.date().iso().min('now').required(),
      otherwise: Joi.optional()
    }),
    timezone: Joi.string().optional(),
    recurring: Joi.object({
      enabled: Joi.boolean().default(false),
      frequency: Joi.string().valid('DAILY', 'WEEKLY', 'MONTHLY').optional(),
      interval: Joi.number().min(1).optional(),
      end_date: Joi.date().iso().optional()
    }).optional()
  }).optional(),
  tags: Joi.array().items(Joi.string().trim()).optional(),
  metadata: Joi.object().optional()
});
```

### 3. Service Layer (45 minutes)
```typescript
// src/services/campaign.service.ts
import { CampaignModel } from '@/lib/db/models/campaign.model';
import { CreateCampaignRequest, Campaign } from '@/types/campaign.types';
import { CostEstimationService } from './cost.estimation.service';
import { SegmentService } from './segment.service';
import { ValidationError, BadRequestError } from '@/lib/errors/error.types';

export class CampaignService {
  static async createCampaign(data: CreateCampaignRequest): Promise<Campaign> {
    try {
      // Validate at least one channel is enabled
      const enabledChannels = Object.entries(data.channels)
        .filter(([_, config]) => config?.enabled)
        .map(([channel, _]) => channel);

      if (enabledChannels.length === 0) {
        throw new ValidationError('At least one channel must be enabled');
      }

      // Validate target configuration
      await this.validateTargetConfig(data.target_config, data.org_id);

      // Estimate recipients and cost
      const recipientCount = await this.estimateRecipients(data.target_config, data.org_id);
      const costEstimate = await CostEstimationService.calculateCampaignCost(
        recipientCount,
        enabledChannels
      );

      // Create campaign
      const campaign = new CampaignModel({
        ...data,
        stats: {
          total_recipients: recipientCount,
          messages_sent: 0,
          messages_delivered: 0,
          messages_failed: 0
        },
        cost_analysis: {
          estimated_cost: costEstimate
        }
      });

      const savedCampaign = await campaign.save();
      return this.formatCampaignResponse(savedCampaign);
    } catch (error) {
      if (error instanceof ValidationError || error instanceof BadRequestError) {
        throw error;
      }
      throw new Error(`Failed to create campaign: ${error.message}`);
    }
  }

  private static async validateTargetConfig(targetConfig: any, orgId: string): Promise<void> {
    if (targetConfig.type === 'SEGMENTS' && targetConfig.segment_ids?.length > 0) {
      // Validate segments exist and belong to organization
      const segments = await SegmentService.validateSegments(targetConfig.segment_ids, orgId);
      if (segments.length !== targetConfig.segment_ids.length) {
        throw new ValidationError('One or more segments not found');
      }
    }
  }

  private static async estimateRecipients(targetConfig: any, orgId: string): Promise<number> {
    switch (targetConfig.type) {
      case 'ALL':
        return await this.countAllContacts(orgId, targetConfig.filters);
      case 'SEGMENTS':
        return await this.countSegmentContacts(targetConfig.segment_ids, orgId);
      case 'CONTACTS':
        return targetConfig.contact_ids?.length || 0;
      default:
        return 0;
    }
  }

  private static formatCampaignResponse(campaign: any): Campaign {
    return {
      _id: campaign._id.toString(),
      name: campaign.name,
      type: campaign.type,
      status: campaign.status,
      estimated_recipients: campaign.stats.total_recipients,
      estimated_cost: campaign.cost_analysis.estimated_cost,
      created_at: campaign.created_at,
      updated_at: campaign.updated_at
    };
  }
}
```

### 4. Controller Implementation (30 minutes)
```typescript
// src/controllers/campaign.controller.ts
import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '@/middleware/auth.middleware';
import { CampaignService } from '@/services/campaign.service';
import { createCampaignSchema } from '@/lib/validation/campaign.validation';
import { ValidationError } from '@/lib/errors/error.types';

export class CampaignController {
  static async createCampaign(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Validate request body
      const { error, value } = createCampaignSchema.validate(req.body);
      if (error) {
        throw new ValidationError(error.details[0].message);
      }

      // Add organization and user context
      const campaignData = {
        ...value,
        org_id: req.user.org_id,
        created_by: req.user.id
      };

      // Create campaign
      const campaign = await CampaignService.createCampaign(campaignData);

      res.status(201).json({
        success: true,
        data: campaign,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 5. Route Definition (15 minutes)
```typescript
// src/routes/campaign.routes.ts
import { Router } from 'express';
import { CampaignController } from '@/controllers/campaign.controller';
import { authenticateRequest } from '@/middleware/auth.middleware';
import { rateLimitMiddleware } from '@/middleware/rate.limit.middleware';

const router = Router();

router.post(
  '/',
  authenticateRequest,
  rateLimitMiddleware,
  CampaignController.createCampaign
);

export default router;
```

### 6. Unit Tests (45 minutes)
```typescript
// tests/unit/controllers/campaign.controller.test.ts
import { CampaignController } from '@/controllers/campaign.controller';
import { CampaignService } from '@/services/campaign.service';
import { ValidationError } from '@/lib/errors/error.types';

jest.mock('@/services/campaign.service');

describe('CampaignController.createCampaign', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = {
      body: {
        name: 'Test Campaign',
        type: 'BROADCAST',
        target_config: { type: 'ALL' },
        channels: {
          sms: { enabled: true, sender_id: 'TEST', content: 'Hello' }
        }
      },
      user: { id: 'user123', org_id: 'org123' },
      headers: { 'x-request-id': 'req123' }
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should create campaign successfully', async () => {
    const mockCampaign = { _id: 'campaign123', name: 'Test Campaign' };
    (CampaignService.createCampaign as jest.Mock).mockResolvedValue(mockCampaign);

    await CampaignController.createCampaign(mockReq, mockRes, mockNext);

    expect(mockRes.status).toHaveBeenCalledWith(201);
    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: mockCampaign,
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        request_id: 'req123'
      })
    });
  });

  it('should handle validation errors', async () => {
    mockReq.body.name = ''; // Invalid name

    await CampaignController.createCampaign(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(ValidationError));
  });
});
```

## Background Processors

### Cost Estimation Service (30 minutes)
```typescript
// src/services/cost.estimation.service.ts
export class CostEstimationService {
  private static readonly COSTS = {
    SMS: 0.05, // $0.05 per SMS
    EMAIL: 0.01, // $0.01 per email
    PUSH: 0.001 // $0.001 per push notification
  };

  static async calculateCampaignCost(
    recipientCount: number,
    enabledChannels: string[]
  ): Promise<any> {
    const costs = {
      sms: 0,
      email: 0,
      push: 0,
      total: 0,
      currency: 'USD'
    };

    enabledChannels.forEach(channel => {
      switch (channel.toUpperCase()) {
        case 'SMS':
          costs.sms = recipientCount * this.COSTS.SMS;
          break;
        case 'EMAIL':
          costs.email = recipientCount * this.COSTS.EMAIL;
          break;
        case 'PUSH':
          costs.push = recipientCount * this.COSTS.PUSH;
          break;
      }
    });

    costs.total = costs.sms + costs.email + costs.push;
    return costs;
  }
}
```

## Testing Checklist
- [ ] Unit tests for controller
- [ ] Unit tests for service layer
- [ ] Integration tests for API endpoint
- [ ] Validation schema tests
- [ ] Database model tests
- [ ] Error handling tests
- [ ] Authentication tests

## Documentation
- [ ] API documentation in Swagger
- [ ] Code comments
- [ ] README updates
- [ ] Postman collection

This task creates a complete, production-ready campaign creation endpoint with proper validation, error handling, authentication, and testing.
