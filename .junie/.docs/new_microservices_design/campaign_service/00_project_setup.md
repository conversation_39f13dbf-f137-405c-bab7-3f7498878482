# Campaign Service - Project Setup

## Base Project Structure

```
shoutout_engage_campaign_service/
├── src/
│   ├── app.ts                          # Main Express app
│   ├── server.ts                       # Server entry point
│   ├── routes/
│   │   ├── index.ts                    # Route aggregator
│   │   ├── campaign.routes.ts          # Campaign endpoints
│   │   ├── segment.routes.ts           # Segment endpoints
│   │   └── health.routes.ts            # Health check endpoints
│   ├── controllers/
│   │   ├── campaign.controller.ts      # Campaign business logic
│   │   ├── segment.controller.ts       # Segment business logic
│   │   └── health.controller.ts        # Health check logic
│   ├── services/
│   │   ├── campaign.service.ts         # Campaign operations
│   │   ├── segment.service.ts          # Segment operations
│   │   ├── template.service.ts         # Template processing
│   │   └── analytics.service.ts        # Analytics calculations
│   ├── middleware/
│   │   ├── auth.middleware.ts          # Supabase JWT auth
│   │   ├── validation.middleware.ts    # Request validation
│   │   ├── error.middleware.ts         # Error handling
│   │   └── rate.limit.middleware.ts    # Rate limiting
│   ├── lib/
│   │   ├── db/
│   │   │   ├── models/
│   │   │   │   ├── campaign.model.ts
│   │   │   │   ├── segment.model.ts
│   │   │   │   └── campaign.log.model.ts
│   │   │   ├── dao/
│   │   │   │   ├── campaign.dao.ts
│   │   │   │   └── segment.dao.ts
│   │   │   └── connectors/
│   │   │       ├── MongooseConnector.ts
│   │   │       └── RedisConnector.ts
│   │   ├── config/
│   │   │   ├── index.ts
│   │   │   ├── database.ts
│   │   │   ├── redis.ts
│   │   │   └── supabase.ts
│   │   ├── constant/
│   │   │   ├── campaign.constants.ts
│   │   │   ├── queue.constants.ts
│   │   │   └── validation.constants.ts
│   │   ├── errors/
│   │   │   ├── error.types.ts
│   │   │   └── error.handler.ts
│   │   ├── utils/
│   │   │   ├── logger.ts
│   │   │   ├── validator.ts
│   │   │   └── date.utils.ts
│   │   └── swagger/
│   │       └── swagger.config.ts
│   ├── types/
│   │   ├── campaign.types.ts
│   │   ├── segment.types.ts
│   │   ├── api.types.ts
│   │   └── auth.types.ts
│   ├── workers/
│   │   └── processors/
│   │       ├── campaign.build.processor.ts
│   │       ├── campaign.send.processor.ts
│   │       └── segment.calculate.processor.ts
│   └── queues/
│       ├── campaign.build.queue.ts
│       ├── campaign.send.queue.ts
│       └── segment.calculate.queue.ts
├── tests/
│   ├── unit/
│   │   ├── controllers/
│   │   ├── services/
│   │   └── utils/
│   ├── integration/
│   │   ├── api/
│   │   └── database/
│   └── setup.ts
├── uploads/                            # Temporary file storage
├── logs/                              # Application logs
├── package.json
├── tsconfig.json
├── jest.config.js
├── .env.example
├── .gitignore
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## Package.json Configuration

```json
{
  "name": "shoutout-engage-campaign-service",
  "version": "1.0.0",
  "description": "Campaign management microservice for ShoutOut Engage",
  "main": "dist/server.js",
  "scripts": {
    "dev": "nodemon src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "format": "prettier --write src/**/*.ts"
  },
  "dependencies": {
    "express": "^4.18.0",
    "mongoose": "^7.0.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "joi": "^17.9.0",
    "winston": "^3.8.0",
    "@supabase/supabase-js": "^2.0.0",
    "bull": "^4.10.0",
    "redis": "^4.6.0",
    "jsonwebtoken": "^9.0.0",
    "uuid": "^9.0.0",
    "moment": "^2.29.0",
    "lodash": "^4.17.21",
    "swagger-jsdoc": "^6.2.0",
    "swagger-ui-express": "^4.6.0",
    "express-rate-limit": "^6.7.0",
    "multer": "^1.4.5"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "@types/express": "^4.17.0",
    "@types/mongoose": "^7.0.0",
    "@types/cors": "^2.8.0",
    "@types/joi": "^17.2.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/uuid": "^9.0.0",
    "@types/lodash": "^4.14.0",
    "@types/swagger-jsdoc": "^6.0.0",
    "@types/swagger-ui-express": "^4.1.0",
    "@types/multer": "^1.4.0",
    "ts-node": "^10.9.0",
    "nodemon": "^3.0.0",
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0",
    "ts-jest": "^29.0.0",
    "eslint": "^8.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "prettier": "^3.0.0"
  }
}
```

## TypeScript Configuration (tsconfig.json)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/types/*": ["types/*"],
      "@/lib/*": ["lib/*"],
      "@/services/*": ["services/*"],
      "@/controllers/*": ["controllers/*"],
      "@/middleware/*": ["middleware/*"],
      "@/routes/*": ["routes/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

## Environment Configuration (.env.example)

```env
# Server Configuration
NODE_ENV=development
PORT=3001
BASE_PATH=/api/campaign/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/shoutout_engage_campaign
MONGODB_TEST_URI=mongodb://localhost:27017/shoutout_engage_campaign_test

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/campaign-service.log

# External Services
MESSAGE_SERVICE_URL=http://localhost:3002
PAYMENT_SERVICE_URL=http://localhost:3003
CORE_SERVICE_URL=http://localhost:3000

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1
```

## Docker Configuration

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src ./src

# Build application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S campaign -u 1001

# Change ownership
RUN chown -R campaign:nodejs /app
USER campaign

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/campaign/v1/health || exit 1

# Start application
CMD ["npm", "start"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  campaign-service:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/shoutout_engage_campaign
      - REDIS_HOST=redis
    depends_on:
      - mongo
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs

  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - campaign_mongo_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=shoutout_engage_campaign

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - campaign_redis_data:/data

volumes:
  campaign_mongo_data:
  campaign_redis_data:
```

## Authentication Middleware (Based on Core Service)

```typescript
// src/middleware/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';
import config from '@/lib/config';

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    org_id: string;
    role: string;
  };
}

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export const authenticateRequest = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Missing or invalid authorization header'
        }
      });
      return;
    }

    const token = authHeader.substring(7);
    
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired token'
        }
      });
      return;
    }

    // Get user profile with organization info
    const { data: profile } = await supabase
      .from('profiles')
      .select('*, organizations(*)')
      .eq('user_id', user.id)
      .single();

    if (!profile) {
      res.status(401).json({
        success: false,
        error: {
          code: 'PROFILE_NOT_FOUND',
          message: 'User profile not found'
        }
      });
      return;
    }

    req.user = {
      id: user.id,
      email: user.email || '',
      org_id: profile.organization_uuid,
      role: profile.user_type
    };

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: 'Authentication error'
      }
    });
  }
};
```

This setup provides the foundation for the Campaign Service with proper authentication, database connections, and project structure following the existing engage core service patterns.
