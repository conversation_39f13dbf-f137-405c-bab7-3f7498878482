# Campaign Service - GET /campaigns/{id} (Get Campaign by ID)

## Task Overview
**Endpoint**: `GET /api/campaign/v1/campaigns/{id}`  
**Purpose**: Retrieve detailed information about a specific campaign  
**Estimated Time**: 1 day  
**Priority**: High  
**Dependencies**: Campaign creation, authentication middleware, database models  

## API Specification

### Request
```http
GET /api/campaign/v1/campaigns/64a1b2c3d4e5f6789012346
Authorization: Bearer <jwt_token>
```

### Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012346",
    "name": "Summer Sale 2024",
    "description": "Promotional campaign for summer collection",
    "type": "BROADCAST",
    "status": "DRAFT",
    "target_config": {
      "type": "SEGMENTS",
      "segment_ids": ["64a1b2c3d4e5f6789012345"],
      "filters": {
        "exclude_unsubscribed": true,
        "exclude_bounced": true,
        "last_activity_days": 30
      }
    },
    "channels": {
      "sms": {
        "enabled": true,
        "sender_id": "SUMMER24",
        "content": "🌞 Summer Sale! Get {{discount}}% off. Use: {{code}}",
        "template_variables": {
          "discount": "30",
          "code": "SUMMER30"
        }
      },
      "email": {
        "enabled": true,
        "sender_email": "<EMAIL>",
        "subject": "Summer Sale - {{discount}}% Off!",
        "content": "Dear {{contact.name}}, don't miss our summer sale...",
        "template_variables": {
          "discount": "30"
        }
      }
    },
    "schedule_config": {
      "is_scheduled": false,
      "scheduled_at": null,
      "timezone": null
    },
    "stats": {
      "total_recipients": 1250,
      "messages_sent": 0,
      "messages_delivered": 0,
      "messages_failed": 0,
      "open_rate": null,
      "click_rate": null,
      "unsubscribe_rate": null
    },
    "cost_analysis": {
      "estimated_cost": {
        "sms": 62.50,
        "email": 12.50,
        "total": 75.00,
        "currency": "USD"
      },
      "actual_cost": {
        "sms": 0,
        "email": 0,
        "total": 0,
        "currency": "USD"
      }
    },
    "tags": ["summer", "promotional"],
    "metadata": {
      "budget_limit": 2000
    },
    "created_at": "2024-08-11T15:30:00Z",
    "updated_at": "2024-08-11T15:30:00Z",
    "started_at": null,
    "completed_at": null
  },
  "meta": {
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

### Error Responses
```http
HTTP/1.1 404 Not Found
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "CAMPAIGN_NOT_FOUND",
    "message": "Campaign not found or access denied",
    "timestamp": "2024-08-11T16:00:00Z",
    "request_id": "req_64a1b2c3d4e5f6789012348"
  }
}
```

## Implementation Tasks

### 1. Service Layer (30 minutes)
```typescript
// src/services/campaign.service.ts (addition)
export class CampaignService {
  static async getCampaignById(campaignId: string, orgId: string): Promise<Campaign> {
    try {
      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(campaignId)) {
        throw new BadRequestError('Invalid campaign ID format');
      }

      // Find campaign with organization check
      const campaign = await CampaignModel.findOne({
        _id: campaignId,
        org_id: orgId
      }).lean();

      if (!campaign) {
        throw new NotFoundError('Campaign not found or access denied');
      }

      return this.formatDetailedCampaignResponse(campaign);
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to retrieve campaign: ${error.message}`);
    }
  }

  private static formatDetailedCampaignResponse(campaign: any): Campaign {
    return {
      _id: campaign._id.toString(),
      name: campaign.name,
      description: campaign.description,
      type: campaign.type,
      status: campaign.status,
      target_config: campaign.target_config,
      channels: campaign.channels,
      schedule_config: campaign.schedule_config,
      stats: campaign.stats,
      cost_analysis: campaign.cost_analysis,
      tags: campaign.tags || [],
      metadata: campaign.metadata || {},
      created_at: campaign.created_at,
      updated_at: campaign.updated_at,
      started_at: campaign.started_at,
      completed_at: campaign.completed_at
    };
  }
}
```

### 2. Controller Implementation (20 minutes)
```typescript
// src/controllers/campaign.controller.ts (addition)
export class CampaignController {
  static async getCampaignById(
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new BadRequestError('Campaign ID is required');
      }

      const campaign = await CampaignService.getCampaignById(id, req.user.org_id);

      res.json({
        success: true,
        data: campaign,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  }
}
```

### 3. Route Definition (10 minutes)
```typescript
// src/routes/campaign.routes.ts (addition)
router.get(
  '/:id',
  authenticateRequest,
  rateLimitMiddleware,
  CampaignController.getCampaignById
);
```

### 4. Validation Middleware (15 minutes)
```typescript
// src/middleware/validation.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { BadRequestError } from '@/lib/errors/error.types';
import mongoose from 'mongoose';

export const validateObjectId = (paramName: string = 'id') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const id = req.params[paramName];
    
    if (!id) {
      throw new BadRequestError(`${paramName} parameter is required`);
    }
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new BadRequestError(`Invalid ${paramName} format`);
    }
    
    next();
  };
};
```

### 5. Enhanced Route with Validation (5 minutes)
```typescript
// src/routes/campaign.routes.ts (updated)
import { validateObjectId } from '@/middleware/validation.middleware';

router.get(
  '/:id',
  authenticateRequest,
  validateObjectId('id'),
  rateLimitMiddleware,
  CampaignController.getCampaignById
);
```

### 6. Unit Tests (30 minutes)
```typescript
// tests/unit/controllers/campaign.controller.test.ts (addition)
describe('CampaignController.getCampaignById', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = {
      params: { id: '64a1b2c3d4e5f6789012346' },
      user: { id: 'user123', org_id: 'org123' },
      headers: { 'x-request-id': 'req123' }
    };
    mockRes = {
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should return campaign successfully', async () => {
    const mockCampaign = {
      _id: '64a1b2c3d4e5f6789012346',
      name: 'Test Campaign',
      type: 'BROADCAST',
      status: 'DRAFT'
    };
    
    (CampaignService.getCampaignById as jest.Mock).mockResolvedValue(mockCampaign);

    await CampaignController.getCampaignById(mockReq, mockRes, mockNext);

    expect(CampaignService.getCampaignById).toHaveBeenCalledWith(
      '64a1b2c3d4e5f6789012346',
      'org123'
    );
    expect(mockRes.json).toHaveBeenCalledWith({
      success: true,
      data: mockCampaign,
      meta: expect.objectContaining({
        timestamp: expect.any(String),
        request_id: 'req123'
      })
    });
  });

  it('should handle missing campaign ID', async () => {
    mockReq.params.id = undefined;

    await CampaignController.getCampaignById(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(BadRequestError));
  });

  it('should handle campaign not found', async () => {
    (CampaignService.getCampaignById as jest.Mock).mockRejectedValue(
      new NotFoundError('Campaign not found')
    );

    await CampaignController.getCampaignById(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(expect.any(NotFoundError));
  });
});
```

### 7. Integration Tests (20 minutes)
```typescript
// tests/integration/campaign.api.test.ts
import request from 'supertest';
import app from '@/app';
import { CampaignModel } from '@/lib/db/models/campaign.model';

describe('GET /api/campaign/v1/campaigns/:id', () => {
  let authToken: string;
  let campaignId: string;
  let orgId: string;

  beforeAll(async () => {
    // Setup test data
    authToken = await getTestAuthToken();
    orgId = 'test-org-123';
    
    const campaign = await CampaignModel.create({
      org_id: orgId,
      created_by: 'user123',
      name: 'Test Campaign',
      type: 'BROADCAST',
      target_config: { type: 'ALL' },
      channels: {
        sms: { enabled: true, sender_id: 'TEST', content: 'Hello' }
      }
    });
    
    campaignId = campaign._id.toString();
  });

  afterAll(async () => {
    await CampaignModel.deleteMany({ org_id: orgId });
  });

  it('should return campaign details', async () => {
    const response = await request(app)
      .get(`/api/campaign/v1/campaigns/${campaignId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data._id).toBe(campaignId);
    expect(response.body.data.name).toBe('Test Campaign');
    expect(response.body.data.type).toBe('BROADCAST');
  });

  it('should return 404 for non-existent campaign', async () => {
    const nonExistentId = '64a1b2c3d4e5f6789012999';
    
    const response = await request(app)
      .get(`/api/campaign/v1/campaigns/${nonExistentId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(404);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('CAMPAIGN_NOT_FOUND');
  });

  it('should return 400 for invalid campaign ID', async () => {
    const response = await request(app)
      .get('/api/campaign/v1/campaigns/invalid-id')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_ID_FORMAT');
  });

  it('should return 401 without authentication', async () => {
    await request(app)
      .get(`/api/campaign/v1/campaigns/${campaignId}`)
      .expect(401);
  });
});
```

### 8. Performance Optimization (15 minutes)
```typescript
// src/services/campaign.service.ts (optimization)
export class CampaignService {
  static async getCampaignById(campaignId: string, orgId: string): Promise<Campaign> {
    try {
      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(campaignId)) {
        throw new BadRequestError('Invalid campaign ID format');
      }

      // Find campaign with selective field projection for better performance
      const campaign = await CampaignModel.findOne(
        {
          _id: campaignId,
          org_id: orgId
        },
        {
          // Exclude large fields if not needed for basic view
          'channels.email.html_content': 0,
          'metadata.large_data': 0
        }
      ).lean();

      if (!campaign) {
        throw new NotFoundError('Campaign not found or access denied');
      }

      return this.formatDetailedCampaignResponse(campaign);
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof NotFoundError) {
        throw error;
      }
      throw new InternalError(`Failed to retrieve campaign: ${error.message}`);
    }
  }
}
```

## Caching Strategy (Optional Enhancement)

### Redis Caching (20 minutes)
```typescript
// src/services/campaign.cache.service.ts
import { RedisConnector } from '@/lib/db/connectors/RedisConnector';

export class CampaignCacheService {
  private static readonly CACHE_TTL = 300; // 5 minutes
  private static readonly CACHE_PREFIX = 'campaign:';

  static async getCachedCampaign(campaignId: string, orgId: string): Promise<Campaign | null> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = `${this.CACHE_PREFIX}${orgId}:${campaignId}`;
      
      const cached = await redis.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      // Log error but don't fail the request
      console.error('Cache retrieval error:', error);
      return null;
    }
  }

  static async setCachedCampaign(campaign: Campaign, orgId: string): Promise<void> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = `${this.CACHE_PREFIX}${orgId}:${campaign._id}`;
      
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(campaign));
    } catch (error) {
      // Log error but don't fail the request
      console.error('Cache storage error:', error);
    }
  }

  static async invalidateCampaignCache(campaignId: string, orgId: string): Promise<void> {
    try {
      const redis = RedisConnector.getClient();
      const cacheKey = `${this.CACHE_PREFIX}${orgId}:${campaignId}`;
      
      await redis.del(cacheKey);
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }
}
```

## Testing Checklist
- [ ] Unit tests for controller
- [ ] Unit tests for service layer
- [ ] Integration tests for API endpoint
- [ ] Error handling tests (404, 400, 401)
- [ ] Authentication tests
- [ ] Performance tests
- [ ] Cache tests (if implemented)

## Documentation
- [ ] API documentation in Swagger
- [ ] Code comments
- [ ] Error response documentation
- [ ] Performance considerations

This task creates a robust campaign retrieval endpoint with proper error handling, validation, caching options, and comprehensive testing.
