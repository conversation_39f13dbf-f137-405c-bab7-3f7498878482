# POST /api/v1/segments - Create Contact Segment

## Endpoint Overview
**Method**: POST  
**URL**: `/api/v1/segments`  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 100 requests per minute

## Purpose
Creates a new contact segment based on specified criteria for targeted campaign messaging.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Request Body
```json
{
  "name": "High-Value Customers",
  "description": "Customers with purchase history > $1000 in last 6 months",
  "criteria": {
    "conditions": [
      {
        "field": "total_purchase_amount",
        "operator": "greater_than",
        "value": 1000,
        "time_period": "6_months"
      },
      {
        "field": "last_activity_date",
        "operator": "within_days",
        "value": 90
      }
    ],
    "logic_operator": "AND",
    "tags": {
      "include": ["premium", "active"],
      "exclude": ["unsubscribed", "bounced"]
    },
    "contact_properties": {
      "location": {
        "countries": ["US", "CA", "GB"],
        "cities": ["New York", "Toronto", "London"]
      },
      "demographics": {
        "age_range": {
          "min": 25,
          "max": 55
        },
        "language": ["en", "fr"]
      }
    },
    "behavioral_criteria": {
      "last_purchase_days": 180,
      "minimum_purchases": 2,
      "engagement_level": "high"
    }
  },
  "filters": {
    "exclude_unsubscribed": true,
    "exclude_bounced": true,
    "exclude_invalid_contacts": true,
    "include_test_contacts": false
  },
  "auto_update": {
    "enabled": true,
    "frequency": "daily",
    "time_of_day": "02:00"
  },
  "metadata": {
    "created_by": "<EMAIL>",
    "department": "marketing",
    "campaign_purpose": "retention"
  },
  "tags": ["high-value", "retention", "q4-2024"]
}
```

## Request Body Schema

### Required Fields
- `name` (string, 3-100 chars): Segment display name
- `criteria` (object): Segmentation criteria configuration

### Optional Fields
- `description` (string, max 500 chars): Segment description
- `filters` (object): Additional filtering options
- `auto_update` (object): Automatic segment refresh configuration
- `metadata` (object): Custom metadata for tracking
- `tags` (array): Tags for segment organization

### Criteria Object Structure
```typescript
interface SegmentCriteria {
  conditions: SegmentCondition[];
  logic_operator: 'AND' | 'OR';
  tags?: {
    include?: string[];
    exclude?: string[];
  };
  contact_properties?: {
    location?: {
      countries?: string[];
      states?: string[];
      cities?: string[];
    };
    demographics?: {
      age_range?: { min: number; max: number; };
      gender?: string[];
      language?: string[];
    };
  };
  behavioral_criteria?: {
    last_purchase_days?: number;
    minimum_purchases?: number;
    engagement_level?: 'low' | 'medium' | 'high';
    last_activity_days?: number;
  };
}

interface SegmentCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'starts_with' | 'ends_with' | 'within_days' | 'older_than_days';
  value: string | number | boolean;
  time_period?: '7_days' | '30_days' | '3_months' | '6_months' | '1_year';
}
```

## Response

### Success Response (201 Created)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "name": "High-Value Customers",
    "description": "Customers with purchase history > $1000 in last 6 months",
    "criteria": {
      "conditions": [
        {
          "field": "total_purchase_amount",
          "operator": "greater_than",
          "value": 1000,
          "time_period": "6_months"
        }
      ],
      "logic_operator": "AND",
      "tags": {
        "include": ["premium", "active"],
        "exclude": ["unsubscribed", "bounced"]
      }
    },
    "filters": {
      "exclude_unsubscribed": true,
      "exclude_bounced": true,
      "exclude_invalid_contacts": true,
      "include_test_contacts": false
    },
    "auto_update": {
      "enabled": true,
      "frequency": "daily",
      "time_of_day": "02:00",
      "last_updated": null,
      "next_update": "2024-12-13T02:00:00Z"
    },
    "stats": {
      "total_contacts": 0,
      "status": "CALCULATING",
      "last_calculated": null,
      "calculation_time_ms": null
    },
    "metadata": {
      "created_by": "<EMAIL>",
      "department": "marketing",
      "campaign_purpose": "retention"
    },
    "tags": ["high-value", "retention", "q4-2024"],
    "status": "ACTIVE",
    "created_at": "2024-12-12T10:30:00Z",
    "updated_at": "2024-12-12T10:30:00Z"
  },
  "message": "Segment created successfully. Contact calculation started."
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "SEGMENT_001",
    "message": "Invalid segment criteria",
    "details": [
      {
        "field": "name",
        "message": "Segment name must be between 3 and 100 characters"
      },
      {
        "field": "criteria.conditions",
        "message": "At least one condition is required"
      }
    ]
  }
}
```

#### 409 Conflict
```json
{
  "success": false,
  "error": {
    "code": "SEGMENT_002",
    "message": "Segment with this name already exists in the organization",
    "details": [
      {
        "field": "name",
        "message": "A segment named 'High-Value Customers' already exists"
      }
    ]
  }
}
```

#### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "SEGMENT_003",
    "message": "Invalid segment criteria configuration",
    "details": [
      {
        "field": "criteria.conditions[0].operator",
        "message": "Operator 'greater_than' is not valid for field type 'string'"
      }
    ]
  }
}
```

## Implementation Details

### Database Model
```typescript
interface SegmentDocument {
  _id: ObjectId;
  org_id: string;
  name: string;
  description?: string;
  criteria: SegmentCriteria;
  filters: SegmentFilters;
  auto_update: AutoUpdateConfig;
  stats: {
    total_contacts: number;
    status: 'CALCULATING' | 'READY' | 'ERROR';
    last_calculated?: Date;
    calculation_time_ms?: number;
    error_message?: string;
  };
  contact_ids: ObjectId[];
  metadata: Record<string, any>;
  tags: string[];
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
  created_by: string;
  created_at: Date;
  updated_at: Date;
}
```

### Business Logic
1. **Validation**: Validate segment criteria and ensure field operators are compatible
2. **Duplicate Check**: Ensure segment name is unique within organization
3. **Queue Job**: Queue background job to calculate segment contacts
4. **Response**: Return segment metadata immediately with calculation status

### Background Processing
- Segment contact calculation is performed asynchronously
- Results are cached and updated based on auto_update configuration
- Large segments (>10,000 contacts) are processed in batches

### Performance Considerations
- Segment calculations are indexed on frequently queried fields
- Complex criteria may take several minutes for large contact lists
- Auto-update scheduling prevents overlapping calculations

### Security & Permissions
- Users can only create segments within their organization
- Segment criteria are validated to prevent injection attacks
- Contact access is limited by organization boundaries

### Integration Points
- **Campaign Service**: Segments are referenced during campaign creation
- **Contact Service**: Segment calculations query contact data
- **Queue System**: Background job processing for calculations

## Usage Examples

### Simple Tag-Based Segment
```json
{
  "name": "Newsletter Subscribers",
  "criteria": {
    "conditions": [],
    "logic_operator": "AND",
    "tags": {
      "include": ["newsletter", "active"]
    }
  }
}
```

### Behavioral Segment
```json
{
  "name": "Recent Purchasers",
  "criteria": {
    "conditions": [
      {
        "field": "last_purchase_date",
        "operator": "within_days",
        "value": 30
      }
    ],
    "logic_operator": "AND",
    "behavioral_criteria": {
      "minimum_purchases": 1,
      "engagement_level": "medium"
    }
  }
}
```

### Geographic Segment
```json
{
  "name": "US East Coast",
  "criteria": {
    "conditions": [],
    "logic_operator": "AND",
    "contact_properties": {
      "location": {
        "countries": ["US"],
        "states": ["NY", "NJ", "CT", "MA", "PA"]
      }
    }
  }
}
```