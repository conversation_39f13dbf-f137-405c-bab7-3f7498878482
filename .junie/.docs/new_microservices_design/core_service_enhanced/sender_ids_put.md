# PUT /api/v1/sender-ids/{id} - Update Sender ID

## Endpoint Overview
**Method**: PUT  
**URL**: `/api/v1/sender-ids/{sender_id}`  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 100 requests per minute

## Purpose
Updates an existing sender ID registration request. Updates are only allowed for pending requests and specific fields depending on the current approval workflow step.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Path Parameters
- `sender_id` (string): MongoDB ObjectId of the sender ID to update

## Request Body
```json
{
  "display_name": "My Updated Store",
  "metadata": {
    "user_type": "BUSINESS",
    "company_name": "My Store Ltd (Updated)",
    "business_nature": "E-commerce retail and wholesale",
    "business_registration_number": "REG123456789-V2",
    "user_name": "<PERSON>",
    "user_designation": "Senior Marketing Manager",
    "sample_content": "Your order #12345 has been shipped and will arrive by 2024-12-15. Track: https://track.mystore.com/12345. Questions? Call 1-800-SUPPORT",
    "intended_usage": "Transactional notifications for order updates, delivery confirmations, customer service, and account security alerts",
    "message_volume_per_month": 7500,
    "enable_international_usage": true,
    "usage_type": "TRANSACTIONAL",
    "country_iso_codes": ["US", "CA", "GB", "AU", "NZ"]
  },
  "add_countries": ["FR", "DE"],
  "remove_countries": ["AU"],
  "workflow_action": {
    "action": "SUBMIT_FOR_REVIEW",
    "notes": "Updated business information and sample content as requested"
  }
}
```

## Updatable Fields by Workflow Step

### DOCUMENT_UPLOAD Stage
**Allowed Updates:**
- `display_name` - Display name for the sender ID
- `metadata` - All metadata fields can be updated
- `add_countries` - Add new countries for registration
- `remove_countries` - Remove countries (only if not yet submitted)
- `workflow_action` - Submit for review or save as draft

### TECHNICAL_REVIEW Stage
**Allowed Updates (Limited):**
- `metadata.sample_content` - Update sample message content
- `metadata.intended_usage` - Clarify usage description
- `metadata.message_volume_per_month` - Update volume estimates
- `workflow_action` - Respond to review feedback

### COMPLIANCE_REVIEW Stage
**Allowed Updates (Very Limited):**
- `metadata.sample_content` - Only if specifically requested by reviewer
- `workflow_action` - Respond to compliance feedback

### FINAL_APPROVAL Stage
**No Updates Allowed** - Sender ID is locked for final approval

## Workflow Actions
```typescript
interface WorkflowAction {
  action: 'SAVE_DRAFT' | 'SUBMIT_FOR_REVIEW' | 'RESPOND_TO_FEEDBACK' | 'WITHDRAW_REQUEST';
  notes?: string;
  reviewer_response?: {
    feedback_addressed: boolean;
    changes_made: string[];
    additional_notes?: string;
  };
}
```

## Request Body Schema

### Updatable Fields
- `display_name` (string, max 50 chars): Human-readable name
- `metadata` (object): Updated application metadata
- `add_countries` (array): ISO country codes to add
- `remove_countries` (array): ISO country codes to remove  
- `workflow_action` (object): Workflow progression action

### Metadata Updates
Same structure as creation, but all fields are optional for updates:
```typescript
interface MetadataUpdate {
  user_type?: "BUSINESS" | "INDIVIDUAL";
  company_name?: string;
  business_nature?: string;
  business_registration_number?: string;
  user_name?: string;
  user_designation?: string;
  user_identification_number?: string;  // For individual users
  sample_content?: string;
  intended_usage?: string;
  message_volume_per_month?: number;
  enable_international_usage?: boolean;
  usage_type?: "TRANSACTIONAL" | "PROMOTIONAL" | "MIXED";
  country_iso_codes?: string[];  // Will be merged with add/remove operations
}
```

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "sender_id": "MYSTORE",
    "display_name": "My Updated Store",
    "transport": "SMS",
    "status": "PENDING",
    "registered_countries": [
      {
        "country_iso_code": "US",
        "country_name": "United States",
        "status": "PENDING",
        "submitted_at": "2024-12-12T10:30:00Z"
      },
      {
        "country_iso_code": "CA",
        "country_name": "Canada",
        "status": "PENDING",
        "submitted_at": "2024-12-12T10:30:00Z"
      },
      {
        "country_iso_code": "GB",
        "country_name": "United Kingdom",
        "status": "PENDING",
        "submitted_at": "2024-12-12T10:30:00Z"
      },
      {
        "country_iso_code": "NZ",
        "country_name": "New Zealand",
        "status": "PENDING",
        "submitted_at": "2024-12-16T14:20:00Z"
      },
      {
        "country_iso_code": "FR",
        "country_name": "France",
        "status": "PENDING",
        "submitted_at": "2024-12-16T14:20:00Z"
      },
      {
        "country_iso_code": "DE",
        "country_name": "Germany",
        "status": "PENDING",
        "submitted_at": "2024-12-16T14:20:00Z"
      }
    ],
    "metadata": {
      "user_type": "BUSINESS",
      "company_name": "My Store Ltd (Updated)",
      "business_nature": "E-commerce retail and wholesale",
      "business_registration_number": "REG123456789-V2",
      "user_name": "John Doe",
      "user_designation": "Senior Marketing Manager",
      "sample_content": "Your order #12345 has been shipped and will arrive by 2024-12-15. Track: https://track.mystore.com/12345. Questions? Call 1-800-SUPPORT",
      "intended_usage": "Transactional notifications for order updates, delivery confirmations, customer service, and account security alerts",
      "message_volume_per_month": 7500,
      "enable_international_usage": true,
      "usage_type": "TRANSACTIONAL",
      "country_iso_codes": ["US", "CA", "GB", "NZ", "FR", "DE"]
    },
    "artifacts": [
      {
        "artifact_id": "artifact_123",
        "artifact_type": "BUSINESS_REGISTRATION_COPY",
        "file_name": "business_registration.pdf",
        "status": "PENDING"
      }
    ],
    "approval_workflow": {
      "current_step": "TECHNICAL_REVIEW",
      "steps_completed": ["DOCUMENT_UPLOAD"],
      "assigned_reviewer": "tech_reviewer_456",
      "review_notes": [
        "Documents received and validated",
        "Updated information submitted for technical review"
      ],
      "estimated_completion": "2024-12-20T16:00:00Z",
      "last_action": {
        "action": "SUBMIT_FOR_REVIEW",
        "performed_by": "user_123456789",
        "performed_at": "2024-12-16T14:20:00Z",
        "notes": "Updated business information and sample content as requested"
      }
    },
    "change_log": [
      {
        "changed_at": "2024-12-16T14:20:00Z",
        "changed_by": "user_123456789",
        "changes": [
          {
            "field": "display_name",
            "old_value": "My Store",
            "new_value": "My Updated Store"
          },
          {
            "field": "metadata.company_name",
            "old_value": "My Store Ltd",
            "new_value": "My Store Ltd (Updated)"
          },
          {
            "field": "registered_countries",
            "action": "ADD",
            "values": ["FR", "DE", "NZ"]
          },
          {
            "field": "registered_countries", 
            "action": "REMOVE",
            "values": ["AU"]
          }
        ],
        "workflow_action": "SUBMIT_FOR_REVIEW"
      }
    ],
    "created_by": "user_123456789",
    "created_at": "2024-12-12T10:30:00Z",
    "updated_at": "2024-12-16T14:20:00Z"
  },
  "message": "Sender ID updated successfully and submitted for technical review."
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_008",
    "message": "Invalid update request",
    "details": [
      {
        "field": "sender_id",
        "message": "Sender ID cannot be modified after creation"
      },
      {
        "field": "metadata.country_iso_codes",
        "message": "Invalid country code 'XX'"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_009",
    "message": "Update not allowed at current workflow step",
    "details": [
      {
        "field": "workflow_step",
        "message": "Updates not allowed during FINAL_APPROVAL step"
      },
      {
        "field": "metadata",
        "message": "Only sample_content can be updated during COMPLIANCE_REVIEW"
      }
    ]
  }
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_005",
    "message": "Sender ID not found",
    "details": [
      {
        "field": "sender_id",
        "message": "No sender ID found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 409 Conflict
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_010",
    "message": "Update conflicts with current state",
    "details": [
      {
        "field": "status",
        "message": "Cannot update approved sender ID"
      },
      {
        "field": "remove_countries",
        "message": "Cannot remove country 'US' - already approved"
      }
    ]
  }
}
```

### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_011",
    "message": "Invalid metadata updates",
    "details": [
      {
        "field": "metadata.user_type",
        "message": "User type cannot be changed after initial submission"
      },
      {
        "field": "add_countries",
        "message": "Maximum 10 countries allowed per sender ID"
      }
    ]
  }
}
```

## Implementation Details

### Update Validation Rules

#### Field-Level Validation
1. **Immutable Fields**: `sender_id`, `transport`, `org_id`, `created_by` cannot be updated
2. **User Type**: Cannot be changed after initial creation
3. **Country Limits**: Maximum 10 countries per sender ID
4. **Workflow Constraints**: Updates restricted based on current workflow step

#### Workflow-Based Validation
```typescript
interface WorkflowUpdateRules {
  DOCUMENT_UPLOAD: {
    allowed_fields: ['display_name', 'metadata', 'add_countries', 'remove_countries'];
    restrictions: {
      user_type_immutable: true;
      max_countries: 10;
    };
  };
  TECHNICAL_REVIEW: {
    allowed_fields: ['metadata.sample_content', 'metadata.intended_usage', 'metadata.message_volume_per_month'];
    restrictions: {
      requires_reviewer_request: true;
    };
  };
  COMPLIANCE_REVIEW: {
    allowed_fields: ['metadata.sample_content'];
    restrictions: {
      requires_reviewer_approval: true;
      change_justification_required: true;
    };
  };
  FINAL_APPROVAL: {
    allowed_fields: [];
    restrictions: {
      no_updates_allowed: true;
    };
  };
}
```

### Country Management Logic
```typescript
async function updateCountries(senderId: string, addCountries: string[], removeCountries: string[]) {
  const senderDoc = await SenderIdModel.findById(senderId);
  
  // Validate country operations
  for (const country of removeCountries) {
    const countryStatus = senderDoc.registered_countries.find(c => c.country_iso_code === country);
    if (countryStatus?.status === 'APPROVED') {
      throw new ConflictError(`Cannot remove approved country: ${country}`);
    }
  }
  
  // Remove countries
  senderDoc.registered_countries = senderDoc.registered_countries.filter(
    c => !removeCountries.includes(c.country_iso_code)
  );
  
  // Add new countries
  for (const countryCode of addCountries) {
    if (!senderDoc.registered_countries.find(c => c.country_iso_code === countryCode)) {
      senderDoc.registered_countries.push({
        country_iso_code: countryCode,
        country_name: getCountryName(countryCode),
        status: 'PENDING',
        submitted_at: new Date()
      });
    }
  }
  
  return senderDoc;
}
```

### Change Tracking
```typescript
interface ChangeLogEntry {
  changed_at: Date;
  changed_by: string;
  changes: FieldChange[];
  workflow_action?: string;
  reviewer_notes?: string;
}

interface FieldChange {
  field: string;
  old_value?: any;
  new_value?: any;
  action?: 'ADD' | 'REMOVE' | 'UPDATE';
  values?: any[];
}
```

### Business Logic

#### Update Permissions
1. **Creator Access**: Original creator can update during DOCUMENT_UPLOAD stage
2. **Reviewer Access**: Reviewers can request specific updates during review stages  
3. **Admin Access**: Admins can make emergency updates with audit trail

#### Workflow Progression
1. **SAVE_DRAFT**: Keep as draft without progression
2. **SUBMIT_FOR_REVIEW**: Move to next review stage
3. **RESPOND_TO_FEEDBACK**: Update based on reviewer feedback
4. **WITHDRAW_REQUEST**: Cancel the sender ID request

#### Notification Triggers
- User notifications on workflow progression
- Reviewer notifications on updates requiring review
- Admin notifications on suspicious update patterns

### Integration Points
- **Audit Service**: All updates logged for compliance
- **Notification Service**: Stakeholders notified of changes
- **Queue System**: Background processing for large updates

## Usage Examples

### Update Basic Information
```json
{
  "display_name": "Updated Store Name",
  "metadata": {
    "company_name": "Updated Company Ltd",
    "user_designation": "VP of Marketing",
    "message_volume_per_month": 8000
  },
  "workflow_action": {
    "action": "SAVE_DRAFT",
    "notes": "Updated company details"
  }
}
```

### Add/Remove Countries
```json
{
  "add_countries": ["FR", "DE", "IT"],
  "remove_countries": ["AU"],
  "workflow_action": {
    "action": "SUBMIT_FOR_REVIEW",
    "notes": "Expanding to European markets, removing Australia"
  }
}
```

### Respond to Review Feedback
```json
{
  "metadata": {
    "sample_content": "UPDATED: Your order #12345 shipped! Track: bit.ly/track12345 Questions? Text HELP",
    "intended_usage": "UPDATED: Transactional messages for order status, shipping notifications, and customer support responses only"
  },
  "workflow_action": {
    "action": "RESPOND_TO_FEEDBACK",
    "notes": "Updated sample content to be under 160 characters as requested",
    "reviewer_response": {
      "feedback_addressed": true,
      "changes_made": [
        "Shortened sample message content",
        "Clarified intended usage scope"
      ]
    }
  }
}
```

### Withdraw Request
```json
{
  "workflow_action": {
    "action": "WITHDRAW_REQUEST",
    "notes": "Business requirements changed, will resubmit with different sender ID"
  }
}
```