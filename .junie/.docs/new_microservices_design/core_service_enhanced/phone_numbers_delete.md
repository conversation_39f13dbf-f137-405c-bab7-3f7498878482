# DELETE /api/v1/phone-numbers/{id} - Release Phone Number

## Endpoint Overview
**Method**: DELETE  
**URL**: `/api/v1/phone-numbers/{phone_number_id}`  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 10 requests per minute

## Purpose
Releases a phone number back to Twilio's available pool. This action includes a grace period for accidental releases, data export options, and proper cleanup of all associated configurations and message history.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Path Parameters
- `phone_number_id` (string): MongoDB ObjectId of the phone number to release

## Query Parameters
```
immediate_release=false       # Skip grace period and release immediately (default: false)
export_data=true             # Export message logs and usage data (default: true)
export_format=json           # Export format: json, csv, xlsx (default: json)
reason=cost_optimization     # Reason for release (optional)
```

## Request Body (Optional)
```json
{
  "release_config": {
    "immediate_release": false,
    "grace_period_hours": 72,
    "export_data": true,
    "export_format": "json",
    "preserve_message_logs": true,
    "reason": "Consolidating phone numbers to reduce costs",
    "alternative_number": "+***********"
  },
  "notification_settings": {
    "notify_admins": true,
    "notify_campaigns": true,
    "custom_message": "This number will be released on 2024-12-19. Please update your systems."
  },
  "cleanup_options": {
    "remove_from_campaigns": true,
    "disable_webhooks": true,
    "export_routing_rules": true,
    "transfer_routing_rules_to": null
  }
}
```

## Release Process Stages

### Stage 1: Validation and Pre-Release (Immediate)
- Validate user permissions and number ownership
- Check for active campaigns using the number
- Validate any dependencies or integrations
- Calculate final billing amounts

### Stage 2: Grace Period (24-168 hours, configurable)
- Number marked as PENDING_RELEASE
- All inbound messages still processed normally
- Usage tracking continues
- Admin notifications sent
- Grace period can be cancelled

### Stage 3: Final Release (After grace period)
- Number released back to Twilio
- All configurations removed
- Message history archived
- Final billing processed
- Cleanup completed

## Response

### Success Response (202 Accepted)
```json
{
  "success": true,
  "data": {
    "phone_number_id": "64a1b2c3d4e5f6789012345",
    "phone_number": "+***********",
    "friendly_name": "Customer Service Line",
    "status": "PENDING_RELEASE",
    "release_schedule": {
      "scheduled_release_at": "2024-12-19T18:30:00Z",
      "grace_period_hours": 72,
      "grace_period_expires": "2024-12-19T18:30:00Z",
      "can_cancel": true,
      "cancel_deadline": "2024-12-19T17:30:00Z"
    },
    "final_billing": {
      "current_month_usage": 45.67,
      "prorated_monthly_fee": 0.32,
      "total_final_charge": 45.99,
      "currency": "USD",
      "billing_date": "2024-12-19T18:30:00Z"
    },
    "affected_integrations": {
      "active_campaigns": [
        {
          "campaign_id": "campaign_123",
          "campaign_name": "Customer Support Responses",
          "status": "ACTIVE",
          "action_required": "UPDATE_PHONE_NUMBER"
        }
      ],
      "webhooks": [
        {
          "webhook_url": "https://api.example.com/webhooks/sms/inbound",
          "type": "inbound_sms",
          "action": "WILL_BE_DISABLED"
        }
      ],
      "routing_rules": 5,
      "message_templates": 3
    },
    "data_export": {
      "export_requested": true,
      "export_format": "json",
      "estimated_records": 12450,
      "export_url": null,
      "export_status": "PROCESSING",
      "estimated_completion": "2024-12-16T18:00:00Z"
    },
    "cleanup_tasks": [
      {
        "task": "remove_from_campaigns",
        "status": "PENDING",
        "scheduled_for": "2024-12-19T18:25:00Z"
      },
      {
        "task": "disable_webhooks",
        "status": "PENDING",
        "scheduled_for": "2024-12-19T18:28:00Z"
      },
      {
        "task": "archive_message_logs",
        "status": "PENDING",
        "scheduled_for": "2024-12-19T18:29:00Z"
      },
      {
        "task": "release_from_twilio",
        "status": "PENDING",
        "scheduled_for": "2024-12-19T18:30:00Z"
      }
    ],
    "notifications": {
      "admins_notified": true,
      "campaigns_notified": true,
      "notification_sent_at": "2024-12-16T17:30:00Z"
    }
  },
  "message": "Phone number scheduled for release in 72 hours. Release can be cancelled before 2024-12-19T17:30:00Z.",
  "warnings": [
    {
      "code": "ACTIVE_CAMPAIGNS_WARNING",
      "message": "1 active campaign is using this phone number. Please update the campaign before release."
    }
  ]
}
```

### Immediate Release Response (200 OK)
```json
{
  "success": true,
  "data": {
    "phone_number_id": "64a1b2c3d4e5f6789012345",
    "phone_number": "+***********",
    "status": "RELEASED",
    "released_at": "2024-12-16T17:30:00Z",
    "twilio_release_status": "SUCCESS",
    "final_billing": {
      "current_month_usage": 45.67,
      "prorated_monthly_fee": 0.32,
      "total_final_charge": 45.99,
      "currency": "USD",
      "billed_at": "2024-12-16T17:30:00Z"
    },
    "cleanup_completed": {
      "campaigns_updated": 1,
      "webhooks_disabled": 2,
      "routing_rules_archived": 5,
      "message_logs_archived": true,
      "twilio_number_released": true
    },
    "data_export": {
      "export_url": "https://exports.example.com/phone-numbers/64a1b2c3d4e5f6789012345_data.json",
      "export_format": "json",
      "records_exported": 12450,
      "export_size_mb": 15.7,
      "expires_at": "2024-12-23T17:30:00Z"
    }
  },
  "message": "Phone number released successfully. All associated data has been exported and archived."
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_015",
    "message": "Invalid release request",
    "details": [
      {
        "field": "grace_period_hours",
        "message": "Grace period must be between 1 and 168 hours"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_016",
    "message": "Release not permitted",
    "details": [
      {
        "field": "permissions",
        "message": "User does not have permission to release phone numbers"
      }
    ]
  }
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_002",
    "message": "Phone number not found",
    "details": [
      {
        "field": "phone_number_id",
        "message": "No phone number found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 409 Conflict
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_017",
    "message": "Cannot release phone number",
    "details": [
      {
        "field": "status",
        "message": "Phone number is already in PENDING_RELEASE status"
      }
    ],
    "current_release": {
      "scheduled_release_at": "2024-12-18T12:00:00Z",
      "grace_period_remaining_hours": 24,
      "can_cancel": true
    }
  }
}
```

### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_018",
    "message": "Release blocked by dependencies",
    "details": [
      {
        "field": "active_campaigns",
        "message": "Cannot release number used by 3 active campaigns"
      }
    ],
    "blocking_dependencies": {
      "active_campaigns": [
        {
          "campaign_id": "campaign_123",
          "campaign_name": "Holiday Promotions",
          "status": "SENDING"
        }
      ],
      "scheduled_messages": 45,
      "pending_webhooks": 12
    },
    "resolution_actions": [
      "Update campaigns to use different phone number",
      "Wait for scheduled messages to complete",
      "Cancel pending webhook deliveries"
    ]
  }
}
```

### 503 Service Unavailable
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_019",
    "message": "Release service temporarily unavailable",
    "details": [
      {
        "field": "twilio_api",
        "message": "Cannot connect to Twilio for number release. Please try again later."
      }
    ],
    "retry_after": 600
  }
}
```

## Implementation Details

### Release Workflow
```typescript
interface PhoneNumberReleaseWorkflow {
  phases: [
    'validation_and_dependency_check',
    'grace_period_scheduling',
    'notification_dispatch',
    'data_export_preparation',
    'cleanup_task_scheduling',
    'final_release_execution'
  ];
  grace_period_hours: number;
  allow_immediate_release: boolean;
  auto_cleanup_enabled: boolean;
}
```

### Dependency Validation
```typescript
interface DependencyChecker {
  async checkActiveCampaigns(phoneNumberId: string): Promise<CampaignDependency[]>;
  async checkScheduledMessages(phoneNumberId: string): Promise<ScheduledMessage[]>;
  async checkWebhookDependencies(phoneNumberId: string): Promise<WebhookDependency[]>;
  async checkIntegrationUsage(phoneNumberId: string): Promise<IntegrationUsage[]>;
  
  async validateReleasePermission(phoneNumberId: string, userId: string): Promise<boolean>;
  async calculateFinalBilling(phoneNumberId: string): Promise<FinalBilling>;
}
```

### Data Export Service
```typescript
interface DataExportService {
  async exportPhoneNumberData(phoneNumberId: string, format: ExportFormat): Promise<ExportResult> {
    const data = await this.gatherExportData(phoneNumberId);
    
    const exportData = {
      phone_number_info: data.phoneNumber,
      message_logs: data.messageLogs,
      usage_statistics: data.usageStats,
      billing_history: data.billingHistory,
      configuration_history: data.configHistory,
      routing_rules: data.routingRules,
      compliance_records: data.complianceRecords
    };
    
    return await this.generateExport(exportData, format);
  }
  
  private async gatherExportData(phoneNumberId: string): Promise<ExportData> {
    // Gather all related data for export
  }
}
```

### Grace Period Management
```typescript
interface GracePeriodManager {
  async scheduleRelease(phoneNumberId: string, gracePeriodHours: number): Promise<ReleaseSchedule>;
  async cancelRelease(phoneNumberId: string, userId: string): Promise<CancellationResult>;
  async executeScheduledRelease(phoneNumberId: string): Promise<ReleaseResult>;
  
  async checkGracePeriodStatus(phoneNumberId: string): Promise<GracePeriodStatus> {
    const releaseSchedule = await this.getReleaseSchedule(phoneNumberId);
    
    return {
      is_in_grace_period: releaseSchedule.status === 'PENDING_RELEASE',
      hours_remaining: this.calculateRemainingHours(releaseSchedule.scheduled_release_at),
      can_cancel: Date.now() < releaseSchedule.cancel_deadline,
      scheduled_release_at: releaseSchedule.scheduled_release_at
    };
  }
}
```

### Business Logic

#### Cleanup Tasks
```typescript
interface CleanupTaskManager {
  async executeCleanupTasks(phoneNumberId: string): Promise<CleanupResult> {
    const tasks = [
      () => this.removeFromCampaigns(phoneNumberId),
      () => this.disableWebhooks(phoneNumberId),
      () => this.archiveMessageLogs(phoneNumberId),
      () => this.exportRoutingRules(phoneNumberId),
      () => this.releaseFromTwilio(phoneNumberId),
      () => this.updateDatabaseStatus(phoneNumberId)
    ];
    
    const results = await Promise.allSettled(tasks.map(task => task()));
    return this.processCleanupResults(results);
  }
}
```

#### Billing Finalization
```typescript
interface BillingFinalizer {
  async calculateFinalBill(phoneNumberId: string, releaseDate: Date): Promise<FinalBill> {
    const usage = await this.getMonthlyUsage(phoneNumberId);
    const prorationFactor = this.calculateProration(releaseDate);
    
    return {
      usage_charges: usage.total_cost,
      prorated_monthly_fee: usage.monthly_fee * prorationFactor,
      total_amount: usage.total_cost + (usage.monthly_fee * prorationFactor),
      currency: 'USD'
    };
  }
}
```

#### Security and Compliance
1. **Permission Validation**: Only authorized users can release numbers
2. **Audit Trail**: All release actions logged with detailed information
3. **Data Retention**: Message logs archived according to compliance requirements
4. **Secure Export**: Export files protected with access controls and expiration
5. **Confirmation Required**: High-value numbers require additional confirmation

### Integration Points
- **Twilio API**: Number release and status updates
- **Campaign Service**: Campaign dependency checks and updates
- **Message Service**: Message log archival and routing cleanup
- **Billing Service**: Final billing calculation and processing
- **Export Service**: Data export and secure file delivery

## Cancel Release Endpoint

### POST /api/v1/phone-numbers/{id}/cancel-release

#### Request Body
```json
{
  "cancellation_reason": "Decided to keep the number for upcoming campaign",
  "notify_admins": true
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "phone_number_id": "64a1b2c3d4e5f6789012345",
    "status": "ACTIVE",
    "release_cancelled_at": "2024-12-17T10:30:00Z",
    "cancelled_by": "user_123456789",
    "cancellation_reason": "Decided to keep the number for upcoming campaign",
    "grace_period_remaining": "48 hours 15 minutes"
  },
  "message": "Phone number release cancelled successfully. Number is now active."
}
```

## Usage Examples

### Basic Release with Grace Period
```bash
DELETE /api/v1/phone-numbers/64a1b2c3d4e5f6789012345?reason=cost_optimization
```

### Immediate Release with Data Export
```bash
DELETE /api/v1/phone-numbers/64a1b2c3d4e5f6789012345?immediate_release=true&export_data=true&export_format=csv
```

### Scheduled Release with Custom Configuration
```json
{
  "release_config": {
    "grace_period_hours": 168,
    "export_data": true,
    "export_format": "xlsx",
    "preserve_message_logs": true,
    "reason": "Phone number consolidation project"
  },
  "notification_settings": {
    "notify_admins": true,
    "notify_campaigns": true,
    "custom_message": "This number will be released as part of our consolidation project."
  },
  "cleanup_options": {
    "remove_from_campaigns": true,
    "disable_webhooks": true,
    "export_routing_rules": true
  }
}
```

### Cancel Pending Release
```bash
POST /api/v1/phone-numbers/64a1b2c3d4e5f6789012345/cancel-release

{
  "cancellation_reason": "Found active integration that needs this number",
  "notify_admins": true
}
```