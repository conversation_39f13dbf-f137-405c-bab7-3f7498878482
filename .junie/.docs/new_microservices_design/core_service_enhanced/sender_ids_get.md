# GET /api/v1/sender-ids - List and Retrieve Sender IDs

## Endpoint Overview
**Method**: GET  
**URL**: `/api/v1/sender-ids` (list) or `/api/v1/sender-ids/{id}` (single)  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 200 requests per minute

## Purpose
Retrieves sender ID registration requests with filtering, pagination, and detailed status information including approval workflow progress.

## Endpoints

### 1. List Sender IDs
**URL**: `GET /api/v1/sender-ids`

#### Request Headers
```
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

#### Query Parameters
```
page=1                           # Page number (default: 1)
page_size=20                    # Items per page (default: 20, max: 100)
search=MYSTORE                  # Search by sender ID or display name
status=PENDING                  # Filter by status: PENDING, APPROVED, REJECTED, SUSPENDED
transport=SMS                   # Filter by transport type
country=US                      # Filter by registered country
user_type=BUSINESS              # Filter by user type: BUSINESS, INDIVIDUAL
usage_type=TRANSACTIONAL        # Filter by usage type: TRANSACTIONAL, PROMOTIONAL, MIXED
sort_by=created_at              # Sort field: created_at, updated_at, sender_id, status
sort_order=desc                 # Sort direction: asc, desc
created_by=user_id              # Filter by creator
workflow_step=DOCUMENT_UPLOAD   # Filter by current workflow step
include_artifacts=false         # Include artifact details (default: false)
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "sender_ids": [
      {
        "_id": "64a1b2c3d4e5f6789012345",
        "org_id": "org_123456789",
        "sender_id": "MYSTORE",
        "display_name": "My Store",
        "transport": "SMS",
        "status": "PENDING",
        "registered_countries": [
          {
            "country_iso_code": "US",
            "country_name": "United States",
            "status": "PENDING",
            "submitted_at": "2024-12-12T10:30:00Z"
          },
          {
            "country_iso_code": "CA",
            "country_name": "Canada", 
            "status": "APPROVED",
            "submitted_at": "2024-12-12T10:30:00Z",
            "approved_at": "2024-12-14T15:45:00Z",
            "approval_reference": "CA-SENDER-2024-001234"
          }
        ],
        "approval_workflow": {
          "current_step": "COMPLIANCE_REVIEW",
          "steps_completed": ["DOCUMENT_UPLOAD", "TECHNICAL_REVIEW"],
          "assigned_reviewer": "reviewer_456",
          "review_notes": [
            "Documents received and validated",
            "Technical requirements met"
          ],
          "estimated_completion": "2024-12-19T10:30:00Z"
        },
        "metadata": {
          "user_type": "BUSINESS",
          "company_name": "My Store Ltd",
          "usage_type": "TRANSACTIONAL",
          "message_volume_per_month": 5000,
          "enable_international_usage": true
        },
        "artifacts_count": 3,
        "created_by": "user_123456789",
        "created_at": "2024-12-12T10:30:00Z",
        "updated_at": "2024-12-15T09:20:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 20,
      "total_pages": 2,
      "total_count": 35,
      "has_next": true,
      "has_previous": false
    },
    "summary": {
      "total_sender_ids": 35,
      "by_status": {
        "PENDING": 12,
        "APPROVED": 18,
        "REJECTED": 3,
        "SUSPENDED": 2
      },
      "by_transport": {
        "SMS": 32,
        "EMAIL": 2,
        "FACEBOOK_MESSENGER": 1
      },
      "by_workflow_step": {
        "DOCUMENT_UPLOAD": 8,
        "TECHNICAL_REVIEW": 3,
        "COMPLIANCE_REVIEW": 1,
        "FINAL_APPROVAL": 0
      }
    }
  },
  "message": "Sender IDs retrieved successfully"
}
```

### 2. Get Single Sender ID
**URL**: `GET /api/v1/sender-ids/{sender_id}`

#### Path Parameters
- `sender_id` (string): MongoDB ObjectId of the sender ID

#### Query Parameters
```
include_artifacts=true          # Include full artifact details (default: false)
include_workflow_history=true   # Include workflow history (default: false)
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "sender_id": "MYSTORE",
    "display_name": "My Store",
    "transport": "SMS",
    "status": "APPROVED",
    "registered_countries": [
      {
        "country_iso_code": "US",
        "country_name": "United States",
        "status": "APPROVED",
        "submitted_at": "2024-12-12T10:30:00Z",
        "approved_at": "2024-12-16T14:20:00Z",
        "approval_reference": "US-SENDER-2024-005678"
      },
      {
        "country_iso_code": "CA",
        "country_name": "Canada",
        "status": "APPROVED", 
        "submitted_at": "2024-12-12T10:30:00Z",
        "approved_at": "2024-12-14T15:45:00Z",
        "approval_reference": "CA-SENDER-2024-001234"
      },
      {
        "country_iso_code": "GB",
        "country_name": "United Kingdom",
        "status": "REJECTED",
        "submitted_at": "2024-12-12T10:30:00Z",
        "rejected_at": "2024-12-15T11:30:00Z",
        "rejected_reason": "Additional documentation required for UK registration"
      }
    ],
    "metadata": {
      "user_type": "BUSINESS",
      "company_name": "My Store Ltd",
      "business_nature": "E-commerce retail",
      "business_registration_number": "REG123456789",
      "user_name": "John Doe",
      "user_designation": "Marketing Manager",
      "sample_content": "Your order #12345 has been shipped and will arrive by 2024-12-15. Track: https://track.mystore.com/12345",
      "intended_usage": "Transactional notifications for order updates, delivery confirmations, and customer service",
      "message_volume_per_month": 5000,
      "enable_international_usage": true,
      "usage_type": "TRANSACTIONAL",
      "country_iso_codes": ["US", "CA", "GB", "AU"]
    },
    "artifacts": [
      {
        "artifact_id": "artifact_123",
        "artifact_type": "BUSINESS_REGISTRATION_COPY",
        "file_name": "business_registration.pdf",
        "file_url": "https://s3.amazonaws.com/artifacts/business_registration.pdf",
        "file_size": 2045672,
        "mime_type": "application/pdf",
        "uploaded_at": "2024-12-13T08:15:00Z",
        "status": "APPROVED",
        "reviewed_by": "reviewer_456",
        "reviewed_at": "2024-12-14T10:30:00Z",
        "review_notes": "Valid business registration certificate"
      },
      {
        "artifact_id": "artifact_124",
        "artifact_type": "SENDER_ID_AGREEMENT",
        "file_name": "sender_agreement_signed.pdf",
        "file_url": "https://s3.amazonaws.com/artifacts/sender_agreement.pdf",
        "file_size": 1024567,
        "mime_type": "application/pdf",
        "uploaded_at": "2024-12-13T09:30:00Z",
        "status": "APPROVED",
        "reviewed_by": "reviewer_456",
        "reviewed_at": "2024-12-14T11:00:00Z",
        "review_notes": "Agreement properly signed and dated"
      }
    ],
    "approval_workflow": {
      "current_step": "FINAL_APPROVAL",
      "steps_completed": [
        "DOCUMENT_UPLOAD",
        "TECHNICAL_REVIEW", 
        "COMPLIANCE_REVIEW"
      ],
      "assigned_reviewer": "admin_789",
      "review_notes": [
        "Documents received and validated",
        "Technical requirements met",
        "Compliance review passed",
        "Ready for final approval"
      ],
      "estimated_completion": "2024-12-17T16:00:00Z",
      "workflow_history": [
        {
          "step": "DOCUMENT_UPLOAD",
          "completed_at": "2024-12-13T09:30:00Z",
          "completed_by": "user_123456789",
          "notes": "All required documents uploaded"
        },
        {
          "step": "TECHNICAL_REVIEW",
          "completed_at": "2024-12-14T14:15:00Z",
          "completed_by": "tech_reviewer_123",
          "notes": "Sender ID format and technical requirements verified"
        },
        {
          "step": "COMPLIANCE_REVIEW", 
          "completed_at": "2024-12-15T16:45:00Z",
          "completed_by": "compliance_reviewer_456",
          "notes": "Regulatory compliance requirements satisfied"
        }
      ]
    },
    "usage_stats": {
      "campaigns_used_in": 5,
      "messages_sent": 12543,
      "last_used": "2024-12-15T18:30:00Z",
      "countries_actively_used": ["US", "CA"]
    },
    "created_by": "user_123456789",
    "created_at": "2024-12-12T10:30:00Z",
    "updated_at": "2024-12-16T14:20:00Z"
  },
  "message": "Sender ID details retrieved successfully"
}
```

## Error Responses

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_005",
    "message": "Sender ID not found",
    "details": [
      {
        "field": "sender_id",
        "message": "No sender ID found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_006", 
    "message": "Access denied to sender ID",
    "details": [
      {
        "field": "org_id",
        "message": "Sender ID belongs to a different organization"
      }
    ]
  }
}
```

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_007",
    "message": "Invalid query parameters",
    "details": [
      {
        "field": "page_size",
        "message": "Page size must be between 1 and 100"
      },
      {
        "field": "status",
        "message": "Invalid status. Allowed: PENDING, APPROVED, REJECTED, SUSPENDED"
      }
    ]
  }
}
```

## Implementation Details

### Database Queries

#### List Query with Filters
```javascript
// MongoDB aggregation pipeline for listing sender IDs
[
  {
    $match: {
      org_id: organizationId,
      ...(status && { status: status }),
      ...(transport && { transport: transport }),
      ...(userType && { 'metadata.user_type': userType }),
      ...(search && {
        $or: [
          { sender_id: { $regex: search, $options: 'i' } },
          { display_name: { $regex: search, $options: 'i' } }
        ]
      })
    }
  },
  {
    $addFields: {
      artifacts_count: { $size: '$artifacts' }
    }
  },
  {
    $sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
  },
  {
    $facet: {
      data: [
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize }
      ],
      summary: [
        {
          $group: {
            _id: null,
            total_count: { $sum: 1 },
            status_counts: {
              $push: {
                $group: {
                  _id: '$status',
                  count: { $sum: 1 }
                }
              }
            }
          }
        }
      ]
    }
  }
]
```

### Performance Optimizations

#### Indexing Strategy
```javascript
// Required indexes for optimal performance
db.sender_ids.createIndex({ "org_id": 1, "status": 1, "created_at": -1 });
db.sender_ids.createIndex({ "org_id": 1, "transport": 1 });
db.sender_ids.createIndex({ "org_id": 1, "metadata.user_type": 1 });
db.sender_ids.createIndex({ "org_id": 1, "sender_id": 1 }, { unique: true });
db.sender_ids.createIndex({ "registered_countries.country_iso_code": 1, "registered_countries.status": 1 });
db.sender_ids.createIndex({ "approval_workflow.current_step": 1, "status": 1 });
```

#### Caching Strategy
- Sender ID lists cached for 2 minutes
- Individual sender ID details cached for 5 minutes
- Approval workflow status cached for 1 minute
- Artifacts metadata cached for 10 minutes

### Business Logic

#### Access Control
1. **Organization Isolation**: Users can only access sender IDs within their organization
2. **Creator Access**: Sender ID creators have full access to their submissions
3. **Role-Based Access**: Admin/reviewer roles can access all sender IDs for approval workflow

#### Status Logic
- **PENDING**: Initial status when created, awaiting document upload or review
- **APPROVED**: All workflow steps completed successfully
- **REJECTED**: Failed compliance or technical review
- **SUSPENDED**: Temporarily disabled due to policy violations

#### Workflow Steps
1. **DOCUMENT_UPLOAD**: User must upload required artifacts
2. **TECHNICAL_REVIEW**: System and technical validation
3. **COMPLIANCE_REVIEW**: Manual regulatory compliance review
4. **FINAL_APPROVAL**: Final approval and activation

### Integration Points

#### Campaign Service Integration
```json
{
  "endpoint": "/api/v1/campaigns",
  "usage": "Reference approved sender IDs in campaign creation",
  "validation": "Only APPROVED sender IDs can be used in campaigns"
}
```

#### Message Service Integration  
```json
{
  "endpoint": "/api/v1/messages",
  "usage": "Approved sender IDs available for message sending",
  "filtering": "Filter by transport type and country approval status"
}
```

## Usage Examples

### Basic Sender ID List
```bash
GET /api/v1/sender-ids?page=1&page_size=10&status=APPROVED
```

### Search and Filter
```bash
GET /api/v1/sender-ids?search=STORE&transport=SMS&user_type=BUSINESS&sort_by=created_at&sort_order=desc
```

### Get Sender ID with Full Details
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345?include_artifacts=true&include_workflow_history=true
```

### Filter by Workflow Step
```bash
GET /api/v1/sender-ids?workflow_step=DOCUMENT_UPLOAD&status=PENDING
```

### Filter by Country Approval
```bash
GET /api/v1/sender-ids?country=US&status=APPROVED&usage_type=TRANSACTIONAL
```

### Filter by Date Range
```bash
GET /api/v1/sender-ids?created_after=2024-12-01&created_before=2024-12-31&sort_by=created_at
```