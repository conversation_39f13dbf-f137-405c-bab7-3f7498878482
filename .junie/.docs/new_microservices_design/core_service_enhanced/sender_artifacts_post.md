# POST /api/v1/sender-ids/{id}/artifacts - Upload Sender ID Document

## Endpoint Overview
**Method**: POST  
**URL**: `/api/v1/sender-ids/{sender_id}/artifacts`  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 20 requests per minute  
**Max File Size**: 10 MB per file  
**Max Files**: 5 files per request

## Purpose
Uploads supporting documents (artifacts) for sender ID registration requests. Documents are required for the approval workflow and vary by country and user type.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data
X-Organization-ID: <organization_id>
```

## Path Parameters
- `sender_id` (string): MongoDB ObjectId of the sender ID

## Form Data Parameters
```
artifact_type=BUSINESS_REGISTRATION_COPY    # Required: Type of document
file=@business_registration.pdf             # Required: Document file
description=Business registration certificate from Delaware Secretary of State
country_specific=US                         # Optional: Country this document applies to
expiry_date=2025-12-31                     # Optional: Document expiration date
metadata={"document_number":"REG123456789"} # Optional: Additional metadata
```

## Supported Artifact Types

### Business User Documents
- `BUSINESS_REGISTRATION_COPY` - Business registration certificate
- `TAX_IDENTIFICATION_COPY` - Tax ID or VAT certificate  
- `SENDER_ID_AGREEMENT` - Signed sender ID agreement form
- `AUTHORIZATION_LETTER` - Authorization letter for SMS usage
- `SAMPLE_MESSAGE_CONTENT` - Screenshot or document with sample messages
- `BUSINESS_LICENSE` - Professional/trade license
- `BANK_STATEMENT` - Recent bank statement for verification

### Individual User Documents  
- `PERSONAL_ID_COPY` - Government-issued ID (passport, driver's license)
- `SENDER_ID_AGREEMENT` - Signed sender ID agreement form
- `PROOF_OF_ADDRESS` - Utility bill or bank statement
- `SAMPLE_MESSAGE_CONTENT` - Screenshot or document with sample messages
- `AUTHORIZATION_LETTER` - Authorization for business use (if applicable)

### Country-Specific Documents
- `REGULATORY_APPROVAL` - Country-specific regulatory approval
- `TELECOM_LICENSE` - Telecommunications license (some countries)
- `CONTENT_TEMPLATE` - Approved message templates
- `COMPLIANCE_CERTIFICATE` - Compliance certification documents

## File Requirements

### Supported File Types
- **PDF**: `.pdf` (preferred for official documents)
- **Images**: `.jpg`, `.jpeg`, `.png` (for scanned documents)
- **Documents**: `.doc`, `.docx` (for sample content)

### File Validation
- **Size Limit**: 10 MB per file
- **Resolution**: Minimum 300 DPI for scanned documents
- **Quality**: Documents must be clearly readable
- **Format**: Official documents should be in PDF format
- **Completeness**: Documents must show all required information

## Request Examples

### Single Document Upload
```bash
curl -X POST \
  https://api.example.com/api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts \
  -H "Authorization: Bearer <token>" \
  -H "X-Organization-ID: org_123456789" \
  -F "artifact_type=BUSINESS_REGISTRATION_COPY" \
  -F "file=@business_registration.pdf" \
  -F "description=Delaware business registration certificate" \
  -F "country_specific=US"
```

### Multiple Documents Upload
```bash
curl -X POST \
  https://api.example.com/api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts \
  -H "Authorization: Bearer <token>" \
  -H "X-Organization-ID: org_123456789" \
  -F "artifact_type[]=BUSINESS_REGISTRATION_COPY" \
  -F "file[]=@business_registration.pdf" \
  -F "artifact_type[]=SENDER_ID_AGREEMENT" \
  -F "file[]=@sender_agreement_signed.pdf"
```

## Response

### Success Response (201 Created)
```json
{
  "success": true,
  "data": {
    "sender_id": "64a1b2c3d4e5f6789012345",
    "artifacts": [
      {
        "artifact_id": "artifact_789012345",
        "artifact_type": "BUSINESS_REGISTRATION_COPY",
        "file_name": "business_registration.pdf",
        "original_file_name": "Delaware_Business_Registration_2024.pdf",
        "file_url": "https://s3.amazonaws.com/shoutout-artifacts/sender-artifacts/64a1b2c3d4e5f6789012345/artifact_789012345.pdf",
        "file_size": 2045672,
        "mime_type": "application/pdf",
        "description": "Delaware business registration certificate",
        "country_specific": "US",
        "expiry_date": "2025-12-31",
        "metadata": {
          "document_number": "REG123456789",
          "issuing_authority": "Delaware Secretary of State"
        },
        "upload_details": {
          "uploaded_at": "2024-12-16T14:30:00Z",
          "uploaded_by": "user_123456789",
          "upload_ip": "*************",
          "upload_user_agent": "Mozilla/5.0...",
          "processing_time_ms": 1245
        },
        "security": {
          "virus_scan_status": "CLEAN",
          "virus_scan_at": "2024-12-16T14:30:15Z",
          "file_hash": "sha256:abc123def456...",
          "encryption_status": "ENCRYPTED_AT_REST"
        },
        "status": "PENDING",
        "review_status": {
          "reviewed_by": null,
          "reviewed_at": null,
          "review_notes": null,
          "approval_status": "PENDING"
        }
      }
    ],
    "workflow_update": {
      "previous_step": "DOCUMENT_UPLOAD",
      "current_step": "DOCUMENT_UPLOAD", 
      "documents_remaining": [
        "SENDER_ID_AGREEMENT",
        "SAMPLE_MESSAGE_CONTENT"
      ],
      "completion_percentage": 33,
      "can_submit_for_review": false,
      "next_action": "Upload remaining required documents"
    },
    "summary": {
      "total_artifacts": 1,
      "required_artifacts_uploaded": 1,
      "required_artifacts_remaining": 2,
      "optional_artifacts_uploaded": 0,
      "all_requirements_met": false
    }
  },
  "message": "Document uploaded successfully. 2 more required documents needed before submission."
}
```

### Batch Upload Success Response (207 Multi-Status)
```json
{
  "success": true,
  "data": {
    "sender_id": "64a1b2c3d4e5f6789012345",
    "results": [
      {
        "status": "success",
        "artifact": {
          "artifact_id": "artifact_789012345",
          "artifact_type": "BUSINESS_REGISTRATION_COPY",
          "file_name": "business_registration.pdf",
          "status": "PENDING"
        }
      },
      {
        "status": "success", 
        "artifact": {
          "artifact_id": "artifact_789012346",
          "artifact_type": "SENDER_ID_AGREEMENT",
          "file_name": "sender_agreement_signed.pdf",
          "status": "PENDING"
        }
      }
    ],
    "workflow_update": {
      "current_step": "TECHNICAL_REVIEW",
      "documents_remaining": [],
      "completion_percentage": 100,
      "can_submit_for_review": true,
      "auto_submitted": true,
      "next_action": "Awaiting technical review"
    },
    "summary": {
      "total_artifacts": 2,
      "successful_uploads": 2,
      "failed_uploads": 0,
      "all_requirements_met": true
    }
  },
  "message": "All documents uploaded successfully. Sender ID automatically submitted for technical review."
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_001",
    "message": "Invalid file upload",
    "details": [
      {
        "field": "file",
        "message": "File size exceeds 10MB limit"
      },
      {
        "field": "artifact_type",
        "message": "BUSINESS_LICENSE is not required for this user type"
      }
    ]
  }
}
```

### 415 Unsupported Media Type
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_002",
    "message": "Unsupported file type",
    "details": [
      {
        "field": "file",
        "message": "File type '.txt' is not supported. Allowed: pdf, jpg, jpeg, png, doc, docx"
      }
    ]
  }
}
```

### 413 Payload Too Large
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_003",
    "message": "File too large",
    "details": [
      {
        "field": "file",
        "message": "File size 15.2MB exceeds maximum allowed size of 10MB"
      }
    ]
  }
}
```

### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_004",
    "message": "Document validation failed",
    "details": [
      {
        "field": "file",
        "message": "Document appears to be corrupted or unreadable"
      },
      {
        "field": "virus_scan",
        "message": "File failed virus scan - contains malicious content"
      }
    ]
  }
}
```

### 409 Conflict
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_005",
    "message": "Document already exists",
    "details": [
      {
        "field": "artifact_type",
        "message": "BUSINESS_REGISTRATION_COPY already uploaded for this sender ID"
      }
    ]
  }
}
```

### Batch Upload Error Response (207 Multi-Status)
```json
{
  "success": false,
  "data": {
    "sender_id": "64a1b2c3d4e5f6789012345",
    "results": [
      {
        "status": "success",
        "artifact": {
          "artifact_id": "artifact_789012345",
          "artifact_type": "BUSINESS_REGISTRATION_COPY"
        }
      },
      {
        "status": "error",
        "error": {
          "code": "ARTIFACT_002",
          "message": "Unsupported file type",
          "artifact_type": "SENDER_ID_AGREEMENT",
          "file_name": "agreement.txt"
        }
      }
    ],
    "summary": {
      "total_uploads": 2,
      "successful_uploads": 1,
      "failed_uploads": 1
    }
  },
  "message": "Batch upload completed with 1 success and 1 failure."
}
```

## Implementation Details

### File Processing Pipeline
```typescript
interface FileProcessingPipeline {
  steps: [
    'virus_scan',
    'file_validation', 
    'document_analysis',
    'metadata_extraction',
    'encryption',
    'storage_upload',
    'database_record'
  ];
  rollback_on_failure: boolean;
  async_processing: boolean;
}
```

### Security Processing
1. **Virus Scanning**: All files scanned using ClamAV or similar
2. **File Type Validation**: MIME type and extension verification
3. **Content Analysis**: OCR/text extraction for document validation
4. **Metadata Sanitization**: Remove EXIF and metadata for privacy
5. **Encryption**: Files encrypted at rest using AWS KMS
6. **Access Control**: Signed URLs with time-limited access

### Storage Architecture
```
S3 Bucket Structure:
shoutout-artifacts/
├── sender-artifacts/
│   ├── {org_id}/
│   │   ├── {sender_id}/
│   │   │   ├── {artifact_id}.{extension}
│   │   │   ├── thumbnails/
│   │   │   │   └── {artifact_id}_thumb.jpg
│   │   │   └── metadata/
│   │   │       └── {artifact_id}_meta.json
```

### Document Requirements by Country

#### United States
**Required:**
- SENDER_ID_AGREEMENT
- BUSINESS_REGISTRATION_COPY (Business) or PERSONAL_ID_COPY (Individual)
- SAMPLE_MESSAGE_CONTENT

**Optional:**
- TAX_IDENTIFICATION_COPY
- AUTHORIZATION_LETTER

#### Canada  
**Required:**
- SENDER_ID_AGREEMENT
- BUSINESS_REGISTRATION_COPY (Business) or PERSONAL_ID_COPY (Individual)
- SAMPLE_MESSAGE_CONTENT
- AUTHORIZATION_LETTER

#### United Kingdom
**Required:**
- SENDER_ID_AGREEMENT  
- BUSINESS_REGISTRATION_COPY (Business) or PERSONAL_ID_COPY (Individual)
- SAMPLE_MESSAGE_CONTENT
- REGULATORY_APPROVAL
- CONTENT_TEMPLATE

### Workflow Integration
```typescript
interface WorkflowTriggers {
  on_first_document: 'UPDATE_STATUS_TO_DOCUMENT_REVIEW';
  on_all_required_uploaded: 'AUTO_SUBMIT_FOR_TECHNICAL_REVIEW';
  on_reviewer_request: 'NOTIFY_USER_FOR_ADDITIONAL_DOCS';
  on_expiry_approaching: 'SEND_RENEWAL_REMINDER';
}
```

### Business Logic

#### Automatic Workflow Progression
- When all required documents are uploaded, sender ID automatically moves to TECHNICAL_REVIEW
- Missing documents prevent workflow progression
- Document expiry triggers renewal notifications
- Failed virus scans block the entire upload

#### Document Versioning
- Multiple versions of the same document type are allowed
- Latest version is used for review
- Previous versions retained for audit trail
- Reviewers can request specific document updates

#### Quality Validation
```typescript
interface DocumentQuality {
  min_resolution: 300; // DPI
  min_file_size: 50000; // 50KB minimum
  max_file_size: 10485760; // 10MB maximum
  required_text_confidence: 0.8; // OCR confidence
  allowed_aspect_ratios: [1.0, 1.414, 0.707]; // A4, Letter variations
}
```

### Integration Points
- **S3 Storage**: Document storage with encryption
- **Lambda Functions**: Async processing for large files
- **SQS Queues**: Background processing pipeline
- **CloudWatch**: Monitoring and alerting
- **Virus Scanning Service**: Real-time threat detection

## Usage Examples

### Upload Business Registration
```bash
curl -X POST \
  "https://api.example.com/api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -H "X-Organization-ID: org_123456789" \
  -F "artifact_type=BUSINESS_REGISTRATION_COPY" \
  -F "file=@/Users/<USER>/Documents/business_reg.pdf" \
  -F "description=Delaware LLC registration certificate" \
  -F "country_specific=US" \
  -F "expiry_date=2025-12-31" \
  -F "metadata={\"registration_number\":\"LLC123456\",\"state\":\"Delaware\"}"
```

### Upload Signed Agreement
```bash
curl -X POST \
  "https://api.example.com/api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts" \
  -F "artifact_type=SENDER_ID_AGREEMENT" \
  -F "file=@/Users/<USER>/Downloads/signed_agreement.pdf" \
  -F "description=Sender ID agreement signed by John Doe"
```

### Upload Sample Content Screenshot
```bash
curl -X POST \
  "https://api.example.com/api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts" \
  -F "artifact_type=SAMPLE_MESSAGE_CONTENT" \
  -F "file=@/Users/<USER>/Desktop/sample_messages.png" \
  -F "description=Screenshot of sample transactional messages"
```

### Batch Upload Multiple Documents
```bash
curl -X POST \
  "https://api.example.com/api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts" \
  -F "uploads[0][artifact_type]=BUSINESS_REGISTRATION_COPY" \
  -F "uploads[0][file]=@business_reg.pdf" \
  -F "uploads[1][artifact_type]=TAX_IDENTIFICATION_COPY" \
  -F "uploads[1][file]=@tax_id.pdf" \
  -F "uploads[2][artifact_type]=SENDER_ID_AGREEMENT" \
  -F "uploads[2][file]=@signed_agreement.pdf"
```