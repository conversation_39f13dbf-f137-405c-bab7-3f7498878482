# GET /api/v1/sender-ids/{id}/artifacts - Retrieve Sender ID Artifacts

## Endpoint Overview
**Method**: GET  
**URL**: `/api/v1/sender-ids/{sender_id}/artifacts` (list) or `/api/v1/sender-ids/{sender_id}/artifacts/{artifact_id}` (single)  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 100 requests per minute

## Purpose
Retrieves uploaded artifacts (documents) for sender ID registration requests, including download URLs, review status, and metadata.

## Endpoints

### 1. List Artifacts
**URL**: `GET /api/v1/sender-ids/{sender_id}/artifacts`

#### Request Headers
```
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

#### Path Parameters
- `sender_id` (string): MongoDB ObjectId of the sender ID

#### Query Parameters
```
artifact_type=BUSINESS_REGISTRATION_COPY # Filter by artifact type
status=PENDING                           # Filter by review status: PENDING, APPROVED, REJECTED
reviewed_by=reviewer_456                 # Filter by reviewer
country_specific=US                      # Filter by country-specific documents
include_expired=false                    # Include expired documents (default: false)
include_download_urls=true               # Include signed download URLs (default: false)
sort_by=uploaded_at                      # Sort field: uploaded_at, artifact_type, status
sort_order=desc                          # Sort direction: asc, desc
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "sender_id": "64a1b2c3d4e5f6789012345",
    "artifacts": [
      {
        "artifact_id": "artifact_789012345",
        "artifact_type": "BUSINESS_REGISTRATION_COPY",
        "file_name": "business_registration.pdf",
        "original_file_name": "Delaware_Business_Registration_2024.pdf",
        "file_size": 2045672,
        "mime_type": "application/pdf",
        "description": "Delaware business registration certificate",
        "country_specific": "US",
        "expiry_date": "2025-12-31",
        "metadata": {
          "document_number": "REG123456789",
          "issuing_authority": "Delaware Secretary of State",
          "registration_date": "2024-01-15"
        },
        "upload_details": {
          "uploaded_at": "2024-12-16T14:30:00Z",
          "uploaded_by": "user_123456789",
          "upload_ip": "*************",
          "processing_time_ms": 1245
        },
        "security": {
          "virus_scan_status": "CLEAN",
          "virus_scan_at": "2024-12-16T14:30:15Z",
          "file_hash": "sha256:abc123def456789...",
          "encryption_status": "ENCRYPTED_AT_REST"
        },
        "status": "APPROVED",
        "review_status": {
          "reviewed_by": "reviewer_456",
          "reviewed_at": "2024-12-17T10:15:00Z",
          "review_notes": "Valid business registration certificate with all required information",
          "approval_status": "APPROVED"
        },
        "download_url": "https://s3.amazonaws.com/shoutout-artifacts/sender-artifacts/signed-url-with-token",
        "thumbnail_url": "https://s3.amazonaws.com/shoutout-artifacts/thumbnails/artifact_789012345_thumb.jpg",
        "download_expires_at": "2024-12-17T18:30:00Z"
      },
      {
        "artifact_id": "artifact_789012346",
        "artifact_type": "SENDER_ID_AGREEMENT",
        "file_name": "sender_agreement_signed.pdf",
        "original_file_name": "ShoutOUT_Sender_Agreement_John_Doe.pdf",
        "file_size": 1024567,
        "mime_type": "application/pdf",
        "description": "Signed sender ID agreement form",
        "country_specific": null,
        "expiry_date": null,
        "upload_details": {
          "uploaded_at": "2024-12-16T15:45:00Z",
          "uploaded_by": "user_123456789",
          "upload_ip": "*************",
          "processing_time_ms": 892
        },
        "security": {
          "virus_scan_status": "CLEAN",
          "virus_scan_at": "2024-12-16T15:45:12Z",
          "file_hash": "sha256:def789abc456123...",
          "encryption_status": "ENCRYPTED_AT_REST"
        },
        "status": "PENDING",
        "review_status": {
          "reviewed_by": null,
          "reviewed_at": null,
          "review_notes": null,
          "approval_status": "PENDING"
        },
        "download_url": "https://s3.amazonaws.com/shoutout-artifacts/sender-artifacts/signed-url-with-token",
        "thumbnail_url": "https://s3.amazonaws.com/shoutout-artifacts/thumbnails/artifact_789012346_thumb.jpg",
        "download_expires_at": "2024-12-17T18:30:00Z"
      }
    ],
    "summary": {
      "total_artifacts": 2,
      "by_status": {
        "PENDING": 1,
        "APPROVED": 1,
        "REJECTED": 0
      },
      "by_type": {
        "BUSINESS_REGISTRATION_COPY": 1,
        "SENDER_ID_AGREEMENT": 1,
        "SAMPLE_MESSAGE_CONTENT": 0
      },
      "required_documents_status": {
        "all_uploaded": false,
        "missing_documents": ["SAMPLE_MESSAGE_CONTENT"],
        "completion_percentage": 67
      },
      "total_file_size": 3070239,
      "last_upload": "2024-12-16T15:45:00Z"
    }
  },
  "message": "Artifacts retrieved successfully"
}
```

### 2. Get Single Artifact
**URL**: `GET /api/v1/sender-ids/{sender_id}/artifacts/{artifact_id}`

#### Path Parameters
- `sender_id` (string): MongoDB ObjectId of the sender ID
- `artifact_id` (string): Unique identifier of the artifact

#### Query Parameters
```
include_download_url=true    # Include signed download URL (default: false)
include_thumbnail=true       # Include thumbnail URL (default: false)
include_metadata=true        # Include full metadata (default: true)
include_review_history=true  # Include review history (default: false)
download_expires_in=3600     # Download URL expiry in seconds (default: 3600)
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "artifact_id": "artifact_789012345",
    "sender_id": "64a1b2c3d4e5f6789012345",
    "artifact_type": "BUSINESS_REGISTRATION_COPY",
    "file_name": "business_registration.pdf",
    "original_file_name": "Delaware_Business_Registration_2024.pdf",
    "file_size": 2045672,
    "mime_type": "application/pdf",
    "description": "Delaware business registration certificate",
    "country_specific": "US",
    "expiry_date": "2025-12-31",
    "metadata": {
      "document_number": "REG123456789",
      "issuing_authority": "Delaware Secretary of State",
      "registration_date": "2024-01-15",
      "document_type": "LLC Registration",
      "state_of_incorporation": "Delaware",
      "business_address": "1234 Business St, Dover, DE 19901",
      "registered_agent": "Corporate Services Inc"
    },
    "upload_details": {
      "uploaded_at": "2024-12-16T14:30:00Z",
      "uploaded_by": "user_123456789",
      "uploaded_by_name": "John Doe",
      "upload_ip": "*************",
      "upload_user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "processing_time_ms": 1245,
      "upload_session_id": "session_abc123def456"
    },
    "file_analysis": {
      "page_count": 3,
      "text_confidence": 0.95,
      "language_detected": "en",
      "document_quality": "HIGH",
      "extracted_text_preview": "STATE OF DELAWARE CERTIFICATE OF FORMATION...",
      "key_fields_detected": [
        "registration_number",
        "company_name", 
        "registration_date",
        "authorized_signature"
      ],
      "compliance_checks": {
        "required_fields_present": true,
        "signature_detected": true,
        "date_format_valid": true,
        "document_authenticity_score": 0.92
      }
    },
    "security": {
      "virus_scan_status": "CLEAN",
      "virus_scan_at": "2024-12-16T14:30:15Z",
      "virus_scan_engine": "ClamAV 0.105.1",
      "file_hash": "sha256:abc123def456789ghi012jkl345mno678pqr901stu234vwx567yza890bcd123",
      "encryption_status": "ENCRYPTED_AT_REST",
      "encryption_algorithm": "AES-256-GCM",
      "access_attempts": [
        {
          "accessed_by": "user_123456789",
          "accessed_at": "2024-12-17T09:15:00Z",
          "access_type": "VIEW",
          "ip_address": "*************"
        }
      ]
    },
    "status": "APPROVED",
    "review_status": {
      "reviewed_by": "reviewer_456",
      "reviewer_name": "Sarah Johnson",
      "reviewed_at": "2024-12-17T10:15:00Z",
      "review_notes": "Valid business registration certificate with all required information. Document is clear, complete, and meets all regulatory requirements.",
      "approval_status": "APPROVED",
      "compliance_score": 95,
      "review_history": [
        {
          "reviewer": "tech_reviewer_123",
          "reviewed_at": "2024-12-17T08:30:00Z",
          "status": "TECHNICAL_APPROVED",
          "notes": "File format valid, virus scan clean, quality sufficient"
        },
        {
          "reviewer": "compliance_reviewer_456",
          "reviewed_at": "2024-12-17T09:45:00Z",
          "status": "COMPLIANCE_APPROVED",
          "notes": "All required legal information present and verified"
        },
        {
          "reviewer": "reviewer_456",
          "reviewed_at": "2024-12-17T10:15:00Z",
          "status": "APPROVED",
          "notes": "Final approval granted"
        }
      ]
    },
    "usage_tracking": {
      "referenced_in_reviews": 3,
      "download_count": 5,
      "last_downloaded": "2024-12-17T11:30:00Z",
      "shared_with_reviewers": 2,
      "print_requests": 1
    },
    "download_url": "https://s3.amazonaws.com/shoutout-artifacts/sender-artifacts/org_123456789/64a1b2c3d4e5f6789012345/artifact_789012345.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...",
    "thumbnail_url": "https://s3.amazonaws.com/shoutout-artifacts/thumbnails/artifact_789012345_thumb.jpg",
    "download_expires_at": "2024-12-17T19:30:00Z",
    "version_info": {
      "version": 1,
      "is_latest": true,
      "previous_versions": [],
      "superseded_by": null
    }
  },
  "message": "Artifact details retrieved successfully"
}
```

### 3. Download Artifact
**URL**: `GET /api/v1/sender-ids/{sender_id}/artifacts/{artifact_id}/download`

#### Query Parameters
```
inline=false                 # Return file inline vs attachment (default: false)
expires_in=3600             # Download URL expiry in seconds (default: 3600)
watermark=true              # Add watermark for reviewers (default: false)
```

#### Response (302 Redirect)
Redirects to signed S3 URL for direct download, or returns the file directly.

### 4. Get Artifact Thumbnail
**URL**: `GET /api/v1/sender-ids/{sender_id}/artifacts/{artifact_id}/thumbnail`

#### Response (200 OK)
Returns JPEG thumbnail of the document (first page for PDFs).

## Error Responses

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_006",
    "message": "Artifact not found",
    "details": [
      {
        "field": "artifact_id",
        "message": "No artifact found with ID 'artifact_789012345'"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_007",
    "message": "Access denied to artifact",
    "details": [
      {
        "field": "permissions",
        "message": "Artifact belongs to a different organization or user lacks permission"
      }
    ]
  }
}
```

### 410 Gone
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_008",
    "message": "Artifact no longer available",
    "details": [
      {
        "field": "artifact_status",
        "message": "Artifact has been deleted or archived"
      }
    ]
  }
}
```

### 423 Locked
```json
{
  "success": false,
  "error": {
    "code": "ARTIFACT_009",
    "message": "Artifact is locked",
    "details": [
      {
        "field": "review_status",
        "message": "Artifact is currently under review and cannot be accessed"
      }
    ]
  }
}
```

## Implementation Details

### Access Control Rules

#### User Permissions
```typescript
interface ArtifactAccessRules {
  creator: {
    can_view: true;
    can_download: true;
    can_replace: true; // Only during DOCUMENT_UPLOAD phase
  };
  reviewer: {
    can_view: true;
    can_download: true;
    can_add_notes: true;
    can_approve_reject: true;
  };
  admin: {
    can_view: true;
    can_download: true;
    can_delete: true;
    can_override_review: true;
  };
  organization_member: {
    can_view: false; // Unless specifically granted
    can_download: false;
  };
}
```

#### Download URL Security
```typescript
interface SecureDownloadConfig {
  expiry_time: '1h' | '24h' | '7d';
  ip_restriction: boolean;
  audit_trail: boolean;
  watermark_for_reviewers: boolean;
  single_use: boolean; // For sensitive documents
}
```

### Performance Optimizations

#### Caching Strategy
- Artifact metadata cached for 10 minutes
- Thumbnail URLs cached for 1 hour  
- Download URLs generated on-demand with short expiry
- File analysis results cached permanently

#### Database Indexes
```javascript
// MongoDB indexes for optimal performance
db.sender_artifacts.createIndex({ "sender_id": 1, "artifact_type": 1 });
db.sender_artifacts.createIndex({ "sender_id": 1, "status": 1 });
db.sender_artifacts.createIndex({ "sender_id": 1, "uploaded_at": -1 });
db.sender_artifacts.createIndex({ "artifact_id": 1 }, { unique: true });
db.sender_artifacts.createIndex({ "review_status.reviewed_by": 1, "review_status.reviewed_at": -1 });
db.sender_artifacts.createIndex({ "expiry_date": 1 }, { sparse: true });
```

### Business Logic

#### Document Expiry Handling
- Expired documents flagged but not automatically deleted
- Renewal reminders sent 30 days before expiry
- Grace period of 90 days after expiry before restriction
- Automatic archiving after 1 year post-expiry

#### Version Management
```typescript
interface ArtifactVersioning {
  max_versions_per_type: 5;
  retention_policy: '1_year_after_superseded';
  auto_archive_old_versions: true;
  preserve_approved_versions: true;
}
```

#### Review Workflow Integration
1. **Technical Review**: File format, virus scan, quality check
2. **Compliance Review**: Legal content verification  
3. **Final Approval**: Business approval for use
4. **Ongoing Monitoring**: Expiry tracking and renewal

### Integration Points
- **S3 Storage**: Secure file storage with encryption
- **CloudFront**: CDN for thumbnail delivery
- **Lambda**: Image processing and thumbnail generation
- **SES**: Email notifications for expiry and approvals
- **CloudWatch**: Access logging and monitoring

## Usage Examples

### List All Artifacts
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts?include_download_urls=true&sort_by=uploaded_at&sort_order=desc
```

### Filter Approved Documents
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts?status=APPROVED&include_download_urls=true
```

### Get Specific Artifact with Full Details
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts/artifact_789012345?include_download_url=true&include_review_history=true
```

### Download Artifact
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts/artifact_789012345/download
```

### Get Document Thumbnail
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts/artifact_789012345/thumbnail
```

### Filter by Country-Specific Documents
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts?country_specific=US&artifact_type=REGULATORY_APPROVAL
```

### Check Required Documents Status
```bash
GET /api/v1/sender-ids/64a1b2c3d4e5f6789012345/artifacts?summary=true
```