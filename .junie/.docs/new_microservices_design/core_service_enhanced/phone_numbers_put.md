# PUT /api/v1/phone-numbers/{id} - Update Phone Number Configuration

## Endpoint Overview
**Method**: PUT  
**URL**: `/api/v1/phone-numbers/{phone_number_id}`  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 50 requests per minute

## Purpose
Updates the configuration of an owned phone number including webhooks, routing rules, billing settings, and compliance options. Changes are synchronized with <PERSON><PERSON><PERSON> automatically.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Path Parameters
- `phone_number_id` (string): MongoDB ObjectId of the phone number to update

## Request Body
```json
{
  "configuration": {
    "webhook_url": "https://api.example.com/webhooks/sms/inbound/v2",
    "status_callback_url": "https://api.example.com/webhooks/sms/status/v2",
    "voice_url": "https://api.example.com/webhooks/voice/inbound/v2",
    "friendly_name": "Updated Customer Service Line"
  },
  "capabilities": {
    "sms": true,
    "mms": true,
    "voice": true
  },
  "integration_settings": {
    "default_sender_ids": ["SUPPORT", "BILLING", "NEWSTORE"],
    "message_routing_rules": [
      {
        "condition": "keyword:URGENT",
        "action": "escalate_to_manager",
        "priority": 1
      },
      {
        "condition": "keyword:HOURS",
        "action": "send_business_hours",
        "priority": 2
      },
      {
        "condition": "business_hours:false",
        "action": "send_auto_reply",
        "priority": 3
      }
    ],
    "business_hours": {
      "enabled": true,
      "timezone": "America/New_York",
      "hours": {
        "monday": {"start": "08:00", "end": "18:00"},
        "tuesday": {"start": "08:00", "end": "18:00"},
        "wednesday": {"start": "08:00", "end": "18:00"},
        "thursday": {"start": "08:00", "end": "18:00"},
        "friday": {"start": "08:00", "end": "18:00"},
        "saturday": {"start": "09:00", "end": "15:00"},
        "sunday": null
      },
      "auto_reply_message": "Thank you for contacting us! Our support team is currently unavailable. We'll respond during business hours: Mon-Fri 8AM-6PM, Sat 9AM-3PM EST."
    },
    "auto_responses": [
      {
        "trigger": "first_message",
        "message": "Welcome! Reply STOP to opt out, HELP for support.",
        "enabled": true
      },
      {
        "trigger": "keyword:HELP",
        "message": "For support, call 1-800-HELP <NAME_EMAIL>. Reply STOP to opt out.",
        "enabled": true
      }
    ]
  },
  "billing": {
    "cost_center": "updated-customer-service",
    "budget_code": "CS-2025-Q1",
    "spending_limit": 250.00,
    "spending_alert_threshold": 200.00,
    "auto_recharge": true
  },
  "compliance": {
    "opt_in_required": true,
    "double_opt_in": true,
    "carrier_lookup": true,
    "spam_filtering": true,
    "message_retention_days": 90
  },
  "metadata": {
    "updated_reason": "Migrated to new webhook infrastructure",
    "department": "Customer Success",
    "contact_person": "Jane Smith",
    "project_code": "CS-MIGRATION-2024"
  }
}
```

## Updatable Fields

### Configuration Updates
- `webhook_url` - Inbound message webhook URL
- `webhook_method` - HTTP method for webhook (POST/GET)
- `status_callback_url` - Delivery status callback URL
- `status_callback_method` - Status callback HTTP method
- `friendly_name` - Display name for the number
- `voice_url` - Voice call webhook URL
- `voice_method` - Voice webhook HTTP method
- `voice_fallback_url` - Fallback URL for voice calls

### Capability Updates (Limited)
- Some capabilities can be enabled if supported by the number
- Disabling capabilities may affect existing integrations
- Voice capability changes may require additional validation

### Integration Settings
- `default_sender_ids` - Associated sender IDs for campaigns
- `message_routing_rules` - Inbound message routing logic
- `business_hours` - Operating hours and auto-responses
- `auto_responses` - Automated response messages

### Billing Settings
- `cost_center` - Internal cost allocation
- `budget_code` - Budget tracking code
- `spending_limit` - Maximum monthly spending limit
- `spending_alert_threshold` - Alert threshold amount
- `auto_recharge` - Automatic balance recharge

### Compliance Settings
- `opt_in_required` - Require explicit opt-in
- `double_opt_in` - Require double opt-in confirmation
- `carrier_lookup` - Enable carrier validation
- `spam_filtering` - Enable spam detection
- `message_retention_days` - Message log retention period

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "twilio_sid": "PNabc123def456789ghi012jkl",
    "phone_number": "+***********",
    "friendly_name": "Updated Customer Service Line",
    "country_iso_code": "US",
    "locality": "New York",
    "region": "NY",
    "capabilities": {
      "sms": true,
      "mms": true,
      "voice": true,
      "fax": false
    },
    "status": "ACTIVE",
    "configuration": {
      "webhook_url": "https://api.example.com/webhooks/sms/inbound/v2",
      "webhook_method": "POST",
      "status_callback_url": "https://api.example.com/webhooks/sms/status/v2",
      "status_callback_method": "POST",
      "voice_url": "https://api.example.com/webhooks/voice/inbound/v2",
      "voice_method": "POST",
      "voice_fallback_url": null,
      "account_sid": "ACabc123def456789ghi012jkl345"
    },
    "integration_settings": {
      "default_sender_ids": ["SUPPORT", "BILLING", "NEWSTORE"],
      "message_routing_rules": [
        {
          "rule_id": "rule_001",
          "condition": "keyword:URGENT",
          "action": "escalate_to_manager",
          "priority": 1,
          "created_at": "2024-12-16T16:45:00Z",
          "updated_at": "2024-12-16T16:45:00Z"
        },
        {
          "rule_id": "rule_002",
          "condition": "keyword:HOURS",
          "action": "send_business_hours",
          "priority": 2,
          "created_at": "2024-12-16T16:45:00Z",
          "updated_at": "2024-12-16T16:45:00Z"
        },
        {
          "rule_id": "rule_003",
          "condition": "business_hours:false",
          "action": "send_auto_reply",
          "priority": 3,
          "created_at": "2024-12-16T16:45:00Z",
          "updated_at": "2024-12-16T16:45:00Z"
        }
      ],
      "business_hours": {
        "enabled": true,
        "timezone": "America/New_York",
        "hours": {
          "monday": {"start": "08:00", "end": "18:00"},
          "tuesday": {"start": "08:00", "end": "18:00"},
          "wednesday": {"start": "08:00", "end": "18:00"},
          "thursday": {"start": "08:00", "end": "18:00"},
          "friday": {"start": "08:00", "end": "18:00"},
          "saturday": {"start": "09:00", "end": "15:00"},
          "sunday": null
        },
        "auto_reply_message": "Thank you for contacting us! Our support team is currently unavailable. We'll respond during business hours: Mon-Fri 8AM-6PM, Sat 9AM-3PM EST."
      },
      "auto_responses": [
        {
          "response_id": "auto_001",
          "trigger": "first_message",
          "message": "Welcome! Reply STOP to opt out, HELP for support.",
          "enabled": true
        },
        {
          "response_id": "auto_002",
          "trigger": "keyword:HELP",
          "message": "For support, call 1-800-HELP <NAME_EMAIL>. Reply STOP to opt out.",
          "enabled": true
        }
      ]
    },
    "billing": {
      "monthly_cost": 1.00,
      "currency": "USD",
      "cost_center": "updated-customer-service",
      "budget_code": "CS-2025-Q1",
      "purchase_date": "2024-11-01T10:00:00Z",
      "next_billing_date": "2025-01-01T00:00:00Z",
      "spending_limit": 250.00,
      "spending_alert_threshold": 200.00,
      "auto_recharge": true,
      "current_month_spend": 45.67,
      "total_spend_to_date": 89.23
    },
    "compliance": {
      "opt_in_required": true,
      "double_opt_in": true,
      "carrier_lookup": true,
      "spam_filtering": true,
      "message_retention_days": 90,
      "regulatory_status": "compliant",
      "opt_out_count": 15,
      "spam_reports": 0,
      "carrier_violations": 0,
      "last_compliance_check": "2024-12-16T16:45:00Z"
    },
    "sync_status": {
      "twilio_sync_status": "synchronized",
      "last_synced": "2024-12-16T16:45:00Z",
      "sync_duration_ms": 1247,
      "webhook_test_status": "passed",
      "webhook_test_time": "2024-12-16T16:45:15Z"
    },
    "change_log": [
      {
        "changed_at": "2024-12-16T16:45:00Z",
        "changed_by": "user_123456789",
        "changes": [
          {
            "field": "configuration.webhook_url",
            "old_value": "https://api.example.com/webhooks/sms/inbound",
            "new_value": "https://api.example.com/webhooks/sms/inbound/v2"
          },
          {
            "field": "integration_settings.message_routing_rules",
            "action": "ADD",
            "value": {
              "condition": "keyword:HOURS",
              "action": "send_business_hours"
            }
          },
          {
            "field": "billing.spending_limit",
            "old_value": 100.00,
            "new_value": 250.00
          }
        ],
        "reason": "Migrated to new webhook infrastructure"
      }
    ],
    "metadata": {
      "updated_reason": "Migrated to new webhook infrastructure",
      "department": "Customer Success",
      "contact_person": "Jane Smith",
      "project_code": "CS-MIGRATION-2024"
    },
    "created_by": "user_123456789",
    "created_at": "2024-11-01T10:00:00Z",
    "updated_at": "2024-12-16T16:45:00Z"
  },
  "message": "Phone number configuration updated successfully",
  "warnings": [
    {
      "code": "WEBHOOK_TEST_WARNING",
      "message": "New webhook URL responded but with non-200 status code. Please verify webhook implementation."
    }
  ]
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_010",
    "message": "Invalid configuration update",
    "details": [
      {
        "field": "configuration.webhook_url",
        "message": "Webhook URL must be a valid HTTPS URL"
      },
      {
        "field": "capabilities.voice",
        "message": "Voice capability cannot be enabled for this number type"
      }
    ]
  }
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_002",
    "message": "Phone number not found",
    "details": [
      {
        "field": "phone_number_id",
        "message": "No phone number found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_011",
    "message": "Access denied to phone number configuration",
    "details": [
      {
        "field": "permissions",
        "message": "User does not have permission to modify this phone number"
      }
    ]
  }
}
```

### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_012",
    "message": "Configuration validation failed",
    "details": [
      {
        "field": "integration_settings.business_hours.timezone",
        "message": "Invalid timezone 'America/Invalid_Timezone'"
      },
      {
        "field": "billing.spending_limit",
        "message": "Spending limit cannot exceed organization maximum of $1000.00"
      }
    ]
  }
}
```

### 424 Failed Dependency
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_013",
    "message": "Webhook validation failed",
    "details": [
      {
        "field": "configuration.webhook_url",
        "message": "Webhook URL is not responding or returned error status"
      }
    ],
    "webhook_test_results": {
      "url": "https://api.example.com/webhooks/sms/inbound/v2",
      "status": "failed",
      "response_code": 500,
      "error": "Internal Server Error",
      "test_time": "2024-12-16T16:45:00Z"
    }
  }
}
```

### 409 Conflict
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_014",
    "message": "Configuration conflict",
    "details": [
      {
        "field": "status",
        "message": "Cannot update configuration while number is in PENDING_RELEASE status"
      }
    ]
  }
}
```

## Implementation Details

### Update Workflow
```typescript
interface PhoneNumberUpdateWorkflow {
  steps: [
    'validate_permissions',
    'validate_configuration',
    'test_webhooks',
    'update_twilio_config',
    'update_local_database',
    'sync_integration_settings',
    'log_changes',
    'send_notifications'
  ];
  rollback_on_failure: boolean;
  webhook_test_timeout: number;
}
```

### Validation Rules

#### Webhook Validation
```typescript
interface WebhookValidator {
  async validateWebhookUrl(url: string): Promise<WebhookTestResult> {
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true }),
        timeout: 5000
      });
      
      return {
        status: response.status === 200 ? 'passed' : 'warning',
        response_code: response.status,
        response_time_ms: response.time
      };
    } catch (error) {
      return {
        status: 'failed',
        error: error.message
      };
    }
  }
}
```

#### Business Rules
1. **Active Numbers Only**: Only ACTIVE numbers can be updated
2. **Capability Limitations**: Some capabilities cannot be changed post-purchase
3. **Webhook Accessibility**: Webhook URLs must be publicly accessible
4. **Organization Limits**: Updates must respect organization-level constraints
5. **Billing Limits**: Spending limits cannot exceed organization maximums

### Twilio Synchronization
```typescript
interface TwilioSyncService {
  async syncConfiguration(phoneNumberSid: string, config: PhoneNumberConfig): Promise<SyncResult> {
    const updates = {
      friendlyName: config.friendly_name,
      smsUrl: config.webhook_url,
      smsMethod: config.webhook_method,
      statusCallback: config.status_callback_url,
      statusCallbackMethod: config.status_callback_method,
      voiceUrl: config.voice_url,
      voiceMethod: config.voice_method
    };
    
    try {
      const updatedNumber = await this.twilioClient.incomingPhoneNumbers(phoneNumberSid).update(updates);
      return { status: 'synchronized', twilio_sid: updatedNumber.sid };
    } catch (error) {
      throw new TwilioSyncError(`Failed to sync with Twilio: ${error.message}`);
    }
  }
}
```

### Change Tracking
```typescript
interface ChangeTracker {
  trackChanges(oldConfig: PhoneNumberConfig, newConfig: PhoneNumberConfig): ChangeLog[] {
    const changes: ChangeLog[] = [];
    
    // Track all field changes
    Object.keys(newConfig).forEach(field => {
      if (oldConfig[field] !== newConfig[field]) {
        changes.push({
          field,
          old_value: oldConfig[field],
          new_value: newConfig[field],
          change_type: 'UPDATE'
        });
      }
    });
    
    return changes;
  }
}
```

### Business Logic

#### Routing Rules Engine
```typescript
interface RoutingRulesEngine {
  validateRules(rules: MessageRoutingRule[]): ValidationResult;
  processInboundMessage(message: InboundMessage, rules: MessageRoutingRule[]): RoutingAction;
  evaluateBusinessHours(timezone: string, hours: BusinessHours): boolean;
  generateAutoResponse(trigger: string, responses: AutoResponse[]): string | null;
}
```

#### Auto-Response System
- Keyword-based responses (HELP, STOP, INFO, etc.)
- Business hours awareness
- First-time sender welcome messages
- Escalation triggers for urgent keywords
- Compliance-required opt-out responses

### Security Considerations
1. **Webhook Security**: URLs tested before configuration
2. **Change Authorization**: User permissions validated
3. **Audit Trail**: All changes logged with timestamps
4. **Rate Limiting**: Prevent excessive configuration changes
5. **Rollback Capability**: Changes can be reverted if needed

### Integration Points
- **Twilio API**: Real-time configuration synchronization
- **Message Service**: Routing rule application
- **Campaign Service**: Sender ID associations
- **Billing Service**: Cost center and limit updates
- **Compliance Service**: Regulatory setting updates

## Usage Examples

### Update Webhook URLs
```json
{
  "configuration": {
    "webhook_url": "https://api.example.com/webhooks/v2/sms/inbound",
    "status_callback_url": "https://api.example.com/webhooks/v2/sms/status"
  }
}
```

### Add New Routing Rules
```json
{
  "integration_settings": {
    "message_routing_rules": [
      {
        "condition": "keyword:ORDER",
        "action": "forward_to_order_team",
        "priority": 1
      },
      {
        "condition": "keyword:BILLING",
        "action": "forward_to_billing",
        "priority": 2
      },
      {
        "condition": "contains:refund",
        "action": "escalate_to_manager",
        "priority": 3
      }
    ]
  }
}
```

### Update Business Hours
```json
{
  "integration_settings": {
    "business_hours": {
      "enabled": true,
      "timezone": "America/Los_Angeles",
      "hours": {
        "monday": {"start": "09:00", "end": "17:00"},
        "tuesday": {"start": "09:00", "end": "17:00"},
        "wednesday": {"start": "09:00", "end": "17:00"},
        "thursday": {"start": "09:00", "end": "17:00"},
        "friday": {"start": "09:00", "end": "17:00"},
        "saturday": null,
        "sunday": null
      },
      "auto_reply_message": "Thanks for reaching out! We're currently closed. Business hours: Mon-Fri 9AM-5PM PST."
    }
  }
}
```

### Update Billing Settings
```json
{
  "billing": {
    "cost_center": "marketing-campaigns",
    "budget_code": "MKT-SMS-2025",
    "spending_limit": 500.00,
    "spending_alert_threshold": 400.00,
    "auto_recharge": false
  }
}
```

### Enable Compliance Features
```json
{
  "compliance": {
    "opt_in_required": true,
    "double_opt_in": true,
    "carrier_lookup": true,
    "spam_filtering": true,
    "message_retention_days": 120
  }
}
```

### Add Auto-Responses
```json
{
  "integration_settings": {
    "auto_responses": [
      {
        "trigger": "first_message",
        "message": "Welcome to our service! Text HELP for assistance or STOP to opt out.",
        "enabled": true
      },
      {
        "trigger": "keyword:MENU",
        "message": "Options: ORDER (check orders), BILLING (account), SUPPORT (help). Reply STOP to opt out.",
        "enabled": true
      }
    ]
  }
}
```