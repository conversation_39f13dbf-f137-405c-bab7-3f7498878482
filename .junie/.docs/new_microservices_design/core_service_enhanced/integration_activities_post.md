# Core Service Enhanced - POST /integration/activities (Third-Party Activity Tracking)

## Task Overview
**Endpoint**: `POST /api/core/v1/integration/activities`  
**Purpose**: Create and track contact activities from third-party integrations and external systems  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Contact management, activity logging, integration contacts endpoint  

## API Specification

### Create Activity Request
```http
POST /api/core/v1/integration/activities
Authorization: Apikey <API_KEY>
Content-Type: application/json

{
  "userId": "94777123456",
  "activityName": "Test Event",
  "activityData": {
    "param1": "val1",
    "purchase_amount": 299.99,
    "product_category": "electronics"
  }
}
```

### Request Parameters
- `userId` (string, required): User ID that was used to create the contact in the integration contacts endpoint
- `activityName` (string, required): Name of the activity or event (will auto-create event type if not exists)
- `activityData` (object, required): JSON object containing arbitrary activity data and parameters

### Response - Success
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "data": {
    "userId": "94777123456",
    "activityName": "Test Event",
    "activityData": {
      "param1": "val1",
      "purchase_amount": 299.99,
      "product_category": "electronics"
    },
    "createdOn": "2024-08-12T10:35:24.054Z",
    "_id": "64a1b2c3d4e5f6789012345",
    "eventId": "3a3be55cafb08ed1878fe0ab7db12345",
    "contactId": "66f019b8c509667d065055e4567890"
  }
}
```

### Response - Contact Not Found
```http
HTTP/1.1 404 Not Found
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "ACTIVITY_001",
    "message": "Contact with userId not found",
    "details": "Contact with userId '94777123456' does not exist in this organization"
  }
}
```

## Request/Response Examples

### Example 1: E-commerce Purchase Activity
**Request:**
```http
POST /api/core/v1/integration/activities
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "userId": "customer_001",
  "activityName": "Purchase Completed",
  "activityData": {
    "order_id": "ORD-12345",
    "total_amount": 157.50,
    "currency": "USD",
    "items": [
      {
        "product_id": "PROD-001",
        "name": "Wireless Headphones",
        "price": 99.99,
        "quantity": 1
      },
      {
        "product_id": "PROD-002",
        "name": "Phone Case",
        "price": 29.99,
        "quantity": 1
      }
    ],
    "payment_method": "credit_card",
    "shipping_address": "123 Main St, City, State",
    "discount_applied": 27.48,
    "source": "mobile_app"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "customer_001",
    "activityName": "Purchase Completed",
    "activityData": {
      "order_id": "ORD-12345",
      "total_amount": 157.50,
      "currency": "USD",
      "items": [
        {
          "product_id": "PROD-001",
          "name": "Wireless Headphones",
          "price": 99.99,
          "quantity": 1
        },
        {
          "product_id": "PROD-002",
          "name": "Phone Case",
          "price": 29.99,
          "quantity": 1
        }
      ],
      "payment_method": "credit_card",
      "shipping_address": "123 Main St, City, State",
      "discount_applied": 27.48,
      "source": "mobile_app"
    },
    "createdOn": "2024-08-12T10:35:24.054Z",
    "_id": "64a1b2c3d4e5f6789012345",
    "eventId": "3a3be55cafb08ed1878fe0ab7db12345",
    "contactId": "66f019b8c509667d065055e4567890"
  }
}
```

### Example 2: Website Engagement Activity
**Request:**
```http
POST /api/core/v1/integration/activities
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "userId": "visitor_789",
  "activityName": "Page View",
  "activityData": {
    "page_url": "https://example.com/products/wireless-headphones",
    "page_title": "Wireless Headphones - Premium Audio",
    "session_id": "sess_abc123def456",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "referrer": "https://google.com/search",
    "time_on_page": 45,
    "scroll_depth": 75,
    "utm_source": "google",
    "utm_medium": "cpc",
    "utm_campaign": "summer_sale"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "visitor_789",
    "activityName": "Page View",
    "activityData": {
      "page_url": "https://example.com/products/wireless-headphones",
      "page_title": "Wireless Headphones - Premium Audio",
      "session_id": "sess_abc123def456",
      "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "referrer": "https://google.com/search",
      "time_on_page": 45,
      "scroll_depth": 75,
      "utm_source": "google",
      "utm_medium": "cpc",
      "utm_campaign": "summer_sale"
    },
    "createdOn": "2024-08-12T10:35:24.054Z",
    "_id": "64a1b2c3d4e5f6789012346",
    "eventId": "3a3be55cafb08ed1878fe0ab7db12346",
    "contactId": "66f019b8c509667d065055e4567891"
  }
}
```

### Example 3: CRM Activity Tracking
**Request:**
```http
POST /api/core/v1/integration/activities
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

{
  "userId": "lead_456",
  "activityName": "Sales Call",
  "activityData": {
    "call_duration": 1800,
    "call_outcome": "interested",
    "sales_rep": "john.doe",
    "notes": "Customer expressed interest in premium package. Follow up scheduled for next week.",
    "next_action": "send_proposal",
    "deal_value": 5000,
    "probability": 60,
    "call_recording_url": "https://recordings.company.com/call_123456"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "lead_456",
    "activityName": "Sales Call",
    "activityData": {
      "call_duration": 1800,
      "call_outcome": "interested",
      "sales_rep": "john.doe",
      "notes": "Customer expressed interest in premium package. Follow up scheduled for next week.",
      "next_action": "send_proposal",
      "deal_value": 5000,
      "probability": 60,
      "call_recording_url": "https://recordings.company.com/call_123456"
    },
    "createdOn": "2024-08-12T10:35:24.054Z",
    "_id": "64a1b2c3d4e5f6789012347",
    "eventId": "3a3be55cafb08ed1878fe0ab7db12347",
    "contactId": "66f019b8c509667d065055e4567892"
  }
}
```

## Error Responses

### 400 Bad Request - Invalid Activity Data
```json
{
  "success": false,
  "error": {
    "code": "ACTIVITY_002",
    "message": "Invalid activity data provided",
    "details": [
      {
        "field": "activityName",
        "message": "Activity name is required and must be a non-empty string"
      },
      {
        "field": "activityData",
        "message": "Activity data must be a valid JSON object"
      }
    ]
  }
}
```

### 401 Unauthorized - Invalid API Key
```json
{
  "success": false,
  "error": {
    "code": "ACTIVITY_003",
    "message": "Invalid or missing API key",
    "details": "Please provide a valid API key in the Authorization header"
  }
}
```

### 404 Not Found - Contact Not Found
```json
{
  "success": false,
  "error": {
    "code": "ACTIVITY_001",
    "message": "Contact with userId not found",
    "details": "Contact with userId 'invalid_user_123' does not exist in this organization"
  }
}
```

### 429 Too Many Requests - Rate Limited
```json
{
  "success": false,
  "error": {
    "code": "ACTIVITY_004",
    "message": "Rate limit exceeded for activity tracking",
    "details": {
      "rate_limit": {
        "limit": 10000,
        "remaining": 0,
        "reset_time": "2024-08-12T11:00:00Z"
      }
    }
  }
}
```

### 500 Internal Server Error - System Error
```json
{
  "success": false,
  "error": {
    "code": "ACTIVITY_005",
    "message": "Failed to create activity",
    "details": "An internal error occurred while processing your request. Please try again later."
  }
}
```

## Technical Implementation

### Activity Processing Flow
```typescript
// src/handlers/IntegrationHandler.ts (extended)
export class IntegrationHandler {
  static async createActivity(req: AuthenticatedRequest, res: Response) {
    try {
      // Extract organization from API key
      const organizationId = req.organizationId;
      
      // Validate request data
      const { error, value } = IntegrationValidator.validateActivityRequest(req.body);
      
      if (error) {
        throw new ValidationError('Invalid activity data', 'ACTIVITY_002', error.details);
      }
      
      const activityData = value as IntegrationActivityRequest;
      
      // Find contact by userId
      const contact = await ContactDAO.findByUserId(organizationId, activityData.userId);
      
      if (!contact) {
        throw new NotFoundError(
          `Contact with userId '${activityData.userId}' not found`,
          'ACTIVITY_001'
        );
      }
      
      // Create or get event type
      const eventType = await this.ensureEventType(organizationId, activityData.activityName);
      
      // Create activity record
      const activity = await ActivityDAO.createActivity({
        org_id: organizationId,
        contact_id: contact._id,
        user_id: activityData.userId,
        event_id: eventType._id,
        activity_name: activityData.activityName,
        activity_data: activityData.activityData,
        created_on: new Date()
      });
      
      // Format response
      const response = {
        userId: activity.user_id,
        activityName: activity.activity_name,
        activityData: activity.activity_data,
        createdOn: activity.created_on,
        _id: activity._id.toString(),
        eventId: activity.event_id.toString(),
        contactId: activity.contact_id.toString()
      };
      
      res.status(201).json({
        success: true,
        data: response
      });
      
    } catch (error) {
      handleError(error, res);
    }
  }
  
  private static async ensureEventType(organizationId: string, activityName: string) {
    // Check if event type exists
    let eventType = await EventTypeDAO.findByName(organizationId, activityName);
    
    if (!eventType) {
      // Create new event type
      eventType = await EventTypeDAO.createEventType({
        org_id: organizationId,
        name: activityName,
        description: `Auto-created event type for ${activityName}`,
        is_active: true,
        created_at: new Date()
      });
    }
    
    return eventType;
  }
}
```

### Activity Validation Schema
```typescript
// src/validators/IntegrationValidator.ts (extended)
export class IntegrationValidator {
  static validateActivityRequest(data: any) {
    const schema = Joi.object({
      userId: Joi.string().required().max(255).messages({
        'string.empty': 'userId is required',
        'any.required': 'userId is required',
        'string.max': 'userId must not exceed 255 characters'
      }),
      activityName: Joi.string().required().min(1).max(255).messages({
        'string.empty': 'Activity name is required and must be a non-empty string',
        'any.required': 'Activity name is required',
        'string.max': 'Activity name must not exceed 255 characters'
      }),
      activityData: Joi.object().required().messages({
        'object.base': 'Activity data must be a valid JSON object',
        'any.required': 'Activity data is required'
      })
    });
    
    return schema.validate(data, { abortEarly: false });
  }
}
```

### Activity Data Access Object
```typescript
// src/lib/db/dao/ActivityDAO.ts
import { ActivityModel } from '../models/activity.model';
import { DatabaseError } from '../../errors/error.types';

export interface CreateActivityRequest {
  org_id: string;
  contact_id: string;
  user_id: string;
  event_id: string;
  activity_name: string;
  activity_data: Record<string, any>;
  created_on: Date;
}

export class ActivityDAO {
  static async createActivity(data: CreateActivityRequest) {
    try {
      const activity = new ActivityModel(data);
      await activity.save();
      
      return activity;
    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to create activity',
        'DB_ACTIVITY_001',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }
  
  static async getActivitiesByContactId(contactId: string, limit: number = 50) {
    try {
      const activities = await ActivityModel
        .find({ contact_id: contactId })
        .sort({ created_on: -1 })
        .limit(limit);
      
      return activities;
    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve activities',
        'DB_ACTIVITY_002'
      );
    }
  }
  
  static async getActivitiesByUserId(orgId: string, userId: string, limit: number = 50) {
    try {
      const activities = await ActivityModel
        .find({ 
          org_id: orgId,
          user_id: userId 
        })
        .sort({ created_on: -1 })
        .limit(limit);
      
      return activities;
    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve user activities',
        'DB_ACTIVITY_003'
      );
    }
  }
}
```

### Event Type Management
```typescript
// src/lib/db/dao/EventTypeDAO.ts
import { EventTypeModel } from '../models/event.type.model';
import { DatabaseError } from '../../errors/error.types';

export interface CreateEventTypeRequest {
  org_id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: Date;
}

export class EventTypeDAO {
  static async findByName(orgId: string, name: string) {
    try {
      const eventType = await EventTypeModel.findOne({
        org_id: orgId,
        name: name,
        is_active: true
      });
      
      return eventType;
    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to find event type',
        'DB_EVENT_001'
      );
    }
  }
  
  static async createEventType(data: CreateEventTypeRequest) {
    try {
      const eventType = new EventTypeModel(data);
      await eventType.save();
      
      return eventType;
    } catch (error: unknown) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to create event type',
        'DB_EVENT_002'
      );
    }
  }
}
```

## Database Schema

### Activity Model
```typescript
// src/lib/db/models/activity.model.ts
import { Schema, model, Document } from 'mongoose';

export interface ActivityDocument extends Document {
  org_id: string;
  contact_id: string;
  user_id: string;
  event_id: string;
  activity_name: string;
  activity_data: Record<string, any>;
  created_on: Date;
}

const activitySchema = new Schema<ActivityDocument>({
  org_id: { type: String, required: true, index: true },
  contact_id: { type: String, required: true, index: true },
  user_id: { type: String, required: true, index: true },
  event_id: { type: String, required: true, index: true },
  activity_name: { type: String, required: true, index: true },
  activity_data: { type: Schema.Types.Mixed, required: true },
  created_on: { type: Date, default: Date.now, index: true }
});

// Compound indexes for performance
activitySchema.index({ org_id: 1, contact_id: 1, created_on: -1 });
activitySchema.index({ org_id: 1, user_id: 1, created_on: -1 });
activitySchema.index({ org_id: 1, activity_name: 1, created_on: -1 });

export const ActivityModel = model<ActivityDocument>('Activity', activitySchema);
```

### Event Type Model
```typescript
// src/lib/db/models/event.type.model.ts
import { Schema, model, Document } from 'mongoose';

export interface EventTypeDocument extends Document {
  org_id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

const eventTypeSchema = new Schema<EventTypeDocument>({
  org_id: { type: String, required: true, index: true },
  name: { type: String, required: true, index: true },
  description: { type: String },
  is_active: { type: Boolean, default: true, index: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

// Compound index for organization and name uniqueness
eventTypeSchema.index({ org_id: 1, name: 1 }, { unique: true });

export const EventTypeModel = model<EventTypeDocument>('EventType', eventTypeSchema);
```

## Use Cases and Integration Examples

### E-commerce Integration
```javascript
// Track customer purchases
$.post("https://api.getshoutout.com/coreservice/integration/activities", {
  "userId": "customer_12345",
  "activityName": "Product Purchase",
  "activityData": {
    "order_id": "ORDER-789",
    "product_name": "Premium Headphones",
    "amount": 199.99,
    "currency": "USD",
    "category": "Electronics"
  }
});
```

### CRM Integration
```javascript
// Track lead interactions
$.post("https://api.getshoutout.com/coreservice/integration/activities", {
  "userId": "lead_98765",
  "activityName": "Demo Scheduled",
  "activityData": {
    "demo_date": "2024-08-15T14:00:00Z",
    "sales_rep": "jane.smith",
    "product_interest": "Enterprise Plan",
    "lead_score": 85
  }
});
```

### Website Analytics Integration
```javascript
// Track user engagement
$.post("https://api.getshoutout.com/coreservice/integration/activities", {
  "userId": "visitor_abc123",
  "activityName": "Form Submission",
  "activityData": {
    "form_name": "Contact Us",
    "page_url": "https://example.com/contact",
    "utm_campaign": "summer_promo",
    "lead_magnet": "Free Trial"
  }
});
```

## Performance Considerations

### Activity Volume Management
- Support for high-volume activity tracking (10,000+ activities per hour)
- Asynchronous processing for bulk activity imports
- Efficient database indexing for activity queries
- Data archiving strategies for long-term storage

### Real-time Processing
- Immediate activity recording with minimal latency
- Background processing for analytics and segmentation
- Real-time activity feeds for dashboard updates
- Event-driven triggers for automated campaigns

### Data Analytics Integration
- Activity data enrichment for customer insights
- Integration with business intelligence tools
- Custom reporting and analytics capabilities
- Machine learning data preparation

## Security and Compliance

### Data Privacy
- PII handling in activity data
- GDPR compliance for EU customer activities
- Data retention and deletion policies
- Activity data anonymization options

### Access Control
- Organization-level activity isolation
- API key-based access control
- Activity data encryption at rest
- Audit logging for compliance

## Monitoring and Analytics

### Key Metrics
- Activity creation volume and rates
- Activity type distribution and trends
- API performance and error rates
- Integration partner usage patterns

### Business Intelligence
- Customer journey mapping through activities
- Behavioral analytics and segmentation
- Conversion funnel analysis
- Engagement scoring and predictions

This activities endpoint provides comprehensive activity tracking capabilities for third-party integrations, enabling rich customer journey mapping and behavioral analytics within the ShoutOUT platform.