# Core Service Enhanced - POST /integration/contacts (Third-Party Contact Integration)

## Task Overview
**Endpoint**: `POST /api/core/v1/integration/contacts`  
**Purpose**: Create or update contacts from third-party integrations and external systems  
**Estimated Time**: 1 day  
**Priority**: Medium  
**Dependencies**: Contact management, Supabase user storage, organization management  

## API Specification

### Create/Update Integration Contacts Request
```http
POST /api/core/v1/integration/contacts
Authorization: Apikey <API_KEY>
Content-Type: application/json

[{
  "user_id": "94777123456",
  "name": "<PERSON>",
  "mobile_number": "94777123456",
  "email": "<EMAIL>",
  "tags": ["lead", "employee"],
  "custom_attributes": {
    "company": "ShoutOUT Labs",
    "position": "Developer",
    "source": "website_signup"
  }
}]
```

### Request Parameters
- `user_id` (string, required): Unique identifier for the person in your system (mobile, email, or system ID)
- `name` (string, required): Full name of the person
- `mobile_number` (string, optional): Mobile number in E.164 format
- `email` (string, optional): Valid email address
- `tags` (array, optional): String array of tags for categorization
- `custom_attributes` (object, optional): Custom key-value pairs for additional data

### Response - Success
```http
HTTP/1.1 201 Created
Content-Type: application/json

[
  {
    "user_id": "94777123456",
    "name": "Duke",
    "mobile_number": "94777123456",
    "email": "<EMAIL>",
    "country_code": "LK",
    "country": "Sri Lanka",
    "_mobile_number_valid": true,
    "_email_valid": true,
    "tags": ["lead", "employee"],
    "custom_attributes": {
      "company": "ShoutOUT Labs",
      "position": "Developer",
      "source": "website_signup"
    },
    "id": "64a1b2c3d4e5f6789012345",
    "created_at": "2024-08-12T10:30:00Z",
    "updated_at": "2024-08-12T10:30:00Z"
  }
]
```

### Response - Validation Error
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "INTEGRATION_001",
    "message": "Invalid contact data provided",
    "details": [
      {
        "field": "mobile_number",
        "message": "Mobile number must be in E.164 format"
      },
      {
        "field": "email",
        "message": "Invalid email address format"
      }
    ]
  }
}
```

## Request/Response Examples

### Example 1: Single Contact Creation
**Request:**
```http
POST /api/core/v1/integration/contacts
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

[{
  "user_id": "cust_001",
  "name": "John Doe",
  "mobile_number": "+12345678901",
  "email": "<EMAIL>",
  "tags": ["customer", "premium"],
  "custom_attributes": {
    "subscription_tier": "gold",
    "signup_date": "2024-01-15",
    "referral_source": "google_ads"
  }
}]
```

**Response:**
```json
[
  {
    "user_id": "cust_001",
    "name": "John Doe",
    "mobile_number": "+12345678901",
    "email": "<EMAIL>",
    "country_code": "US",
    "country": "United States",
    "_mobile_number_valid": true,
    "_email_valid": true,
    "tags": ["customer", "premium"],
    "custom_attributes": {
      "subscription_tier": "gold",
      "signup_date": "2024-01-15",
      "referral_source": "google_ads"
    },
    "id": "64a1b2c3d4e5f6789012345",
    "created_at": "2024-08-12T10:30:00Z",
    "updated_at": "2024-08-12T10:30:00Z"
  }
]
```

### Example 2: Bulk Contact Creation
**Request:**
```http
POST /api/core/v1/integration/contacts
Authorization: Apikey abc123def456ghi789
Content-Type: application/json

[
  {
    "user_id": "emp_001",
    "name": "Alice Smith",
    "mobile_number": "+94771234567",
    "email": "<EMAIL>",
    "tags": ["employee", "marketing"]
  },
  {
    "user_id": "emp_002", 
    "name": "Bob Johnson",
    "email": "<EMAIL>",
    "tags": ["employee", "sales"]
  },
  {
    "user_id": "lead_001",
    "name": "Carol Brown",
    "mobile_number": "+447123456789",
    "tags": ["lead", "demo_requested"]
  }
]
```

**Response:**
```json
[
  {
    "user_id": "emp_001",
    "name": "Alice Smith",
    "mobile_number": "+94771234567",
    "email": "<EMAIL>",
    "country_code": "LK",
    "country": "Sri Lanka",
    "_mobile_number_valid": true,
    "_email_valid": true,
    "tags": ["employee", "marketing"],
    "id": "64a1b2c3d4e5f6789012346",
    "created_at": "2024-08-12T10:30:00Z",
    "updated_at": "2024-08-12T10:30:00Z"
  },
  {
    "user_id": "emp_002",
    "name": "Bob Johnson",
    "email": "<EMAIL>",
    "_mobile_number_valid": false,
    "_email_valid": true,
    "tags": ["employee", "sales"],
    "id": "64a1b2c3d4e5f6789012347",
    "created_at": "2024-08-12T10:30:00Z",
    "updated_at": "2024-08-12T10:30:00Z"
  },
  {
    "user_id": "lead_001",
    "name": "Carol Brown",
    "mobile_number": "+447123456789",
    "country_code": "GB",
    "country": "United Kingdom",
    "_mobile_number_valid": true,
    "_email_valid": false,
    "tags": ["lead", "demo_requested"],
    "id": "64a1b2c3d4e5f6789012348",
    "created_at": "2024-08-12T10:30:00Z",
    "updated_at": "2024-08-12T10:30:00Z"
  }
]
```

## Error Responses

### 401 Unauthorized - Invalid API Key
```json
{
  "success": false,
  "error": {
    "code": "INTEGRATION_002",
    "message": "Invalid or missing API key",
    "details": "Please provide a valid API key in the Authorization header"
  }
}
```

### 422 Unprocessable Entity - Duplicate User ID
```json
{
  "success": false,
  "error": {
    "code": "INTEGRATION_003",
    "message": "Contact with user_id already exists",
    "details": [
      {
        "field": "user_id",
        "message": "Contact with user_id 'cust_001' already exists for this organization"
      }
    ]
  }
}
```

### 429 Too Many Requests - Rate Limited
```json
{
  "success": false,
  "error": {
    "code": "INTEGRATION_004",
    "message": "Rate limit exceeded",
    "details": {
      "rate_limit": {
        "limit": 1000,
        "remaining": 0,
        "reset_time": "2024-08-12T11:00:00Z"
      }
    }
  }
}
```

### 500 Internal Server Error - System Error
```json
{
  "success": false,
  "error": {
    "code": "INTEGRATION_005",
    "message": "Failed to process contact integration",
    "details": "An internal error occurred while processing your request. Please try again later."
  }
}
```

## Technical Implementation

### Data Processing Flow
```typescript
// src/handlers/IntegrationHandler.ts
import { ContactDAO } from '../lib/db/dao/ContactDAO';
import { IntegrationValidator } from '../validators/IntegrationValidator';

export class IntegrationHandler {
  static async createContacts(req: AuthenticatedRequest, res: Response) {
    try {
      // Extract organization from API key
      const organizationId = req.organizationId;
      
      // Validate request data
      const { error, value } = IntegrationValidator.validateContactsRequest(req.body);
      
      if (error) {
        throw new ValidationError('Invalid contact data', 'INTEGRATION_001', error.details);
      }
      
      const contactsData = value as IntegrationContactRequest[];
      const processedContacts = [];
      
      for (const contactData of contactsData) {
        try {
          // Process individual contact
          const processedContact = await this.processContact(organizationId, contactData);
          processedContacts.push(processedContact);
          
        } catch (contactError) {
          // Log individual contact errors but continue processing
          logger.warn('Failed to process individual contact', {
            user_id: contactData.user_id,
            error: contactError.message
          });
          
          // Add error information to response
          processedContacts.push({
            user_id: contactData.user_id,
            error: contactError.message,
            status: 'failed'
          });
        }
      }
      
      res.status(201).json(processedContacts);
      
    } catch (error) {
      handleError(error, res);
    }
  }
  
  private static async processContact(organizationId: string, contactData: IntegrationContactRequest) {
    // Validate and format phone number
    const formattedPhone = this.formatPhoneNumber(contactData.mobile_number);
    
    // Validate email
    const emailValid = this.validateEmail(contactData.email);
    
    // Check for existing contact
    const existingContact = await ContactDAO.findByUserId(organizationId, contactData.user_id);
    
    if (existingContact) {
      // Update existing contact
      return await ContactDAO.updateContact(existingContact.id, {
        ...contactData,
        mobile_number: formattedPhone.number,
        _mobile_number_valid: formattedPhone.valid,
        _email_valid: emailValid,
        country_code: formattedPhone.countryCode,
        country: formattedPhone.country,
        updated_at: new Date()
      });
    } else {
      // Create new contact
      return await ContactDAO.createContact({
        org_id: organizationId,
        ...contactData,
        mobile_number: formattedPhone.number,
        _mobile_number_valid: formattedPhone.valid,
        _email_valid: emailValid,
        country_code: formattedPhone.countryCode,
        country: formattedPhone.country
      });
    }
  }
}
```

### API Key Authentication
```typescript
// src/middlewares/apikey.auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { UnauthorizedError } from '../lib/errors/error.types';

export interface ApiKeyRequest extends Request {
  organizationId?: string;
  apiKeyId?: string;
}

export const apiKeyAuthMiddleware = async (
  req: ApiKeyRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const apiKey = req.headers.authorization?.replace('Apikey ', '');
    
    if (!apiKey) {
      throw new UnauthorizedError('API key required', 'INTEGRATION_002');
    }
    
    // Validate API key and get organization
    const apiKeyData = await validateApiKey(apiKey);
    
    if (!apiKeyData) {
      throw new UnauthorizedError('Invalid API key', 'INTEGRATION_002');
    }
    
    req.organizationId = apiKeyData.organization_id;
    req.apiKeyId = apiKeyData.id;
    
    next();
  } catch (error) {
    handleError(error, res);
  }
};
```

### Validation Schema
```typescript
// src/validators/IntegrationValidator.ts
import Joi from 'joi';

export class IntegrationValidator {
  static validateContactsRequest(data: any) {
    const schema = Joi.array().items(
      Joi.object({
        user_id: Joi.string().required().max(255),
        name: Joi.string().required().max(255),
        mobile_number: Joi.string().optional().pattern(/^\+[1-9]\d{1,14}$/),
        email: Joi.string().optional().email(),
        tags: Joi.array().items(Joi.string()).optional(),
        custom_attributes: Joi.object().optional()
      }).or('mobile_number', 'email') // At least one contact method required
    ).min(1).max(1000); // Max 1000 contacts per request
    
    return schema.validate(data, { abortEarly: false });
  }
}
```

## Security Requirements

### API Key Management
- API keys tied to specific organizations
- Rate limiting: 1000 requests per hour per API key
- API key rotation capability
- Access logging and audit trails

### Data Protection
- Input sanitization and validation
- PII data encryption at rest
- GDPR compliance for EU contacts
- Data retention policies

### Access Control
- Organization-level data isolation
- API key permissions and scopes
- Request logging for compliance

## Performance Considerations

### Bulk Processing
- Maximum 1000 contacts per request
- Batch processing for database operations
- Asynchronous processing for large datasets
- Progress tracking for long-running operations

### Caching Strategy
- Cache country/region lookups
- Cache API key validations (5 minutes TTL)
- Phone number format validation caching

### Database Optimization
- Indexes on user_id, organization_id, mobile_number, email
- Bulk insert operations for performance
- Connection pooling for high concurrency

## Monitoring and Analytics

### Key Metrics
- API request volume and response times
- Success/failure rates for contact creation
- Data validation error patterns
- API key usage analytics

### Business Intelligence
- Contact import sources and volumes
- Data quality metrics
- Integration partner usage patterns
- Customer engagement correlation

## Testing Requirements

### Unit Tests
- Contact data validation logic
- Phone number and email validation
- Duplicate handling logic
- Error scenarios and edge cases

### Integration Tests
- End-to-end contact creation flow
- API key authentication testing
- Rate limiting validation
- Bulk processing performance tests

### Load Testing
- High-volume contact imports
- Concurrent API key usage
- Database performance under load
- System resilience testing

## Dependencies

### Internal Services
- **Contact DAO**: Data persistence layer
- **Organization Service**: API key management
- **Validation Service**: Data format validation
- **Logging Service**: Audit trail and monitoring

### External Services
- **Country/Region Service**: Phone number validation
- **Email Validation Service**: Email format and domain validation
- **Rate Limiting Service**: Request throttling

### Configuration
- API rate limiting settings
- Data validation rules
- Contact import limits
- Error handling preferences

This integration contacts endpoint provides a robust API for third-party systems to create and manage contacts within the ShoutOUT platform, with comprehensive validation, error handling, and security features.