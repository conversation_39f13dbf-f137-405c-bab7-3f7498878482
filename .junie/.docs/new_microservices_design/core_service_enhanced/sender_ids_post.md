# POST /api/v1/sender-ids - Create Sender ID Request

## Endpoint Overview
**Method**: POST  
**URL**: `/api/v1/sender-ids`  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 50 requests per minute

## Purpose
Creates a new sender ID registration request for SMS messaging with regulatory approval workflow.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Request Body
```json
{
  "sender_id": "MYSTORE",
  "display_name": "My Store",
  "transport": "SMS",
  "metadata": {
    "user_type": "BUSINESS",
    "company_name": "My Store Ltd",
    "business_nature": "E-commerce retail",
    "business_registration_number": "REG123456789",
    "user_name": "John Doe",
    "user_designation": "Marketing Manager",
    "sample_content": "Your order #12345 has been shipped and will arrive by 2024-12-15. Track: https://track.mystore.com/12345",
    "intended_usage": "Transactional notifications for order updates, delivery confirmations, and customer service",
    "message_volume_per_month": 5000,
    "enable_international_usage": true,
    "usage_type": "TRANSACTIONAL",
    "country_iso_codes": ["US", "CA", "GB", "AU"]
  }
}
```

## Request Body Schema

### Required Fields
- `sender_id` (string, 3-11 chars): Alphanumeric sender ID (will be uppercase)
- `transport` (string): Message transport type - currently only "SMS" supported
- `metadata` (object): Application metadata based on user type

### Optional Fields
- `display_name` (string, max 50 chars): Human-readable name for the sender ID

### Metadata Object Structure

#### For Business Users (`user_type: "BUSINESS"`)
```typescript
interface BusinessMetadata {
  user_type: "BUSINESS";
  company_name: string;                    // Required
  business_nature: string;                 // Required  
  business_registration_number?: string;   // Optional but recommended
  user_name: string;                       // Required - contact person
  user_designation?: string;               // Contact person's role
  sample_content: string;                  // Required - example message
  intended_usage: string;                  // Required - usage description
  message_volume_per_month: number;        // Required - estimated volume
  enable_international_usage: boolean;     // Required
  usage_type: "TRANSACTIONAL" | "PROMOTIONAL" | "MIXED"; // Required
  country_iso_codes: string[];             // Required - target countries
}
```

#### For Individual Users (`user_type: "INDIVIDUAL"`)
```typescript
interface IndividualMetadata {
  user_type: "INDIVIDUAL";
  user_name: string;                       // Required
  user_identification_number: string;     // Required - ID/passport number
  sample_content: string;                  // Required
  intended_usage: string;                  // Required
  message_volume_per_month: number;        // Required
  enable_international_usage: boolean;     // Required
  usage_type: "TRANSACTIONAL" | "PROMOTIONAL" | "MIXED"; // Required
  country_iso_codes: string[];             // Required
}
```

## Response

### Success Response (201 Created)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "sender_id": "MYSTORE",
    "display_name": "My Store",
    "transport": "SMS",
    "status": "PENDING",
    "registered_countries": [
      {
        "country_iso_code": "US",
        "country_name": "United States",
        "status": "PENDING",
        "submitted_at": "2024-12-12T10:30:00Z"
      },
      {
        "country_iso_code": "CA", 
        "country_name": "Canada",
        "status": "PENDING",
        "submitted_at": "2024-12-12T10:30:00Z"
      },
      {
        "country_iso_code": "GB",
        "country_name": "United Kingdom", 
        "status": "PENDING",
        "submitted_at": "2024-12-12T10:30:00Z"
      },
      {
        "country_iso_code": "AU",
        "country_name": "Australia",
        "status": "PENDING", 
        "submitted_at": "2024-12-12T10:30:00Z"
      }
    ],
    "metadata": {
      "user_type": "BUSINESS",
      "company_name": "My Store Ltd",
      "business_nature": "E-commerce retail",
      "business_registration_number": "REG123456789",
      "user_name": "John Doe",
      "user_designation": "Marketing Manager",
      "sample_content": "Your order #12345 has been shipped...",
      "intended_usage": "Transactional notifications for order updates...",
      "message_volume_per_month": 5000,
      "enable_international_usage": true,
      "usage_type": "TRANSACTIONAL",
      "country_iso_codes": ["US", "CA", "GB", "AU"]
    },
    "artifacts": [],
    "approval_workflow": {
      "current_step": "DOCUMENT_UPLOAD",
      "steps_completed": [],
      "assigned_reviewer": null,
      "review_notes": [],
      "estimated_completion": "2024-12-19T10:30:00Z"
    },
    "created_by": "user_123456789",
    "created_at": "2024-12-12T10:30:00Z",
    "updated_at": "2024-12-12T10:30:00Z"
  },
  "message": "Sender ID request created successfully. Please upload required documents to proceed with approval."
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_001",
    "message": "Invalid sender ID format",
    "details": [
      {
        "field": "sender_id",
        "message": "Sender ID must be 3-11 alphanumeric characters"
      }
    ]
  }
}
```

#### 409 Conflict
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_002", 
    "message": "Sender ID already exists",
    "details": [
      {
        "field": "sender_id",
        "message": "Sender ID 'MYSTORE' already exists for this organization"
      }
    ]
  }
}
```

#### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_003",
    "message": "Invalid metadata for business user type",
    "details": [
      {
        "field": "metadata.company_name", 
        "message": "Company name is required for business users"
      },
      {
        "field": "metadata.country_iso_codes",
        "message": "At least one country code is required"
      }
    ]
  }
}
```

#### 429 Too Many Requests
```json
{
  "success": false,
  "error": {
    "code": "SENDER_ID_004",
    "message": "Rate limit exceeded",
    "details": [
      {
        "field": "rate_limit",
        "message": "Maximum 50 requests per minute exceeded. Try again in 60 seconds."
      }
    ]
  }
}
```

## Implementation Details

### Database Model
```typescript
interface SenderIdDocument {
  _id: ObjectId;
  org_id: string;
  created_by: string;
  sender_id: string;              // 3-11 alphanumeric, uppercase
  display_name?: string;
  transport: 'SMS' | 'EMAIL' | 'FACEBOOK_MESSENGER';
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SUSPENDED';
  registered_countries: CountryRegistration[];
  metadata: BusinessMetadata | IndividualMetadata;
  artifacts: SenderArtifact[];
  approval_workflow: ApprovalWorkflow;
  created_at: Date;
  updated_at: Date;
}

interface CountryRegistration {
  country_iso_code: string;
  country_name: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  submitted_at: Date;
  approved_at?: Date;
  rejected_at?: Date;
  rejected_reason?: string;
  approval_reference?: string;
}

interface ApprovalWorkflow {
  current_step: 'DOCUMENT_UPLOAD' | 'TECHNICAL_REVIEW' | 'COMPLIANCE_REVIEW' | 'FINAL_APPROVAL';
  steps_completed: string[];
  assigned_reviewer?: string;
  review_notes: string[];
  estimated_completion?: Date;
}
```

### Business Logic

#### Validation Rules
1. **Sender ID Format**: 3-11 alphanumeric characters only
2. **Uniqueness**: Sender ID must be unique within organization
3. **Transport Support**: Currently only SMS supported
4. **Country Codes**: Must be valid ISO 2-letter codes
5. **User Type Validation**: Metadata must match user type requirements

#### Approval Workflow Steps
1. **Document Upload**: User uploads required artifacts
2. **Technical Review**: System validates technical requirements
3. **Compliance Review**: Manual review for regulatory compliance  
4. **Final Approval**: Final approval and activation

#### Required Documents (varies by country)
- **Business Registration Certificate** (Business users)
- **Personal ID/Passport Copy** (Individual users) 
- **Sender ID Agreement Form** (All users)
- **Sample Message Content** (All users)
- **Authorization Letter** (Some countries)

### Security & Compliance
- All sender ID applications are logged for audit trails
- Documents are encrypted at rest and in transit
- PII data is handled according to privacy regulations
- Country-specific compliance requirements are enforced

### Integration Points
- **Campaign Service**: Sender IDs are referenced in campaign creation
- **Message Service**: Approved sender IDs are used for SMS delivery
- **Queue System**: Approval workflow uses background job processing
- **File Storage**: Artifacts are stored in AWS S3 with encryption

## Usage Examples

### Business User Application
```json
{
  "sender_id": "SHOPIFY",
  "display_name": "Shopify Store",
  "transport": "SMS",
  "metadata": {
    "user_type": "BUSINESS",
    "company_name": "Shopify Commerce Inc",
    "business_nature": "E-commerce platform",
    "business_registration_number": "BC123456789",
    "user_name": "Sarah Johnson",
    "user_designation": "SMS Manager",
    "sample_content": "Hi {{name}}, your order {{order_id}} is confirmed! Delivery by {{date}}. Questions? Reply HELP",
    "intended_usage": "Order confirmations, shipping updates, customer service notifications",
    "message_volume_per_month": 50000,
    "enable_international_usage": true,
    "usage_type": "TRANSACTIONAL",
    "country_iso_codes": ["US", "CA", "GB", "AU", "NZ"]
  }
}
```

### Individual User Application  
```json
{
  "sender_id": "JOHNDOE",
  "display_name": "John's Services", 
  "transport": "SMS",
  "metadata": {
    "user_type": "INDIVIDUAL",
    "user_name": "John Doe",
    "user_identification_number": "ID123456789",
    "sample_content": "Your appointment with John's Plumbing is scheduled for tomorrow at 2 PM. Call 555-0123 to reschedule.",
    "intended_usage": "Appointment reminders and service notifications for plumbing business",
    "message_volume_per_month": 500,
    "enable_international_usage": false,
    "usage_type": "TRANSACTIONAL", 
    "country_iso_codes": ["US"]
  }
}
```

### Promotional Use Case
```json
{
  "sender_id": "DEALS24", 
  "display_name": "Daily Deals",
  "transport": "SMS",
  "metadata": {
    "user_type": "BUSINESS",
    "company_name": "Daily Deals Marketing LLC",
    "business_nature": "Marketing and promotions",
    "user_name": "Mike Smith",
    "sample_content": "🔥 FLASH SALE: 50% off all electronics! Today only. Shop now: deals24.com/flash Use code SAVE50",
    "intended_usage": "Promotional messages for daily deals and special offers",
    "message_volume_per_month": 25000,
    "enable_international_usage": false,
    "usage_type": "PROMOTIONAL",
    "country_iso_codes": ["US"]
  }
}
```