# GET /api/v1/phone-numbers - Search Available Numbers and List Owned Numbers

## Endpoint Overview
**Method**: GET  
**URL**: `/api/v1/phone-numbers/available` (search) or `/api/v1/phone-numbers` (owned) or `/api/v1/phone-numbers/{id}` (single)  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 200 requests per minute

## Purpose
Searches for available phone numbers for purchase through Twilio, lists owned phone numbers, and retrieves detailed information about specific numbers including usage statistics and configuration.

## Endpoints

### 1. Search Available Phone Numbers
**URL**: `GET /api/v1/phone-numbers/available`

#### Request Headers
```
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

#### Query Parameters
```
country=US                    # Required: ISO 2-letter country code
area_code=212                 # Filter by area code
contains=*800*                # Number must contain specified pattern
near_number=+12125551234      # Find numbers near existing number
near_lat_long=40.7589,-73.9851 # Find numbers near coordinates (lat,long)
distance=50                   # Maximum distance in miles from coordinates
sms_enabled=true              # Require SMS capability (default: true)
voice_enabled=false           # Require voice capability (default: false)  
mms_enabled=false             # Require MMS capability (default: false)
exclude_all_address_required=true # Exclude numbers requiring address validation
exclude_local_address_required=true # Exclude numbers requiring local address
exclude_foreign_address_required=true # Exclude numbers requiring foreign address
beta=false                    # Include beta numbers (default: false)
limit=50                      # Number of results (default: 20, max: 100)
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "search_criteria": {
      "country": "US",
      "area_code": "212",
      "sms_enabled": true,
      "voice_enabled": false,
      "mms_enabled": false,
      "limit": 50
    },
    "available_numbers": [
      {
        "phone_number": "+12125551001",
        "friendly_name": "(*************",
        "locality": "New York",
        "region": "NY",
        "country": "US",
        "iso_country": "US",
        "area_code": "212",
        "capabilities": {
          "sms": true,
          "mms": true,
          "voice": true,
          "fax": false
        },
        "address_requirements": "none",
        "beta": false,
        "rate_center": "New York",
        "latitude": "40.7589",
        "longitude": "-73.9851",
        "pricing": {
          "monthly_cost": 1.00,
          "setup_cost": 0.00,
          "currency": "USD",
          "per_message_cost": {
            "sms_inbound": 0.0075,
            "sms_outbound": 0.0075,
            "mms_inbound": 0.02,
            "mms_outbound": 0.02
          }
        },
        "availability": "available",
        "reserved_until": null
      },
      {
        "phone_number": "+12125551002",
        "friendly_name": "(*************", 
        "locality": "New York",
        "region": "NY",
        "country": "US",
        "iso_country": "US",
        "area_code": "212",
        "capabilities": {
          "sms": true,
          "mms": false,
          "voice": true,
          "fax": false
        },
        "address_requirements": "none",
        "beta": false,
        "rate_center": "New York",
        "latitude": "40.7589",
        "longitude": "-73.9851",
        "pricing": {
          "monthly_cost": 1.00,
          "setup_cost": 0.00,
          "currency": "USD",
          "per_message_cost": {
            "sms_inbound": 0.0075,
            "sms_outbound": 0.0075,
            "mms_inbound": null,
            "mms_outbound": null
          }
        },
        "availability": "available",
        "reserved_until": null
      }
    ],
    "search_metadata": {
      "total_found": 156,
      "returned_count": 2,
      "search_time_ms": 245,
      "twilio_request_id": "RErequest123456789",
      "cached_results": false,
      "cache_expires_at": "2024-12-16T15:10:00Z"
    },
    "recommendations": {
      "suggested_area_codes": ["646", "917", "347"],
      "nearby_available_counts": {
        "212": 156,
        "646": 89,
        "917": 234,
        "347": 67
      },
      "alternative_locations": [
        {
          "locality": "Brooklyn",
          "region": "NY", 
          "available_count": 89,
          "distance_miles": 8.5
        }
      ]
    }
  },
  "message": "Available phone numbers retrieved successfully"
}
```

### 2. List Owned Phone Numbers
**URL**: `GET /api/v1/phone-numbers`

#### Query Parameters
```
page=1                        # Page number (default: 1)
page_size=20                  # Items per page (default: 20, max: 100)
country=US                    # Filter by country
area_code=212                 # Filter by area code
status=ACTIVE                 # Filter by status: ACTIVE, RELEASED, PENDING_RELEASE
capabilities=sms,voice        # Filter by capabilities (comma-separated)
search=555                    # Search in phone number or friendly name
sort_by=created_at            # Sort field: created_at, phone_number, monthly_cost
sort_order=desc               # Sort direction: asc, desc
include_usage_stats=false     # Include usage statistics (default: false)
include_configuration=false   # Include webhook configuration (default: false)
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "phone_numbers": [
      {
        "_id": "64a1b2c3d4e5f6789012345",
        "org_id": "org_123456789",
        "twilio_sid": "PNabc123def456789ghi012jkl",
        "phone_number": "+12125551001",
        "friendly_name": "(*************",
        "country_iso_code": "US",
        "locality": "New York",
        "region": "NY",
        "capabilities": {
          "sms": true,
          "mms": true,
          "voice": true,
          "fax": false
        },
        "status": "ACTIVE",
        "configuration": {
          "webhook_url": "https://api.example.com/webhooks/sms/inbound",
          "status_callback_url": "https://api.example.com/webhooks/sms/status",
          "application_sid": null,
          "voice_url": null,
          "voice_method": "POST"
        },
        "usage_stats": {
          "messages_sent": 1247,
          "messages_received": 893,
          "voice_calls_made": 0,
          "voice_calls_received": 0,
          "last_activity": "2024-12-15T18:30:00Z",
          "total_cost_current_month": 15.67,
          "message_volume_trend": "increasing"
        },
        "billing": {
          "monthly_cost": 1.00,
          "currency": "USD",
          "purchase_date": "2024-11-01T10:00:00Z",
          "next_billing_date": "2025-01-01T00:00:00Z",
          "total_cost_to_date": 46.67,
          "auto_recharge": true
        },
        "created_by": "user_123456789",
        "created_at": "2024-11-01T10:00:00Z",
        "updated_at": "2024-12-15T18:30:00Z"
      },
      {
        "_id": "64a1b2c3d4e5f6789012346",
        "org_id": "org_123456789",
        "twilio_sid": "PNdef456ghi789jkl012mno345",
        "phone_number": "+14155552001",
        "friendly_name": "(*************",
        "country_iso_code": "US",
        "locality": "San Francisco", 
        "region": "CA",
        "capabilities": {
          "sms": true,
          "mms": false,
          "voice": false,
          "fax": false
        },
        "status": "ACTIVE",
        "configuration": {
          "webhook_url": "https://api.example.com/webhooks/sms/inbound",
          "status_callback_url": "https://api.example.com/webhooks/sms/status",
          "application_sid": null,
          "voice_url": null,
          "voice_method": "POST"
        },
        "usage_stats": {
          "messages_sent": 3421,
          "messages_received": 2187,
          "voice_calls_made": 0,
          "voice_calls_received": 0,
          "last_activity": "2024-12-16T12:15:00Z",
          "total_cost_current_month": 28.43,
          "message_volume_trend": "stable"
        },
        "billing": {
          "monthly_cost": 1.00,
          "currency": "USD",
          "purchase_date": "2024-10-15T14:30:00Z",
          "next_billing_date": "2025-01-01T00:00:00Z",
          "total_cost_to_date": 63.43,
          "auto_recharge": true
        },
        "created_by": "user_987654321",
        "created_at": "2024-10-15T14:30:00Z",
        "updated_at": "2024-12-16T12:15:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 20,
      "total_pages": 1,
      "total_count": 2,
      "has_next": false,
      "has_previous": false
    },
    "summary": {
      "total_numbers": 2,
      "active_numbers": 2,
      "released_numbers": 0,
      "by_country": {
        "US": 2,
        "CA": 0,
        "GB": 0
      },
      "by_capabilities": {
        "sms_enabled": 2,
        "mms_enabled": 1,
        "voice_enabled": 1
      },
      "total_monthly_cost": 2.00,
      "total_current_month_usage_cost": 44.10
    }
  },
  "message": "Phone numbers retrieved successfully"
}
```

### 3. Get Single Phone Number
**URL**: `GET /api/v1/phone-numbers/{phone_number_id}`

#### Path Parameters
- `phone_number_id` (string): MongoDB ObjectId of the phone number

#### Query Parameters
```
include_usage_details=true    # Include detailed usage breakdown (default: false)
include_message_logs=false    # Include recent message logs (default: false)  
usage_period=30               # Usage data period in days (default: 30)
include_cost_breakdown=true   # Include detailed cost analysis (default: false)
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "twilio_sid": "PNabc123def456789ghi012jkl",
    "phone_number": "+12125551001",
    "friendly_name": "(*************",
    "country_iso_code": "US",
    "locality": "New York",
    "region": "NY",
    "postal_code": "10001",
    "capabilities": {
      "sms": true,
      "mms": true,
      "voice": true,
      "fax": false
    },
    "address_requirements": "none",
    "beta": false,
    "status": "ACTIVE",
    "configuration": {
      "webhook_url": "https://api.example.com/webhooks/sms/inbound",
      "webhook_method": "POST",
      "status_callback_url": "https://api.example.com/webhooks/sms/status",
      "status_callback_method": "POST",
      "application_sid": null,
      "voice_url": null,
      "voice_method": "POST",
      "voice_fallback_url": null,
      "sms_url": "https://api.example.com/webhooks/sms/inbound",
      "sms_method": "POST",
      "sms_fallback_url": null,
      "account_sid": "ACabc123def456789ghi012jkl345"
    },
    "usage_stats": {
      "period": "last_30_days",
      "messages_sent": 1247,
      "messages_received": 893,
      "voice_calls_made": 0,
      "voice_calls_received": 0,
      "total_messages": 2140,
      "last_activity": "2024-12-15T18:30:00Z",
      "first_activity": "2024-11-01T10:15:00Z",
      "average_daily_volume": 71,
      "peak_daily_volume": 156,
      "message_volume_trend": "increasing",
      "daily_breakdown": [
        {
          "date": "2024-12-15",
          "sent": 23,
          "received": 18,
          "cost": 0.31
        },
        {
          "date": "2024-12-14", 
          "sent": 45,
          "received": 32,
          "cost": 0.58
        }
      ],
      "usage_by_type": {
        "transactional": 1890,
        "promotional": 120,
        "customer_service": 130
      },
      "top_message_destinations": [
        {
          "country": "US",
          "count": 1247,
          "percentage": 100.0
        }
      ]
    },
    "billing": {
      "monthly_cost": 1.00,
      "currency": "USD",
      "purchase_date": "2024-11-01T10:00:00Z",
      "next_billing_date": "2025-01-01T00:00:00Z",
      "cost_breakdown": {
        "base_monthly_fee": 1.00,
        "sms_inbound_cost": 6.70,
        "sms_outbound_cost": 9.35,
        "mms_inbound_cost": 0.00,
        "mms_outbound_cost": 0.00,
        "voice_inbound_cost": 0.00,
        "voice_outbound_cost": 0.00,
        "total_usage_cost": 16.05,
        "total_monthly_cost": 17.05
      },
      "billing_history": [
        {
          "period": "2024-12",
          "base_fee": 1.00,
          "usage_cost": 15.67,
          "total": 16.67,
          "status": "current"
        },
        {
          "period": "2024-11",
          "base_fee": 1.00,
          "usage_cost": 28.00,
          "total": 29.00,
          "status": "paid"
        }
      ],
      "auto_recharge": true,
      "spending_limit": 100.00,
      "spending_alert_threshold": 80.00
    },
    "integration_settings": {
      "campaigns_using_number": 3,
      "active_automations": 1,
      "sender_id_assignments": ["MYSTORE", "SUPPORT"],
      "message_routing_rules": [
        {
          "condition": "keyword:STOP",
          "action": "unsubscribe",
          "priority": 1
        },
        {
          "condition": "keyword:HELP",
          "action": "forward_to_support",
          "priority": 2
        }
      ]
    },
    "compliance": {
      "opt_out_count": 23,
      "spam_reports": 0,
      "carrier_violations": 0,
      "regulatory_status": "compliant",
      "last_compliance_check": "2024-12-16T06:00:00Z"
    },
    "created_by": "user_123456789",
    "created_by_name": "John Doe",
    "created_at": "2024-11-01T10:00:00Z",
    "updated_at": "2024-12-15T18:30:00Z"
  },
  "message": "Phone number details retrieved successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_001",
    "message": "Invalid search parameters",
    "details": [
      {
        "field": "country",
        "message": "Country code is required for number search"
      },
      {
        "field": "area_code",
        "message": "Invalid area code format"
      }
    ]
  }
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_002",
    "message": "Phone number not found",
    "details": [
      {
        "field": "phone_number_id",
        "message": "No phone number found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_003",
    "message": "Access denied to phone number",
    "details": [
      {
        "field": "org_id",
        "message": "Phone number belongs to a different organization"
      }
    ]
  }
}
```

### 503 Service Unavailable
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_004",
    "message": "Twilio service temporarily unavailable",
    "details": [
      {
        "field": "twilio_api",
        "message": "Unable to connect to Twilio API. Please try again later."
      }
    ]
  }
}
```

## Implementation Details

### Twilio Integration
```typescript
interface TwilioPhoneNumberService {
  searchAvailableNumbers(criteria: NumberSearchCriteria): Promise<AvailableNumber[]>;
  getNumberDetails(sid: string): Promise<TwilioNumber>;
  validateNumberAvailability(phoneNumber: string): Promise<boolean>;
  getNumberPricing(country: string): Promise<NumberPricing>;
}
```

### Caching Strategy
- Available number searches cached for 5 minutes
- Owned number lists cached for 2 minutes
- Individual number details cached for 10 minutes
- Twilio API responses cached to reduce rate limiting

### Database Queries
```javascript
// MongoDB aggregation for owned numbers with usage stats
[
  {
    $match: {
      org_id: organizationId,
      status: { $ne: 'DELETED' }
    }
  },
  {
    $lookup: {
      from: 'message_logs',
      let: { phoneNumber: '$phone_number' },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ['$from', '$$phoneNumber'] },
                { $gte: ['$created_at', thirtyDaysAgo] }
              ]
            }
          }
        },
        {
          $group: {
            _id: null,
            messages_sent: { $sum: 1 },
            total_cost: { $sum: '$cost' }
          }
        }
      ],
      as: 'usage_stats'
    }
  }
]
```

### Performance Optimizations
- Number search results paginated to prevent large responses
- Usage statistics calculated asynchronously for large datasets
- Twilio webhook responses cached to reduce API calls
- Database indexes on frequently queried fields

### Business Logic

#### Number Availability Caching
```typescript
interface NumberAvailabilityCache {
  key: string; // country + area_code + capabilities hash
  data: AvailableNumber[];
  expires_at: Date;
  twilio_request_count: number;
}
```

#### Cost Calculation
```typescript
interface CostCalculator {
  calculateMonthlyCost(number: PhoneNumber): Promise<MonthlyCost>;
  calculateUsageCost(messages: MessageLog[]): Promise<UsageCost>;
  projectMonthlyCost(usage: UsageStats): Promise<ProjectedCost>;
}
```

### Integration Points
- **Twilio API**: Real-time number search and management
- **Message Service**: Usage statistics from message logs
- **Billing Service**: Cost tracking and payment processing
- **Campaign Service**: Number assignment to campaigns
- **Analytics Service**: Usage trend analysis

## Usage Examples

### Search for NYC Numbers with SMS
```bash
GET /api/v1/phone-numbers/available?country=US&area_code=212&sms_enabled=true&limit=10
```

### Search Near Coordinates
```bash  
GET /api/v1/phone-numbers/available?country=US&near_lat_long=40.7589,-73.9851&distance=25&sms_enabled=true
```

### List Owned Numbers with Usage
```bash
GET /api/v1/phone-numbers?include_usage_stats=true&sort_by=created_at&sort_order=desc
```

### Get Number Details with Cost Breakdown
```bash
GET /api/v1/phone-numbers/64a1b2c3d4e5f6789012345?include_usage_details=true&include_cost_breakdown=true&usage_period=60
```

### Filter by Capabilities
```bash
GET /api/v1/phone-numbers?capabilities=sms,mms&status=ACTIVE&country=US
```

### Search Numbers by Pattern
```bash
GET /api/v1/phone-numbers/available?country=US&contains=*800*&voice_enabled=true
```