# POST /api/v1/phone-numbers - Purchase Phone Number

## Endpoint Overview
**Method**: POST  
**URL**: `/api/v1/phone-numbers`  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 20 requests per minute

## Purpose
Purchases a phone number from Twilio and configures it for SMS/voice messaging within the organization. Includes automatic webhook setup and billing integration.

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Organization-ID: <organization_id>
```

## Request Body
```json
{
  "phone_number": "+***********",
  "capabilities": {
    "sms": true,
    "mms": true,
    "voice": false
  },
  "configuration": {
    "webhook_url": "https://api.example.com/webhooks/sms/inbound",
    "status_callback_url": "https://api.example.com/webhooks/sms/status",
    "friendly_name": "NYC Support Line"
  },
  "billing": {
    "cost_center": "marketing",
    "budget_code": "SMS-2024-Q4",
    "auto_recharge": true,
    "spending_limit": 100.00,
    "spending_alert_threshold": 80.00
  },
  "integration_settings": {
    "default_sender_ids": ["SUPPORT", "MYSTORE"],
    "message_routing_rules": [
      {
        "condition": "keyword:STOP",
        "action": "unsubscribe",
        "priority": 1
      },
      {
        "condition": "keyword:HELP", 
        "action": "forward_to_support",
        "priority": 2
      }
    ],
    "business_hours": {
      "enabled": true,
      "timezone": "America/New_York",
      "hours": {
        "monday": {"start": "09:00", "end": "17:00"},
        "tuesday": {"start": "09:00", "end": "17:00"},
        "wednesday": {"start": "09:00", "end": "17:00"},
        "thursday": {"start": "09:00", "end": "17:00"},
        "friday": {"start": "09:00", "end": "17:00"},
        "saturday": null,
        "sunday": null
      }
    }
  },
  "compliance": {
    "opt_in_required": true,
    "double_opt_in": false,
    "carrier_lookup": true,
    "spam_filtering": true
  },
  "metadata": {
    "purchased_for": "Customer support and order notifications",
    "department": "Customer Service",
    "contact_person": "Sarah Johnson"
  }
}
```

## Request Body Schema

### Required Fields
- `phone_number` (string): The phone number to purchase (must be available)

### Optional Fields
- `capabilities` (object): Desired capabilities configuration
- `configuration` (object): Webhook and general configuration
- `billing` (object): Billing and cost management settings
- `integration_settings` (object): Integration with campaigns and routing
- `compliance` (object): Compliance and regulatory settings  
- `metadata` (object): Additional metadata for tracking

### Capabilities Object
```typescript
interface PhoneNumberCapabilities {
  sms?: boolean;        // Enable SMS (default: true)
  mms?: boolean;        // Enable MMS (default: false)  
  voice?: boolean;      // Enable voice calls (default: false)
  fax?: boolean;        // Enable fax (default: false)
}
```

### Configuration Object
```typescript
interface PhoneNumberConfiguration {
  webhook_url?: string;           // Inbound message webhook
  webhook_method?: 'POST' | 'GET'; // Webhook HTTP method (default: POST)
  status_callback_url?: string;   // Delivery status webhook
  status_callback_method?: 'POST' | 'GET'; // Status webhook method (default: POST)
  friendly_name?: string;         // Display name for the number
  voice_url?: string;            // Voice call webhook (if voice enabled)
  voice_method?: 'POST' | 'GET'; // Voice webhook method (default: POST)
  voice_fallback_url?: string;   // Voice fallback webhook
}
```

## Response

### Success Response (201 Created)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "twilio_sid": "PNabc123def456789ghi012jkl",
    "phone_number": "+***********",
    "friendly_name": "NYC Support Line",
    "country_iso_code": "US",
    "locality": "New York",
    "region": "NY",
    "area_code": "212",
    "capabilities": {
      "sms": true,
      "mms": true,
      "voice": false,
      "fax": false
    },
    "address_requirements": "none",
    "beta": false,
    "status": "ACTIVE",
    "configuration": {
      "webhook_url": "https://api.example.com/webhooks/sms/inbound",
      "webhook_method": "POST",
      "status_callback_url": "https://api.example.com/webhooks/sms/status",
      "status_callback_method": "POST",
      "application_sid": null,
      "voice_url": null,
      "voice_method": "POST",
      "account_sid": "ACabc123def456789ghi012jkl345"
    },
    "billing": {
      "monthly_cost": 1.00,
      "setup_cost": 0.00,
      "currency": "USD",
      "cost_center": "marketing",
      "budget_code": "SMS-2024-Q4",
      "purchase_date": "2024-12-16T15:30:00Z",
      "next_billing_date": "2025-01-01T00:00:00Z",
      "auto_recharge": true,
      "spending_limit": 100.00,
      "spending_alert_threshold": 80.00,
      "current_month_spend": 0.00,
      "total_spend_to_date": 0.00
    },
    "integration_settings": {
      "default_sender_ids": ["SUPPORT", "MYSTORE"],
      "message_routing_rules": [
        {
          "rule_id": "rule_001",
          "condition": "keyword:STOP",
          "action": "unsubscribe",
          "priority": 1,
          "created_at": "2024-12-16T15:30:00Z"
        },
        {
          "rule_id": "rule_002",
          "condition": "keyword:HELP",
          "action": "forward_to_support",
          "priority": 2,
          "created_at": "2024-12-16T15:30:00Z"
        }
      ],
      "business_hours": {
        "enabled": true,
        "timezone": "America/New_York",
        "hours": {
          "monday": {"start": "09:00", "end": "17:00"},
          "tuesday": {"start": "09:00", "end": "17:00"},
          "wednesday": {"start": "09:00", "end": "17:00"},
          "thursday": {"start": "09:00", "end": "17:00"},
          "friday": {"start": "09:00", "end": "17:00"},
          "saturday": null,
          "sunday": null
        }
      }
    },
    "compliance": {
      "opt_in_required": true,
      "double_opt_in": false,
      "carrier_lookup": true,
      "spam_filtering": true,
      "regulatory_status": "compliant",
      "opt_out_count": 0,
      "spam_reports": 0,
      "carrier_violations": 0
    },
    "purchase_details": {
      "purchase_time_ms": 2341,
      "twilio_request_id": "RErequest123456789abcdef",
      "verification_status": "verified",
      "activation_status": "active",
      "webhook_setup_status": "configured"
    },
    "usage_stats": {
      "messages_sent": 0,
      "messages_received": 0,
      "voice_calls_made": 0,
      "voice_calls_received": 0,
      "last_activity": null,
      "first_activity": null,
      "total_cost_current_month": 0.00,
      "message_volume_trend": null
    },
    "metadata": {
      "purchased_for": "Customer support and order notifications",
      "department": "Customer Service", 
      "contact_person": "Sarah Johnson"
    },
    "created_by": "user_123456789",
    "created_by_name": "John Doe",
    "created_at": "2024-12-16T15:30:00Z",
    "updated_at": "2024-12-16T15:30:00Z"
  },
  "message": "Phone number purchased and configured successfully",
  "warnings": [
    {
      "code": "BILLING_INFO",
      "message": "Monthly billing will begin on 2025-01-01. Setup cost of $0.00 has been charged."
    }
  ]
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_005",
    "message": "Invalid phone number purchase request",
    "details": [
      {
        "field": "phone_number",
        "message": "Phone number format is invalid"
      },
      {
        "field": "webhook_url",
        "message": "Webhook URL must be a valid HTTPS URL"
      }
    ]
  }
}
```

### 409 Conflict
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_006",
    "message": "Phone number no longer available",
    "details": [
      {
        "field": "phone_number",
        "message": "Phone number +*********** has been purchased by another customer"
      }
    ],
    "suggestions": {
      "alternative_numbers": [
        "+***********",
        "+***********",
        "+***********"
      ],
      "search_url": "/api/v1/phone-numbers/available?country=US&area_code=212&sms_enabled=true"
    }
  }
}
```

### 402 Payment Required
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_007",
    "message": "Insufficient funds for phone number purchase",
    "details": [
      {
        "field": "billing",
        "message": "Account balance ($5.23) insufficient for purchase. Required: $1.00 setup + $1.00 monthly fee."
      }
    ],
    "required_amount": {
      "setup_cost": 1.00,
      "first_month": 1.00,
      "total_required": 2.00,
      "current_balance": 5.23,
      "shortfall": 0.00
    }
  }
}
```

### 422 Unprocessable Entity
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_008",
    "message": "Phone number purchase validation failed",
    "details": [
      {
        "field": "capabilities.voice",
        "message": "Voice capability not available for this number"
      },
      {
        "field": "integration_settings.business_hours",
        "message": "Invalid timezone specified"
      }
    ]
  }
}
```

### 503 Service Unavailable
```json
{
  "success": false,
  "error": {
    "code": "PHONE_NUMBER_009",
    "message": "Twilio service temporarily unavailable",
    "details": [
      {
        "field": "twilio_api",
        "message": "Unable to process phone number purchase. Please try again in a few minutes."
      }
    ],
    "retry_after": 300,
    "support_reference": "TW-ERROR-**********"
  }
}
```

## Implementation Details

### Purchase Workflow
```typescript
interface PhoneNumberPurchaseWorkflow {
  steps: [
    'validate_availability',
    'check_billing',
    'purchase_from_twilio',
    'configure_webhooks',
    'setup_routing_rules',
    'initialize_compliance',
    'save_to_database',
    'send_notifications'
  ];
  rollback_on_failure: boolean;
  async_post_processing: boolean;
}
```

### Validation Rules
1. **Number Availability**: Must verify number is still available before purchase
2. **Billing Validation**: Account must have sufficient funds
3. **Webhook URL Validation**: Must be valid HTTPS URLs accessible by Twilio
4. **Capabilities Validation**: Requested capabilities must be supported by number
5. **Organization Limits**: Check organization phone number limits

### Twilio Integration
```typescript
interface TwilioPurchaseService {
  async purchaseNumber(phoneNumber: string, config: PurchaseConfig): Promise<TwilioNumber> {
    try {
      const purchasedNumber = await this.twilioClient.incomingPhoneNumbers.create({
        phoneNumber: phoneNumber,
        smsUrl: config.webhook_url,
        smsMethod: config.webhook_method,
        statusCallback: config.status_callback_url,
        statusCallbackMethod: config.status_callback_method,
        friendlyName: config.friendly_name,
        voiceUrl: config.voice_url,
        voiceMethod: config.voice_method
      });
      
      return purchasedNumber;
    } catch (error) {
      throw new TwilioPurchaseError(error);
    }
  }
}
```

### Business Logic

#### Automatic Configuration
- Webhooks automatically configured upon purchase
- Default routing rules applied based on capabilities
- Compliance settings initialized with organization defaults
- Billing tracking starts immediately

#### Cost Management
```typescript
interface CostManagement {
  calculatePurchaseCost(number: string): Promise<PurchaseCost>;
  validateBillingAccount(orgId: string, cost: number): Promise<boolean>;
  setupSpendingLimits(phoneNumberId: string, limits: SpendingLimits): Promise<void>;
  initializeBillingTracking(phoneNumber: PhoneNumber): Promise<void>;
}
```

#### Webhook Validation
- All webhook URLs tested before configuration
- Fallback URLs configured for reliability  
- Webhook signatures enabled for security
- Timeout and retry policies applied

### Security Considerations
1. **Webhook Security**: URLs validated and tested before configuration
2. **Access Control**: Only authorized users can purchase numbers
3. **Billing Validation**: Prevent unauthorized purchases
4. **Audit Trail**: All purchases logged with user information
5. **Rate Limiting**: Prevent bulk unauthorized purchases

### Integration Points
- **Billing Service**: Cost tracking and payment processing
- **Campaign Service**: Number assignment to campaigns  
- **Message Service**: Inbound message routing
- **Compliance Service**: Regulatory compliance tracking
- **Notification Service**: Purchase confirmations and alerts

## Usage Examples

### Basic SMS Number Purchase
```json
{
  "phone_number": "+***********",
  "capabilities": {
    "sms": true,
    "mms": false,
    "voice": false
  },
  "configuration": {
    "webhook_url": "https://api.example.com/webhooks/sms/inbound",
    "friendly_name": "SMS Marketing Line"
  }
}
```

### Complete Setup with All Options
```json
{
  "phone_number": "+***********",
  "capabilities": {
    "sms": true,
    "mms": true,
    "voice": true
  },
  "configuration": {
    "webhook_url": "https://api.example.com/webhooks/sms/inbound",
    "status_callback_url": "https://api.example.com/webhooks/sms/status",
    "voice_url": "https://api.example.com/webhooks/voice/inbound",
    "friendly_name": "Customer Service Line"
  },
  "billing": {
    "cost_center": "customer-service",
    "budget_code": "CS-2024-Q4",
    "auto_recharge": true,
    "spending_limit": 500.00,
    "spending_alert_threshold": 400.00
  },
  "integration_settings": {
    "default_sender_ids": ["SUPPORT", "BILLING"],
    "message_routing_rules": [
      {
        "condition": "keyword:URGENT",
        "action": "escalate_to_manager",
        "priority": 1
      },
      {
        "condition": "business_hours:false",
        "action": "send_auto_reply",
        "priority": 2
      }
    ],
    "business_hours": {
      "enabled": true,
      "timezone": "America/Los_Angeles",
      "hours": {
        "monday": {"start": "08:00", "end": "18:00"},
        "tuesday": {"start": "08:00", "end": "18:00"},
        "wednesday": {"start": "08:00", "end": "18:00"},
        "thursday": {"start": "08:00", "end": "18:00"},
        "friday": {"start": "08:00", "end": "18:00"},
        "saturday": {"start": "10:00", "end": "14:00"},
        "sunday": null
      }
    }
  },
  "compliance": {
    "opt_in_required": true,
    "double_opt_in": true,
    "carrier_lookup": true,
    "spam_filtering": true
  },
  "metadata": {
    "purchased_for": "Customer service and billing inquiries",
    "department": "Customer Success",
    "contact_person": "Mike Johnson",
    "project_code": "CS-EXPANSION-2024"
  }
}
```

### Voice-Only Number Purchase
```json
{
  "phone_number": "+***********",
  "capabilities": {
    "sms": false,
    "mms": false,
    "voice": true
  },
  "configuration": {
    "voice_url": "https://api.example.com/webhooks/voice/inbound",
    "voice_fallback_url": "https://api.example.com/webhooks/voice/fallback",
    "friendly_name": "Sales Hotline"
  },
  "billing": {
    "cost_center": "sales",
    "auto_recharge": true,
    "spending_limit": 1000.00
  },
  "metadata": {
    "purchased_for": "Sales lead generation hotline",
    "department": "Sales"
  }
}
```

### International Number Purchase
```json
{
  "phone_number": "+447700900123",
  "capabilities": {
    "sms": true,
    "mms": false,
    "voice": false
  },
  "configuration": {
    "webhook_url": "https://api.example.com/webhooks/sms/inbound",
    "friendly_name": "UK Customer Support"
  },
  "compliance": {
    "opt_in_required": true,
    "carrier_lookup": true,
    "spam_filtering": true
  },
  "metadata": {
    "purchased_for": "UK market expansion",
    "department": "International",
    "region": "EMEA"
  }
}
```