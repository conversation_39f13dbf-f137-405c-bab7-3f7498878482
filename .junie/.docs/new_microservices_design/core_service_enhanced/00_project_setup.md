# Enhanced Core Service - Project Setup Documentation

## Service Overview

**Service Name**: Enhanced ShoutOUT Engage Core Service  
**Purpose**: Extended version of the existing core service with additional functionality for sender ID management, phone number management, contact segmentation, and enhanced contact features  
**Port**: 3001 (Enhanced features on existing service)  
**Database**: MongoDB (extends existing collections)  
**Queue System**: Redis Bull MQ  

## Architecture Enhancement

This service enhances the existing `shoutout_engage_core_service` by adding:

1. **Sender ID Management**: Sender ID registration, approval workflow, and artifact handling
2. **Phone Number Management**: Twilio integration for phone number purchase and management  
3. **Contact Segmentation**: Advanced contact segmentation with real-time calculation
4. **Enhanced Contact Features**: Extended contact management capabilities
5. **Integration Points**: Deep integration with Campaign, Message, and Payment services

## Project Structure

```
shoutout_engage_core_service/ (Enhanced)
├── src/
│   ├── routes/
│   │   ├── contacts.ts                    # Existing contact routes
│   │   ├── sender-ids.ts                  # NEW: Sender ID management
│   │   ├── phone-numbers.ts               # NEW: Phone number management  
│   │   ├── segments.ts                    # NEW: Contact segmentation
│   │   └── sender-artifacts.ts            # NEW: Document management
│   ├── handlers/
│   │   ├── ContactsHandler.ts             # Existing contact handler
│   │   ├── SenderIdHandler.ts             # NEW: Sender ID operations
│   │   ├── PhoneNumberHandler.ts          # NEW: Phone number operations
│   │   ├── SegmentHandler.ts              # NEW: Segment operations
│   │   └── ArtifactHandler.ts             # NEW: File upload handler
│   ├── lib/
│   │   ├── db/
│   │   │   ├── models/
│   │   │   │   ├── contact.model.ts       # Existing contact model
│   │   │   │   ├── sender.id.model.ts     # NEW: Sender ID model
│   │   │   │   ├── phone.number.model.ts  # NEW: Phone number model
│   │   │   │   ├── segment.model.ts       # NEW: Contact segment model
│   │   │   │   └── sender.artifact.model.ts # NEW: Artifact model
│   │   │   ├── dao/
│   │   │   │   ├── ContactDAO.ts          # Existing contact DAO
│   │   │   │   ├── SenderIdDAO.ts         # NEW: Sender ID DAO
│   │   │   │   ├── PhoneNumberDAO.ts      # NEW: Phone number DAO
│   │   │   │   ├── SegmentDAO.ts          # NEW: Segment DAO
│   │   │   │   └── ArtifactDAO.ts         # NEW: Artifact DAO
│   │   │   └── connectors/
│   │   │       ├── mongodb.connector.ts   # Existing MongoDB connector
│   │   │       └── redis.connector.ts     # Existing Redis connector
│   │   ├── services/
│   │   │   ├── twilio.service.ts          # NEW: Twilio integration
│   │   │   ├── file.storage.service.ts    # NEW: S3/file storage
│   │   │   ├── segment.processor.service.ts # NEW: Segment calculations
│   │   │   └── sender.workflow.service.ts  # NEW: Approval workflows
│   │   ├── utils/
│   │   │   ├── file.validator.ts          # NEW: File validation
│   │   │   ├── segment.builder.ts         # NEW: Segment query builder
│   │   │   └── country.utils.ts           # NEW: Country/region utilities
│   │   ├── queues/
│   │   │   ├── processors/
│   │   │   │   ├── segment.calculation.processor.ts # NEW: Async segment processing
│   │   │   │   ├── sender.approval.processor.ts    # NEW: Approval workflows
│   │   │   │   └── file.processing.processor.ts    # NEW: File processing
│   │   │   └── segment.queue.ts           # NEW: Segment queue management
│   │   ├── validators/
│   │   │   ├── ContactsValidator.ts       # Existing contact validation
│   │   │   ├── SenderIdValidator.ts       # NEW: Sender ID validation
│   │   │   ├── PhoneNumberValidator.ts    # NEW: Phone number validation
│   │   │   └── SegmentValidator.ts        # NEW: Segment validation
│   │   └── middlewares/
│   │       ├── auth.middleware.ts         # Existing auth middleware
│   │       ├── file.upload.middleware.ts  # NEW: File upload handling
│   │       └── segment.cache.middleware.ts # NEW: Segment caching
│   ├── types/
│   │   ├── contact.types.ts               # Existing contact types
│   │   ├── sender.id.types.ts             # NEW: Sender ID types
│   │   ├── phone.number.types.ts          # NEW: Phone number types
│   │   ├── segment.types.ts               # NEW: Segment types
│   │   └── file.upload.types.ts           # NEW: File upload types
│   └── config/
│       ├── database.config.ts             # Existing DB config
│       ├── queue.config.ts                # Existing queue config  
│       ├── twilio.config.ts               # NEW: Twilio configuration
│       └── file.storage.config.ts         # NEW: File storage config
├── workers/
│   └── processors/
│       ├── segment.worker.ts              # NEW: Segment calculation worker
│       ├── sender.approval.worker.ts      # NEW: Approval workflow worker
│       └── file.processing.worker.ts      # NEW: File processing worker
├── uploads/                               # NEW: Local file staging
├── tests/
│   ├── unit/
│   │   ├── handlers/
│   │   │   ├── SenderIdHandler.test.ts    # NEW: Sender ID tests
│   │   │   ├── PhoneNumberHandler.test.ts # NEW: Phone number tests
│   │   │   └── SegmentHandler.test.ts     # NEW: Segment tests
│   │   ├── dao/
│   │   │   ├── SenderIdDAO.test.ts        # NEW: Sender ID DAO tests
│   │   │   ├── PhoneNumberDAO.test.ts     # NEW: Phone number DAO tests
│   │   │   └── SegmentDAO.test.ts         # NEW: Segment DAO tests
│   │   └── services/
│   │       ├── twilio.service.test.ts     # NEW: Twilio service tests
│   │       └── segment.processor.test.ts  # NEW: Segment processor tests
│   └── integration/
│       ├── sender.id.integration.test.ts  # NEW: Sender ID integration
│       ├── phone.number.integration.test.ts # NEW: Phone number integration
│       └── segment.integration.test.ts    # NEW: Segment integration
├── package.json                           # Enhanced with new dependencies
├── tsconfig.json                          # TypeScript configuration
├── .env.example                           # Enhanced environment variables
├── docker-compose.yml                     # Enhanced with new services
└── README.md                              # Updated documentation
```

## Enhanced Dependencies

### New Production Dependencies
```json
{
  "dependencies": {
    // Existing dependencies remain...
    "express": "^4.18.0",
    "mongoose": "^7.0.0", 
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "joi": "^17.9.0",
    "winston": "^3.8.0",
    "@supabase/supabase-js": "^2.0.0",
    "bull": "^4.10.0",
    "jsonwebtoken": "^9.0.0",
    "uuid": "^9.0.0",
    "moment": "^2.29.0",
    
    // NEW: Enhanced service dependencies
    "twilio": "^4.19.0",                    // Phone number management
    "aws-sdk": "^2.1450.0",                // S3 file storage
    "multer": "^1.4.5",                    // File upload handling
    "multer-s3": "^3.0.1",                 // S3 multer integration
    "sharp": "^0.32.0",                    // Image processing
    "mime-types": "^2.1.35",               // MIME type detection
    "country-list": "^2.2.0",              // Country utilities
    "libphonenumber-js": "^1.10.44",       // Phone number validation
    "pdf-lib": "^1.17.1",                  // PDF generation
    "archiver": "^5.3.1",                  // File compression
    "csv-writer": "^1.6.0",                // CSV export
    "xlsx": "^0.18.5",                     // Excel export
    "node-cron": "^3.0.2",                 // Scheduled tasks
    "redis": "^4.6.0",                     // Enhanced Redis operations
    "ioredis": "^5.3.0",                   // Advanced Redis features
    "lodash": "^4.17.21"                   // Utility functions
  }
}
```

### New Development Dependencies
```json
{
  "devDependencies": {
    // Existing dev dependencies remain...
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "@types/express": "^4.17.0",
    "@types/mongoose": "^7.0.0",
    "ts-node": "^10.9.0",
    "nodemon": "^3.0.0",
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0",
    "eslint": "^8.0.0",
    "prettier": "^3.0.0",
    
    // NEW: Enhanced development dependencies
    "@types/multer": "^1.4.7",             // File upload types
    "@types/multer-s3": "^3.0.0",          // S3 multer types
    "@types/mime-types": "^2.1.1",         // MIME types
    "@types/archiver": "^5.3.2",           // Archive types
    "@types/csv-writer": "^1.6.2",         // CSV writer types
    "@types/lodash": "^4.14.195",          // Lodash types
    "mongodb-memory-server": "^8.12.0",    // In-memory MongoDB for tests
    "supertest": "^6.3.0",                 // HTTP testing
    "faker": "^6.6.6",                     // Test data generation
    "aws-sdk-mock": "^5.8.0",              // AWS SDK mocking
    "twilio-mock": "^1.0.0"                // Twilio API mocking
  }
}
```

## Environment Variables

### Enhanced .env Configuration
```bash
# Existing environment variables remain...
NODE_ENV=development
PORT=3001
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
MONGODB_URI=mongodb://localhost:27017/shoutout_engage
REDIS_URL=redis://localhost:6379

# NEW: Enhanced service configuration
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
TWILIO_MESSAGING_SERVICE_SID=your_messaging_service_sid
TWILIO_WEBHOOK_URL=https://your-domain.com/webhooks/twilio

# AWS S3 Configuration (for file storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key  
AWS_REGION=us-east-1
AWS_S3_BUCKET=shoutout-engage-artifacts
AWS_S3_SENDER_ARTIFACTS_FOLDER=sender-artifacts
AWS_S3_TEMP_FOLDER=temp-uploads

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx
UPLOAD_TEMP_DIR=./uploads/temp
UPLOAD_PROCESSING_DIR=./uploads/processing

# Segment Processing Configuration
SEGMENT_CALCULATION_BATCH_SIZE=1000
SEGMENT_MAX_CONTACTS=100000
SEGMENT_CACHE_TTL_MINUTES=15
SEGMENT_AUTO_UPDATE_ENABLED=true

# Sender ID Configuration  
SENDER_ID_APPROVAL_WEBHOOK_URL=https://your-domain.com/webhooks/sender-approval
SENDER_ID_MIN_LENGTH=3
SENDER_ID_MAX_LENGTH=11
SENDER_ID_ARTIFACT_RETENTION_DAYS=90

# Phone Number Configuration
PHONE_NUMBER_SEARCH_LIMIT=50
PHONE_NUMBER_PURCHASE_WEBHOOK_URL=https://your-domain.com/webhooks/phone-purchase
PHONE_NUMBER_RELEASE_DELAY_DAYS=7

# Queue Configuration (Enhanced)
QUEUE_SEGMENT_CALCULATION=segment.calculation.queue
QUEUE_SENDER_APPROVAL=sender.approval.queue
QUEUE_FILE_PROCESSING=file.processing.queue
QUEUE_PHONE_NUMBER_WEBHOOK=phone.number.webhook.queue

# External Service URLs (for integration)
CAMPAIGN_SERVICE_URL=http://localhost:3002
MESSAGE_SERVICE_URL=http://localhost:3003
PAYMENT_SERVICE_URL=http://localhost:3004

# Monitoring and Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/enhanced-core-service.log
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true
```

## Database Schema Enhancements

### New MongoDB Collections

#### 1. Sender IDs Collection
```javascript
// Collection: sender_ids
{
  _id: ObjectId,
  org_id: String,
  created_by: String,
  sender_id: String,              // 3-11 alphanumeric characters
  display_name: String,
  transport: String,              // SMS, EMAIL, FACEBOOK_MESSENGER
  status: String,                 // PENDING, APPROVED, REJECTED, SUSPENDED
  registered_countries: [{
    country_iso_code: String,
    country_name: String,
    status: String,
    submitted_at: Date,
    approved_at: Date,
    rejected_at: Date,
    rejected_reason: String
  }],
  metadata: {
    user_type: String,            // BUSINESS, INDIVIDUAL
    company_name: String,
    business_nature: String,
    user_name: String,
    sample_content: String,
    intended_usage: String,
    usage_type: String            // TRANSACTIONAL, PROMOTIONAL, MIXED
  },
  artifacts: [{
    artifact_id: String,
    artifact_type: String,
    file_name: String,
    file_url: String,
    file_size: Number,
    uploaded_at: Date,
    status: String
  }],
  created_at: Date,
  updated_at: Date
}

// Indexes
db.sender_ids.createIndex({ "org_id": 1, "sender_id": 1 }, { unique: true });
db.sender_ids.createIndex({ "org_id": 1, "status": 1 });
db.sender_ids.createIndex({ "org_id": 1, "transport": 1 });
```

#### 2. Phone Numbers Collection  
```javascript
// Collection: phone_numbers
{
  _id: ObjectId,
  org_id: String,
  twilio_sid: String,
  phone_number: String,
  country_iso_code: String,
  capabilities: {
    sms: Boolean,
    voice: Boolean,
    mms: Boolean
  },
  status: String,                 // ACTIVE, RELEASED, PENDING_RELEASE
  configuration: {
    webhook_url: String,
    status_callback_url: String,
    application_sid: String
  },
  usage_stats: {
    messages_sent: Number,
    messages_received: Number,
    voice_calls: Number,
    last_activity: Date
  },
  billing: {
    monthly_cost: Number,
    currency: String,
    purchase_date: Date,
    next_billing_date: Date
  },
  created_by: String,
  created_at: Date,
  updated_at: Date
}

// Indexes
db.phone_numbers.createIndex({ "org_id": 1, "status": 1 });
db.phone_numbers.createIndex({ "twilio_sid": 1 }, { unique: true });
db.phone_numbers.createIndex({ "phone_number": 1 }, { unique: true });
```

#### 3. Contact Segments Collection
```javascript
// Collection: segments  
{
  _id: ObjectId,
  org_id: String,
  name: String,
  description: String,
  criteria: {
    conditions: [{
      field: String,
      operator: String,
      value: Mixed,
      time_period: String
    }],
    logic_operator: String,       // AND, OR
    tags: {
      include: [String],
      exclude: [String]
    },
    contact_properties: Object,
    behavioral_criteria: Object
  },
  filters: {
    exclude_unsubscribed: Boolean,
    exclude_bounced: Boolean,
    exclude_invalid_contacts: Boolean
  },
  auto_update: {
    enabled: Boolean,
    frequency: String,            // daily, weekly, monthly
    time_of_day: String,
    last_updated: Date,
    next_update: Date
  },
  stats: {
    total_contacts: Number,
    status: String,               // CALCULATING, READY, ERROR
    last_calculated: Date,
    calculation_time_ms: Number,
    error_message: String
  },
  contact_ids: [ObjectId],
  metadata: Object,
  tags: [String],
  status: String,                 // ACTIVE, INACTIVE, ARCHIVED
  created_by: String,
  created_at: Date,
  updated_at: Date
}

// Indexes
db.segments.createIndex({ "org_id": 1, "status": 1, "created_at": -1 });
db.segments.createIndex({ "org_id": 1, "tags": 1 });
db.segments.createIndex({ "org_id": 1, "stats.status": 1 });
db.segments.createIndex({ "auto_update.enabled": 1, "auto_update.next_update": 1 });
```

## Queue Configuration

### New Queue Definitions
```typescript
// Enhanced queue configuration
export const ENHANCED_CORE_QUEUES = {
  // Segment Processing
  SEGMENT_CALCULATION: 'segment.calculation.queue',
  SEGMENT_AUTO_UPDATE: 'segment.auto.update.queue',
  
  // Sender ID Workflows
  SENDER_APPROVAL_WORKFLOW: 'sender.approval.workflow.queue', 
  SENDER_DOCUMENT_PROCESSING: 'sender.document.processing.queue',
  
  // File Processing
  FILE_UPLOAD_PROCESSING: 'file.upload.processing.queue',
  FILE_VIRUS_SCAN: 'file.virus.scan.queue',
  FILE_CLEANUP: 'file.cleanup.queue',
  
  // Phone Number Management
  PHONE_NUMBER_WEBHOOK: 'phone.number.webhook.queue',
  PHONE_NUMBER_BILLING_UPDATE: 'phone.number.billing.update.queue',
  
  // Integration Queues
  CAMPAIGN_INTEGRATION: 'campaign.integration.queue',
  MESSAGE_INTEGRATION: 'message.integration.queue',
  PAYMENT_INTEGRATION: 'payment.integration.queue'
} as const;
```

## API Endpoint Structure

### New API Routes

#### Sender ID Management
```
POST   /api/v1/sender-ids              # Create sender ID request
GET    /api/v1/sender-ids              # List sender IDs
GET    /api/v1/sender-ids/{id}         # Get sender ID details
PUT    /api/v1/sender-ids/{id}         # Update sender ID
DELETE /api/v1/sender-ids/{id}         # Delete sender ID request

POST   /api/v1/sender-ids/{id}/artifacts    # Upload artifact
GET    /api/v1/sender-ids/{id}/artifacts    # List artifacts  
DELETE /api/v1/sender-ids/{id}/artifacts/{artifact_id}  # Delete artifact
```

#### Phone Number Management
```
GET    /api/v1/phone-numbers/available      # Search available numbers
POST   /api/v1/phone-numbers               # Purchase phone number
GET    /api/v1/phone-numbers               # List owned numbers
GET    /api/v1/phone-numbers/{id}          # Get number details
PUT    /api/v1/phone-numbers/{id}          # Update number configuration  
DELETE /api/v1/phone-numbers/{id}          # Release phone number
```

#### Contact Segmentation
```
POST   /api/v1/segments                    # Create segment
GET    /api/v1/segments                    # List segments
GET    /api/v1/segments/{id}               # Get segment details
PUT    /api/v1/segments/{id}               # Update segment
DELETE /api/v1/segments/{id}               # Delete segment
GET    /api/v1/segments/{id}/contacts      # Get segment contacts
POST   /api/v1/segments/{id}/recalculate   # Trigger recalculation
```

## Docker Configuration

### Enhanced docker-compose.yml
```yaml
version: '3.8'
services:
  enhanced-core-service:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb:27017/shoutout_engage
      - REDIS_URL=redis://redis:6379
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - mongodb
      - redis
      - localstack
    networks:
      - shoutout-engage-network

  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - shoutout-engage-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - shoutout-engage-network

  localstack:
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3
      - DEFAULT_REGION=us-east-1
    volumes:
      - localstack_data:/tmp/localstack
    networks:
      - shoutout-engage-network

volumes:
  mongodb_data:
  localstack_data:

networks:
  shoutout-engage-network:
    external: true
```

## Development Setup Instructions

### 1. Prerequisites
```bash
# Install Node.js 18+ and npm
node --version  # Should be 18+
npm --version   # Should be 9+

# Install MongoDB (if running locally)
# Install Redis (if running locally)
```

### 2. Project Setup
```bash
# Navigate to existing core service directory
cd shoutout_engage_core_service

# Install new dependencies
npm install twilio aws-sdk multer multer-s3 sharp mime-types country-list
npm install libphonenumber-js pdf-lib archiver csv-writer xlsx node-cron
npm install ioredis lodash

# Install new dev dependencies
npm install -D @types/multer @types/multer-s3 @types/mime-types @types/archiver
npm install -D @types/csv-writer @types/lodash mongodb-memory-server supertest
npm install -D faker aws-sdk-mock twilio-mock

# Copy enhanced environment variables
cp .env.example .env
# Update .env with your enhanced configuration values
```

### 3. Database Setup
```bash
# Run MongoDB migrations for new collections
npm run migrate:enhanced

# Create database indexes
npm run db:index

# Seed test data (optional)
npm run db:seed:enhanced
```

### 4. Development Commands
```bash
# Start development server with enhanced features
npm run dev:enhanced

# Run tests for enhanced functionality
npm run test:enhanced

# Run specific test suites
npm run test:sender-ids
npm run test:phone-numbers  
npm run test:segments

# Build enhanced service
npm run build:enhanced

# Start production server
npm run start:enhanced
```

## Integration Testing

### Test Scenarios
1. **Sender ID Workflow**: Complete sender ID registration and approval process
2. **Phone Number Management**: Number purchase, configuration, and release
3. **Segment Processing**: Complex segment creation and contact calculation
4. **File Upload Processing**: Document upload and virus scanning
5. **Cross-Service Integration**: Integration with Campaign and Message services

### Performance Testing
- Segment calculation with 100K+ contacts
- Concurrent file uploads
- High-frequency phone number webhooks
- Real-time segment updates

## Monitoring and Logging

### Enhanced Logging Categories
- **Sender ID Operations**: Registration, approval, and artifact management
- **Phone Number Events**: Purchase, configuration changes, and webhooks  
- **Segment Processing**: Calculation performance and errors
- **File Operations**: Upload, processing, and cleanup events
- **Integration Events**: Cross-service communication logs

### Metrics to Monitor
- Segment calculation performance
- File upload success rates  
- Twilio API response times
- Queue processing latency
- AWS S3 upload/download metrics

This enhanced core service builds upon the existing foundation while adding powerful new capabilities for sender ID management, phone number operations, and advanced contact segmentation.