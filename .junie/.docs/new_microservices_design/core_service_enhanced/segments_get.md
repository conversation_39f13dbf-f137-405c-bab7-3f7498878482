# GET /api/v1/segments - List and Retrieve Contact Segments

## Endpoint Overview
**Method**: GET  
**URL**: `/api/v1/segments` (list) or `/api/v1/segments/{id}` (single)  
**Service**: Enhanced Core Service  
**Authentication**: Required (Supabase JWT)  
**Rate Limit**: 200 requests per minute

## Purpose
Retrieves contact segments with filtering, pagination, and detailed segment information including contact counts and status.

## Endpoints

### 1. List Segments
**URL**: `GET /api/v1/segments`

#### Request Headers
```
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

#### Query Parameters
```
page=1                    # Page number (default: 1)
page_size=20             # Items per page (default: 20, max: 100)
search=high-value        # Search in name and description
status=ACTIVE            # Filter by status: ACTIVE, INACTIVE, ARCHIVED
tags=retention,premium   # Filter by tags (comma-separated)
sort_by=created_at       # Sort field: name, created_at, updated_at, contact_count
sort_order=desc          # Sort direction: asc, desc
created_by=user_id       # Filter by creator
calculation_status=READY # Filter by calculation status: CALCULATING, READY, ERROR
auto_update=true         # Filter by auto-update enabled
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "segments": [
      {
        "_id": "64a1b2c3d4e5f6789012345",
        "org_id": "org_123456789",
        "name": "High-Value Customers",
        "description": "Customers with purchase history > $1000 in last 6 months",
        "criteria": {
          "conditions": [
            {
              "field": "total_purchase_amount",
              "operator": "greater_than",
              "value": 1000,
              "time_period": "6_months"
            }
          ],
          "logic_operator": "AND",
          "tags": {
            "include": ["premium", "active"],
            "exclude": ["unsubscribed", "bounced"]
          }
        },
        "stats": {
          "total_contacts": 2547,
          "status": "READY",
          "last_calculated": "2024-12-12T10:30:00Z",
          "calculation_time_ms": 3245
        },
        "auto_update": {
          "enabled": true,
          "frequency": "daily",
          "time_of_day": "02:00",
          "last_updated": "2024-12-12T02:00:00Z",
          "next_update": "2024-12-13T02:00:00Z"
        },
        "tags": ["high-value", "retention", "q4-2024"],
        "status": "ACTIVE",
        "created_by": "<EMAIL>",
        "created_at": "2024-12-10T14:20:00Z",
        "updated_at": "2024-12-12T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 20,
      "total_pages": 3,
      "total_count": 45,
      "has_next": true,
      "has_previous": false
    },
    "summary": {
      "total_segments": 45,
      "active_segments": 38,
      "calculating_segments": 2,
      "total_segment_contacts": 125430,
      "segments_with_auto_update": 35
    }
  },
  "message": "Segments retrieved successfully"
}
```

### 2. Get Single Segment
**URL**: `GET /api/v1/segments/{segment_id}`

#### Request Headers
```
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
```

#### Path Parameters
- `segment_id` (string): MongoDB ObjectId of the segment

#### Query Parameters
```
include_contacts=false   # Include contact details (default: false)
contact_limit=100       # Max contacts to return if include_contacts=true
contact_fields=name,email # Specific contact fields to include
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "org_id": "org_123456789",
    "name": "High-Value Customers",
    "description": "Customers with purchase history > $1000 in last 6 months",
    "criteria": {
      "conditions": [
        {
          "field": "total_purchase_amount",
          "operator": "greater_than",
          "value": 1000,
          "time_period": "6_months"
        },
        {
          "field": "last_activity_date",
          "operator": "within_days",
          "value": 90
        }
      ],
      "logic_operator": "AND",
      "tags": {
        "include": ["premium", "active"],
        "exclude": ["unsubscribed", "bounced"]
      },
      "contact_properties": {
        "location": {
          "countries": ["US", "CA", "GB"]
        },
        "demographics": {
          "age_range": {
            "min": 25,
            "max": 55
          }
        }
      },
      "behavioral_criteria": {
        "last_purchase_days": 180,
        "minimum_purchases": 2,
        "engagement_level": "high"
      }
    },
    "filters": {
      "exclude_unsubscribed": true,
      "exclude_bounced": true,
      "exclude_invalid_contacts": true,
      "include_test_contacts": false
    },
    "auto_update": {
      "enabled": true,
      "frequency": "daily",
      "time_of_day": "02:00",
      "last_updated": "2024-12-12T02:00:00Z",
      "next_update": "2024-12-13T02:00:00Z"
    },
    "stats": {
      "total_contacts": 2547,
      "status": "READY",
      "last_calculated": "2024-12-12T10:30:00Z",
      "calculation_time_ms": 3245,
      "breakdown": {
        "by_location": {
          "US": 1823,
          "CA": 456,
          "GB": 268
        },
        "by_engagement": {
          "high": 2547,
          "medium": 0,
          "low": 0
        }
      }
    },
    "usage_stats": {
      "campaigns_used_in": 12,
      "last_used_in_campaign": "2024-12-11T16:45:00Z",
      "total_messages_sent": 34512,
      "average_campaign_performance": {
        "delivery_rate": 98.5,
        "open_rate": 24.3,
        "click_rate": 3.2
      }
    },
    "contacts": {
      "sample": [
        {
          "_id": "contact_123",
          "name": "John Smith",
          "email": "<EMAIL>",
          "total_purchase_amount": 1250.00,
          "last_activity_date": "2024-12-10T09:15:00Z"
        }
      ],
      "total_included": 100,
      "note": "Showing first 100 contacts. Use include_contacts=true&contact_limit=N for more."
    },
    "metadata": {
      "created_by": "<EMAIL>",
      "department": "marketing",
      "campaign_purpose": "retention"
    },
    "tags": ["high-value", "retention", "q4-2024"],
    "status": "ACTIVE",
    "created_by": "<EMAIL>",
    "created_at": "2024-12-10T14:20:00Z",
    "updated_at": "2024-12-12T10:30:00Z"
  },
  "message": "Segment details retrieved successfully"
}
```

### 3. Get Segment Contacts
**URL**: `GET /api/v1/segments/{segment_id}/contacts`

#### Query Parameters
```
page=1                          # Page number
page_size=50                   # Items per page (max: 500)
fields=name,email,phone        # Specific fields to return
sort_by=name                   # Sort by contact field
sort_order=asc                 # Sort direction
search=john                    # Search within segment contacts
filters={"location":"US"}      # Additional contact filters (JSON)
export_format=csv              # Export format: json, csv, xlsx
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "segment_id": "64a1b2c3d4e5f6789012345",
    "segment_name": "High-Value Customers",
    "contacts": [
      {
        "_id": "contact_123",
        "name": "John Smith",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "total_purchase_amount": 1250.00,
        "last_activity_date": "2024-12-10T09:15:00Z",
        "location": {
          "country": "US",
          "state": "NY",
          "city": "New York"
        },
        "tags": ["premium", "active"],
        "engagement_level": "high"
      }
    ],
    "pagination": {
      "current_page": 1,
      "page_size": 50,
      "total_pages": 52,
      "total_count": 2547,
      "has_next": true,
      "has_previous": false
    },
    "segment_stats": {
      "last_calculated": "2024-12-12T10:30:00Z",
      "calculation_status": "READY"
    }
  },
  "message": "Segment contacts retrieved successfully"
}
```

## Error Responses

### 404 Not Found
```json
{
  "success": false,
  "error": {
    "code": "SEGMENT_004",
    "message": "Segment not found",
    "details": [
      {
        "field": "segment_id",
        "message": "No segment found with ID '64a1b2c3d4e5f6789012345'"
      }
    ]
  }
}
```

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": "SEGMENT_005",
    "message": "Invalid query parameters",
    "details": [
      {
        "field": "page_size",
        "message": "Page size must be between 1 and 100"
      },
      {
        "field": "sort_by",
        "message": "Invalid sort field. Allowed: name, created_at, updated_at, contact_count"
      }
    ]
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": {
    "code": "SEGMENT_006",
    "message": "Access denied to segment",
    "details": [
      {
        "field": "org_id",
        "message": "Segment belongs to a different organization"
      }
    ]
  }
}
```

## Implementation Details

### Database Queries

#### List Segments Query
```javascript
// MongoDB aggregation pipeline for listing segments
[
  {
    $match: {
      org_id: organizationId,
      status: { $in: ['ACTIVE', 'INACTIVE'] }
    }
  },
  {
    $lookup: {
      from: 'contacts',
      localField: 'contact_ids',
      foreignField: '_id',
      as: 'contact_details',
      pipeline: [{ $count: 'count' }]
    }
  },
  {
    $addFields: {
      'stats.total_contacts': { $arrayElemAt: ['$contact_details.count', 0] }
    }
  },
  {
    $sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
  },
  {
    $skip: (page - 1) * pageSize
  },
  {
    $limit: pageSize
  }
]
```

#### Single Segment with Contacts
```javascript
// Efficient contact sampling for large segments
[
  {
    $match: { _id: ObjectId(segmentId), org_id: organizationId }
  },
  {
    $lookup: {
      from: 'contacts',
      let: { contactIds: '$contact_ids' },
      pipeline: [
        { $match: { $expr: { $in: ['$_id', '$$contactIds'] } } },
        { $sample: { size: contactLimit } },
        { $project: contactProjection }
      ],
      as: 'contacts'
    }
  }
]
```

### Performance Optimizations

#### Indexing Strategy
```javascript
// Required indexes for optimal performance
db.segments.createIndex({ "org_id": 1, "status": 1, "created_at": -1 });
db.segments.createIndex({ "org_id": 1, "tags": 1 });
db.segments.createIndex({ "org_id": 1, "stats.status": 1 });
db.segments.createIndex({ "org_id": 1, "created_by": 1 });
db.segments.createIndex({ "auto_update.enabled": 1, "auto_update.next_update": 1 });
```

#### Caching Strategy
- Segment metadata cached for 5 minutes
- Contact counts cached for 15 minutes
- Full contact lists cached for 1 minute (for pagination)
- Auto-update schedules cached until next execution

### Business Logic

#### Access Control
1. **Organization Isolation**: Users can only access segments within their organization
2. **Creator Access**: Segment creators have full access to their segments
3. **Role-Based Access**: Admin users can access all segments in their organization

#### Performance Considerations
1. **Large Segments**: Segments with >10,000 contacts use sampling for preview
2. **Real-time Updates**: Contact counts are updated asynchronously
3. **Export Limits**: CSV/Excel exports limited to 50,000 contacts
4. **Rate Limiting**: Higher limits for segment contact endpoints

### Integration Points

#### Campaign Service Integration
```json
{
  "endpoint": "/api/v1/campaigns",
  "usage": "Reference segments by ID in campaign targeting",
  "example": {
    "target_config": {
      "type": "SEGMENTS",
      "segment_ids": ["64a1b2c3d4e5f6789012345"]
    }
  }
}
```

#### Analytics Integration
```json
{
  "endpoint": "/api/v1/segments/{id}/analytics",
  "metrics": [
    "campaign_usage",
    "performance_trends",
    "contact_growth",
    "engagement_rates"
  ]
}
```

## Usage Examples

### Basic Segment List
```bash
GET /api/v1/segments?page=1&page_size=10&status=ACTIVE
```

### Search and Filter
```bash
GET /api/v1/segments?search=premium&tags=retention,high-value&sort_by=contact_count&sort_order=desc
```

### Get Segment with Contacts
```bash
GET /api/v1/segments/64a1b2c3d4e5f6789012345?include_contacts=true&contact_limit=50&contact_fields=name,email,phone
```

### Export Segment Contacts
```bash
GET /api/v1/segments/64a1b2c3d4e5f6789012345/contacts?export_format=csv&fields=name,email,phone,total_purchase_amount
```

### Filter by Calculation Status
```bash
GET /api/v1/segments?calculation_status=CALCULATING&sort_by=created_at
```