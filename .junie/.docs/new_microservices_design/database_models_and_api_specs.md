# Database Models and API Specifications

## MongoDB Database Models

### Campaign Service Models

#### 1. Campaign Model
```typescript
interface Campaign {
  _id: ObjectId;
  org_id: string;
  created_by: string;
  name: string;
  description?: string;
  type: 'BROADCAST' | 'TRIGGERED' | 'DRIP';
  status: 'DRAFT' | 'BUILDING' | 'SCHEDULED' | 'SENDING' | 'COMPLETED' | 'CANCELLED' | 'FAILED';
  
  // Targeting
  target_type: 'CONTACTS' | 'SEGMENTS' | 'ALL';
  contact_ids?: ObjectId[];
  segment_ids?: ObjectId[];
  
  // Message Content
  channels: {
    sms?: {
      enabled: boolean;
      sender_id: string;
      content: string;
      template_variables?: Record<string, any>;
    };
    email?: {
      enabled: boolean;
      sender_email: string;
      subject: string;
      content: string;
      html_content?: string;
      template_variables?: Record<string, any>;
    };
    push?: {
      enabled: boolean;
      title: string;
      content: string;
      template_variables?: Record<string, any>;
    };
  };
  
  // Scheduling
  is_scheduled: boolean;
  scheduled_at?: Date;
  timezone?: string;
  
  // Analytics & Reporting
  stats: {
    total_recipients: number;
    messages_sent: number;
    messages_delivered: number;
    messages_failed: number;
    open_rate?: number;
    click_rate?: number;
    unsubscribe_rate?: number;
  };
  
  // Cost tracking
  estimated_cost: {
    sms: number;
    email: number;
    push: number;
    total: number;
    currency: string;
  };
  
  // Metadata
  tags?: string[];
  metadata?: Record<string, any>;
  
  created_at: Date;
  updated_at: Date;
  started_at?: Date;
  completed_at?: Date;
}
```

#### 2. Campaign Segment Model
```typescript
interface CampaignSegment {
  _id: ObjectId;
  org_id: string;
  created_by: string;
  name: string;
  description?: string;
  
  // Segment Criteria
  criteria: {
    conditions: Array<{
      field: string; // contact field name
      operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than' | 'in' | 'not_in';
      value: any;
      logic?: 'AND' | 'OR';
    }>;
    tags?: {
      include?: string[];
      exclude?: string[];
    };
    date_filters?: {
      created_after?: Date;
      created_before?: Date;
      last_activity_after?: Date;
      last_activity_before?: Date;
    };
  };
  
  // Cached Results
  contact_count: number;
  last_calculated_at?: Date;
  
  // Status
  status: 'ACTIVE' | 'INACTIVE';
  
  created_at: Date;
  updated_at: Date;
}
```

#### 3. Campaign Log Model
```typescript
interface CampaignLog {
  _id: ObjectId;
  campaign_id: ObjectId;
  org_id: string;
  contact_id: ObjectId;
  
  // Message Details
  channel: 'SMS' | 'EMAIL' | 'PUSH';
  sender: string;
  recipient: string;
  content: string;
  subject?: string; // for email
  
  // Status Tracking
  status: 'QUEUED' | 'SENT' | 'DELIVERED' | 'FAILED' | 'BOUNCED' | 'OPENED' | 'CLICKED' | 'UNSUBSCRIBED';
  provider: string;
  provider_message_id?: string;
  
  // Timestamps
  queued_at: Date;
  sent_at?: Date;
  delivered_at?: Date;
  opened_at?: Date;
  clicked_at?: Date;
  failed_at?: Date;
  
  // Error Information
  error_code?: string;
  error_message?: string;
  
  // Cost
  cost: number;
  currency: string;
  
  // Metadata
  metadata?: Record<string, any>;
  
  created_at: Date;
  updated_at: Date;
}
```

### Message Service Models

#### 1. Message Log Model
```typescript
interface MessageLog {
  _id: ObjectId;
  org_id: string;
  
  // Message Details
  message_id: string; // unique identifier
  transport: 'SMS' | 'EMAIL' | 'PUSH' | 'FACEBOOK_MESSENGER';
  from: string;
  to: string;
  content: string;
  subject?: string;
  
  // Campaign Association
  campaign_id?: ObjectId;
  is_campaign_message: boolean;
  
  // Provider Information
  provider: string;
  provider_message_id?: string;
  provider_response?: Record<string, any>;
  
  // Status Tracking
  status: 'QUEUED' | 'SENT' | 'DELIVERED' | 'FAILED' | 'BOUNCED';
  status_history: Array<{
    status: string;
    timestamp: Date;
    details?: string;
  }>;
  
  // Delivery Information
  queued_at: Date;
  sent_at?: Date;
  delivered_at?: Date;
  failed_at?: Date;
  
  // Error Information
  error_code?: string;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  
  // Cost Information
  cost: number;
  currency: string;
  
  // Metadata
  metadata?: Record<string, any>;
  
  created_at: Date;
  updated_at: Date;
}
```

#### 2. Provider Configuration Model
```typescript
interface ProviderConfiguration {
  _id: ObjectId;
  org_id: string;
  
  // Provider Details
  provider_name: string;
  provider_type: 'SMS' | 'EMAIL' | 'PUSH' | 'FACEBOOK_MESSENGER';
  is_active: boolean;
  priority: number; // for failover ordering
  
  // Configuration
  config: {
    api_key?: string;
    api_secret?: string;
    endpoint_url?: string;
    sender_id?: string;
    webhook_url?: string;
    additional_params?: Record<string, any>;
  };
  
  // Rate Limiting
  rate_limits: {
    requests_per_second: number;
    requests_per_minute: number;
    requests_per_hour: number;
    requests_per_day: number;
  };
  
  // Health Monitoring
  health_status: 'HEALTHY' | 'DEGRADED' | 'UNHEALTHY';
  last_health_check: Date;
  error_rate: number;
  
  // Cost Information
  cost_per_message: number;
  currency: string;
  
  created_at: Date;
  updated_at: Date;
}
```

### Payment Service Models

#### 1. Subscription Model
```typescript
interface Subscription {
  _id: ObjectId;
  org_id: string;
  user_id: string;
  
  // Subscription Details
  subscription_id: string; // Stripe subscription ID
  plan_id: string;
  plan_name: string;
  
  // Status
  status: 'ACTIVE' | 'CANCELLED' | 'PAST_DUE' | 'UNPAID' | 'INCOMPLETE';
  
  // Billing Information
  billing_cycle: 'MONTHLY' | 'YEARLY';
  amount: number;
  currency: string;
  
  // Payment Method
  payment_method_id: string;
  payment_method_type: 'CARD' | 'BANK_TRANSFER';
  
  // Billing Dates
  current_period_start: Date;
  current_period_end: Date;
  next_billing_date: Date;
  
  // Trial Information
  trial_start?: Date;
  trial_end?: Date;
  is_trial: boolean;
  
  // Usage Tracking
  usage_limits: {
    sms_messages: number;
    email_messages: number;
    contacts: number;
    campaigns: number;
  };
  
  usage_current: {
    sms_messages: number;
    email_messages: number;
    contacts: number;
    campaigns: number;
  };
  
  // Metadata
  metadata?: Record<string, any>;
  
  created_at: Date;
  updated_at: Date;
  cancelled_at?: Date;
}
```

#### 2. Invoice Model
```typescript
interface Invoice {
  _id: ObjectId;
  org_id: string;
  user_id: string;
  
  // Invoice Details
  invoice_id: string; // Stripe invoice ID
  invoice_number: string;
  subscription_id?: ObjectId;
  
  // Status
  status: 'DRAFT' | 'OPEN' | 'PAID' | 'VOID' | 'UNCOLLECTIBLE';
  
  // Amounts
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  
  // Line Items
  line_items: Array<{
    description: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    period_start?: Date;
    period_end?: Date;
  }>;
  
  // Dates
  invoice_date: Date;
  due_date: Date;
  paid_date?: Date;
  
  // Payment Information
  payment_method?: string;
  payment_intent_id?: string;
  
  // PDF Information
  pdf_url?: string;
  
  created_at: Date;
  updated_at: Date;
}
```

### Enhanced Core Service Models

#### 1. Sender ID Model
```typescript
interface SenderId {
  _id: ObjectId;
  org_id: string;
  created_by: string;
  
  // Sender Details
  sender_id: string;
  display_name?: string;
  transport: 'SMS' | 'EMAIL' | 'FACEBOOK_MESSENGER';
  
  // Status
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SUSPENDED';
  
  // Registration Information
  registered_countries: Array<{
    country_iso_code: string;
    country_name: string;
    status: 'PENDING' | 'APPROVED' | 'REJECTED';
    approved_at?: Date;
    rejected_reason?: string;
  }>;
  
  // Metadata
  metadata: {
    user_type: 'BUSINESS' | 'INDIVIDUAL';
    company_name?: string;
    business_nature?: string;
    user_name: string;
    user_designation?: string;
    user_identification_number?: string;
    sample_content: string;
    enable_international_usage: boolean;
    usage: 'TRANSACTIONAL' | 'PROMOTIONAL';
    country_iso_codes: string[];
  };
  
  // Artifacts
  artifacts: Array<{
    artifact_id: string;
    artifact_type: 'SENDER_ID_AGREEMENT' | 'BUSINESS_REGISTRATION_COPY' | 'PERSONAL_ID_COPY';
    file_url: string;
    uploaded_at: Date;
    status: 'PENDING' | 'APPROVED' | 'REJECTED';
  }>;
  
  created_at: Date;
  updated_at: Date;
  approved_at?: Date;
  rejected_at?: Date;
}
```

#### 2. Phone Number Model
```typescript
interface PhoneNumber {
  _id: ObjectId;
  org_id: string;
  
  // Phone Number Details
  phone_number: string; // E.164 format
  provider_id: string;
  provider_phone_id: string;
  
  // Capabilities
  services: Array<'SMS' | 'VOICE'>;
  type: 'LOCAL' | 'TOLL_FREE';
  
  // Location
  country_iso: string;
  country_name: string;
  
  // Costs
  monthly_cost: number;
  activation_cost: number;
  currency: string;
  
  service_costs: {
    inbound: {
      sms?: number;
      voice?: number;
    };
    outbound: {
      sms?: number;
      voice?: number;
    };
  };
  
  // Status
  status: 'ACTIVE' | 'SUSPENDED' | 'RELEASED';
  
  // Usage Statistics
  usage_stats: {
    inbound_sms: number;
    outbound_sms: number;
    inbound_voice: number;
    outbound_voice: number;
    last_used: Date;
  };
  
  created_at: Date;
  updated_at: Date;
  released_at?: Date;
}
```

## API Endpoint Specifications

### Campaign Service API

#### Create Campaign
```http
POST /api/v1/campaigns
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "name": "Summer Sale Campaign",
  "description": "Promotional campaign for summer sale",
  "type": "BROADCAST",
  "target_type": "SEGMENTS",
  "segment_ids": ["64a1b2c3d4e5f6789012345"],
  "channels": {
    "sms": {
      "enabled": true,
      "sender_id": "MYSTORE",
      "content": "Hi {{name}}, don't miss our summer sale! Get 20% off. Shop now: {{link}}"
    },
    "email": {
      "enabled": true,
      "sender_email": "<EMAIL>",
      "subject": "Summer Sale - 20% Off Everything!",
      "content": "Dear {{name}}, enjoy our summer sale...",
      "html_content": "<html>...</html>"
    }
  },
  "is_scheduled": true,
  "scheduled_at": "2024-08-15T10:00:00Z",
  "timezone": "UTC"
}

Response (201 Created):
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012346",
    "name": "Summer Sale Campaign",
    "status": "DRAFT",
    "estimated_cost": {
      "sms": 25.50,
      "email": 5.00,
      "total": 30.50,
      "currency": "USD"
    },
    "created_at": "2024-08-11T15:30:00Z"
  }
}
```

#### Build Campaign
```http
POST /api/v1/campaigns/64a1b2c3d4e5f6789012346/build
Authorization: Bearer <jwt_token>

Response (200 OK):
{
  "success": true,
  "data": {
    "job_id": "build_job_123",
    "status": "BUILDING",
    "estimated_recipients": 1250,
    "estimated_completion": "2024-08-11T15:35:00Z"
  }
}
```

### Message Service API

#### Send Single Message
```http
POST /api/v1/messages
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "transport": "SMS",
  "provider": "TWILIO",
  "from": "MYSTORE",
  "to": "+94771234567",
  "content": "Your order #12345 is ready for pickup!",
  "campaign_id": "64a1b2c3d4e5f6789012346"
}

Response (201 Created):
{
  "success": true,
  "data": {
    "message_id": "msg_64a1b2c3d4e5f6789012347",
    "status": "QUEUED",
    "estimated_delivery": "2024-08-11T15:31:00Z",
    "cost": 0.05,
    "currency": "USD"
  }
}
```

### Payment Service API

#### Create Subscription
```http
POST /api/v1/subscriptions
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "plan_id": "starter_monthly",
  "payment_method_id": "pm_1234567890",
  "billing_cycle": "MONTHLY"
}

Response (201 Created):
{
  "success": true,
  "data": {
    "subscription_id": "sub_1234567890",
    "status": "ACTIVE",
    "current_period_end": "2024-09-11T15:30:00Z",
    "next_billing_date": "2024-09-11T15:30:00Z",
    "amount": 29.99,
    "currency": "USD"
  }
}
```

### Enhanced Core Service API

#### Create Sender ID
```http
POST /api/v1/senders
Content-Type: application/json
Authorization: Bearer <jwt_token>

{
  "sender_id": "MYSTORE",
  "transport": "SMS",
  "metadata": {
    "user_type": "BUSINESS",
    "company_name": "My Store Ltd",
    "business_nature": "Retail",
    "user_name": "John Doe",
    "user_designation": "Marketing Manager",
    "sample_content": "Your order is ready for pickup",
    "enable_international_usage": true,
    "country_iso_codes": ["LK", "IN"],
    "usage": "TRANSACTIONAL"
  }
}

Response (201 Created):
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012348",
    "sender_id": "MYSTORE",
    "status": "PENDING",
    "registered_countries": [
      {
        "country_iso_code": "LK",
        "country_name": "Sri Lanka",
        "status": "PENDING"
      },
      {
        "country_iso_code": "IN",
        "country_name": "India",
        "status": "PENDING"
      }
    ],
    "created_at": "2024-08-11T15:30:00Z"
  }
}
```

This comprehensive database and API specification provides the foundation for implementing all four microservices with consistent patterns and proper data modeling.
