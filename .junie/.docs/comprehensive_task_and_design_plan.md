# Comprehensive Task and Design Plan - ShoutOut Engage Microservices

## Executive Summary

This document provides a detailed task breakdown and design plan for developing four new microservices and enhancing the existing core service. The implementation follows the established patterns from `shoutout_engage_core_service` and integrates campaign management, message processing, payment handling, and sender ID functionality.

## Project Structure Overview

```
shoutout_engage_ecosystem/
├── shoutout_engage_core_service/          # Existing (Enhanced)
├── shoutout_engage_campaign_service/      # New - Campaign Management
├── shoutout_engage_message_service/       # New - Message Processing
├── shoutout_engage_payment_service/       # New - Payment & Billing
└── shared/                               # Shared utilities and types
    ├── types/
    ├── constants/
    ├── utils/
    └── queue-configs/
```

## Detailed Task Breakdown

### Phase 1: Foundation and Infrastructure (Weeks 1-2)

#### Task 1.1: Project Structure Setup
**Duration**: 3 days
**Priority**: High
**Dependencies**: None

**Subtasks**:
1. **Campaign Service Setup**
   ```bash
   # Create project structure
   mkdir shoutout_engage_campaign_service
   cd shoutout_engage_campaign_service
   
   # Initialize package.json with specific configuration
   npm init -y
   
   # Install core dependencies
   npm install express@^4.18.0 mongoose@^7.0.0 cors@^2.8.5
   npm install helmet@^7.0.0 joi@^17.9.0 winston@^3.8.0
   npm install @supabase/supabase-js@^2.0.0 bull@^4.10.0
   npm install jsonwebtoken@^9.0.0 uuid@^9.0.0 moment@^2.29.0
   
   # Install dev dependencies
   npm install -D typescript@^5.0.0 @types/node@^20.0.0
   npm install -D @types/express@^4.17.0 @types/mongoose@^7.0.0
   npm install -D ts-node@^10.9.0 nodemon@^3.0.0
   npm install -D jest@^29.0.0 @types/jest@^29.0.0
   npm install -D eslint@^8.0.0 prettier@^3.0.0
   
   # Create folder structure
   mkdir -p src/{routes,controllers,services,middleware,types,config}
   mkdir -p src/lib/{db/{models,dao,connectors},constant,errors,utils}
   mkdir -p workers/{processors} queues tests/{unit,integration}
   mkdir -p uploads logs
   ```

2. **Message Service Setup**
   - Similar structure with message-specific dependencies
   - Add provider-specific packages: `twilio`, `@sendgrid/mail`, `node-pushnotifications`

3. **Payment Service Setup**
   - Similar structure with payment-specific dependencies
   - Add Stripe integration: `stripe@^12.0.0`

#### Task 1.2: Shared Infrastructure Configuration
**Duration**: 4 days
**Priority**: High
**Dependencies**: Task 1.1

**Deliverables**:
1. **Shared Queue Configuration**
   ```typescript
   // shared/queue-configs/queue.constants.ts
   export const MICROSERVICE_QUEUES = {
     // Campaign Service Queues
     CAMPAIGN_BUILD: 'campaign.build.queue',
     CAMPAIGN_SEND: 'campaign.send.queue',
     CAMPAIGN_SCHEDULE: 'campaign.schedule.queue',
     CAMPAIGN_ANALYTICS: 'campaign.analytics.queue',
     
     // Message Service Queues
     MESSAGE_OUTBOUND: 'message.outbound.queue',
     MESSAGE_SYSTEM: 'message.system.queue',
     MESSAGE_DELIVERY_STATUS: 'message.delivery.status.queue',
     MESSAGE_RETRY: 'message.retry.queue',
     
     // Payment Service Queues
     PAYMENT_PROCESS: 'payment.process.queue',
     PAYMENT_SUBSCRIPTION: 'payment.subscription.queue',
     PAYMENT_INVOICE: 'payment.invoice.queue',
     PAYMENT_WEBHOOK: 'payment.webhook.queue',
     
     // Cross-Service Communication
     NOTIFICATION_EMAIL: 'notification.email.queue',
     NOTIFICATION_SMS: 'notification.sms.queue',
     AUDIT_LOG: 'audit.log.queue',
     SYSTEM_ALERT: 'system.alert.queue'
   } as const;
   
   export const QUEUE_PRIORITIES = {
     CRITICAL: 10,
     HIGH: 7,
     NORMAL: 5,
     LOW: 3,
     BACKGROUND: 1
   } as const;
   ```

2. **Shared TypeScript Types**
   ```typescript
   // shared/types/campaign.types.ts
   export interface CampaignBase {
     _id: string;
     org_id: string;
     created_by: string;
     name: string;
     description?: string;
     type: CampaignType;
     status: CampaignStatus;
     created_at: Date;
     updated_at: Date;
   }
   
   export enum CampaignType {
     BROADCAST = 'BROADCAST',
     TRIGGERED = 'TRIGGERED',
     DRIP = 'DRIP',
     AB_TEST = 'AB_TEST'
   }
   
   export enum CampaignStatus {
     DRAFT = 'DRAFT',
     BUILDING = 'BUILDING',
     SCHEDULED = 'SCHEDULED',
     SENDING = 'SENDING',
     COMPLETED = 'COMPLETED',
     CANCELLED = 'CANCELLED',
     FAILED = 'FAILED'
   }
   ```

#### Task 1.3: Database Schema Design and Implementation
**Duration**: 5 days
**Priority**: High
**Dependencies**: Task 1.1

**Database Collections Design**:

1. **Campaign Service Collections**
   ```javascript
   // campaigns collection
   {
     _id: ObjectId,
     org_id: String,
     created_by: String,
     name: String,
     description: String,
     type: String, // BROADCAST, TRIGGERED, DRIP
     status: String, // DRAFT, BUILDING, SCHEDULED, etc.
     
     // Targeting configuration
     target_config: {
       type: String, // CONTACTS, SEGMENTS, ALL
       contact_ids: [ObjectId],
       segment_ids: [ObjectId],
       filters: Object
     },
     
     // Message content for different channels
     channels: {
       sms: {
         enabled: Boolean,
         sender_id: String,
         content: String,
         template_variables: Object
       },
       email: {
         enabled: Boolean,
         sender_email: String,
         subject: String,
         content: String,
         html_content: String,
         template_variables: Object
       },
       push: {
         enabled: Boolean,
         title: String,
         content: String,
         template_variables: Object
       }
     },
     
     // Scheduling
     schedule_config: {
       is_scheduled: Boolean,
       scheduled_at: Date,
       timezone: String,
       recurring: {
         enabled: Boolean,
         frequency: String, // DAILY, WEEKLY, MONTHLY
         interval: Number,
         end_date: Date
       }
     },
     
     // Analytics and reporting
     stats: {
       total_recipients: Number,
       messages_sent: Number,
       messages_delivered: Number,
       messages_failed: Number,
       open_rate: Number,
       click_rate: Number,
       unsubscribe_rate: Number,
       bounce_rate: Number
     },
     
     // Cost tracking
     cost_analysis: {
       estimated_cost: {
         sms: Number,
         email: Number,
         push: Number,
         total: Number,
         currency: String
       },
       actual_cost: {
         sms: Number,
         email: Number,
         push: Number,
         total: Number,
         currency: String
       }
     },
     
     // A/B Testing (if applicable)
     ab_test_config: {
       enabled: Boolean,
       variants: [{
         name: String,
         percentage: Number,
         content: Object
       }],
       winner_criteria: String,
       test_duration: Number
     },
     
     // Metadata and tags
     tags: [String],
     metadata: Object,
     
     // Timestamps
     created_at: Date,
     updated_at: Date,
     started_at: Date,
     completed_at: Date
   }
   
   // Indexes for campaigns
   db.campaigns.createIndex({ "org_id": 1, "status": 1 });
   db.campaigns.createIndex({ "org_id": 1, "created_at": -1 });
   db.campaigns.createIndex({ "org_id": 1, "type": 1 });
   db.campaigns.createIndex({ "schedule_config.scheduled_at": 1 }, { sparse: true });
   db.campaigns.createIndex({ "created_by": 1, "created_at": -1 });
   ```

2. **Message Service Collections**
   ```javascript
   // message_logs collection
   {
     _id: ObjectId,
     org_id: String,
     message_id: String, // Unique identifier
     
     // Message details
     transport: String, // SMS, EMAIL, PUSH, FACEBOOK_MESSENGER
     from: String,
     to: String,
     content: String,
     subject: String, // For email
     
     // Campaign association
     campaign_id: ObjectId,
     is_campaign_message: Boolean,
     
     // Provider information
     provider: String,
     provider_message_id: String,
     provider_response: Object,
     
     // Status tracking with history
     status: String, // QUEUED, SENT, DELIVERED, FAILED, BOUNCED
     status_history: [{
       status: String,
       timestamp: Date,
       details: String,
       provider_status: String
     }],
     
     // Delivery tracking
     queued_at: Date,
     sent_at: Date,
     delivered_at: Date,
     opened_at: Date,
     clicked_at: Date,
     failed_at: Date,
     bounced_at: Date,
     
     // Error handling
     error_code: String,
     error_message: String,
     retry_count: Number,
     max_retries: Number,
     next_retry_at: Date,
     
     // Cost tracking
     cost: Number,
     currency: String,
     
     // Engagement tracking
     engagement: {
       opened: Boolean,
       clicked: Boolean,
       unsubscribed: Boolean,
       links_clicked: [String],
       user_agent: String,
       ip_address: String
     },
     
     // Metadata
     metadata: Object,
     
     created_at: Date,
     updated_at: Date
   }
   
   // Indexes for message_logs
   db.message_logs.createIndex({ "org_id": 1, "transport": 1 });
   db.message_logs.createIndex({ "org_id": 1, "created_at": -1 });
   db.message_logs.createIndex({ "campaign_id": 1, "status": 1 });
   db.message_logs.createIndex({ "message_id": 1 }, { unique: true });
   db.message_logs.createIndex({ "status": 1, "next_retry_at": 1 });
   db.message_logs.createIndex({ "provider": 1, "created_at": -1 });
   ```

### Phase 2: Enhanced Core Service Development (Weeks 3-4)

#### Task 2.1: Sender ID Management Implementation
**Duration**: 6 days
**Priority**: High
**Dependencies**: Phase 1

**Implementation Details**:

1. **Database Model**
   ```typescript
   // lib/db/models/sender.id.model.ts
   import { Schema, model } from 'mongoose';
   
   const senderIdSchema = new Schema({
     org_id: { type: String, required: true, index: true },
     created_by: { type: String, required: true, index: true },
     
     // Sender identification
     sender_id: { 
       type: String, 
       required: true,
       validate: {
         validator: function(v: string) {
           return /^[A-Za-z0-9]{3,11}$/.test(v);
         },
         message: 'Sender ID must be 3-11 alphanumeric characters'
       }
     },
     display_name: String,
     transport: { 
       type: String, 
       enum: ['SMS', 'EMAIL', 'FACEBOOK_MESSENGER'], 
       required: true 
     },
     
     // Status tracking
     status: {
       type: String,
       enum: ['PENDING', 'APPROVED', 'REJECTED', 'SUSPENDED'],
       default: 'PENDING',
       index: true
     },
     
     // Country-specific registrations
     registered_countries: [{
       country_iso_code: { type: String, required: true },
       country_name: { type: String, required: true },
       status: {
         type: String,
         enum: ['PENDING', 'APPROVED', 'REJECTED'],
         default: 'PENDING'
       },
       submitted_at: { type: Date, default: Date.now },
       approved_at: Date,
       rejected_at: Date,
       rejected_reason: String,
       approval_reference: String
     }],
     
     // Application metadata
     metadata: {
       user_type: { 
         type: String, 
         enum: ['BUSINESS', 'INDIVIDUAL'], 
         required: true 
       },
       // Business-specific fields
       company_name: {
         type: String,
         required: function() { return this.metadata.user_type === 'BUSINESS'; }
       },
       business_nature: {
         type: String,
         required: function() { return this.metadata.user_type === 'BUSINESS'; }
       },
       business_registration_number: String,
       
       // Common fields
       user_name: { type: String, required: true },
       user_designation: String,
       user_identification_number: {
         type: String,
         required: function() { return this.metadata.user_type === 'INDIVIDUAL'; }
       },
       
       // Usage information
       sample_content: { type: String, required: true },
       intended_usage: { type: String, required: true },
       message_volume_per_month: Number,
       enable_international_usage: { type: Boolean, required: true },
       usage_type: { 
         type: String, 
         enum: ['TRANSACTIONAL', 'PROMOTIONAL', 'MIXED'], 
         required: true 
       },
       country_iso_codes: { type: [String], required: true }
     },
     
     // Document artifacts
     artifacts: [{
       artifact_id: { type: String, required: true },
       artifact_type: {
         type: String,
         enum: [
           'SENDER_ID_AGREEMENT',
           'BUSINESS_REGISTRATION_COPY',
           'PERSONAL_ID_COPY',
           'AUTHORIZATION_LETTER',
           'SAMPLE_MESSAGE_CONTENT'
         ],
         required: true
       },
       file_name: String,
       file_url: String,
       file_size: Number,
       mime_type: String,
       uploaded_at: { type: Date, default: Date.now },
       status: { 
         type: String, 
         enum: ['PENDING', 'APPROVED', 'REJECTED'], 
         default: 'PENDING' 
       },
       reviewed_by: String,
       reviewed_at: Date,
       review_notes: String
     }],
     
     // Approval workflow
     approval_workflow: {
       current_step: {
         type: String,
         enum: ['DOCUMENT_UPLOAD', 'TECHNICAL_REVIEW', 'COMPLIANCE_REVIEW', 'FINAL_APPROVAL'],
         default: 'DOCUMENT_UPLOAD'
       },
       steps_completed: [String],
       assigned_reviewer: String,
       review_notes: [String],
       estimated_completion: Date
     },
     
     created_at: { type: Date, default: Date.now },
     updated_at: { type: Date, default: Date.now }
   }, {
     timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
   });
   
   // Compound indexes
   senderIdSchema.index({ org_id: 1, sender_id: 1 }, { unique: true });
   senderIdSchema.index({ org_id: 1, status: 1 });
   senderIdSchema.index({ org_id: 1, transport: 1 });
   senderIdSchema.index({ 'registered_countries.country_iso_code': 1, status: 1 });
   
   export const SenderIdModel = model('SenderId', senderIdSchema);
   ```

2. **Service Layer Implementation**
   ```typescript
   // services/sender.id.service.ts
   export class SenderIdService {
     static async createSenderIdRequest(data: CreateSenderIdRequest): Promise<SenderId> {
       // Validate sender ID uniqueness within organization
       const existingSender = await SenderIdModel.findOne({
         org_id: data.org_id,
         sender_id: data.sender_id,
         status: { $ne: 'REJECTED' }
       });
       
       if (existingSender) {
         throw new ConflictError('Sender ID already exists for this organization');
       }
       
       // Create sender ID record
       const senderIdDoc = new SenderIdModel({
         org_id: data.org_id,
         created_by: data.created_by,
         sender_id: data.sender_id.toUpperCase(),
         transport: data.transport,
         metadata: data.metadata,
         registered_countries: data.metadata.country_iso_codes.map(code => ({
           country_iso_code: code,
           country_name: this.getCountryName(code),
           status: 'PENDING'
         }))
       });
       
       const savedSender = await senderIdDoc.save();
       
       // Queue notification to admin for review
       await this.queueAdminNotification(savedSender);
       
       return savedSender;
     }
     
     static async uploadArtifact(
       senderId: string, 
       artifactType: string, 
       file: Express.Multer.File
     ): Promise<SenderIdArtifact> {
       // Upload file to S3 or similar storage
       const fileUrl = await FileStorageService.uploadFile(file, 'sender-artifacts');
       
       // Update sender record with artifact
       const artifact = {
         artifact_id: uuidv4(),
         artifact_type: artifactType,
         file_name: file.originalname,
         file_url: fileUrl,
         file_size: file.size,
         mime_type: file.mimetype,
         uploaded_at: new Date(),
         status: 'PENDING'
       };
       
       await SenderIdModel.findByIdAndUpdate(senderId, {
         $push: { artifacts: artifact },
         $set: { updated_at: new Date() }
       });
       
       return artifact;
     }
   }
   ```

#### Task 2.2: Phone Number Management System
**Duration**: 4 days
**Priority**: Medium
**Dependencies**: Task 2.1

**Key Features**:
1. Integration with Twilio for number purchase/release
2. Number capability management (SMS/Voice)
3. Usage tracking and billing
4. Webhook handling for inbound messages

### Phase 3: Campaign Service Development (Weeks 5-7)

#### Task 3.1: Campaign Management Core
**Duration**: 8 days
**Priority**: High
**Dependencies**: Phase 2

**Implementation Scope**:
1. **Campaign CRUD Operations**
2. **Template Management System**
3. **Segment-based Targeting**
4. **Campaign Building Process**
5. **Scheduling System**
6. **A/B Testing Framework**

**Detailed API Specifications**:
```typescript
// Campaign Creation API
POST /api/v1/campaigns
{
  "name": "Black Friday Sale 2024",
  "description": "Promotional campaign for Black Friday deals",
  "type": "BROADCAST",
  "target_config": {
    "type": "SEGMENTS",
    "segment_ids": ["64a1b2c3d4e5f6789012345", "64a1b2c3d4e5f6789012346"],
    "filters": {
      "exclude_unsubscribed": true,
      "exclude_bounced": true,
      "last_activity_days": 30
    }
  },
  "channels": {
    "sms": {
      "enabled": true,
      "sender_id": "BLACKFRI",
      "content": "🔥 BLACK FRIDAY SALE! Get {{discount}}% off everything! Use code: {{promo_code}}. Shop now: {{shop_url}}",
      "template_variables": {
        "discount": "50",
        "promo_code": "BF2024",
        "shop_url": "https://shop.example.com/bf2024"
      }
    },
    "email": {
      "enabled": true,
      "sender_email": "<EMAIL>",
      "sender_name": "Example Store",
      "subject": "🔥 BLACK FRIDAY: {{discount}}% OFF Everything!",
      "content": "Don't miss our biggest sale of the year...",
      "html_content": "<html>...</html>",
      "template_variables": {
        "discount": "50",
        "customer_name": "{{contact.name}}",
        "shop_url": "https://shop.example.com/bf2024"
      }
    }
  },
  "schedule_config": {
    "is_scheduled": true,
    "scheduled_at": "2024-11-29T09:00:00Z",
    "timezone": "America/New_York"
  },
  "ab_test_config": {
    "enabled": true,
    "variants": [
      {
        "name": "Variant A - 50% Off",
        "percentage": 50,
        "content": {
          "sms": "🔥 BLACK FRIDAY! 50% OFF everything!",
          "email": { "subject": "50% OFF Everything - Black Friday!" }
        }
      },
      {
        "name": "Variant B - Buy One Get One",
        "percentage": 50,
        "content": {
          "sms": "🔥 BLACK FRIDAY! Buy One Get One FREE!",
          "email": { "subject": "BOGO FREE - Black Friday Special!" }
        }
      }
    ],
    "winner_criteria": "CLICK_RATE",
    "test_duration": 24
  },
  "tags": ["black-friday", "promotional", "2024"],
  "metadata": {
    "campaign_manager": "<EMAIL>",
    "budget_limit": 5000,
    "expected_roi": 3.5
  }
}
```

#### Task 3.2: Segment Management and Analytics
**Duration**: 5 days
**Priority**: High
**Dependencies**: Task 3.1

**Segment Processing Engine**:
```typescript
// services/segment.processor.service.ts
export class SegmentProcessorService {
  static async processSegmentCriteria(segmentId: string): Promise<ContactSegmentResult> {
    const segment = await CampaignSegmentModel.findById(segmentId);
    if (!segment) throw new NotFoundError('Segment not found');

    const pipeline = this.buildAggregationPipeline(segment.criteria);
    const contacts = await ContactModel.aggregate(pipeline);

    // Update segment with calculated results
    await CampaignSegmentModel.findByIdAndUpdate(segmentId, {
      contact_count: contacts.length,
      last_calculated_at: new Date(),
      contact_ids: contacts.map(c => c._id)
    });

    return {
      segment_id: segmentId,
      total_contacts: contacts.length,
      contacts: contacts,
      calculation_time: new Date()
    };
  }

  private static buildAggregationPipeline(criteria: SegmentCriteria): any[] {
    const pipeline = [];

    // Base match stage
    pipeline.push({
      $match: {
        org_id: criteria.org_id,
        status: 'ACTIVE'
      }
    });

    // Add condition filters
    if (criteria.conditions?.length > 0) {
      const conditionMatch = this.buildConditionMatch(criteria.conditions);
      pipeline.push({ $match: conditionMatch });
    }

    // Add tag filters
    if (criteria.tags?.include?.length > 0) {
      pipeline.push({
        $match: {
          'tags.tag_name': { $in: criteria.tags.include }
        }
      });
    }

    // Add date filters
    if (criteria.date_filters) {
      const dateMatch = this.buildDateMatch(criteria.date_filters);
      pipeline.push({ $match: dateMatch });
    }

    return pipeline;
  }
}
```

### Phase 4: Message Service Development (Weeks 8-10)

#### Task 4.1: Multi-Provider Message Router
**Duration**: 7 days
**Priority**: High
**Dependencies**: Phase 3

**Provider Architecture**:
```typescript
// services/providers/base.provider.ts
export abstract class BaseMessageProvider {
  abstract providerName: string;
  abstract supportedTransports: MessageTransport[];

  abstract async sendMessage(message: MessageData): Promise<ProviderResult>;
  abstract async getDeliveryStatus(messageId: string): Promise<DeliveryStatus>;
  abstract async handleWebhook(payload: any): Promise<WebhookResult>;

  protected async logMessage(message: MessageData, result: ProviderResult): Promise<void> {
    await MessageLogModel.create({
      org_id: message.orgId,
      message_id: message.messageId,
      transport: message.transport,
      provider: this.providerName,
      status: result.success ? 'SENT' : 'FAILED',
      provider_message_id: result.providerId,
      cost: result.cost,
      currency: result.currency,
      sent_at: result.success ? new Date() : undefined,
      failed_at: result.success ? undefined : new Date(),
      error_message: result.error
    });
  }
}

// services/providers/twilio.sms.provider.ts
export class TwilioSMSProvider extends BaseMessageProvider {
  providerName = 'TWILIO_SMS';
  supportedTransports = [MessageTransport.SMS];

  private client = twilio(process.env.TWILIO_SID, process.env.TWILIO_TOKEN);

  async sendMessage(message: MessageData): Promise<ProviderResult> {
    try {
      const result = await this.client.messages.create({
        body: message.content,
        from: message.from,
        to: message.to,
        statusCallback: `${process.env.BASE_URL}/webhooks/twilio/status`
      });

      await this.logMessage(message, {
        success: true,
        providerId: result.sid,
        cost: this.calculateCost(message.to),
        currency: 'USD'
      });

      return {
        success: true,
        providerId: result.sid,
        cost: this.calculateCost(message.to),
        currency: 'USD'
      };
    } catch (error) {
      await this.logMessage(message, {
        success: false,
        error: error.message,
        cost: 0,
        currency: 'USD'
      });

      throw new ProviderError(`Twilio SMS failed: ${error.message}`);
    }
  }
}
```

#### Task 4.2: Delivery Tracking and Analytics
**Duration**: 4 days
**Priority**: Medium
**Dependencies**: Task 4.1

**Analytics Engine**:
```typescript
// services/message.analytics.service.ts
export class MessageAnalyticsService {
  static async generateCampaignReport(campaignId: string): Promise<CampaignAnalytics> {
    const pipeline = [
      { $match: { campaign_id: new ObjectId(campaignId) } },
      {
        $group: {
          _id: '$transport',
          total_sent: { $sum: 1 },
          delivered: { $sum: { $cond: [{ $eq: ['$status', 'DELIVERED'] }, 1, 0] } },
          failed: { $sum: { $cond: [{ $eq: ['$status', 'FAILED'] }, 1, 0] } },
          opened: { $sum: { $cond: ['$engagement.opened', 1, 0] } },
          clicked: { $sum: { $cond: ['$engagement.clicked', 1, 0] } },
          total_cost: { $sum: '$cost' }
        }
      }
    ];

    const results = await MessageLogModel.aggregate(pipeline);

    return {
      campaign_id: campaignId,
      generated_at: new Date(),
      by_transport: results.reduce((acc, result) => {
        acc[result._id] = {
          total_sent: result.total_sent,
          delivered: result.delivered,
          failed: result.failed,
          delivery_rate: (result.delivered / result.total_sent) * 100,
          open_rate: result._id === 'EMAIL' ? (result.opened / result.delivered) * 100 : null,
          click_rate: (result.clicked / result.delivered) * 100,
          total_cost: result.total_cost
        };
        return acc;
      }, {}),
      overall_stats: this.calculateOverallStats(results)
    };
  }
}
```

### Phase 5: Payment Service Development (Weeks 11-13)

#### Task 5.1: Stripe Integration and Subscription Management
**Duration**: 8 days
**Priority**: High
**Dependencies**: Phase 4

**Subscription Service Implementation**:
```typescript
// services/subscription.management.service.ts
export class SubscriptionManagementService {
  private static stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

  static async createSubscription(data: CreateSubscriptionRequest): Promise<Subscription> {
    try {
      // Create or retrieve Stripe customer
      let customer = await this.getOrCreateStripeCustomer(data.userId, data.orgId);

      // Attach payment method to customer
      await this.stripe.paymentMethods.attach(data.paymentMethodId, {
        customer: customer.id
      });

      // Set as default payment method
      await this.stripe.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: data.paymentMethodId
        }
      });

      // Create subscription
      const stripeSubscription = await this.stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: data.planId }],
        default_payment_method: data.paymentMethodId,
        billing_cycle_anchor: data.billingCycleAnchor,
        proration_behavior: 'create_prorations',
        expand: ['latest_invoice.payment_intent']
      });

      // Save subscription to database
      const subscription = new SubscriptionModel({
        org_id: data.orgId,
        user_id: data.userId,
        stripe_subscription_id: stripeSubscription.id,
        stripe_customer_id: customer.id,
        plan_id: data.planId,
        status: stripeSubscription.status.toUpperCase(),
        billing_cycle: this.getBillingCycle(data.planId),
        amount: stripeSubscription.items.data[0].price.unit_amount / 100,
        currency: stripeSubscription.items.data[0].price.currency.toUpperCase(),
        current_period_start: new Date(stripeSubscription.current_period_start * 1000),
        current_period_end: new Date(stripeSubscription.current_period_end * 1000),
        trial_start: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : undefined,
        trial_end: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : undefined,
        usage_limits: await this.getPlanLimits(data.planId),
        usage_current: this.getInitialUsage()
      });

      const savedSubscription = await subscription.save();

      // Queue welcome email
      await this.queueWelcomeEmail(savedSubscription);

      return savedSubscription;
    } catch (error) {
      throw new PaymentError(`Failed to create subscription: ${error.message}`);
    }
  }

  static async handleWebhook(event: Stripe.Event): Promise<void> {
    switch (event.type) {
      case 'invoice.payment_succeeded':
        await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;
      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;
      case 'customer.subscription.deleted':
        await this.handleSubscriptionCancelled(event.data.object as Stripe.Subscription);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  }
}
```

### Phase 6: Integration and Testing (Weeks 14-15)

#### Task 6.1: End-to-End Integration Testing
**Duration**: 6 days
**Priority**: Critical
**Dependencies**: All previous phases

**Integration Test Scenarios**:
```typescript
// tests/integration/complete.campaign.flow.test.ts
describe('Complete Campaign Flow Integration', () => {
  let authToken: string;
  let orgId: string;
  let campaignId: string;

  beforeAll(async () => {
    // Setup test environment
    authToken = await getTestAuthToken();
    orgId = await createTestOrganization();
    await seedTestContacts(orgId, 100);
  });

  it('should execute complete campaign lifecycle', async () => {
    // 1. Create campaign
    const campaignResponse = await request(campaignApp)
      .post('/api/v1/campaigns')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Integration Test Campaign',
        type: 'BROADCAST',
        target_config: {
          type: 'ALL'
        },
        channels: {
          sms: {
            enabled: true,
            sender_id: 'TEST',
            content: 'Test message to {{contact.name}}'
          }
        }
      })
      .expect(201);

    campaignId = campaignResponse.body.data._id;

    // 2. Build campaign
    const buildResponse = await request(campaignApp)
      .post(`/api/v1/campaigns/${campaignId}/build`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    // 3. Wait for build completion
    await waitForJobCompletion(buildResponse.body.data.job_id, 30000);

    // 4. Verify campaign is ready
    const campaignStatus = await request(campaignApp)
      .get(`/api/v1/campaigns/${campaignId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(campaignStatus.body.data.status).toBe('READY');

    // 5. Send campaign
    await request(campaignApp)
      .post(`/api/v1/campaigns/${campaignId}/send`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    // 6. Verify messages were queued
    const messageQueue = await getQueueStats(MICROSERVICE_QUEUES.MESSAGE_OUTBOUND);
    expect(messageQueue.waiting).toBeGreaterThan(0);

    // 7. Process messages and verify delivery
    await processAllQueuedMessages();

    // 8. Check campaign analytics
    const analytics = await request(campaignApp)
      .get(`/api/v1/campaigns/${campaignId}/analytics`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(analytics.body.data.stats.messages_sent).toBeGreaterThan(0);
  });
});
```

#### Task 6.2: Performance Testing and Optimization
**Duration**: 4 days
**Priority**: High
**Dependencies**: Task 6.1

**Load Testing Scenarios**:
- Campaign creation with 10,000+ contacts
- Concurrent message processing (1000 messages/second)
- Database query optimization under load
- Redis queue performance testing

This comprehensive plan provides detailed specifications for each phase of development, including database schemas, API designs, service implementations, and testing strategies. The plan ensures consistency with existing patterns while introducing new functionality systematically.
