# Inbound Message Service - REST API Architecture

## Overview
This document outlines the REST API architecture for transforming the existing SQS-based inbound message service into a modern REST API following industry best practices.

## Current Architecture Analysis

### Existing Components
- **SQS Consumer**: Processes messages from AWS SQS queues
- **Message Handlers**: <PERSON><PERSON><PERSON><PERSON>, OptOut<PERSON><PERSON>ler, <PERSON>rgingHandler
- **Message Mappers**: Various provider mappers (Twilio, Nexmo, Dialog, etc.)
- **Services**: CoreService, RealtimeService, HookService, DialogService
- **DAOs**: Database access objects for MongoDB operations
- **Infrastructure**: MongoDB, Redis, AWS SQS

### Current Message Flow
1. Messages arrive in SQS queue
2. GenericConsumer processes messages in batches
3. InboundMessageHandler maps and routes messages
4. Specific handlers process based on message type
5. Results stored in MongoDB and sent to realtime service

## Proposed REST API Architecture

### 1. API Endpoints Design

#### Message Processing Endpoints
```
POST /api/v1/messages/inbound
- Process single inbound message
- Body: Inbound message payload
- Response: Processing result

POST /api/v1/messages/batch
- Process batch of inbound messages
- Body: Array of message payloads
- Response: Batch processing results
```

#### Competition Endpoints
```
POST /api/v1/competitions/{competitionId}/messages
- Process competition-specific message
- Path: competitionId
- Body: Message payload
- Response: Competition processing result

GET /api/v1/competitions/{competitionId}/logs
- Retrieve competition logs
- Query params: limit, offset, dateFrom, dateTo
- Response: Paginated competition logs

GET /api/v1/competitions/{competitionId}/reports
- Get competition reports
- Query params: participantId, groupBy
- Response: Competition report data
```

#### Opt-out Endpoints
```
POST /api/v1/optouts
- Process opt-out request
- Body: Opt-out message payload
- Response: Opt-out processing result

GET /api/v1/optouts/{ownerId}
- Get opt-out status for owner
- Path: ownerId
- Response: Opt-out records
```

#### Hook Management Endpoints
```
GET /api/v1/hooks/{hookId}
- Retrieve hook configuration
- Path: hookId
- Response: Hook mapping details

PUT /api/v1/hooks/{hookId}
- Update hook configuration
- Path: hookId
- Body: Hook configuration
- Response: Updated hook details
```

#### Health and Monitoring
```
GET /api/v1/health
- Service health check
- Response: Service status

GET /api/v1/metrics
- Service metrics and statistics
- Response: Processing metrics
```

### 2. Request/Response Schema Design

#### Inbound Message Schema
```json
{
  "ownerId": "string",
  "message": "string",
  "sourceId": "string",
  "destinationId": "string",
  "operator": "string",
  "createdOn": "datetime",
  "hookId": "string",
  "messageAttributes": {
    "provider": "string",
    "metadata": {}
  }
}
```

#### Competition Message Response
```json
{
  "success": true,
  "data": {
    "competitionLogId": "string",
    "competitionId": "string",
    "participantId": "string",
    "attributes": {},
    "isValid": true,
    "processedAt": "datetime"
  },
  "processing": {
    "duration": "number",
    "realTimeUpdateSent": true
  }
}
```

#### Error Response Schema
```json
{
  "success": false,
  "error": {
    "code": "string",
    "message": "string",
    "details": {},
    "timestamp": "datetime",
    "requestId": "string"
  }
}
```

### 3. Controller-Service-Repository Pattern

#### Controllers Layer
- `MessageController`: Handle inbound message endpoints
- `CompetitionController`: Manage competition-related operations
- `OptOutController`: Handle opt-out requests
- `HookController`: Manage hook configurations
- `HealthController`: Health checks and monitoring

#### Services Layer
- `MessageProcessingService`: Core message processing logic
- `CompetitionService`: Competition-specific business logic
- `OptOutService`: Opt-out management
- `HookMappingService`: Hook configuration management
- `ValidationService`: Request validation
- `MetricsService`: Performance metrics collection

#### Repository Layer
- `MessageRepository`: Database operations for messages
- `CompetitionRepository`: Competition data access
- `HookRepository`: Hook configuration data access
- `CacheRepository`: Redis caching operations

### 4. Middleware Architecture

#### Authentication Middleware
```javascript
// API Key based authentication
app.use('/api', authenticateApiKey);

// JWT token authentication for admin endpoints
app.use('/api/admin', authenticateJWT);
```

#### Validation Middleware
```javascript
// Request validation using Joi schemas
app.use('/api/v1/messages', validateMessagePayload);
app.use('/api/v1/competitions', validateCompetitionRequest);
```

#### Rate Limiting
```javascript
// Rate limiting per API key
app.use('/api', rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000 // limit each API key to 1000 requests per windowMs
}));
```

#### Logging and Monitoring
```javascript
// Request logging
app.use(requestLogger);

// Error handling
app.use(errorHandler);

// Metrics collection
app.use(metricsCollector);
```

### 5. Error Handling Strategy

#### Error Categories
- **Validation Errors** (400): Invalid request data
- **Authentication Errors** (401): Missing or invalid API key
- **Authorization Errors** (403): Insufficient permissions
- **Not Found Errors** (404): Resource not found
- **Rate Limit Errors** (429): Too many requests
- **Server Errors** (500): Internal processing errors

#### Error Response Format
All errors follow consistent format with error codes, messages, and contextual details.

### 6. Security Considerations

#### Authentication
- API Key authentication for external integrations
- JWT tokens for admin panel access
- Request signing for sensitive operations

#### Data Protection
- Input sanitization and validation
- SQL injection prevention (using parameterized queries)
- XSS protection
- CORS configuration

#### Rate Limiting
- Per-API-key rate limiting
- IP-based rate limiting for anonymous endpoints
- Burst protection mechanisms

### 7. Performance Optimization

#### Caching Strategy
- Redis caching for frequently accessed data
- Competition configurations caching
- Hook mappings caching
- Response caching for read-heavy endpoints

#### Database Optimization
- Proper indexing for query performance
- Connection pooling
- Query optimization
- Read replicas for analytics queries

#### Async Processing
- Background job processing for non-critical operations
- Queue-based processing for heavy computations
- Event-driven architecture for real-time updates

### 8. Monitoring and Observability

#### Metrics Collection
- Request/response metrics
- Processing time metrics
- Error rate monitoring
- Database performance metrics

#### Logging Strategy
- Structured logging with correlation IDs
- Different log levels (error, warn, info, debug)
- Centralized log aggregation

#### Health Checks
- Database connectivity checks
- External service health checks
- Resource utilization monitoring

## Implementation Phases

### Phase 1: Core API Framework
1. Set up Express.js server with middleware
2. Implement authentication and validation
3. Create basic CRUD endpoints
4. Add error handling and logging

### Phase 2: Message Processing APIs
1. Convert existing handlers to service layer
2. Implement message processing endpoints
3. Add async processing capabilities
4. Integrate with existing database operations

### Phase 3: Advanced Features
1. Add competition and opt-out specific endpoints
2. Implement caching layer
3. Add rate limiting and security features
4. Performance optimization

### Phase 4: Documentation and Testing
1. Complete API documentation
2. Add comprehensive test coverage
3. Performance testing and optimization
4. Deployment documentation

This architecture provides a solid foundation for transforming the existing message service into a modern, scalable REST API while maintaining backward compatibility and ensuring high performance.