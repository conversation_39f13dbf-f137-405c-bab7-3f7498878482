# Campaign Service REST API Documentation

## Overview

This document provides comprehensive documentation for redesigning the existing serverless Campaign Service as a modern REST API. The service manages marketing campaigns, message logs, subscriptions, and blacklist operations with support for SMS, Email, and other messaging channels.

## Architecture Pattern

The Campaign Service follows a **microservices architecture** with RESTful API design principles:

- **Resource-Based URLs**: Each endpoint represents a specific resource (campaigns, logs, subscriptions)
- **HTTP Methods**: Standard HTTP methods (GET, POST, PUT, DELETE) for CRUD operations
- **Stateless Communication**: Each request contains all necessary information
- **JSON Data Format**: All requests and responses use JSON
- **JWT Authentication**: Token-based authentication for secure access
- **Error Handling**: Consistent HTTP status codes and error responses

## Base URL

```
https://api.shoutout.com/campaign/v1
```

## Authentication

All API endpoints require JWT authentication via the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

## Core Resources

### 1. Campaigns

#### Campaign Object Structure

```json
{
  "_id": "string",
  "name": "string",
  "type": "BROADCAST|TRIGGERED",
  "status": "DRAFT|SCHEDULED|RUNNING|COMPLETED|CANCELLED",
  "isScheduled": 0|1,
  "launchOn": "2023-01-01T00:00:00Z",
  "createdOn": "2023-01-01T00:00:00Z",
  "modifiedOn": "2023-01-01T00:00:00Z",
  "sms": {
    "from": "string",
    "content": "string"
  },
  "email": {
    "from": "string",
    "subject": "string",
    "content": "string"
  },
  "report": {
    "sms": {
      "totalCost": "number",
      "totalCount": "number"
    },
    "email": {
      "totalCost": "number",
      "totalCount": "number"
    }
  }
}
```

### 2. Message Logs

#### Log Object Structure

```json
{
  "_id": "string",
  "campaignId": "string",
  "contactId": "string",
  "from": "string",
  "to": "string",
  "transport": "SMS|EMAIL",
  "status": "string",
  "createdOn": "2023-01-01T00:00:00Z",
  "modifiedOn": "2023-01-01T00:00:00Z"
}
```

### 3. Inbound Logs

#### Inbound Log Object Structure

```json
{
  "_id": "string",
  "destinationId": "string",
  "sourceId": "string",
  "transport": "SMS|EMAIL",
  "content": "string",
  "createdOn": "2023-01-01T00:00:00Z"
}
```

## API Endpoints

### Campaign Management

#### GET /campaigns

Retrieve campaigns with filtering and pagination.

**Query Parameters:**
- `fromDate` (optional): Filter campaigns from date (YYYY-MM-DD)
- `toDate` (optional): Filter campaigns to date (YYYY-MM-DD)
- `type` (optional): Campaign type filter
- `limit` (optional): Number of results to return (default: 50)
- `skip` (optional): Number of results to skip for pagination

**Response:**
```json
{
  "data": [
    {
      "_id": "campaign_id",
      "name": "Campaign Name",
      "type": "BROADCAST",
      "status": "COMPLETED",
      "createdOn": "2023-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "limit": 50,
    "skip": 0,
    "total": 100
  }
}
```

#### GET /campaigns/{campaign_id}

Retrieve a specific campaign by ID.

**Response:**
```json
{
  "data": {
    "_id": "campaign_id",
    "name": "Campaign Name",
    "type": "BROADCAST",
    "status": "COMPLETED",
    "sms": {
      "from": "ShoutOUT",
      "content": "Hello World"
    },
    "email": {
      "from": "<EMAIL>",
      "subject": "Newsletter",
      "content": "<html>...</html>"
    }
  }
}
```

#### POST /campaigns

Create a new campaign.

**Request Body:**
```json
{
  "name": "Campaign Name",
  "type": "BROADCAST",
  "isScheduled": 0,
  "sms": {
    "from": "ShoutOUT",
    "content": "Hello {{name}}"
  },
  "email": {
    "from": "<EMAIL>",
    "subject": "Welcome",
    "content": "<html>Welcome {{name}}</html>"
  }
}
```

**Response:**
```json
{
  "data": {
    "_id": "new_campaign_id",
    "name": "Campaign Name",
    "status": "DRAFT",
    "createdOn": "2023-01-01T00:00:00Z"
  }
}
```

#### PUT /campaigns/{campaign_id}

Update an existing campaign.

**Request Body:** Same as POST /campaigns

#### DELETE /campaigns/{campaign_id}

Soft delete a campaign (sets isVisible: 0).

**Response:**
```json
{
  "message": "Campaign deleted successfully"
}
```

#### POST /campaigns/{campaign_id}/retry

Retry a failed campaign.

**Response:**
```json
{
  "message": "Campaign retry initiated"
}
```

### Campaign Logs

#### GET /logs

Retrieve message logs with filtering.

**Query Parameters:**
- `campaignId` (optional): Filter by campaign ID
- `contactId` (optional): Filter by contact ID
- `fromDate` (optional): Filter from date
- `toDate` (optional): Filter to date
- `transport` (optional): SMS or EMAIL
- `from` (optional): Filter by sender
- `to` (optional): Filter by recipient
- `status` (optional): Filter by message status
- `limit` (optional): Results limit
- `skip` (optional): Pagination offset

**Response:**
```json
{
  "data": [
    {
      "_id": "log_id",
      "campaignId": "campaign_id",
      "from": "ShoutOUT",
      "to": "+94771234567",
      "transport": "SMS",
      "status": "delivered",
      "createdOn": "2023-01-01T00:00:00Z"
    }
  ]
}
```

#### GET /logs/{log_id}

Retrieve a specific message log.

#### POST /logs/export

Export logs as CSV file.

**Request Body:**
```json
{
  "campaignId": "campaign_id",
  "fromDate": "2023-01-01",
  "toDate": "2023-01-31",
  "sendingEmail": "<EMAIL>"
}
```

### Inbound Logs

#### GET /inbound-logs

Retrieve inbound message logs.

**Query Parameters:**
- `destinationId` (optional): Filter by destination
- `sourceId` (optional): Filter by source
- `fromDate` (optional): Filter from date
- `toDate` (optional): Filter to date
- `transport` (optional): SMS or EMAIL
- `search` (optional): Text search
- `limit` (optional): Results limit
- `skip` (optional): Pagination offset

#### GET /inbound-logs/summary

Get inbound logs summary grouped by date and transport.

**Response:**
```json
{
  "data": [
    {
      "date": "2023-01-01",
      "transport": "SMS",
      "totalMessagesCount": 150
    }
  ]
}
```

### Admin Operations

#### GET /admin/campaigns

Admin endpoint for campaign management with extended permissions.

#### PUT /admin/campaigns/{campaign_id}

Admin update with additional privileges.

#### DELETE /admin/campaigns/{campaign_id}

Admin delete with extended permissions.

### Subscriptions

#### GET /subscriptions/{campaignId}/{ownerId}/{address}

Handle unsubscribe requests.

### Blacklist Management

The blacklist functionality is handled internally and doesn't expose direct REST endpoints.

## Error Handling

### Standard HTTP Status Codes

- `200 OK`: Successful GET, PUT requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation errors
- `500 Internal Server Error`: Server errors

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid campaign data",
    "details": {
      "field": "name",
      "issue": "Name is required"
    }
  }
}
```

## Data Models

### Campaign Status Values

- `DRAFT`: Campaign created but not launched
- `SCHEDULED`: Campaign scheduled for future launch
- `BUILDING`: Campaign contacts being processed
- `CHARGING`: Campaign costs being calculated
- `SENDING`: Campaign messages being sent
- `COMPLETED`: Campaign finished successfully
- `CANCELLED`: Campaign was cancelled
- `FAILED`: Campaign failed during execution

### Transport Types

- `SMS`: Short Message Service
- `EMAIL`: Electronic Mail
- `VOICE`: Voice calls (if supported)

### Message Status Codes

- `1029`: Delivered
- `1030`: Failed/Undelivered
- `PENDING`: Message queued for delivery
- `SENT`: Message sent to provider
- `DELIVERED`: Message delivered to recipient
- `FAILED`: Message delivery failed

## Rate Limiting

- **Rate Limit**: 1000 requests per hour per user
- **Burst Limit**: 10 requests per second
- **Headers**:
    - `X-RateLimit-Limit`: Total requests allowed
    - `X-RateLimit-Remaining`: Requests remaining
    - `X-RateLimit-Reset`: Time when rate limit resets

## Pagination

For endpoints that return lists, use standard pagination:

**Query Parameters:**
- `limit`: Maximum number of items (default: 50, max: 100)
- `skip`: Number of items to skip (default: 0)

**Response Headers:**
- `X-Total-Count`: Total number of items
- `Link`: Navigation links (first, prev, next, last)

## Filtering and Sorting

### Date Filtering
- Use ISO 8601 format: `YYYY-MM-DD` or `YYYY-MM-DDTHH:MM:SSZ`
- Support `fromDate` and `toDate` parameters

### Sorting
- Use `sort` parameter with field names
- Prefix with `-` for descending order
- Example: `?sort=-createdOn,name`

## Webhook Events

The service supports webhook notifications for campaign events:

- `campaign.created`
- `campaign.updated`
- `campaign.started`
- `campaign.completed`
- `campaign.failed`
- `message.delivered`
- `message.failed`

## Security Considerations

1. **Authentication**: JWT tokens with expiration
2. **Authorization**: Role-based access control
3. **Input Validation**: Strict validation on all inputs
4. **Rate Limiting**: Prevent API abuse
5. **HTTPS Only**: All communications over HTTPS
6. **Data Encryption**: Sensitive data encrypted at rest
7. **Audit Logging**: Track all API access and changes

## Deployment Architecture

### Recommended Stack

- **API Gateway**: AWS API Gateway or similar
- **Compute**: Node.js on AWS Lambda or containers
- **Database**: MongoDB Atlas or self-hosted
- **Cache**: Redis for session and query caching
- **Message Queue**: AWS SQS for async processing
- **File Storage**: AWS S3 for exports and attachments
- **Monitoring**: CloudWatch, DataDog, or similar

### Environment Configuration

```yaml
environments:
  development:
    api_base_url: "https://dev-api.company.com/campaign/v1"
    database_url: "mongodb://dev-db:27017/campaigns"
    
  staging:
    api_base_url: "https://staging-api.company.com/campaign/v1"
    database_url: "mongodb://staging-db:27017/campaigns"
    
  production:
    api_base_url: "https://api.company.com/campaign/v1"
    database_url: "mongodb://prod-cluster/campaigns"
```

## Migration from Serverless

### Key Changes

1. **Unified API Gateway**: Single entry point instead of multiple Lambda functions
2. **Consistent Response Format**: Standardized JSON responses
3. **Improved Error Handling**: HTTP status codes and error objects
4. **Better Documentation**: OpenAPI/Swagger specification
5. **Rate Limiting**: Built-in rate limiting and throttling
6. **Monitoring**: Enhanced logging and metrics

### Migration Steps

1. **API Design**: Define OpenAPI specification
2. **Database Migration**: Ensure data compatibility
3. **Authentication**: Implement JWT authentication
4. **Testing**: Comprehensive API testing
5. **Documentation**: Update client documentation
6. **Deployment**: Gradual rollout with feature flags
7. **Monitoring**: Set up alerts and dashboards

This documentation provides a comprehensive foundation for redesigning the Campaign Service as a modern REST API while maintaining compatibility with existing functionality.