# Redis Queue and Inter-Service Communication Design

## Overview

This document details the Redis Bull MQ architecture for inter-service communication, job processing, and system notifications across all ShoutOut Engage microservices.

## Queue Architecture

### Queue Naming Convention
```
{service}.{operation}.queue
```

### Queue Categories

#### 1. Campaign Service Queues
```typescript
export const CAMPAIGN_QUEUES = {
  // Core campaign operations
  CAMPAIGN_BUILD: 'campaign.build.queue',
  CAMPAIGN_SEND: 'campaign.send.queue',
  CAMPAIGN_SCHEDULE: 'campaign.schedule.queue',
  CAMPAIGN_CANCEL: 'campaign.cancel.queue',
  
  // Analytics and reporting
  CAMPAIGN_ANALYTICS: 'campaign.analytics.queue',
  CAMPAIGN_REPORT: 'campaign.report.queue',
  
  // Segment processing
  SEGMENT_CALCULATE: 'campaign.segment.calculate.queue',
  SEGMENT_REFRESH: 'campaign.segment.refresh.queue',
  
  // A/B testing
  AB_TEST_EVALUATE: 'campaign.abtest.evaluate.queue',
  AB_TEST_WINNER: 'campaign.abtest.winner.queue'
} as const;
```

#### 2. Message Service Queues
```typescript
export const MESSAGE_QUEUES = {
  // Outbound message processing
  MESSAGE_OUTBOUND: 'message.outbound.queue',
  MESSAGE_BULK: 'message.bulk.queue',
  MESSAGE_RETRY: 'message.retry.queue',
  MESSAGE_PRIORITY: 'message.priority.queue',
  
  // Delivery tracking
  DELIVERY_STATUS: 'message.delivery.status.queue',
  DELIVERY_WEBHOOK: 'message.delivery.webhook.queue',
  
  // Provider management
  PROVIDER_HEALTH: 'message.provider.health.queue',
  PROVIDER_FAILOVER: 'message.provider.failover.queue',
  
  // System notifications
  SYSTEM_EMAIL: 'message.system.email.queue',
  SYSTEM_SMS: 'message.system.sms.queue',
  SYSTEM_PUSH: 'message.system.push.queue'
} as const;
```

#### 3. Payment Service Queues
```typescript
export const PAYMENT_QUEUES = {
  // Payment processing
  PAYMENT_PROCESS: 'payment.process.queue',
  PAYMENT_RETRY: 'payment.retry.queue',
  PAYMENT_REFUND: 'payment.refund.queue',
  
  // Subscription management
  SUBSCRIPTION_CREATE: 'payment.subscription.create.queue',
  SUBSCRIPTION_UPDATE: 'payment.subscription.update.queue',
  SUBSCRIPTION_CANCEL: 'payment.subscription.cancel.queue',
  SUBSCRIPTION_RENEWAL: 'payment.subscription.renewal.queue',
  
  // Invoice processing
  INVOICE_GENERATE: 'payment.invoice.generate.queue',
  INVOICE_SEND: 'payment.invoice.send.queue',
  INVOICE_REMINDER: 'payment.invoice.reminder.queue',
  
  // Webhook processing
  STRIPE_WEBHOOK: 'payment.stripe.webhook.queue',
  PAYMENT_WEBHOOK: 'payment.webhook.queue'
} as const;
```

#### 4. Core Service Queues (Enhanced)
```typescript
export const CORE_QUEUES = {
  // Existing queues
  CONTACT_BULK_INSERT: 'contact.bulkInsert.queue',
  EMAIL: 'email',
  
  // New sender ID queues
  SENDER_ID_REVIEW: 'core.senderid.review.queue',
  SENDER_ID_APPROVAL: 'core.senderid.approval.queue',
  ARTIFACT_PROCESS: 'core.artifact.process.queue',
  
  // Phone number management
  PHONE_NUMBER_PURCHASE: 'core.phonenumber.purchase.queue',
  PHONE_NUMBER_RELEASE: 'core.phonenumber.release.queue',
  
  // System maintenance
  DATA_CLEANUP: 'core.data.cleanup.queue',
  BACKUP_PROCESS: 'core.backup.process.queue'
} as const;
```

#### 5. Cross-Service Communication Queues
```typescript
export const CROSS_SERVICE_QUEUES = {
  // Audit and logging
  AUDIT_LOG: 'system.audit.log.queue',
  ERROR_LOG: 'system.error.log.queue',
  
  // Notifications
  NOTIFICATION_EMAIL: 'system.notification.email.queue',
  NOTIFICATION_SMS: 'system.notification.sms.queue',
  NOTIFICATION_PUSH: 'system.notification.push.queue',
  NOTIFICATION_WEBHOOK: 'system.notification.webhook.queue',
  
  // Analytics and reporting
  ANALYTICS_EVENT: 'system.analytics.event.queue',
  REPORT_GENERATE: 'system.report.generate.queue',
  
  // System alerts
  SYSTEM_ALERT: 'system.alert.queue',
  HEALTH_CHECK: 'system.health.check.queue'
} as const;
```

## Queue Configuration

### Priority Levels
```typescript
export const QUEUE_PRIORITIES = {
  CRITICAL: 10,    // System alerts, payment failures
  HIGH: 7,         // Campaign sending, message delivery
  NORMAL: 5,       // Regular operations
  LOW: 3,          // Analytics, reporting
  BACKGROUND: 1    // Cleanup, maintenance
} as const;
```

### Queue Options Configuration
```typescript
export const QUEUE_CONFIGURATIONS = {
  // High-priority, time-sensitive operations
  CRITICAL: {
    removeOnComplete: 50,
    removeOnFail: 100,
    attempts: 5,
    backoff: {
      type: 'exponential' as const,
      delay: 1000,
    },
    delay: 0,
    priority: QUEUE_PRIORITIES.CRITICAL,
    jobId: undefined // Allow Bull to generate unique IDs
  },
  
  // Standard message processing
  MESSAGE_PROCESSING: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential' as const,
      delay: 2000,
    },
    delay: 0,
    priority: QUEUE_PRIORITIES.HIGH,
    rateLimiter: {
      max: 100, // 100 jobs per interval
      duration: 60000 // 1 minute
    }
  },
  
  // Bulk operations
  BULK_PROCESSING: {
    removeOnComplete: 20,
    removeOnFail: 10,
    attempts: 2,
    backoff: {
      type: 'fixed' as const,
      delay: 5000,
    },
    delay: 0,
    priority: QUEUE_PRIORITIES.NORMAL,
    timeout: 300000 // 5 minutes timeout
  },
  
  // Background tasks
  BACKGROUND: {
    removeOnComplete: 10,
    removeOnFail: 5,
    attempts: 1,
    delay: 0,
    priority: QUEUE_PRIORITIES.BACKGROUND,
    timeout: 600000 // 10 minutes timeout
  }
} as const;
```

## Inter-Service Communication Patterns

### 1. Campaign to Message Service Communication

#### Campaign Send Flow
```typescript
// Campaign Service - Campaign Controller
export class CampaignController {
  static async sendCampaign(req: AuthenticatedRequest, res: Response) {
    const { campaignId } = req.params;
    
    // 1. Validate campaign is ready
    const campaign = await CampaignService.validateCampaignForSending(campaignId);
    
    // 2. Queue campaign for sending
    const jobData: CampaignSendJobData = {
      jobId: uuidv4(),
      orgId: req.user.org_id,
      createdBy: req.user.id,
      campaignId: campaignId,
      priority: QUEUE_PRIORITIES.HIGH,
      metadata: {
        estimated_recipients: campaign.stats.total_recipients,
        channels: Object.keys(campaign.channels).filter(ch => campaign.channels[ch].enabled)
      }
    };
    
    await CampaignSendQueue.addJob(jobData);
    
    res.json({
      success: true,
      data: {
        job_id: jobData.jobId,
        status: 'QUEUED',
        estimated_recipients: campaign.stats.total_recipients
      }
    });
  }
}

// Campaign Service - Send Processor
export class CampaignSendProcessor extends BaseJobProcessor {
  async processJob(job: Job<CampaignSendJobData>): Promise<void> {
    const { campaignId, orgId } = job.data;
    
    // 1. Get campaign details
    const campaign = await CampaignModel.findById(campaignId);
    
    // 2. Get target contacts
    const contacts = await this.getTargetContacts(campaign);
    
    // 3. Generate individual messages for each contact and channel
    const messages = await this.generateMessages(campaign, contacts);
    
    // 4. Queue messages to Message Service
    for (const message of messages) {
      const messageJobData: MessageJobData = {
        jobId: uuidv4(),
        orgId: orgId,
        createdBy: campaign.created_by,
        messageId: message.messageId,
        transport: message.transport,
        provider: message.provider,
        from: message.from,
        to: message.to,
        content: message.content,
        subject: message.subject,
        campaignId: campaignId,
        priority: QUEUE_PRIORITIES.HIGH
      };
      
      // Add to message service queue
      await MessageOutboundQueue.addJob(messageJobData);
    }
    
    // 5. Update campaign status
    await CampaignModel.findByIdAndUpdate(campaignId, {
      status: 'SENDING',
      started_at: new Date(),
      'stats.messages_queued': messages.length
    });
  }
}
```

### 2. Message Service to Payment Service Communication

#### Usage Tracking Flow
```typescript
// Message Service - After successful message send
export class MessageProcessor extends BaseJobProcessor {
  async processJob(job: Job<MessageJobData>): Promise<void> {
    const messageData = job.data;
    
    try {
      // 1. Send message via provider
      const result = await ProviderService.sendMessage(messageData);
      
      // 2. Log message
      await MessageLogService.logMessage(messageData, result);
      
      // 3. Queue usage tracking to Payment Service
      const usageData: UsageTrackingJobData = {
        jobId: uuidv4(),
        orgId: messageData.orgId,
        createdBy: messageData.createdBy,
        usage_type: messageData.transport.toLowerCase(),
        quantity: 1,
        cost: result.cost,
        currency: result.currency,
        reference_id: messageData.messageId,
        reference_type: 'MESSAGE',
        metadata: {
          campaign_id: messageData.campaignId,
          provider: messageData.provider,
          transport: messageData.transport
        }
      };
      
      await UsageTrackingQueue.addJob(usageData);
      
    } catch (error) {
      // Handle error and retry logic
      throw error;
    }
  }
}
```

### 3. System Notification Patterns

#### Cross-Service Notification System
```typescript
// Shared Notification Service
export class NotificationService {
  static async sendSystemNotification(notification: SystemNotification): Promise<void> {
    const jobData: SystemNotificationJobData = {
      jobId: uuidv4(),
      orgId: notification.orgId,
      createdBy: 'SYSTEM',
      notification_type: notification.type,
      recipients: notification.recipients,
      template: notification.template,
      data: notification.data,
      priority: notification.priority || QUEUE_PRIORITIES.NORMAL
    };
    
    switch (notification.channel) {
      case 'EMAIL':
        await SystemEmailQueue.addJob(jobData);
        break;
      case 'SMS':
        await SystemSMSQueue.addJob(jobData);
        break;
      case 'PUSH':
        await SystemPushQueue.addJob(jobData);
        break;
      case 'WEBHOOK':
        await SystemWebhookQueue.addJob(jobData);
        break;
    }
  }
  
  // Usage examples:
  static async notifyCampaignCompleted(campaignId: string, orgId: string): Promise<void> {
    await this.sendSystemNotification({
      type: 'CAMPAIGN_COMPLETED',
      channel: 'EMAIL',
      orgId: orgId,
      recipients: ['<EMAIL>'],
      template: 'campaign_completion',
      data: { campaignId },
      priority: QUEUE_PRIORITIES.NORMAL
    });
  }
  
  static async notifyPaymentFailed(subscriptionId: string, orgId: string): Promise<void> {
    await this.sendSystemNotification({
      type: 'PAYMENT_FAILED',
      channel: 'EMAIL',
      orgId: orgId,
      recipients: ['<EMAIL>'],
      template: 'payment_failure',
      data: { subscriptionId },
      priority: QUEUE_PRIORITIES.CRITICAL
    });
  }
}
```

## Queue Monitoring and Health Checks

### Queue Health Monitoring
```typescript
export class QueueHealthMonitor {
  static async checkAllQueues(): Promise<QueueHealthReport> {
    const allQueues = [
      ...Object.values(CAMPAIGN_QUEUES),
      ...Object.values(MESSAGE_QUEUES),
      ...Object.values(PAYMENT_QUEUES),
      ...Object.values(CORE_QUEUES),
      ...Object.values(CROSS_SERVICE_QUEUES)
    ];
    
    const healthChecks = await Promise.all(
      allQueues.map(queueName => this.checkQueueHealth(queueName))
    );
    
    return {
      timestamp: new Date(),
      overall_status: healthChecks.every(check => check.healthy) ? 'HEALTHY' : 'DEGRADED',
      queue_count: allQueues.length,
      healthy_queues: healthChecks.filter(check => check.healthy).length,
      unhealthy_queues: healthChecks.filter(check => !check.healthy),
      details: healthChecks
    };
  }
  
  private static async checkQueueHealth(queueName: string): Promise<QueueHealthCheck> {
    try {
      const queue = QueueManager.getQueue(queueName);
      const stats = await queue.getJobCounts();
      
      const isHealthy = stats.failed < 100 && stats.stalled === 0;
      
      return {
        queue_name: queueName,
        healthy: isHealthy,
        stats: stats,
        last_checked: new Date(),
        issues: isHealthy ? [] : this.identifyIssues(stats)
      };
    } catch (error) {
      return {
        queue_name: queueName,
        healthy: false,
        error: error.message,
        last_checked: new Date(),
        issues: ['QUEUE_UNAVAILABLE']
      };
    }
  }
}
```

This Redis queue and communication design ensures reliable, scalable inter-service communication while maintaining proper separation of concerns and fault tolerance across all microservices.
