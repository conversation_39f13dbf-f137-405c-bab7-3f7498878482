# ShoutOUT Engage Service Guidelines

This document provides guidelines and information for working with the ShoutOUT Engage service codebase.

## Project Structure

The ShoutOUT Engage service follows a modular architecture with clear separation of concerns:

- **app.ts**: Main application entry point
- **bin/**: Server startup scripts
- **config/**: Configuration files
- **handlers/**: Business logic handlers (e.g., ContactsHandler)
- **lib/**: Utility libraries and core functionality
  - **db/**: Database-related code
    - **dao/**: Data Access Objects (e.g., ContactDAO)
    - **models/**: Database models (e.g., contact.model)
  - **errors/**: Error handling utilities
    - **app-error.ts**: Base error class
    - **error-types.ts**: Specialized error classes
    - **error-handler.ts**: Centralized error handler
  - **middlewares/**: Express middlewares
  - **utils/**: Utility functions
- **routes/**: API route definitions with Swagger documentation
- **services/**: Service classes for external integrations
- **tests/**: Test files
  - **unit/**: Unit tests
  - **integration/**: Integration tests
  - **fixtures/**: Test data fixtures
  - **utils/**: Test utilities
- **types/**: TypeScript type definitions
- **validators/**: Request validation logic

## Code Patterns and Conventions

### Error Handling

The project uses a centralized error handling approach with a hierarchy of error classes:

1. **Base Error Class**: `AppError` extends the built-in Error class and adds:
   - HTTP status code
   - Error code for identifying the error type
   - Operational status flag (expected vs. unexpected errors)
   - Optional details for field-specific errors

2. **Specialized Error Classes**:
   - `ValidationError` (422 Unprocessable Entity)
   - `BadRequestError` (400 Bad Request)
   - `DuplicateError` (409 Conflict)
   - `NotFoundError` (404 Not Found)
   - `DatabaseError` (500 Internal Server Error)
   - `OrganizationError` (400 Bad Request)
   - `UnauthorizedError` (401 Unauthorized)
   - `ForbiddenError` (403 Forbidden)
   - `InternalError` (500 Internal Server Error)

3. **Centralized Error Handler**: The `handleError` function in `error-handler.ts` processes errors and sends appropriate responses.

4. **Usage Pattern**:
   ```typescript
   try {
     // Code that might throw an error
   } catch (error) {
     // Either use the centralized error handler
     handleError(error, res);
     // Or throw a specific error to be handled by middleware
     throw new ValidationError('Validation failed', 'VALIDATION_001');
   }
   ```

5. **Type-Safe Error Handling**:
   - Always use type assertions or type guards when handling caught errors
   - For unknown error types, use proper type narrowing:
   ```typescript
   try {
     // Code that might throw an error
   } catch (error: unknown) {
     // Type guard to check if error is an instance of Error
     if (error instanceof Error) {
       // Now TypeScript knows error is of type Error
       console.error(error.message);
       
       // Check for specific error types
       if (error instanceof NotFoundError || error instanceof BadRequestError) {
         throw error;
       }
     }
     
     // Handle generic unknown errors
     throw new DatabaseError(
       'Operation failed',
       'DB_001',
       error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
     );
   }
   ```

6. **Error Propagation**:
   - When catching errors from lower layers, preserve the original error message when possible:
   ```typescript
   try {
     // Database operation
   } catch (error: unknown) {
     // Preserve original error message if available
     throw new DatabaseError(
       error instanceof Error ? error.message : 'Database operation failed',
       'DB_001',
       error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
     );
   }
   ```

7. **Handling External Errors**:
   - For external errors (e.g., MongoDB, Mongoose, Prisma), use error.name checks when instanceof is not applicable:
   ```typescript
   try {
     // MongoDB operation
   } catch (error: unknown) {
     // Check for MongoDB-specific errors
     if (error instanceof Error) {
       if (error.name === 'MongoError' && 'code' in error && error.code === 11000) {
         throw new DuplicateError(
           'Resource already exists',
           'DUPLICATE_001'
         );
       }
       
       if (error.name === 'ValidationError' && 'errors' in error) {
         const details = Object.keys(error.errors).map(field => ({
           field,
           message: error.errors[field].message
         }));
         
         throw new ValidationError(
           'Validation failed',
           'VALIDATION_001',
           details
         );
       }
     }
     
     // Handle generic errors
     throw new DatabaseError(
       error instanceof Error ? error.message : 'Database operation failed',
       'DB_001'
     );
   }
   ```

8. **Layer-Specific Error Handling**:
   - **DAO Layer**: Throw specific errors (BadRequestError, NotFoundError, DuplicateError) for known error conditions, and DatabaseError for unknown errors
   ```typescript
   // Example from ContactDAO.getContactById
   try {
     // Validate input
     if (!ObjectId.isValid(contactId)) {
       throw new BadRequestError(
         'Invalid contact ID format',
         'CONTACT_002',
         [{ field: 'contactId', message: 'Contact ID must be a valid MongoDB ObjectId' }]
       );
     }
     
     // Database operation
     const contact = await ContactModel.findOne({
       _id: new ObjectId(contactId),
       org_id: organizationId
     });
     
     // Check result
     if (!contact) {
       throw new NotFoundError(
         `Contact with ID ${contactId} not found in organization ${organizationId}`,
         'CONTACT_003'
       );
     }
     
     return contact;
   } catch (error: unknown) {
     // Rethrow specific errors
     if (error instanceof Error && 
         (error.name === 'NotFoundError' || error.name === 'BadRequestError')) {
       throw error;
     }
     
     // Convert unknown errors to DatabaseError
     throw new DatabaseError(
       error instanceof Error ? error.message : `Failed to retrieve contact`,
       'DB_004',
       error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
     );
   }
   ```
   
   - **Handler Layer**: Handle errors from DAO layer, add business logic errors, and use the centralized error handler
   ```typescript
   // Example from ContactsHandler.getContactById
   try {
     // Extract and validate input
     if (!req.organizationId) {
       throw new OrganizationError('Organization context is required', 'ORG_001');
     }
     
     try {
       // Call DAO method
       const contact = await ContactDAO.getContactById(
         req.organizationId,
         contactId
       );
       
       // Format response
       const formattedContact = ContactDAO.formatContactResponse(contact);
       
       // Return success response
       res.status(200).json(formattedContact);
     } catch (dbError: unknown) {
       // Rethrow specific errors from DAO layer
       if (dbError instanceof Error && 
           (dbError instanceof NotFoundError || dbError instanceof BadRequestError)) {
         throw dbError;
       }
       
       // Convert unknown errors to DatabaseError
       throw new DatabaseError('Failed to retrieve contact', 'DB_001');
     }
   } catch (error) {
     // Use centralized error handler
     handleError(error, res);
   }
   ```
   
   - **Route Layer**: Use middleware for validation and pass errors to the next function
   ```typescript
   // Example from contacts.ts
   router.get('/:id', 
     supabaseAuthMiddleware, 
     ContactsValidator.validateGetContactByIdMiddleware, 
     async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
       try {
         await ContactsHandler.getContactById(req, res);
       } catch (error) {
         next(error);
       }
     }
   );
   ```

### API Design

The API follows RESTful principles with these patterns:

1. **Route Definition**: Routes are defined in the `routes/` directory with Swagger documentation.

2. **Middleware Pattern**: Routes use middleware for:
   - Authentication (supabaseAuthMiddleware)
   - Validation (e.g., ContactsValidator.validateGetContactsMiddleware)
   - Error handling (try/catch with next(error))

3. **Handler Pattern**: Business logic is implemented in handler classes (e.g., ContactsHandler).

4. **DAO Pattern**: Data access is abstracted in DAO classes (e.g., ContactDAO).

5. **Swagger Documentation**: APIs are documented using Swagger/OpenAPI annotations.

### TypeScript Usage

The project uses TypeScript for type safety with these conventions:

1. **Interface-based Types**: Types are defined as interfaces in the `types/` directory.

2. **Explicit Type Annotations**: Function parameters and return types are explicitly annotated.

3. **Type Guards**: Type guards are used to narrow types in conditional blocks.
   ```typescript
   // Example of a type guard for error handling
   if (error instanceof Error) {
     // TypeScript now knows error is of type Error
     console.error(error.message);
   }
   
   // Example of a custom type guard
   function isAppError(error: unknown): error is AppError {
     return error instanceof Error && 'statusCode' in error && 'errorCode' in error;
   }
   
   if (isAppError(error)) {
     // TypeScript now knows error is of type AppError
     console.error(`${error.errorCode}: ${error.message}`);
   }
   ```

4. **Generics**: Generics are used for reusable type patterns.

5. **Handling Unknown Types**:
   - Always use `unknown` instead of `any` for variables with uncertain types
   - Use type assertions only when you're certain of the type
   - Prefer type guards over type assertions for safer code
   ```typescript
   // Avoid this:
   function handleError(error: any) {
     console.error(error.message); // Unsafe, error might not have a message property
   }
   
   // Do this instead:
   function handleError(error: unknown) {
     if (error instanceof Error) {
       console.error(error.message); // Safe, TypeScript knows error has a message property
     } else {
       console.error('Unknown error occurred');
     }
   }
   ```

6. **Import All Required Types**:
   - Always import all error types that you're checking for
   ```typescript
   // Import all error types you need to check
   import {
     DatabaseError,
     NotFoundError,
     BadRequestError
   } from '../lib/errors/error-types';
   
   try {
     // Code that might throw errors
   } catch (error: unknown) {
     // Now you can safely check for these error types
     if (error instanceof NotFoundError || error instanceof BadRequestError) {
       throw error;
     }
     // ...
   }
   ```

## Testing

The project uses Jest for testing with these practices:

1. **Test Organization**:
   - Unit tests in `tests/unit/`
   - Integration tests in `tests/integration/`
   - Tests are organized by component (handlers, validators, dao, etc.)

2. **Mocking**:
   - Dependencies are mocked using Jest's mocking capabilities
   - Mock implementations are provided for complex dependencies
   - Use `mockImplementation` instead of `mockRejectedValue` for throwing errors:
   ```typescript
   // Prefer this:
   mockFunction.mockImplementation(() => {
     throw new NotFoundError('Resource not found', 'NOT_FOUND_001');
   });
   
   // Over this:
   mockFunction.mockRejectedValue({
     name: 'NotFoundError',
     message: 'Resource not found',
     errorCode: 'NOT_FOUND_001'
   });
   ```

3. **Test Helpers**:
   - MockResponseHelper for creating mock Express request and response objects
   - Fixtures for test data
   - When using MockResponseHelper, ensure you're passing parameters in the correct order:
   ```typescript
   // For routes with path parameters
   const reqPath = MockResponseHelper.createMockRequest({}, {
     id: '507f1f77bcf86cd799439011' // Path params go in the second argument
   });
   
   // For routes with query parameters
   const reqQuery = MockResponseHelper.createMockRequest({}, {}, {
     page: 1,
     page_size: 20 // Query params go in the third argument
   });
   ```

4. **Test Coverage**:
   - Input validation
   - Business logic
   - Error handling
   - Response formatting
   - Edge cases

5. **Error Testing**:
   - Test for exact error messages when possible
   - Use `expect.stringContaining()` for partial message matching when full messages might change
   - Ensure error codes are consistent and tested
   ```typescript
   // Testing for exact error message
   await expect(functionThatThrows()).rejects.toThrow('Exact error message');
   
   // Testing for partial error message
   await expect(functionThatThrows()).rejects.toThrow(expect.stringContaining('partial message'));
   
   // Testing for error code
   try {
     await functionThatThrows();
     fail('Expected function to throw');
   } catch (error) {
     expect(error).toBeInstanceOf(ValidationError);
     expect(error.errorCode).toBe('VALIDATION_001');
   }
   ```

6. **Database Testing**:
   - Use MongoDB Memory Server for database tests
   - Ensure proper cleanup after tests
   - Prevent open handles by properly managing database connections:
   ```typescript
   // In your test setup file (e.g., tests/setup.ts)
   
   // Setup MongoDB Memory Server before all tests
   beforeAll(async () => {
     mongoServer = await MongoMemoryServer.create();
     const mongoUri = mongoServer.getUri();
     await mongoose.connect(mongoUri);
     
     // Create indexes explicitly for tests
     await YourModel.createIndexes();
   });
   
   // Clean up after each test
   afterEach(async () => {
     const collections = mongoose.connection.collections;
     for (const key in collections) {
       await collections[key].deleteMany({});
     }
   });
   
   // Cleanup after all tests
   afterAll(async () => {
     await mongoose.connection.dropDatabase();
     await mongoose.connection.close();
     await mongoServer.stop();
   });
   ```

7. **Integration Testing**:
   - Use a shared setup function for Express app configuration to avoid duplication
   - Centralize middleware, route, and error handler setup
   - Use descriptive test organization with nested describe blocks
   - Add comments to explain test-specific setup
   ```typescript
   /**
    * Setup function to create and configure the Express app for testing
    * 
    * This function centralizes the Express app setup for all tests to avoid duplication.
    * It configures:
    * - Express middleware
    * - Authentication middleware
    * - Routes
    * - Error handling middleware
    * 
    * @returns Configured Express application
    */
   function setupTestApp(): Application {
     const app = express();
     // ... app setup code
     return app;
   }
   
   // Shared variables for all tests
   let app: Application;
   
   // Setup once before all tests
   beforeAll(() => {
     app = setupTestApp();
   });
   
   describe('POST /endpoint Tests', () => {
     // Test-specific setup
     beforeEach(() => {
       // Setup specific to these tests
     });
     
     // Test cases...
   });
   ```

### Running Tests

To run tests, use the following commands:

- `npm test`: Run all tests
- `npm run test:unit`: Run only unit tests
- `npm run test:integration`: Run only integration tests
- `npm run test:coverage`: Run tests with coverage report
- `npm run test:watch`: Run tests in watch mode

## Build and Run

To build and run the project, use the following commands:

- `npm run build`: Compile TypeScript to JavaScript
- `npm start`: Start the production server
- `npm run dev`: Start the development server with hot reloading
- `npm run type-check`: Check TypeScript types without emitting files

## Code Style and Conventions

1. **Error Codes**: Error codes follow a pattern of `[DOMAIN]_[NUMBER]`, e.g., `CONTACT_001`.

2. **Function Documentation**: Functions are documented with JSDoc comments that include:
   - Description
   - Parameter descriptions
   - Return value description
   - Thrown exceptions

3. **File Organization**: Files are organized by functionality, with related code grouped together.

4. **Naming Conventions**:
   - PascalCase for classes and interfaces
   - camelCase for variables and functions
   - UPPER_CASE for constants

5. **MongoDB Models**:
   - Use the `ensureIndexes` utility for robust index management:
   ```typescript
   // Import the utility
   import { ensureIndexes } from '../../utils/db/indexUtils';
   
   // Define your schema
   const userSchema = new Schema({
     email: { type: String, required: true, index: true },
     // ... other fields
   });
   
   // Create the model
   export const UserModel = model<UserDocument>('User', userSchema);
   
   // Ensure indexes are created (only in non-test environments)
   ensureIndexes(UserModel, userSchema, 'User');
   ```
   
   The `ensureIndexes` utility provides several advantages over direct index creation:
   
   - **Prevents duplicate index creation**: Checks if indexes already exist before creating them
   - **Handles connection issues gracefully**: Properly checks for MongoDB connection availability
   - **Improves logging**: Uses the application's logging system instead of console.log
   - **Reduces test environment issues**: Automatically skips index creation in test environments
   - **Type-safe implementation**: Includes proper TypeScript null/undefined checks
   
   The utility is located in `lib/utils/db/indexUtils.ts` and works by:
   1. Checking if the application is in a test environment (skips if true)
   2. Waiting for the MongoDB connection to be established
   3. Checking if the collection already exists
   4. If the collection exists, checking if all required indexes are already created
   5. Creating indexes only when necessary
   
   This approach prevents unnecessary index creation operations and avoids potential issues with duplicate indexes.

## Logging System

The project uses Pino for logging with a specialized implementation to prevent open handles in Jest tests:

1. **Logger Configuration**:
   - The logger is configured differently in test vs. non-test environments
   - Use the `logger` function to create logger instances:
   ```typescript
   import { logger } from '../lib/logger';
   import config from '../lib/config';
   
   const log = logger(config.logger);
   
   // Use the logger
   log.info('This is an info message');
   log.error('This is an error message', { details: 'Additional information' });
   ```

2. **LoggerManager**:
   - The `LoggerManager` class tracks all logger instances and their transports
   - It provides methods to properly close loggers and prevent open handles
   - You don't need to interact with this class directly in most cases

3. **Test Environment Handling**:
   - In test environments, the logger uses a simpler configuration without transports
   - This prevents worker threads that could cause open handles in Jest
   - Always call `closeLoggers()` in the `afterAll` hook of your Jest tests:
   ```typescript
   // In your test setup file (e.g., tests/setup.ts)
   import { closeLoggers } from '../lib/logger';
   
   // Other test setup...
   
   afterAll(async () => {
     // Other cleanup...
     await closeLoggers(); // This prevents open handles from loggers
   }, 30000); // Increase timeout for cleanup operations
   ```

4. **Preventing Open Handles**:
   - Open handles in Jest are often caused by unclosed resources like transports
   - The `closeLoggers()` function properly flushes all loggers and closes their transports
   - If you see Jest reporting open handles related to worker threads, ensure you're calling `closeLoggers()`
   - To debug open handles, run tests with the `--detectOpenHandles` flag:
   ```bash
   npm test -- --detectOpenHandles
   ```

5. **Custom Logger Implementation**:
   - If you need to create a custom logger, follow the pattern in `lib/logger.ts`
   - Always track instances and provide cleanup methods
   - Use environment detection to adjust behavior in test environments

## Working with the Codebase

When making changes to the codebase, follow these guidelines:

1. **Error Handling**: Use the centralized error handling approach with appropriate error classes.

2. **API Documentation**: Update Swagger documentation when changing API endpoints.

3. **Testing**: Write tests for new functionality and update existing tests when changing behavior.

4. **Type Safety**: Maintain type safety by using TypeScript features appropriately.

5. **Validation**: Validate input data using Joi schemas in the validators.

6. **Separation of Concerns**: Maintain the separation between routes, handlers, validators, and DAOs.

7. **Logging**: Use the provided logger system and ensure proper cleanup in tests.