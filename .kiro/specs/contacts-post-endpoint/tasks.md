# Implementation Plan

- [x] 1. Create global type definitions and enums
  - Create types/ directory structure with contact.types.ts, tag.types.ts, and activity.types.ts
  - Define ContactStatus, TagType, and ContactEventType enums
  - Define Contact, ContactTag, Tag, and ContactActivity interfaces
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

- [x] 2. Implement database schemas and models
  - [x] 2.1 Create contact.model.ts with Mongoose schema
    - Implement Contact schema with all required and optional fields
    - Add pre-save hooks for timestamps and validation
    - Create compound indexes for performance optimization
    - _Requirements: 2.1, 2.2, 2.5_

  - [x] 2.2 Create embedded tag schema
    - Implement tag.schema.ts in lib/db/models/embedded/ directory
    - Define ContactTag embedded schema structure
    - Add validation for tag fields
    - _Requirements: 2.3_

  - [x] 2.3 Create contact activity schema
    - Implement contact.activity.schema.ts with activity tracking fields
    - Add indexes for efficient querying by contact and organization
    - Define activity event type validation
    - _Requirements: 2.4_

- [x] 3. Create request validation layer
  - Implement ContactsValidator.ts in validators/ directory
  - Create Joi validation schema for contact creation requests
  - Validate required fields (name, email, phone) and optional fields
  - Implement email format and phone number validation
  - Add validation for tags array and additional_fields object
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 4. Implement data access layer
  - Create ContactDAO.ts in lib/db/dao/ directory
  - Implement createContact method with MongoDB operations
  - Add duplicate contact checking by email within organization
  - Handle ObjectId generation and database error scenarios
  - Implement proper error handling and logging
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 5. Set up testing framework and infrastructure
  - Install and configure Jest testing framework with TypeScript support
  - Set up MongoDB Memory Server for database testing
  - Configure test scripts in package.json
  - Create test directory structure and configuration files
  - Set up test utilities and mocking helpers
  - Add test coverage reporting
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 6. Update authentication middleware with organization context
  - Extend existing supabaseAuthMiddleware to fetch organization_uuid from profiles table
  - Attach organization ID to AuthenticatedRequest interface for all authenticated requests
  - Handle cases where user has no organization association gracefully
  - Add proper logging for organization context extraction
  - _Requirements: 5.1, 5.4, 5.5_

- [x] 7. Create business logic handler
  - Implement ContactsHandler.ts in handlers/ directory
  - Create static createContact method for business logic
  - Use organization ID from authentication middleware context
  - Integrate with ContactDAO for data operations
  - Handle response formatting and error scenarios
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 8. Update contacts route with POST endpoint
  - Add POST endpoint to existing contacts.ts route file
  - Integrate validation middleware at router level (auth middleware already provides organization context)
  - Add comprehensive Swagger documentation for the endpoint
  - Route requests to ContactsHandler.createContact method
  - Handle HTTP status codes and error responses
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 9. Write comprehensive unit tests
  - [x] 9.1 Create validation tests
    - Test valid contact creation payloads
    - Test validation failures for missing required fields
    - Test email and phone format validation
    - Test additional_fields and tags validation
    - _Requirements: 7.2_

  - [ ] 9.2 Create authentication middleware tests
    - Test successful organization extraction from user profile in auth middleware
    - Test error handling when user has no organization
    - Test error handling for database lookup failures
    - Test proper attachment of organization ID to request context
    - _Requirements: 5.4, 5.5_

  - [x] 9.3 Create handler unit tests
    - Test successful contact creation flow with mocked dependencies
    - Test duplicate contact handling scenarios
    - Test error handling and response formatting
    - Test business logic validation with organization context
    - _Requirements: 7.3_

  - [x] 9.4 Create DAO unit tests
    - Test database operations with MongoDB memory server
    - Test ObjectId generation and validation
    - Test error handling for database failures
    - Test duplicate detection logic
    - _Requirements: 7.4_

- [x] 10. Write integration tests
  - Create API endpoint integration tests
  - Test complete request/response flow from route to database
  - Test authentication middleware integration
  - Test organization context extraction within auth middleware
  - Test validation middleware integration with actual payloads
  - Test error response formatting and HTTP status codes
  - _Requirements: 7.1, 7.5_