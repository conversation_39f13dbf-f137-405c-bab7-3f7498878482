# Design Document

## Overview

This design implements a comprehensive contacts POST endpoint for the ShoutOUT Engage service. The solution follows a layered architecture pattern with clear separation of concerns: routing, validation, business logic, and data access. The implementation leverages the existing Express.js framework, MongoDB with Mongoose, and follows established patterns in the codebase.

The design emphasizes type safety, validation, error handling, and maintainability while providing a robust foundation for contact management functionality.

## Architecture

### High-Level Architecture

```mermaid
graph TD
    A[Client Request] --> B[Express Router]
    B --> C[Validation Middleware]
    C --> D[ContactsHandler]
    D --> E[ContactDAO]
    E --> F[MongoDB]
    
    G[Types/Enums] --> C
    G --> D
    G --> E
    
    H[Swagger Documentation] --> B
```

### Directory Structure

```
shoutout_engage_service/
├── types/                          # Global type definitions
│   ├── contact.types.ts            # Contact-related types
│   ├── tag.types.ts                # Tag-related types
│   └── activity.types.ts           # Activity-related types
├── validators/                     # Request validation
│   └── ContactsValidator.ts        # Contact validation schemas
├── handlers/                       # Business logic handlers
│   └── ContactsHandler.ts          # Contact business logic
├── lib/db/
│   ├── models/                     # Database schemas
│   │   ├── contact.model.ts        # Contact schema
│   │   └── embedded/
│   │       └── tag.schema.ts       # Tag schema
│   ├── dao/                        # Data access objects
│   │   └── ContactDAO.ts           # Contact data operations
│   └── models/
│       └── contact.activity.schema.ts # Activity schema
└── routes/
    └── contacts.ts                 # Updated with POST endpoint
```

## Components and Interfaces

### 1. Type Definitions (types/)

**contact.types.ts**
- Defines `ContactStatus` enum: 'active' | 'archived' | 'deleted' | 'forgotten'
- Defines `ContactTag` interface for embedded tags
- Defines `Contact` interface with all required and optional fields

**tag.types.ts**
- Defines `TagType` enum: 'user' | 'system'
- Defines `Tag` interface for standalone tag documents

**activity.types.ts**
- Defines `ContactEventType` enum for activity tracking
- Defines `ContactActivity` interface for activity logging

### 2. Database Models (lib/db/models/)

**contact.model.ts**
- Implements MongoDB schema using Mongoose
- Includes all fields from Contact interface
- Defines compound indexes for performance optimization
- Implements pre-save hooks for timestamps and validation

**embedded/tag.schema.ts**
- Defines embedded tag schema for contact tags
- Implements validation for tag structure

**contact.activity.schema.ts**
- Defines activity tracking schema
- Implements indexes for efficient querying by contact and organization

### 3. Validation Layer (validators/)

**ContactsValidator.ts**
- Implements Joi validation schemas for contact creation
- Validates required fields: org_id, created_by, name, email, phone
- Validates optional fields with appropriate constraints
- Implements email format validation
- Implements phone number validation
- Validates tag structure and additional fields

### 4. Business Logic Layer (handlers/)

**ContactsHandler.ts**
- Implements static `createContact` method
- Handles business logic for contact creation
- Manages error handling and response formatting
- Coordinates with DAO layer for data operations
- Implements duplicate detection logic

### 5. Data Access Layer (lib/db/dao/)

**ContactDAO.ts**
- Implements MongoDB operations using Mongoose
- Provides `createContact` method for database insertion
- Handles ObjectId generation and validation
- Implements error handling for database operations
- Provides methods for duplicate checking

### 6. API Layer (routes/)

**contacts.ts (updated)**
- Adds POST endpoint with Swagger documentation
- Implements validation middleware integration
- Routes requests to ContactsHandler
- Handles HTTP status codes and responses

## Data Models

### Contact Schema

```typescript
interface Contact {
  _id: ObjectId;
  org_id: string;           // Organization identifier
  created_by: string;       // User who created the contact
  name: string;             // Contact name (required)
  email: string;            // Contact email (required, validated)
  phone: string;            // Contact phone (required)
  country?: string;         // Optional country name
  country_code?: string;    // Optional country code (ISO)
  avatar_url?: string;      // Optional profile image URL
  tags: ContactTag[];       // Array of embedded tags
  additional_fields: {      // Flexible key-value storage
    [key: string]: string | number | boolean;
  };
  status: ContactStatus;    // Contact status enum
  created_at: Date;         // Auto-generated timestamp
  updated_at: Date;         // Auto-updated timestamp
}
```

### Database Indexes

```javascript
// Performance optimization indexes
db.contacts.createIndex({ org_id: 1, "tags.tag_name": 1 });
db.contacts.createIndex({ org_id: 1, email: 1 }, { unique: true });
db.contacts.createIndex({ org_id: 1, status: 1 });
db.contacts.createIndex({ created_at: -1 });
```

### Tag Schema (Embedded)

```typescript
interface ContactTag {
  tag_id: ObjectId;         // Reference to Tag collection
  tag_name: string;         // Denormalized tag name for performance
}
```

### Activity Schema

```typescript
interface ContactActivity {
  _id: ObjectId;
  org_id: string;           // Organization identifier
  contact_id: ObjectId;     // Reference to Contact
  event_type: ContactEventType; // Type of activity
  campaign_id?: string;     // Optional campaign reference
  campaign_name?: string;   // Optional campaign name
  metadata?: Record<string, any>; // Flexible metadata storage
  created_at: Date;         // Activity timestamp
}
```

## Error Handling

### Validation Errors
- **400 Bad Request**: Invalid input data with detailed field-level errors
- **422 Unprocessable Entity**: Business logic validation failures

### Authentication Errors
- **401 Unauthorized**: Missing or invalid authentication token
- **403 Forbidden**: Insufficient permissions for organization

### Database Errors
- **409 Conflict**: Duplicate contact (same email within organization)
- **500 Internal Server Error**: Database connection or operation failures

### Error Response Format

```typescript
interface ErrorResponse {
  error: string;            // Human-readable error message
  errorCode?: string;       // Application-specific error code
  details?: {               // Validation error details
    field: string;
    message: string;
  }[];
}
```

## Testing Strategy

### Unit Tests

**Validation Tests**
- Test valid contact creation payloads
- Test invalid payloads with missing required fields
- Test email format validation
- Test phone number validation
- Test additional fields validation

**Handler Tests**
- Test successful contact creation flow
- Test duplicate contact handling
- Test error scenarios with mocked dependencies
- Test business logic validation

**DAO Tests**
- Test database operations with MongoDB memory server
- Test index creation and performance
- Test error handling for database failures
- Test ObjectId generation and validation

### Integration Tests

**API Endpoint Tests**
- Test complete request/response flow
- Test authentication middleware integration
- Test validation middleware integration
- Test error response formatting

**Database Integration Tests**
- Test with actual MongoDB instance
- Test index performance with sample data
- Test concurrent contact creation scenarios

### Test Data Management

**Test Fixtures**
- Valid contact creation payloads
- Invalid payloads for validation testing
- Mock organization and user data
- Sample tag and activity data

## API Documentation

### Swagger Schema Definitions

```yaml
components:
  schemas:
    ContactCreateRequest:
      type: object
      required:
        - name
        - email
        - phone
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
        email:
          type: string
          format: email
        phone:
          type: string
          pattern: '^[+]?[0-9\s\-\(\)]+$'
        country:
          type: string
          maxLength: 100
        country_code:
          type: string
          pattern: '^[A-Z]{2}$'
        avatar_url:
          type: string
          format: uri
        tags:
          type: array
          items:
            $ref: '#/components/schemas/ContactTag'
        additional_fields:
          type: object
          additionalProperties:
            oneOf:
              - type: string
              - type: number
              - type: boolean
    
    ContactResponse:
      type: object
      properties:
        _id:
          type: string
        org_id:
          type: string
        created_by:
          type: string
        name:
          type: string
        email:
          type: string
        phone:
          type: string
        country:
          type: string
        country_code:
          type: string
        avatar_url:
          type: string
        tags:
          type: array
          items:
            $ref: '#/components/schemas/ContactTag'
        additional_fields:
          type: object
        status:
          type: string
          enum: [active, archived, deleted, forgotten]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
```

### Endpoint Documentation

```yaml
/contacts:
  post:
    summary: Create a new contact
    description: Creates a new contact within the authenticated user's organization
    tags:
      - Contacts
    security:
      - Token: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ContactCreateRequest'
    responses:
      201:
        description: Contact created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactResponse'
      400:
        description: Invalid request data
      401:
        description: Unauthorized
      409:
        description: Contact already exists
      500:
        description: Internal server error
```

## Security Considerations

### Authentication
- Supabase JWT token validation via existing middleware
- Organization-level access control
- User identification for audit trails

### Data Validation
- Input sanitization to prevent injection attacks
- Email format validation to prevent malformed data
- Phone number format validation
- URL validation for avatar_url field

### Data Privacy
- No sensitive data logging in production
- Proper error message sanitization
- Audit trail for contact creation activities

## Performance Considerations

### Database Optimization
- Compound indexes for common query patterns
- Efficient tag querying with embedded structure
- Pagination support for future list endpoints

### Caching Strategy
- Redis caching for frequently accessed contact data
- Tag name caching for performance optimization
- Organization-level caching considerations

### Scalability
- Horizontal scaling support with stateless handlers
- Database connection pooling optimization
- Async/await patterns for non-blocking operations