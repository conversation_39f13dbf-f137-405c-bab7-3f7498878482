# Design Document

## Overview

The CSV Header Extraction feature provides a secure endpoint for authenticated users to upload CSV/Excel files and extract their headers for validation against the contact schema. This feature enables users to preview and validate their data structure before importing contacts, ensuring data integrity and providing a better user experience.

The system validates file format, size, and structure constraints, then processes the file to extract headers and returns both the CSV headers and available contact schema fields in a structured format for frontend consumption.

## Architecture

### High-Level Flow
1. **Authentication**: Request passes through Supabase authentication middleware
2. **File Upload**: Multipart form data handling with file validation
3. **File Processing**: CSV/Excel parsing and header extraction via CSV service
4. **Schema Retrieval**: Contact model field extraction
5. **Response Formation**: Structured JSON response with headers and schema

### Component Interaction
```mermaid
sequenceDiagram
    participant Client
    participant Router
    participant Auth
    participant Validator
    participant Handler
    participant CSVService
    participant ContactModel
    
    Client->>Router: POST /contacts/csv-headers (multipart/form-data)
    Router->>Auth: Authenticate request
    Auth-->>Router: User context
    Router->>Validator: Validate file constraints
    Validator-->>Router: Validation result
    Router->>Handler: Process file
    Handler->>CSVService: Extract headers
    CSVService-->>Handler: CSV headers array
    Handler->>ContactModel: Get schema fields
    ContactModel-->>Handler: Schema fields
    Handler-->>Router: Combined response
    Router-->>Client: JSON response
```

## Components and Interfaces

### 1. Route Definition
- **Endpoint**: `POST /contacts/csv-headers`
- **Content-Type**: `multipart/form-data`
- **Authentication**: Required (Supabase middleware)
- **File Field**: `file` (single file upload)

### 2. File Validation Middleware
```typescript
interface FileValidationRules {
  allowedExtensions: string[];
  maxSizeBytes: number;
  maxRows: number;
  requireHeaders: boolean;
  singleSheetOnly: boolean;
}
```

### 3. CSV Service Interface
```typescript
interface CSVService {
  extractHeaders(file: Express.Multer.File): Promise<string[]>;
  validateFileStructure(file: Express.Multer.File): Promise<ValidationResult>;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  rowCount?: number;
  sheetCount?: number;
}
```

### 4. Contact Schema Extractor
```typescript
interface ContactSchemaField {
  name: string;
  type: string;
  required: boolean;
  description?: string;
}

interface SchemaExtractor {
  getContactFields(): ContactSchemaField[];
}
```

### 5. Response Interface
```typescript
interface CSVHeaderExtractionResponse {
  success: boolean;
  data: {
    csvHeaders: string[];
    contactSchema: ContactSchemaField[];
    fileInfo: {
      name: string;
      size: number;
      rowCount: number;
    };
  };
}
```

## Data Models

### File Upload Constraints
```typescript
const FILE_CONSTRAINTS = {
  allowedExtensions: ['.csv', '.xlsx', '.xls'],
  maxSizeBytes: 25 * 1024 * 1024, // 25MB
  maxRows: 400000,
  requireHeaders: true,
  singleSheetOnly: true
};
```

### Contact Schema Fields
Based on the existing contact model, the following fields will be extracted:
- `name` (string, required)
- `email` (string, required)
- `phone` (string, required)
- `country` (string, optional)
- `country_code` (string, optional)
- `avatar_url` (string, optional)
- `additional_fields` (object, optional)

## Error Handling

### Validation Errors
```typescript
interface FileValidationError {
  code: string;
  message: string;
  field?: string;
}

const ERROR_CODES = {
  INVALID_EXTENSION: 'FILE_001',
  FILE_TOO_LARGE: 'FILE_002',
  TOO_MANY_ROWS: 'FILE_003',
  MULTIPLE_SHEETS: 'FILE_004',
  NO_HEADERS: 'FILE_005',
  PROCESSING_ERROR: 'FILE_006'
};
```

### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  error: string;
  errorCode: string;
  details?: FileValidationError[];
}
```

## Testing Strategy

### Unit Tests
1. **File Validation Tests**
   - Valid file extensions
   - File size limits
   - Row count limits
   - Header presence validation

2. **CSV Service Tests**
   - Header extraction from CSV files
   - Header extraction from Excel files
   - Error handling for corrupted files

3. **Schema Extraction Tests**
   - Contact model field extraction
   - Field type mapping
   - Required field identification

### Integration Tests
1. **End-to-End Upload Tests**
   - Complete file upload and processing flow
   - Authentication integration
   - Response format validation

2. **Error Scenario Tests**
   - Invalid file format handling
   - Oversized file handling
   - Authentication failure scenarios

### Test Data
- Sample CSV files with various structures
- Excel files with single and multiple sheets
- Files exceeding size and row limits
- Corrupted/invalid files

## Implementation Dependencies

### Required Packages
- `multer`: File upload handling
- `csv-parser` or `fast-csv`: CSV file processing
- `xlsx`: Excel file processing
- `file-type`: File type detection

### File Structure
```
shoutout_engage_service/
├── routes/
│   └── contacts.ts (extend existing)
├── handlers/
│   └── ContactsHandler.ts (extend existing)
├── services/
│   └── csv.service.ts (new)
├── validators/
│   └── FileValidator.ts (new)
├── lib/
│   └── utils/
│       └── schemaExtractor.ts (new)
└── types/
    └── file.types.ts (new)
```

## Security Considerations

1. **File Type Validation**: Strict MIME type and extension checking
2. **File Size Limits**: Prevent DoS attacks through large file uploads
3. **Temporary File Cleanup**: Ensure uploaded files are cleaned up after processing
4. **Authentication**: All requests must be authenticated
5. **Input Sanitization**: Sanitize extracted headers before returning to client

## Performance Considerations

1. **Streaming Processing**: Use streaming for large file processing
2. **Memory Management**: Avoid loading entire files into memory
3. **Timeout Handling**: Set appropriate timeouts for file processing
4. **Concurrent Upload Limits**: Limit concurrent file uploads per user