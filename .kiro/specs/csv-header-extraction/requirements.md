# Requirements Document

## Introduction

This feature provides an endpoint for users to upload CSV/Excel files and extract their headers for validation against the contact schema. The system validates file format, size, and structure, then returns both the extracted headers and available contact schema fields to help users map their data correctly before importing contacts.

## Requirements

### Requirement 1

**User Story:** As an authenticated user, I want to upload a CSV/Excel file to extract its headers, so that I can validate my data structure before importing contacts.

#### Acceptance Criteria

1. WHEN a user uploads a file THEN the system SHALL authenticate the request before processing
2. WHEN a file is uploaded THEN the system SHALL validate the file extension is .csv, .xlsx, or .xls
3. WHEN a file is uploaded THEN the system SHALL verify the file size is less than 25MB
4. WHEN an Excel file is uploaded THEN the system SHALL ensure it contains only one sheet
5. WHEN a file is processed THEN the system SHALL extract headers from the first row
6. WHEN headers are extracted THEN the system SHALL return both CSV headers and contact schema fields
7. IF authentication fails THEN the system SHALL return a 401 unauthorized error
8. IF file validation fails THEN the system SHALL return appropriate error messages with validation details

### Requirement 2

**User Story:** As a user, I want clear validation feedback on my uploaded file, so that I can understand what needs to be corrected.

#### Acceptance Criteria

1. WHEN file extension is invalid THEN the system SHALL return an error specifying accepted formats (.csv, .xlsx, .xls)
2. WHEN file size exceeds 25MB THEN the system SHALL return an error suggesting to split the file
3. WHEN file has more than 400,000 rows THEN the system SHALL return an error suggesting to split the file
4. WHEN Excel file has multiple sheets THEN the system SHALL return an error requesting single sheet format
5. WHEN first row is empty THEN the system SHALL return an error requiring column headers
6. WHEN file processing fails THEN the system SHALL return descriptive error messages

### Requirement 3

**User Story:** As a user, I want to receive both my CSV headers and the available contact schema fields, so that I can understand how to map my data for import.

#### Acceptance Criteria

1. WHEN file is successfully processed THEN the system SHALL return extracted CSV headers as an array
2. WHEN file is successfully processed THEN the system SHALL return contact schema fields from contact.model.ts
3. WHEN headers are returned THEN the system SHALL provide them in a structured JSON format
4. WHEN schema fields are returned THEN the system SHALL include field names and types
5. WHEN response is sent THEN the system SHALL include both headers and schema in a single response object

### Requirement 4

**User Story:** As a developer, I want the CSV processing to use existing service architecture, so that the code is maintainable and follows established patterns.

#### Acceptance Criteria

1. WHEN processing CSV files THEN the system SHALL utilize /services/csv.service.ts for header extraction
2. WHEN retrieving schema fields THEN the system SHALL reference contact.model.ts for field definitions
3. WHEN handling requests THEN the system SHALL follow existing authentication middleware patterns
4. WHEN validating files THEN the system SHALL use consistent validation patterns with other endpoints
5. WHEN returning responses THEN the system SHALL follow established API response formats