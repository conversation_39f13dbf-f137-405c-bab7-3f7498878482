# Design Document

## Overview

This design outlines the refactoring of the current error handling system from a multi-layered, overlapping approach to a single, streamlined error pipeline. The refactor will eliminate duplicate error handling mechanisms while maintaining backward compatibility and improving developer experience.

### Current State Analysis

The existing system has 4 overlapping layers:
1. **AppError classes** - Well-structured error hierarchy (KEEP)
2. **ErrorMonitoring class** - Comprehensive logging with context (CONSOLIDATE)
3. **error-handler.ts** - Centralized HTTP response handling (ENHANCE)
4. **Manual error handling in handlers** - Inconsistent patterns (ELIMINATE)

### Target Architecture

**Single Error Pipeline:**
```
Controller/Service → Throw AppError → Express Error Middleware → HTTP Response
                                            ↓
                                    Automatic Logging & Monitoring
```

## Architecture

### Core Components

#### 1. Enhanced Express Error Middleware
- **Location**: `lib/middleware/error.middleware.ts`
- **Purpose**: Single point for all error processing
- **Responsibilities**:
  - Catch all unhandled errors
  - Transform non-AppError instances to AppError
  - Automatic logging with context
  - Generate consistent HTTP responses
  - Error monitoring and metrics

#### 2. Preserved AppError Hierarchy
- **Location**: `lib/errors/` (existing)
- **Purpose**: Structured error representation
- **No changes needed** - current implementation is solid

#### 3. Context-Aware Error Logger
- **Location**: `lib/utils/errorLogger.ts` (new)
- **Purpose**: Centralized logging with automatic context extraction
- **Replaces**: ErrorMonitoring class functionality

#### 4. Error Context Middleware
- **Location**: `lib/middleware/error.context.middleware.ts`
- **Purpose**: Capture request context for error logging
- **Responsibilities**:
  - Extract user/organization from request
  - Generate request IDs
  - Store context in request object

### Data Flow

```mermaid
graph TD
    A[Request] --> B[Error Context Middleware]
    B --> C[Route Handler]
    C --> D[Service Layer]
    D --> E{Error Occurs?}
    E -->|Yes| F[Throw AppError]
    E -->|No| G[Success Response]
    F --> H[Express Error Middleware]
    H --> I[Error Logger]
    H --> J[HTTP Error Response]
    I --> K[Log with Context]
    I --> L[Error Metrics]
```

## Components and Interfaces

### 1. Error Context Interface

```typescript
interface ErrorContext {
  requestId: string;
  userId?: string;
  organizationId?: string;
  endpoint: string;
  method: string;
  userAgent?: string;
  ip?: string;
  timestamp: Date;
}
```

### 2. Enhanced Error Middleware

```typescript
interface ErrorMiddleware {
  // Main error handler
  handleError(error: any, req: Request, res: Response, next: NextFunction): void;
  
  // Error transformation
  transformToAppError(error: any): AppError;
  
  // Context extraction
  extractErrorContext(req: Request): ErrorContext;
}
```

### 3. Error Logger Interface

```typescript
interface ErrorLogger {
  // Main logging method
  logError(error: AppError, context: ErrorContext, metadata?: Record<string, any>): void;
  
  // Specialized logging methods
  logBulkInsertError(error: AppError, context: BulkInsertContext): void;
  logJobError(error: AppError, context: JobContext): void;
  
  // Metrics tracking
  trackErrorMetrics(error: AppError, context: ErrorContext): void;
}
```

### 4. Error Recovery Interface

```typescript
interface ErrorRecovery {
  // Get recovery suggestions
  getRecoverySuggestions(error: AppError): string[];
  
  // Check if error is recoverable
  isRecoverable(error: AppError): boolean;
  
  // Get retry strategy
  getRetryStrategy(error: AppError): RetryStrategy | null;
}
```

## Data Models

### Error Context Model

```typescript
class ErrorContext {
  requestId: string;
  userId?: string;
  organizationId?: string;
  endpoint: string;
  method: string;
  userAgent?: string;
  ip?: string;
  timestamp: Date;
  jobId?: string;
  fileName?: string;
  rowNumber?: number;
  
  constructor(req: Request, additionalContext?: Partial<ErrorContext>);
  
  toLogObject(): Record<string, any>;
}
```

### Enhanced AppError (minimal changes)

```typescript
// Add context storage capability
class AppError extends Error {
  // ... existing properties
  
  // New: Store context when error is created
  context?: Partial<ErrorContext>;
  
  // New: Add context to error
  withContext(context: Partial<ErrorContext>): AppError;
}
```

## Error Handling

### Error Categories and Handling Strategy

#### 1. Operational Errors (Expected)
- **Examples**: Validation failures, duplicate resources, not found
- **Handling**: Log as warnings, return structured error response
- **Recovery**: Provide clear user guidance

#### 2. Programming Errors (Unexpected)
- **Examples**: Null pointer exceptions, type errors
- **Handling**: Log as errors with full stack trace, return generic error
- **Recovery**: Alert development team

#### 3. System Errors (Infrastructure)
- **Examples**: Database connection failures, external service timeouts
- **Handling**: Log as errors, implement retry logic where appropriate
- **Recovery**: Automatic retry with exponential backoff

### Error Transformation Rules

```typescript
// Transform common errors to AppError
const errorTransformRules = {
  // MongoDB errors
  MongoError: (error) => error.code === 11000 
    ? new DuplicateError('Resource already exists', 'DUPLICATE_001')
    : new DatabaseError('Database operation failed', 'DB_001'),
    
  // Mongoose validation errors
  ValidationError: (error) => new ValidationError(
    'Validation failed', 
    'VALIDATION_001', 
    extractValidationDetails(error)
  ),
  
  // Prisma errors
  PrismaClientKnownRequestError: (error) => transformPrismaError(error),
  
  // Default transformation
  Error: (error) => new InternalError(error.message, 'INTERNAL_001', false)
};
```

## Testing Strategy

### Unit Tests

#### 1. Error Middleware Tests
- **File**: `tests/unit/middleware/error.middleware.test.ts`
- **Coverage**:
  - AppError handling
  - Non-AppError transformation
  - Context extraction
  - Response formatting
  - Logging integration

#### 2. Error Logger Tests
- **File**: `tests/unit/utils/errorLogger.test.ts`
- **Coverage**:
  - Error categorization
  - Context-aware logging
  - Metrics tracking
  - Data sanitization

#### 3. Error Context Tests
- **File**: `tests/unit/middleware/errorContext.middleware.test.ts`
- **Coverage**:
  - Request context extraction
  - User/organization identification
  - Request ID generation

### Integration Tests

#### 1. End-to-End Error Flow
- **File**: `tests/integration/error-handling.test.ts`
- **Coverage**:
  - Complete error pipeline
  - Different error types
  - Response consistency
  - Logging verification

#### 2. API Error Responses
- **File**: `tests/integration/api-errors.test.ts`
- **Coverage**:
  - Consistent error response format
  - Proper HTTP status codes
  - Error code consistency
  - Backward compatibility

### Performance Tests

#### 1. Error Handling Performance
- **File**: `tests/performance/error-handling.test.ts`
- **Coverage**:
  - Error processing latency
  - Memory usage during error handling
  - Logging performance impact

## Migration Strategy

### Phase 1: Infrastructure Setup
1. Create new error middleware
2. Create error logger utility
3. Create error context middleware
4. Add comprehensive tests

### Phase 2: Gradual Handler Migration
1. Update one handler at a time
2. Remove manual error handling
3. Remove direct ErrorMonitoring calls
4. Verify error responses remain consistent

### Phase 3: Cleanup
1. Remove ErrorMonitoring class
2. Remove handleError from error-handler.ts
3. Update documentation
4. Remove unused imports

### Backward Compatibility

#### Error Response Format
```typescript
// Maintain existing format
interface ErrorResponse {
  error: string;
  errorCode: string;
  details?: Array<{
    field: string;
    message: string;
  }>;
}
```

#### HTTP Status Codes
- All existing status codes preserved
- Error codes remain unchanged
- Response structure identical

### Rollback Strategy

1. **Feature Flag**: Use environment variable to toggle new error handling
2. **Gradual Rollout**: Enable per-handler basis
3. **Monitoring**: Track error response consistency
4. **Quick Revert**: Keep old code until full migration verified

## Performance Considerations

### Optimizations

1. **Lazy Context Extraction**: Only extract context when error occurs
2. **Async Logging**: Non-blocking error logging
3. **Error Caching**: Cache error transformation rules
4. **Minimal Stack Traces**: Only capture for programming errors

### Monitoring

1. **Error Rate Tracking**: Monitor error frequency by type
2. **Response Time Impact**: Measure error handling overhead
3. **Memory Usage**: Track error object lifecycle
4. **Log Volume**: Monitor logging performance impact

## Security Considerations

### Data Sanitization

1. **PII Masking**: Automatic masking of sensitive data in logs
2. **Stack Trace Filtering**: Remove sensitive paths from stack traces
3. **Error Message Sanitization**: Prevent information leakage

### Access Control

1. **Error Details**: Limit error details based on user role
2. **Internal Errors**: Never expose internal system details
3. **Audit Logging**: Track error access patterns

## Deployment Strategy

### Environment Configuration

```typescript
interface ErrorConfig {
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  enableStackTraces: boolean;
  enableMetrics: boolean;
  sanitizeLogs: boolean;
  maxErrorDetails: number;
}
```

### Monitoring Integration

1. **Metrics Export**: Compatible with existing monitoring tools
2. **Alert Configuration**: Maintain existing alert thresholds
3. **Dashboard Updates**: Update error monitoring dashboards
4. **Log Aggregation**: Ensure compatibility with log aggregation systems