# Implementation Plan

- [x] 1. Update Contact Model with Text Index for Search
  - [x] 1.1 Add text index on name, email, and phone fields with appropriate weights
    - Configure weights to prioritize name matches (name: 10, email: 5, phone: 5)
    - Ensure index is created efficiently to avoid performance impact
  
  - [x] 1.2 Add specific indexes for partial matching
    - Add compound index on org_id and email
    - Add compound index on org_id and phone
    - Ensure indexes support efficient filtering

- [x] 2. Create Type Definitions for API Parameters and Responses
  - [x] 2.1 Define ContactsQueryParams interface
    - Include pagination parameters (page, page_size)
    - Include sorting parameters (sort_by, sort_direction)
    - Include filtering parameters (status, tag_id, search)
    - Include react-query compatible parameters (filterField, filterValue, filterOperator)
    - Include advanced filtering parameter (filter)
  
  - [x] 2.2 Define PaginatedContactsResponse interface
    - Include data array of ContactResponse objects
    - Include pagination metadata (total_count, page, page_size, total_pages, has_next_page, has_prev_page)
  
  - [x] 2.3 Define FilterQuery and Rule interfaces for react-querybuilder compatibility
    - Support nested filter structures with combinators (AND/OR)
    - Define supported operators for filter rules
    - Ensure compatibility with react-querybuilder's output format

- [x] 3. Implement Query Parameter Validation for GET Contacts API
  - [x] 3.1 Create getContactsSchema in ContactsValidator
    - Validate pagination parameters (page, page_size)
    - Validate sorting parameters (sort_by, sort_direction)
    - Validate filtering parameters (status, tag_id, search)
    - Validate react-query parameters (filterField, filterValue, filterOperator)
    - Validate filter JSON string for advanced filtering
  
  - [x] 3.2 Implement validateGetContacts method
    - Apply schema validation to query parameters
    - Return validation result with value and error
    - Handle special validation for react-query parameters
  
  - [x] 3.3 Implement validateGetContactsMiddleware
    - Apply validation before request reaches handler
    - Return appropriate error responses with detailed messages for invalid parameters
    - Pass validated parameters to next middleware

- [x] 4. Extend ContactDAO with Contact Retrieval Methods
  - [x] 4.1 Implement getContacts method
    - Accept organization ID and query parameters
    - Build MongoDB query based on parameters
    - Apply pagination using skip and limit
    - Return contacts and total count
  
  - [x] 4.2 Implement buildContactsQuery method
    - Build base query with organization filter
    - Apply status filter if provided
    - Apply tag filter if provided
    - Apply text search for name, email, and phone if provided
    - Apply react-query simple filtering if provided
    - Apply advanced filter if provided
  
  - [x] 4.3 Implement search functionality
    - Implement text search using MongoDB's $text operator
    - Configure search to prioritize name matches
    - Support partial matching for email and phone
    - Handle multi-word searches appropriately
  
  - [x] 4.4 Implement react-query filter support
    - Implement applyOperator method for simple filtering
    - Implement convertFilterToMongoQuery method for advanced filtering
    - Support nested conditions with AND/OR logic
    - Handle all supported operators

- [x] 5. Implement ContactsHandler.getContacts Method
  - [x] 5.1 Extract and validate organization ID
    - Return 400 error if organization ID is missing
    - Include appropriate error code and message
  
  - [x] 5.2 Process query parameters
    - Handle default values for pagination and sorting
    - Process search parameters for name, email, and phone
    - Process react-query filter parameters
  
  - [x] 5.3 Call ContactDAO.getContacts with parameters
    - Pass organization ID and processed query parameters
    - Handle database errors with appropriate error responses
  
  - [x] 5.4 Format response with pagination metadata
    - Format contacts using existing formatContactsResponse method
    - Calculate pagination metadata (total_count, page, page_size, total_pages, etc.)
    - Return 200 response with data and pagination

- [x] 6. Create GET Contacts API Route
  - [ ] 6.1 Add GET endpoint to contacts router
    - Apply supabaseAuthMiddleware for authentication
    - Apply validateGetContactsMiddleware for parameter validation
    - Route to ContactsHandler.getContacts
  
  - [x] 6.2 Add comprehensive Swagger documentation
    - Document all query parameters including pagination, filtering, and search
    - Document react-query compatibility parameters
    - Document response format with pagination metadata
    - Provide examples of different query scenarios

- [x] 7. Write Comprehensive Unit Tests
  - [x] 7.1 Test ContactsValidator
    - Test validation of valid pagination parameters
    - Test validation of valid sorting parameters
    - Test validation of valid filtering parameters
    - Test validation of valid react-query parameters
    - Test validation of invalid parameters with appropriate error messages
    - Test validation of filter JSON for both valid and invalid formats
  
  - [x] 7.2 Test filter conversion utility
    - Test conversion of simple filters
    - Test conversion of complex nested filters
    - Test handling of different operators
    - Test compatibility with react-querybuilder output format
  
  - [x] 7.3 Test search functionality
    - Test name search prioritization
    - Test email search with partial matching
    - Test phone search with partial matching
    - Test multi-word search capabilities
  
  - [x] 7.4 Test ContactsHandler.getContacts
    - Test successful retrieval with various parameters
    - Test pagination metadata calculation
    - Test error handling for various scenarios
    - Mock DAO layer for controlled testing
  
  - [x] 7.5 Test ContactDAO methods
    - Test query building with different parameter combinations
    - Test pagination implementation
    - Test search functionality with different search terms
    - Test react-query filter support
    - Mock database responses for predictable testing

- [x] 8. Write Integration Tests for API Endpoint
  - [x] 8.1 Test GET contacts endpoint with various scenarios
    - Test basic pagination functionality
    - Test filtering by status
    - Test filtering by tag
    - Test name, email, and phone search functionality
    - Test react-query simple filtering
    - Test advanced filtering with complex conditions
    - Test sorting by different fields
  
  - [x] 8.2 Test error handling and edge cases
    - Test invalid parameters with expected error responses
    - Test unauthorized access scenarios
    - Test database error handling
    - Test empty result sets with proper pagination metadata
  
  - [x] 8.3 Test performance with larger datasets
    - Test pagination with many records
    - Test search performance with various search terms
    - Test complex filtering performance