# Design Document: GET Contacts API Endpoint with Pagination

## Overview

This design document outlines the implementation of a GET contacts API endpoint with pagination, advanced filtering compatible with react-query, powerful search capabilities for name, email, and phone fields, and flexible sorting options for the ShoutOUT Engage service. The endpoint will allow API consumers to efficiently retrieve and filter contacts with various query parameters to customize the results according to their specific needs.

## Architecture

The implementation follows the existing architecture of the ShoutOUT Engage service:

```mermaid
flowchart TD
    Client[Client] --> Router[Router Layer]
    Router --> Validator[Validation Layer]
    Validator --> Handler[Handler Layer]
    Handler --> DAO[Data Access Layer]
    DAO --> Database[MongoDB]
    Database --> DAO
    DAO --> Handler
    Handler --> Router
    Router --> Client
```

### Request Flow

```mermaid
sequenceDiagram
    participant Client
    participant Router
    participant Validator
    participant Handler
    participant DAO
    participant Database

    Client->>Router: GET /contacts with query params
    Router->>Validator: Validate query parameters
    
    alt Invalid Parameters
        Validator-->>Router: Validation errors
        Router-->>Client: 400 Bad Request with error details
    else Valid Parameters
        Validator-->>Router: Validated parameters
        Router->>Handler: Process request
        Handler->>DAO: Get contacts with params
        DAO->>Database: Execute query
        Database-->>DAO: Return contacts
        DAO->>DAO: Format contacts
        DAO-->>Handler: Formatted contacts
        Handler->>Handler: Build paginated response
        Handler-->>Router: Response with data and pagination
        Router-->>Client: 200 OK with response
    end
```

## Components and Interfaces

### Query Parameters Interface

```typescript
interface ContactsQueryParams {
  // Pagination
  page?: number;         // Default: 1
  page_size?: number;    // Default: 20, Max: 100
  
  // Sorting
  sort_by?: string;      // Default: 'created_at'
  sort_direction?: 'asc' | 'desc'; // Default: 'desc'
  
  // Basic Filtering
  status?: ContactStatus | ContactStatus[];
  tag_id?: string;
  search?: string;
  
  // Advanced Filtering for react-query compatibility
  filter?: string;       // JSON string of complex filter object
  
  // React-query specific parameters
  filterField?: string;  // For simple field filtering
  filterValue?: string;  // Value to filter by
  filterOperator?: string; // Operator for filtering (eq, contains, etc.)
}
```

### Pagination Response Interface

```typescript
interface PaginatedContactsResponse {
  data: ContactResponse[];
  pagination: {
    total_count: number;
    page: number;
    page_size: number;
    total_pages: number;
    has_next_page: boolean;
    has_prev_page: boolean;
  };
}
```

### Advanced Filter Interface

```typescript
interface FilterQuery {
  combinator: 'and' | 'or';
  rules: Array<Rule | FilterQuery>;
}

interface Rule {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 
            'greater' | 'less' | 'greater_or_equal' | 'less_or_equal' | 
            'in' | 'not_in' | 'is_null' | 'is_not_null';
  value: string | number | boolean | Array<string | number | boolean> | null;
}
```

## Database Schema Updates

To support efficient text search specifically for name, email, and phone fields, we'll add a text index to the Contact model with appropriate weights to prioritize name matches:

```typescript
// Add text index for search functionality
contactSchema.index(
  { name: 'text', email: 'text', phone: 'text' },
  { 
    weights: { name: 10, email: 5, phone: 5 }, 
    name: 'contact_text_search' 
  }
);
```

Additionally, we'll add specific indexes to support partial matching on email and phone fields:

```typescript
// Add indexes for partial matching on email and phone
contactSchema.index({ org_id: 1, email: 1 });
contactSchema.index({ org_id: 1, phone: 1 });
```

## Implementation Details

### 1. Query Parameter Validation

The `ContactsValidator` class will be extended with methods to validate GET request query parameters:

```typescript
static readonly getContactsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  page_size: Joi.number().integer().min(1).max(100).default(20),
  sort_by: Joi.string().valid('name', 'email', 'phone', 'created_at', 'updated_at').default('created_at'),
  sort_direction: Joi.string().valid('asc', 'desc').default('desc'),
  status: Joi.alternatives().try(
    Joi.string().valid(...Object.values(ContactStatus)),
    Joi.array().items(Joi.string().valid(...Object.values(ContactStatus)))
  ),
  tag_id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/),
  search: Joi.string().trim().min(1),
  filter: Joi.string().custom((value, helpers) => {
    try {
      const parsed = JSON.parse(value);
      // Validate filter structure
      return parsed;
    } catch (error) {
      return helpers.error('string.filterJson');
    }
  })
}).options({
  stripUnknown: true,
  abortEarly: false
});
```

### 2. DAO Layer Implementation

The `ContactDAO` class will be extended with methods to retrieve contacts with pagination, advanced filtering for react-query, and search capabilities:

```typescript
static async getContacts(
  organizationId: string,
  params: ContactsQueryParams
): Promise<{ contacts: ContactDocument[], totalCount: number }> {
  // Build query
  const query = this.buildContactsQuery(organizationId, params);
  
  // Calculate pagination
  const page = params.page || 1;
  const pageSize = params.page_size || 20;
  const skip = (page - 1) * pageSize;
  
  // Build sort options
  const sortField = params.sort_by || 'created_at';
  const sortDirection = params.sort_direction === 'asc' ? 1 : -1;
  const sortOptions = { [sortField]: sortDirection };
  
  // Add secondary sort by created_at if not already sorting by it
  if (sortField !== 'created_at') {
    sortOptions.created_at = -1;
  }
  
  // Execute query with pagination
  const contacts = await ContactModel.find(query)
    .sort(sortOptions)
    .skip(skip)
    .limit(pageSize)
    .exec();
  
  // Get total count for pagination metadata
  const totalCount = await ContactModel.countDocuments(query);
  
  return { contacts, totalCount };
}

static buildContactsQuery(
  organizationId: string,
  params: ContactsQueryParams
): Record<string, any> {
  // Base query - always filter by organization
  const query: Record<string, any> = { org_id: organizationId };
  
  // Apply status filter
  if (params.status) {
    query.status = Array.isArray(params.status) 
      ? { $in: params.status } 
      : params.status;
  }
  
  // Apply tag filter
  if (params.tag_id) {
    query['tags.tag_id'] = new ObjectId(params.tag_id);
  }
  
  // Apply text search for name, email, and phone
  if (params.search) {
    const searchTerm = params.search.trim();
    
    if (searchTerm) {
      // Use $text for full text search with weights
      query.$text = { $search: searchTerm };
      
      // Add score field for sorting by relevance if needed
      // query.score = { $meta: "textScore" };
      
      // Alternative approach for more flexible partial matching
      // const searchRegex = new RegExp(escapeRegExp(searchTerm), 'i');
      // query.$or = [
      //   { name: searchRegex },
      //   { email: searchRegex },
      //   { phone: searchRegex }
      // ];
    }
  }
  
  // Apply react-query simple filtering
  if (params.filterField && params.filterValue) {
    const operator = params.filterOperator || 'eq';
    query[params.filterField] = this.applyOperator(params.filterValue, operator);
  }
  
  // Apply advanced filter from react-querybuilder
  if (params.filter) {
    try {
      const filterQuery = JSON.parse(params.filter);
      const mongoQuery = this.convertFilterToMongoQuery(filterQuery);
      Object.assign(query, mongoQuery);
    } catch (error) {
      // Log error but don't fail - validation should catch this
      console.error('Error parsing filter JSON:', error);
    }
  }
  
  return query;
}

static applyOperator(value: any, operator: string): any {
  switch (operator) {
    case 'eq':
      return value;
    case 'neq':
      return { $ne: value };
    case 'contains':
      return { $regex: escapeRegExp(value), $options: 'i' };
    case 'gt':
      return { $gt: value };
    case 'lt':
      return { $lt: value };
    case 'gte':
      return { $gte: value };
    case 'lte':
      return { $lte: value };
    default:
      return value;
  }
}

static convertFilterToMongoQuery(
  filter: FilterQuery
): Record<string, any> {
  // Implementation of filter conversion logic for react-querybuilder
  // This will convert the FilterQuery structure to MongoDB query operators
  
  // Handle empty filter
  if (!filter || !filter.rules || filter.rules.length === 0) {
    return {};
  }
  
  // Process rules based on combinator
  const combinator = filter.combinator === 'or' ? '$or' : '$and';
  const conditions = filter.rules.map(rule => {
    // Handle nested filter groups
    if ('combinator' in rule) {
      return this.convertFilterToMongoQuery(rule);
    }
    
    // Handle leaf rules
    return this.convertRuleToMongoCondition(rule);
  });
  
  return { [combinator]: conditions };
}

static convertRuleToMongoCondition(rule: Rule): Record<string, any> {
  const { field, operator, value } = rule;
  
  // Map operators to MongoDB operators
  switch (operator) {
    case 'equals':
      return { [field]: value };
    case 'not_equals':
      return { [field]: { $ne: value } };
    case 'contains':
      return { [field]: { $regex: escapeRegExp(value), $options: 'i' } };
    case 'not_contains':
      return { [field]: { $not: { $regex: escapeRegExp(value), $options: 'i' } } };
    case 'greater':
      return { [field]: { $gt: value } };
    case 'less':
      return { [field]: { $lt: value } };
    case 'greater_or_equal':
      return { [field]: { $gte: value } };
    case 'less_or_equal':
      return { [field]: { $lte: value } };
    case 'in':
      return { [field]: { $in: Array.isArray(value) ? value : [value] } };
    case 'not_in':
      return { [field]: { $nin: Array.isArray(value) ? value : [value] } };
    case 'is_null':
      return { [field]: { $exists: false } };
    case 'is_not_null':
      return { [field]: { $exists: true } };
    default:
      return {};
  }
}

// Helper function to escape special characters in regex
static escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
```

### 3. Handler Implementation

The `ContactsHandler` class will be extended with a method to handle GET requests:

```typescript
static async getContacts(req: AuthenticatedRequest, res: Response): Promise<void> {
  try {
    // Get organization ID from auth middleware
    if (!req.organizationId) {
      res.status(400).json({
        error: 'Organization context is required',
        errorCode: 'ORG_001'
      } as ErrorResponse);
      return;
    }

    // Get validated query parameters
    const params = req.query as unknown as ContactsQueryParams;
    
    // Get contacts from DAO
    const { contacts, totalCount } = await ContactDAO.getContacts(
      req.organizationId,
      params
    );
    
    // Format contacts for response
    const formattedContacts = ContactDAO.formatContactsResponse(contacts);
    
    // Build pagination metadata
    const page = params.page || 1;
    const pageSize = params.page_size || 20;
    const totalPages = Math.ceil(totalCount / pageSize);
    
    // Build response
    const response: PaginatedContactsResponse = {
      data: formattedContacts,
      pagination: {
        total_count: totalCount,
        page,
        page_size: pageSize,
        total_pages: totalPages,
        has_next_page: page < totalPages,
        has_prev_page: page > 1
      }
    };
    
    res.status(200).json(response);
  } catch (error) {
    ContactsHandler.handleError(error, res);
  }
}
```

### 4. Route Implementation

The contacts route will be updated to include the GET endpoint:

```typescript
/**
 * @swagger
 * /contacts:
 *   get:
 *     summary: Get contacts with pagination
 *     description: Retrieves a paginated list of contacts with optional filtering, sorting, and search
 *     tags:
 *       - Contacts
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: page_size
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           enum: [name, email, phone, created_at, updated_at]
 *           default: created_at
 *         description: Field to sort by
 *       - in: query
 *         name: sort_direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort direction
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, archived, deleted]
 *         description: Filter by contact status
 *       - in: query
 *         name: tag_id
 *         schema:
 *           type: string
 *         description: Filter by tag ID
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, email, or phone
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: JSON string of complex filter object
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedContactsResponse'
 *       400:
 *         description: Invalid query parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', 
  supabaseAuthMiddleware, 
  ContactsValidator.validateGetContactsMiddleware, 
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      await ContactsHandler.getContacts(req, res);
    } catch (error) {
      next(error);
    }
  }
);
```

## Advanced Filter Conversion

The conversion from the FilterQuery structure to MongoDB query operators will be implemented as follows:

```typescript
static convertFilterToMongoQuery(filter: FilterQuery): Record<string, any> {
  // Handle empty filter
  if (!filter || !filter.rules || filter.rules.length === 0) {
    return {};
  }
  
  // Process rules based on combinator
  const combinator = filter.combinator === 'or' ? '$or' : '$and';
  const conditions = filter.rules.map(rule => {
    // Handle nested filter groups
    if ('combinator' in rule) {
      return this.convertFilterToMongoQuery(rule);
    }
    
    // Handle leaf rules
    return this.convertRuleToMongoCondition(rule);
  });
  
  return { [combinator]: conditions };
}

static convertRuleToMongoCondition(rule: Rule): Record<string, any> {
  const { field, operator, value } = rule;
  
  // Map operators to MongoDB operators
  switch (operator) {
    case 'equals':
      return { [field]: value };
    case 'not_equals':
      return { [field]: { $ne: value } };
    case 'contains':
      return { [field]: { $regex: value, $options: 'i' } };
    case 'not_contains':
      return { [field]: { $not: { $regex: value, $options: 'i' } } };
    case 'greater':
      return { [field]: { $gt: value } };
    case 'less':
      return { [field]: { $lt: value } };
    case 'greater_or_equal':
      return { [field]: { $gte: value } };
    case 'less_or_equal':
      return { [field]: { $lte: value } };
    case 'in':
      return { [field]: { $in: Array.isArray(value) ? value : [value] } };
    case 'not_in':
      return { [field]: { $nin: Array.isArray(value) ? value : [value] } };
    case 'is_null':
      return { [field]: { $exists: false } };
    case 'is_not_null':
      return { [field]: { $exists: true } };
    default:
      return {};
  }
}
```

## Performance Considerations

1. **Indexing**: The implementation uses existing indexes and adds a text index for search functionality.
2. **Pagination**: Results are paginated to limit the amount of data returned.
3. **Projection**: Only necessary fields are returned in the response.
4. **Query Optimization**: Queries are built to leverage MongoDB's query optimizer.
5. **Caching**: For frequently accessed data, caching can be implemented in the future.

## Security Considerations

1. **Authentication**: All requests are authenticated using the existing supabaseAuthMiddleware.
2. **Organization Isolation**: Contacts are always filtered by organization ID to prevent data leakage.
3. **Input Validation**: All query parameters are validated to prevent injection attacks.
4. **Rate Limiting**: API rate limiting can be implemented to prevent abuse.
5. **Error Handling**: Errors are handled gracefully without exposing sensitive information.

## Testing Strategy

1. **Unit Tests**:
   - Test validation of query parameters
   - Test filter conversion logic
   - Test handler with mocked DAO
   - Test DAO with mocked database

2. **Integration Tests**:
   - Test the complete request flow
   - Test pagination with various page sizes
   - Test filtering with different combinations
   - Test search functionality
   - Test error scenarios

3. **Performance Tests**:
   - Test with large datasets
   - Test search performance