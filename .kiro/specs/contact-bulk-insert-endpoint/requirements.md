# Requirements Document

## Introduction

This feature implements a contact bulk insert endpoint that handles large-scale contact imports from CSV files through an asynchronous job processing system. The system will process contact data row by row to manage memory efficiently, validate each contact, generate detailed error reports, and send comprehensive email notifications. All processing is managed through Bull MQ and Redis with a proper folder structure for job processors.

## Requirements

### Requirement 1

**User Story:** As a user, I want to bulk insert contacts from CSV files through an API endpoint, so that I can efficiently import large datasets without blocking the request.

#### Acceptance Criteria

1. WHEN a user uploads a CSV file to the bulk insert endpoint THEN the system SHALL accept the file and return a job ID immediately
2. WHEN the CSV file format is invalid THEN the system SHALL validate the file structure and return appropriate error messages
3. WHEN the bulk insert job is created THEN the system SHALL queue it for background processing
4. WHEN the CSV file exceeds size limits THEN the system SHALL reject the request with a clear error message
5. WHEN the CSV file is empty THEN the system SHALL return an appropriate error message

### Requirement 2

**User Story:** As a system administrator, I want contact bulk inserts to be processed by background jobs with proper folder structure, so that the API remains responsive and the codebase is well-organized.

#### Acceptance Criteria

1. WHEN a bulk insert job is queued THEN the job processor SHALL pick it up and process contacts row by row to manage memory efficiently
2. WHEN processing contacts THEN the system SHALL validate each row and collect invalid data for error reporting
3. WHEN a job encounters invalid rows THEN the system SHALL continue processing valid contacts and track errors separately
4. WHEN a job completes THEN the system SHALL update the job status and record detailed processing statistics
5. WHEN a job fails completely THEN the system SHALL mark it as failed and preserve error information
6. WHEN job processors are implemented THEN they SHALL inherit from a common base job processor class
7. WHEN organizing code THEN job processors SHALL be placed in a dedicated jobs folder structure

### Requirement 3

**User Story:** As a user, I want to receive detailed email notifications with error reports when my bulk insert job completes, so that I know exactly which contacts were processed and which failed.

#### Acceptance Criteria

1. WHEN a bulk insert job completes successfully THEN the system SHALL send a success email with processing statistics to the user
2. WHEN a bulk insert job fails completely THEN the system SHALL send a failure email with error details to the user
3. WHEN a bulk insert job completes with some invalid rows THEN the system SHALL send a detailed email with success counts, error counts, and a comprehensive error report
4. WHEN sending notification emails THEN the system SHALL include job statistics, processing time, and a downloadable error report for failed rows
5. WHEN there are validation errors THEN the error report SHALL include row numbers, field names, error messages, and the original invalid data
6. WHEN the error report is large THEN the system SHALL provide it as an attachment or downloadable link

### Requirement 4

**User Story:** As a system administrator, I want system emails to be processed through a separate queue, so that email delivery doesn't impact the main application performance.

#### Acceptance Criteria

1. WHEN the system needs to send any email THEN it SHALL queue the email request instead of sending immediately
2. WHEN an email is queued THEN the email processor SHALL pick it up and attempt delivery
3. WHEN email delivery fails THEN the system SHALL retry according to configured retry policies
4. WHEN email delivery fails permanently THEN the system SHALL log the failure and mark the email as failed
5. WHEN the email queue is overwhelmed THEN the system SHALL handle backpressure gracefully

### Requirement 5

**User Story:** As a developer, I want the system to use Bull MQ with Redis for job and email queues, so that we have reliable, scalable queue management.

#### Acceptance Criteria

1. WHEN the application starts THEN it SHALL connect to Redis and initialize Bull MQ queues
2. WHEN jobs are added to queues THEN Bull MQ SHALL persist them in Redis for reliability
3. WHEN workers process jobs THEN they SHALL inherit from a common base job processor class
4. WHEN queues experience failures THEN the system SHALL handle reconnection and recovery automatically
5. WHEN monitoring queue health THEN the system SHALL provide metrics and status information

### Requirement 8

**User Story:** As a developer, I want a common base job processor class that handles standard job lifecycle management, so that all job processors have consistent behavior and error handling.

#### Acceptance Criteria

1. WHEN creating job processors THEN they SHALL extend the BaseJobProcessor abstract class
2. WHEN a job starts processing THEN the BaseJobProcessor SHALL update the job status to PROCESSING
3. WHEN a job completes successfully THEN the BaseJobProcessor SHALL update the status to COMPLETED and call onJobCompleted
4. WHEN a job fails THEN the BaseJobProcessor SHALL update the status to FAILED, log errors, and call onJobFailed
5. WHEN job processors are implemented THEN they SHALL override the abstract processJob method with their specific logic
6. WHEN the BaseJobProcessor is created THEN it SHALL initialize the Bull MQ queue with Redis configuration
7. WHEN queue events occur THEN the BaseJobProcessor SHALL handle error, failed, and completed events with proper logging
8. WHEN job status updates are needed THEN the BaseJobProcessor SHALL use CommonJobDAO to persist status changes
9. WHEN job errors occur THEN the BaseJobProcessor SHALL record error details in the common job database

### Requirement 6

**User Story:** As a user, I want to check the status of my bulk insert job, so that I can monitor the progress of my import operation.

#### Acceptance Criteria

1. WHEN a user requests job status with a valid job ID THEN the system SHALL return current status and progress information
2. WHEN a user requests status for a non-existent job THEN the system SHALL return a not found error
3. WHEN a job is in progress THEN the status SHALL include progress percentage, rows processed, and estimated completion time
4. WHEN a job is completed THEN the status SHALL include final statistics, processing summary, and error report if applicable

### Requirement 7

**User Story:** As a developer, I want the system to process CSV data row by row with proper memory management, so that large files don't cause memory issues.

#### Acceptance Criteria

1. WHEN processing a CSV file THEN the system SHALL read and process one row at a time using streaming
2. WHEN a row is processed THEN the system SHALL immediately validate and either insert or collect error information
3. WHEN memory usage approaches limits THEN the system SHALL handle backpressure and pause processing if needed
4. WHEN processing large files THEN the system SHALL not load the entire file into memory at once
5. WHEN validation fails for a row THEN the system SHALL collect the error details without stopping the entire process