# Implementation Plan

- [x] 1. Set up universal job infrastructure and constants
  - Create job constants, types, and database models that will support all future job types
  - Implement universal job status management system
  - _Requirements: 8.1, 8.6, 8.7, 8.8, 8.9_

- [x] 1.1 Create job constants and types
  - Write job constants in lib/constant/job.constants.ts with job status, types, and error codes
  - Write queue constants in lib/constant/queue.constants.ts with queue names and options
  - Create job types in types/job.types.ts with universal job interfaces
  - _Requirements: 8.1, 8.6_

- [x] 1.2 Create universal job database model
  - Write job.model.ts with universal job schema supporting all job types
  - Implement job document interface with progress tracking and error handling
  - Add proper indexes for job queries and performance
  - _Requirements: 8.1, 8.7, 8.8_

- [x] 1.3 Implement CommonJobDAO for universal job management
  - Write CommonJobDAO.ts with CRUD operations for all job types
  - Implement job status updates, progress tracking, and error logging
  - Add job querying methods with filtering and pagination
  - _Requirements: 8.1, 8.7, 8.8, 8.9_

- [x] 2. Create universal queue management system
  - Implement queue manager and Bull MQ integration with Redis
  - Set up queue infrastructure that can handle multiple job types
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 2.1 Implement QueueManager for universal queue handling
  - Write queue.manager.ts with singleton pattern for queue management
  - Implement queue initialization, job dispatching, and monitoring
  - Add queue health monitoring and statistics methods
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 2.2 Create specific queue configurations
  - Write contact.bulk.insert.queue.ts with bulk insert queue setup
  - Write email.queue.ts with email queue configuration
  - Configure queue options, retry policies, and error handling
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 3. Implement universal BaseJobProcessor
  - Create abstract base class that all job processors will inherit from
  - Implement common job lifecycle management and error handling
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8, 8.9_

- [x] 3.1 Create BaseJobProcessor abstract class
  - Write BaseJobProcessor.ts in workers/processors/ with universal job handling
  - Implement job status updates, progress tracking, and error management
  - Add event handlers for queue events and job lifecycle
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8, 8.9_

- [x] 4. Implement contact bulk insert job processor
  - Create processor that extends BaseJobProcessor for CSV processing
  - Implement row-by-row processing with memory management
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 4.1 Create ContactBulkInsertProcessor
  - Write contact.bulk.insert.processor.ts extending BaseJobProcessor
  - Implement CSV streaming and row-by-row contact processing
  - Add contact validation and duplicate handling logic
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 4.2 Implement CSV processing with error collection
  - Create CSV streaming processor with memory-efficient row processing
  - Implement error collection system using existing AppError types
  - Add progress tracking and status updates during processing
  - _Requirements: 2.2, 2.3, 2.4, 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 5. Create email job processor for notifications
  - Implement email processor that extends BaseJobProcessor
  - Handle email sending with retry logic and failure handling
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 5.1 Create EmailProcessor
  - Write email.processor.ts extending BaseJobProcessor
  - Implement email sending logic with template support
  - Add retry mechanisms and failure handling for email delivery
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 5.2 Implement email notification system
  - Create email templates for job completion notifications
  - Implement email queuing for bulk insert job results
  - Add error report attachment handling for failed rows
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 6. Create business services for bulk insert operations
  - Implement service layer for bulk insert job creation and management
  - Add file handling and validation services
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 6.1, 6.2, 6.3, 6.4_

- [x] 6.1 Implement ContactBulkInsertService
  - Write contact.bulk.insert.service.ts with job creation and status methods
  - Add CSV file validation and temporary file storage
  - Implement job status querying and progress reporting
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 6.1, 6.2, 6.3, 6.4_

- [x] 6.2 Create EmailService for notification handling
  - Write email.service.ts with email template rendering and sending
  - Implement email queuing integration with job completion
  - Add support for error report attachments and email templates
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 6.3 Implement ErrorReportService
  - Write error.report.service.ts for generating CSV error reports
  - Create error report formatting with row numbers and error details
  - Add temporary file storage and cleanup for error reports
  - _Requirements: 3.3, 3.5, 3.6_

- [x] 7. Create API endpoint and validation
  - Implement bulk insert endpoint with file upload handling
  - Add request validation and error handling
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 7.1 Add bulk insert endpoint to contacts router
  - Update routes/contacts.ts with POST /contacts/bulk-insert endpoint
  - Implement file upload middleware and request validation
  - Add proper error handling and response formatting
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 7.2 Create bulk insert request validation
  - Update validators/ContactsValidator.ts with bulk insert validation
  - Add CSV file validation rules and error messages
  - Implement request parameter validation for bulk insert
  - _Requirements: 1.1, 1.2, 1.4, 1.5_

- [x] 7.3 Implement bulk insert handler
  - Update handlers/ContactsHandler.ts with bulk insert handler method
  - Add job creation logic and immediate response with job ID
  - Implement proper error handling and status codes
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 8. Add job status endpoint
  - Create endpoint for checking bulk insert job status and progress
  - Implement job status querying with progress information
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 8.1 Add job status endpoint to contacts router
  - Add GET /contacts/bulk-insert/:jobId/status endpoint
  - Implement job ID validation and status retrieval
  - Add progress information and error details in response
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 8.2 Create job status handler
  - Add job status handler method to ContactsHandler.ts
  - Implement job status formatting and response structure
  - Add proper error handling for non-existent jobs
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 9. Integrate workers with job processors
  - Update worker system to start job processors
  - Ensure proper worker lifecycle management
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 9.1 Update workers/main.ts to start job processors
  - Import and initialize ContactBulkInsertProcessor and EmailProcessor
  - Start job processors alongside existing worker thread functionality
  - Add proper shutdown handling for job processors
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 10. Add Bull MQ dependency and configuration
  - Install Bull MQ package and configure Redis integration
  - Update package.json and ensure proper dependency management
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 10.1 Install Bull MQ and update dependencies
  - Add bull package to package.json dependencies
  - Install @types/bull for TypeScript support
  - Update package-lock.json with new dependencies
  - _Requirements: 5.1, 5.2_

- [x] 10.2 Configure Bull MQ with existing Redis setup
  - Update RedisConnector to support Bull MQ configuration
  - Ensure Bull MQ uses existing Redis connection settings
  - Test Redis connectivity for queue operations
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 11. Create comprehensive tests
  - Write unit tests for job processors, services, and API endpoints
  - Add integration tests for end-to-end bulk insert workflow
  - _Requirements: All requirements_

- [x] 11.1 Write unit tests for job infrastructure
  - Test BaseJobProcessor functionality and job lifecycle
  - Test CommonJobDAO operations and database interactions
  - Test QueueManager and queue operations
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8, 8.9, 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 11.2 Write unit tests for contact bulk insert processor
  - Test CSV processing, contact validation, and error handling
  - Test memory management and row-by-row processing
  - Test progress tracking and status updates
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 11.3 Write unit tests for email processor and services
  - Test email sending, retry logic, and failure handling
  - Test email template rendering and notification system
  - Test error report generation and attachment handling
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 11.4 Write integration tests for API endpoints
  - Test bulk insert endpoint with file upload and job creation
  - Test job status endpoint with progress tracking
  - Test end-to-end workflow from upload to email notification
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 6.1, 6.2, 6.3, 6.4_

- [x] 12. Add documentation and error handling improvements
  - Update API documentation with new endpoints
  - Ensure proper error handling throughout the system
  - _Requirements: All requirements_

- [x] 12.1 Update API documentation
  - Add Swagger documentation for bulk insert endpoints
  - Document request/response formats and error codes
  - Add examples for bulk insert and job status endpoints
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 6.1, 6.2, 6.3, 6.4_

- [x] 12.2 Implement comprehensive error handling
  - Ensure all error scenarios use existing AppError types
  - Add proper error logging and monitoring
  - Test error scenarios and recovery mechanisms
  - _Requirements: All requirements_