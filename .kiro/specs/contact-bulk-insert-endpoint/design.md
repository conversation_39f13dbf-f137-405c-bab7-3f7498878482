# Design Document

## Overview

This design implements a contact bulk insert endpoint that processes CSV files asynchronously using Bull MQ and Redis. The system follows a job-based architecture where CSV uploads are queued for background processing, with comprehensive error reporting and email notifications sent through a separate email queue.

The design leverages the existing MongoDB/Mongoose setup for contact storage, Redis for queue management, and introduces a **generic, extensible job processing infrastructure** that can be easily extended for future features like email campaigns, SMS campaigns, and other background processing tasks.

**Key Design Principle**: Create a common job processing foundation that can be extended for any future job type (email campaigns, SMS campaigns, data exports, etc.) while maintaining consistent behavior, error handling, and monitoring across all job types.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Client] -->|POST /contacts/bulk-insert| B[Bulk Insert Endpoint]
    B --> C[CSV Validation]
    C --> D[Job Creation]
    D --> E[Contact Bulk Insert Queue]
    E --> F[Contact Bulk Insert Processor]
    F --> G[CSV Row-by-Row Processing]
    G --> H[Contact Validation & Insert]
    H --> I[Error Collection]
    I --> J[Job Completion]
    J --> K[Email Queue]
    K --> L[Email Processor]
    L --> M[Email Service]
    
    N[Redis] --> E
    N --> K
    O[MongoDB] --> H
    P[Job Status Database] --> D
    P --> F
    P --> J
```

### Folder Structure (All Processors in Workers Folder)

```
shoutout_engage_service/
├── lib/
│   ├── constant/
│   │   ├── file.constants.ts            # Existing file constants
│   │   ├── job.constants.ts             # Universal job constants (status, types, queue options)
│   │   └── queue.constants.ts           # Queue configuration constants
│   ├── db/
│   │   ├── dao/
│   │   │   └── CommonJobDAO.ts          # Universal job status management for ALL job types
│   │   └── models/
│   │       └── job.model.ts             # Universal job status model for ALL job types
│   └── errors/
│       └── error-types.ts               # Existing error handling system (use existing errors)
├── services/
│   ├── csv.service.ts                   # Existing CSV service
│   ├── contact.bulk.insert.service.ts      # Business logic for bulk insert
│   ├── email.service.ts                  # Email sending service
│   ├── error.report.service.ts            # Error report generation
│   └── [Future: EmailCampaignService.ts, SMSCampaignService.ts, etc.]
├── types/
│   ├── job.types.ts                     # Universal job type interfaces
│   ├── contact.bulk.insert.types.ts     # Contact bulk insert job data types
│   ├── email.types.ts                   # Email job data types
│   └── [Future: email-campaign.types.ts, sms-campaign.types.ts, etc.]
├── queues/
│   ├── queue.manager.ts                  # Universal queue management for ALL job types
│   ├── contact.bulk.insert.queue.ts        # Contact bulk insert queue setup
│   ├── email.queue.ts                    # Email queue setup
│   └── [Future: EmailCampaignQueue.ts, SMSCampaignQueue.ts, etc.]
├── workers/
│   ├── processors/
│   │   ├── base.job.processor.ts          # Abstract base class for ALL job processors
│   │   ├── contact.bulk.insert.processor.ts # Contact bulk insert job processor
│   │   ├── email.processor.ts            # Email sending job processor
│   │   └── [Future: EmailCampaignProcessor.ts, SMSCampaignProcessor.ts, etc.]
│   └── main.ts                          # Updated to start job processors
├── routes/
│   └── contacts.ts                      # Updated with bulk insert endpoint
├── handlers/
│   └── ContactsHandler.ts               # Updated with bulk insert handler
└── validators/
    └── ContactsValidator.ts             # Updated with bulk insert validation
```

**Clean Organization**:
- `workers/processors/` - ALL job processors (no duplication)
- `workers/main.ts` - Starts all job processors
- `lib/` - Core infrastructure (database, errors)
- `services/` - Business services
- `queues/` - Queue management

**Future Extensibility**: Easy to add new job types by adding processor to workers/processors/

## Components and Interfaces

### 1. BaseJobProcessor (Universal Abstract Class in Workers)

**Located in workers/processors/base.job.processor.ts - Foundation for ALL job processors**

```typescript
export abstract class BaseJobProcessor<T = any> {
  protected abstract queueName: string;
  protected abstract jobType: JobType;
  protected queue: Queue.Queue;
  protected log = logger(config.logger);

  constructor() {
    this.queue = new Queue(this.queueName, {
      redis: RedisConnector.getConfig(),
      defaultJobOptions: DEFAULT_QUEUE_OPTIONS
    });
  }

  // Universal job lifecycle management (used by ALL job types)
  public startProcess(): void;
  protected abstract processJob(job: Job<T>): Promise<any>;
  protected async updateJobStatus(jobId: string, status: JOB_STATUS): Promise<void>;
  protected async onJobCompleted(job: Job<T>, result: any): Promise<void>;
  protected async onJobFailed(job: Job<T>, error: any): Promise<void>;
  private setupEventHandlers(): void;
  public getQueue(): Queue.Queue;
  
  // Universal progress tracking (useful for campaigns, bulk operations, etc.)
  protected async updateJobProgress(jobId: string, progress: JobProgress): Promise<void>;
  
  // Universal error handling (consistent across all job types)
  protected async addJobError(jobId: string, error: JobError): Promise<void>;
}

// Future job processors will simply extend this:
// workers/processors/EmailCampaignProcessor.ts extends BaseJobProcessor<EmailCampaignJobData>
// workers/processors/SMSCampaignProcessor.ts extends BaseJobProcessor<SMSCampaignJobData>
// workers/processors/DataExportProcessor.ts extends BaseJobProcessor<DataExportJobData>
```
```

### 2. Contact Bulk Insert Processor

**Located in workers/processors/contact.bulk.insert.processor.ts**

```typescript
export class ContactBulkInsertProcessor extends BaseJobProcessor<ContactBulkInsertJobData> {
  protected queueName = 'contact.bulkInsert.queue';
  protected jobType = JobType.CONTACT_BULK_INSERT;

  protected async processJob(job: Job<ContactBulkInsertJobData>): Promise<ContactBulkInsertResult>;
  private async processCSVFile(filePath: string, orgId: string, createdBy: string): Promise<ProcessingResult>;
  private async validateAndInsertContact(contactData: any, orgId: string, createdBy: string): Promise<ValidationResult>;
  private async generateErrorReport(errors: ContactError[]): Promise<string>;
  protected async onJobCompleted(job: Job<ContactBulkInsertJobData>, result: ContactBulkInsertResult): Promise<void>;
}
```

### 3. Email Processor

**Located in workers/processors/email.processor.ts**

```typescript
export class EmailProcessor extends BaseJobProcessor<EmailJobData> {
  protected queueName = 'email';
  protected jobType = JobType.EMAIL;

  protected async processJob(job: Job<EmailJobData>): Promise<EmailResult>;
  private async sendEmail(emailData: EmailJobData): Promise<void>;
  private async handleEmailFailure(job: Job<EmailJobData>, error: any): Promise<void>;
}
```

### 4. Universal Queue Manager (Root Level)

**This manages ALL queue types: bulk insert, email campaigns, SMS campaigns, etc.**

```typescript
export class QueueManager {
  private static instance: QueueManager;
  private queues: Map<string, Queue.Queue> = new Map();

  static getInstance(): QueueManager;
  
  // Universal queue management (supports any job type)
  public initializeQueues(): void;
  public getQueue(queueName: string): Queue.Queue;
  public addJob(queueName: string, jobData: any, options?: JobOptions): Promise<Job>;
  public startProcessors(): void;
  public closeQueues(): Promise<void>;
  
  // Universal queue monitoring (useful for all job types)
  public getQueueStats(queueName: string): Promise<QueueStats>;
  public getAllQueueStats(): Promise<Map<string, QueueStats>>;
  public pauseQueue(queueName: string): Promise<void>;
  public resumeQueue(queueName: string): Promise<void>;
  
  // Universal job management
  public getJob(queueName: string, jobId: string): Promise<Job | null>;
  public cancelJob(queueName: string, jobId: string): Promise<void>;
  public retryJob(queueName: string, jobId: string): Promise<void>;
}

// Future usage examples:
// queueManager.addJob('email-campaign', emailCampaignData);
// queueManager.addJob('sms-campaign', smsCampaignData);
// queueManager.addJob('data-export', dataExportData);
```
```

### 5. Workers Integration (All in Workers Folder)

**Updated workers/main.ts to start job processors**

```typescript
// workers/main.ts - Updated to start job processors
import { ContactBulkInsertProcessor } from './processors/contact.bulk.insert.processor';
import { EmailProcessor } from './processors/email.processor';

// Initialize job processors
const contactBulkInsertProcessor = new ContactBulkInsertProcessor();
const emailProcessor = new EmailProcessor();

// Start job processors
contactBulkInsertProcessor.startProcess();
emailProcessor.startProcess();

// Future processors can be easily added:
// const emailCampaignProcessor = new EmailCampaignProcessor();
// const smsCampaignProcessor = new SMSCampaignProcessor();
// emailCampaignProcessor.startProcess();
// smsCampaignProcessor.startProcess();

// Existing worker thread code continues...
// (keep existing worker thread implementation)
```
```
```

### 6. Contact Bulk Insert Service

**Uses CommonJobDAO for all job operations**

```typescript
export class ContactBulkInsertService {
  static async createBulkInsertJob(
    file: Express.Multer.File,
    orgId: string,
    createdBy: string
  ): Promise<{ jobId: string }>;

  static async getJobStatus(jobId: string): Promise<JobStatusResponse>;
  
  private static async validateCSVFile(file: Express.Multer.File): Promise<void>;
  private static async saveUploadedFile(file: Express.Multer.File): Promise<string>;
}

// All job operations use CommonJobDAO:
// - CommonJobDAO.createJob(jobData)
// - CommonJobDAO.updateJobStatus(jobId, status)
// - CommonJobDAO.updateJobProgress(jobId, progress)
// - CommonJobDAO.addJobError(jobId, error)
// - CommonJobDAO.getJobById(jobId)
```
```

## Data Models

### 1. Universal Job Status Model (MongoDB)

**This model supports ALL job types: bulk insert, email campaigns, SMS campaigns, data exports, etc.**

```typescript
export interface JobDocument extends Document {
  _id: Schema.Types.ObjectId;
  jobId: string;
  jobType: JobType; // CONTACT_BULK_INSERT, EMAIL_CAMPAIGN, SMS_CAMPAIGN, DATA_EXPORT, etc.
  status: JOB_STATUS; // PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
  orgId: string;
  createdBy: string;
  
  // Universal job data (flexible for any job type)
  data: any; // ContactBulkInsertJobData | EmailCampaignJobData | SMSCampaignJobData | etc.
  result?: any; // Job-specific result data
  
  // Universal error tracking
  errors: JobError[];
  
  // Universal progress tracking (useful for campaigns, bulk operations, etc.)
  progress: {
    totalItems?: number;    // Total contacts, emails, SMS, etc.
    processedItems?: number; // Processed contacts, emails, SMS, etc.
    successfulItems?: number; // Successful contacts, emails, SMS, etc.
    failedItems?: number;    // Failed contacts, emails, SMS, etc.
    percentage?: number;     // Progress percentage
    currentStep?: string;    // Current processing step
    estimatedCompletion?: Date; // Estimated completion time
  };
  
  // Universal timing
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Universal metadata
  metadata?: {
    priority?: number;
    retryCount?: number;
    maxRetries?: number;
    tags?: string[];
    [key: string]: any; // Extensible for job-specific metadata
  };
}

// Job Types (moved to lib/constant/job.constants.ts)
// Job Status (moved to lib/constant/job.constants.ts)
// Error Codes (moved to lib/constant/job.constants.ts)
```
```

### 2. Contact Bulk Insert Job Data

```typescript
export interface ContactBulkInsertJobData {
  jobId: string;
  orgId: string;
  createdBy: string;
  filePath: string;
  fileName: string;
  fileSize: number;
  userEmail: string;
}

export interface ContactBulkInsertResult {
  totalRows: number;
  successfulRows: number;
  failedRows: number;
  processingTime: number;
  errorReportPath?: string;
  errors: ContactError[];
}
```

### 3. Email Job Data

```typescript
export interface EmailJobData {
  jobId: string;
  to: string;
  subject: string;
  template: string;
  data: any;
  attachments?: EmailAttachment[];
  priority: EmailPriority;
}

export interface EmailAttachment {
  filename: string;
  path: string;
  contentType?: string;
}
```

### 4. Contact Error Model (Using Existing Error System)

```typescript
export interface ContactProcessingError {
  rowNumber: number;
  contactData: any;
  error: AppError; // Uses existing AppError from lib/errors/app-error.ts
  timestamp: Date;
}

// Job errors stored in database use existing AppError structure
export interface JobError {
  type: 'VALIDATION' | 'DUPLICATE' | 'SYSTEM' | 'DATABASE';
  message: string;
  errorCode: string;
  details?: Array<{ field: string; message: string }>; // Existing format
  timestamp: Date;
  rowNumber?: number; // For CSV processing errors
}
```

## Error Handling (Using Existing Error System)

### 1. CSV Processing Errors (Using Existing Error Types)

- **File Format Errors**: Use `BadRequestError` for invalid CSV structure, unsupported file types
- **Row Validation Errors**: Use `ValidationError` for invalid contact data, missing required fields
- **Duplicate Contact Errors**: Use `DuplicateError` for duplicate contacts
- **System Errors**: Use `DatabaseError` for database issues, `InternalError` for file system errors

### 2. Error Collection Strategy (Compatible with Existing System)

```typescript
class ErrorCollector {
  private errors: ContactProcessingError[] = [];
  
  addError(rowNumber: number, contactData: any, appError: AppError): void;
  getErrors(): ContactProcessingError[];
  hasErrors(): boolean;
  getErrorCount(): number;
  generateReport(): Promise<string>;
}

interface ContactProcessingError {
  rowNumber: number;
  contactData: any;
  error: AppError; // Uses existing AppError system
  timestamp: Date;
}
```

### 3. Error Codes and Constants (Using Existing Structure)

**New constants in lib/constant/job.constants.ts:**
```typescript
export const JOB_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
} as const;

export const JOB_TYPE = {
  CONTACT_BULK_INSERT: 'CONTACT_BULK_INSERT',
  EMAIL: 'EMAIL',
  // Future: EMAIL_CAMPAIGN, SMS_CAMPAIGN, etc.
} as const;

export const JOB_ERROR_CODES = {
  JOB_NOT_FOUND: 'JOB_001',
  JOB_CREATION_FAILED: 'JOB_002',
  JOB_PROCESSING_FAILED: 'JOB_003',
  BULK_INSERT_FAILED: 'BULK_INSERT_001',
  EMAIL_QUEUE_FAILED: 'EMAIL_QUEUE_001'
} as const;
```

**Queue constants in lib/constant/queue.constants.ts:**
```typescript
export const QUEUE_NAMES = {
  CONTACT_BULK_INSERT: 'contact.bulkInsert.queue',
  EMAIL: 'email'
} as const;

export const DEFAULT_QUEUE_OPTIONS = {
  removeOnComplete: 100,
  removeOnFail: 50,
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
} as const;
```

### 4. Error Report Generation

- Generate CSV file with failed rows and error details using existing error format
- Include original data, row numbers, and AppError details
- Store error reports in temporary storage with expiration
- Provide downloadable links in email notifications

## Testing Strategy

### 1. Unit Tests

- **BaseJobProcessor**: Test abstract class functionality, job lifecycle management
- **ContactBulkInsertProcessor**: Test CSV processing, contact validation, error handling
- **EmailProcessor**: Test email sending, retry logic, failure handling
- **QueueManager**: Test queue initialization, job dispatching
- **Services**: Test business logic, file handling, validation

### 2. Integration Tests

- **End-to-End Bulk Insert**: Upload CSV → Job Processing → Email Notification
- **Queue Integration**: Test job queuing, processing, and status updates
- **Database Integration**: Test job status persistence, contact insertion
- **Redis Integration**: Test queue persistence, job retry mechanisms

### 3. Performance Tests

- **Large File Processing**: Test memory usage with large CSV files
- **Concurrent Job Processing**: Test multiple bulk insert jobs
- **Queue Performance**: Test job throughput and processing times

### 4. Error Scenario Tests

- **Invalid CSV Files**: Test various malformed CSV scenarios
- **Duplicate Contact Handling**: Test duplicate detection and reporting
- **System Failure Recovery**: Test job recovery after system restart
- **Email Delivery Failures**: Test email retry and failure handling

## Memory Management

### 1. Streaming CSV Processing

```typescript
class CSVStreamProcessor {
  async processFile(filePath: string): Promise<ProcessingResult> {
    return new Promise((resolve, reject) => {
      const results: ProcessingResult = {
        totalRows: 0,
        successfulRows: 0,
        failedRows: 0,
        errors: []
      };

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', async (row) => {
          // Process one row at a time
          await this.processRow(row, results);
        })
        .on('end', () => {
          resolve(results);
        })
        .on('error', reject);
    });
  }
}
```

### 2. Batch Processing Strategy

- Process contacts in small batches (50-100 rows)
- Implement backpressure handling for memory management
- Use streaming for large file processing
- Clean up temporary files after processing

## Email Notification System

### 1. Email Templates

- **Success Template**: Job completion summary with statistics
- **Partial Success Template**: Summary with error report attachment
- **Failure Template**: Error details and troubleshooting information

### 2. Email Queue Configuration

```typescript
const EMAIL_QUEUE_OPTIONS = {
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};
```

### 3. Email Service Integration

- Support for multiple email providers (SMTP, SendGrid, etc.)
- Template rendering with dynamic data
- Attachment handling for error reports
- Delivery status tracking and retry logic