-- Create enum type for user_type
CREATE TYPE user_type AS ENUM ('super_admin', 'admin', 'user');

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS profiles (
    uuid UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    organization_uuid UUID REFERENCES organizations(uuid) ON DELETE SET NULL,
    first_name TEXT,
    last_name TEXT,
    user_type user_type NOT NULL DEFAULT 'user',
    email TEXT,
    phone_number TEXT,
    is_email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    is_mobile_verified BOOLEAN NOT NULL DEFAULT FALSE,
    user_status TEXT NOT NULL DEFAULT 'inactive',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_organization_uuid ON profiles(organization_uuid);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_user_status ON profiles(user_status);

-- Create a trigger to automatically update the updated_at timestamp
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Allow service role to manage all profiles
CREATE POLICY "Service role can manage all profiles"
    ON public.profiles
    FOR ALL
    USING (auth.role() = 'service_role');

-- Allow users to read their own profile
CREATE POLICY "Users can view their own profile"
    ON public.profiles
    FOR SELECT
    USING (auth.uid() = user_id);

-- Allow users to update their own profile
CREATE POLICY "Users can update their own profile"
    ON public.profiles
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Create policies for organizations table that depend on profiles table
-- Allow users to read organizations they belong to
CREATE POLICY "Users can view organizations they belong to"
    ON public.organizations
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1
            FROM profiles
            WHERE profiles.organization_uuid = organizations.uuid
            AND profiles.user_id = auth.uid()
        )
    );

-- Allow users to update organizations they belong to
CREATE POLICY "Users can update organizations they belong to"
    ON public.organizations
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1
            FROM profiles
            WHERE profiles.organization_uuid = organizations.uuid
            AND profiles.user_id = auth.uid()
            AND profiles.user_type IN ('super_admin', 'admin')
        )
    );

-- Add comments to the table and columns
COMMENT ON TABLE profiles IS 'User profiles with additional information';
COMMENT ON COLUMN profiles.uuid IS 'Primary key for the profile';
COMMENT ON COLUMN profiles.user_id IS 'Foreign key to the auth.users table';
COMMENT ON COLUMN profiles.organization_uuid IS 'Foreign key to the organization the user belongs to';
COMMENT ON COLUMN profiles.first_name IS 'User first name';
COMMENT ON COLUMN profiles.last_name IS 'User last name';
COMMENT ON COLUMN profiles.user_type IS 'User type: super_admin, admin, or user';
COMMENT ON COLUMN profiles.email IS 'User email address';
COMMENT ON COLUMN profiles.phone_number IS 'User phone number';
COMMENT ON COLUMN profiles.is_email_verified IS 'Whether the user email is verified';
COMMENT ON COLUMN profiles.is_mobile_verified IS 'Whether the user mobile number is verified';
COMMENT ON COLUMN profiles.user_status IS 'User status: active or inactive';

-- Create function to handle user profile creation
CREATE OR REPLACE FUNCTION create_user_with_profile(
    user_id UUID,
    organization_uuid UUID,
    first_name TEXT DEFAULT NULL,
    last_name TEXT DEFAULT NULL,
    email TEXT DEFAULT NULL,
    phone_number TEXT DEFAULT NULL,
    user_type user_type DEFAULT 'user'
) RETURNS UUID AS $$
DECLARE
    profile_uuid UUID;
BEGIN
    -- Create the user profile
    INSERT INTO public.profiles (
        uuid,
        user_id,
        organization_uuid,
        first_name,
        last_name,
        email,
        phone_number,
        user_type,
        user_status,
        is_email_verified,
        is_mobile_verified
    ) VALUES (
        gen_random_uuid(),
        user_id,
        organization_uuid,
        first_name,
        last_name,
        email,
        phone_number,
        user_type,
        'inactive',
        FALSE,
        FALSE
    ) RETURNING uuid INTO profile_uuid;

    RETURN profile_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment to explain the function
COMMENT ON FUNCTION create_user_with_profile(UUID, UUID, TEXT, TEXT, TEXT, TEXT, user_type) IS 'Creates a user profile with the given parameters';