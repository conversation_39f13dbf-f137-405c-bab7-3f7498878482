-- Create a function to handle organization and profile creation after email verification
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION handle_email_verification()
RETURNS TRIGGER AS $$
DECLARE
  org_name TEXT;
  org_uuid UUID;
  display_name TEXT;
  phone_number TEXT;
  first_name TEXT;
  last_name TEXT;
  error_context TEXT;
  operation_step TEXT := 'initialization';
BEGIN
  -- Initialize error context for debugging
  error_context := format('User ID: %s, Email: %s', NEW.id, COALESCE(NEW.email, 'NULL'));

  -- Log the start of the function execution with detailed context
  RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Started processing for %', error_context;

  -- Validate that we have the required data
  IF NEW.id IS NULL THEN
    RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: User ID is NULL, cannot proceed. Context: %', error_context;
    RETURN NEW;
  END IF;

  IF NEW.email IS NULL OR NEW.email = '' THEN
    RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: User email is NULL or empty, cannot proceed. Context: %', error_context;
    RETURN NEW;
  END IF;

  -- Check if the email was just confirmed (email_confirmed_at was updated from null)
  IF OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL THEN
    operation_step := 'email_confirmation_detected';
    RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Email confirmation detected for %', error_context;

    -- Extract and validate organization name
    operation_step := 'organization_name_extraction';
    BEGIN
      IF NEW.raw_user_meta_data IS NOT NULL AND NEW.raw_user_meta_data->>'organization_name' IS NOT NULL THEN
        org_name := trim(NEW.raw_user_meta_data->>'organization_name');
        RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Using organization name from metadata: "%s" for %', org_name, error_context;
      ELSE
        -- Generate organization name from email
        IF position('@' in NEW.email) > 1 THEN
          org_name := 'Organization ' || substr(NEW.email, 1, position('@' in NEW.email) - 1);
        ELSE
          org_name := 'Organization ' || NEW.id::text;
        END IF;
        RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Generated organization name: "%s" for %', org_name, error_context;
      END IF;
    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Error during organization name extraction for %: % (SQLSTATE: %)',
        error_context, SQLERRM, SQLSTATE;
      org_name := 'Organization ' || NEW.id::text; -- Fallback
      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Using fallback organization name: "%s"', org_name;
    END;

    -- Extract user metadata safely
    operation_step := 'user_metadata_extraction';
    BEGIN
      display_name := COALESCE(trim(NEW.raw_user_meta_data->>'display_name'), '');
      phone_number := COALESCE(trim(NEW.raw_user_meta_data->>'phone_number'), '');

      -- Parse first and last name from display_name
      IF display_name IS NOT NULL AND display_name != '' THEN
        first_name := trim(split_part(display_name, ' ', 1));
        last_name := trim(split_part(display_name, ' ', 2));
        -- If there's no space, put everything in first_name
        IF first_name = display_name THEN
          last_name := NULL;
        END IF;
      END IF;

      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Extracted metadata - Display: "%s", Phone: "%s", First: "%s", Last: "%s" for %',
        COALESCE(display_name, 'NULL'), COALESCE(phone_number, 'NULL'),
        COALESCE(first_name, 'NULL'), COALESCE(last_name, 'NULL'), error_context;
    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Error during metadata extraction for %: % (SQLSTATE: %)',
        error_context, SQLERRM, SQLSTATE;
      -- Set safe defaults
      display_name := '';
      phone_number := '';
      first_name := NULL;
      last_name := NULL;
    END;

    -- Create organization with comprehensive error handling
    operation_step := 'organization_creation';
    BEGIN
      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Attempting to create organization "%s" for %', org_name, error_context;

      INSERT INTO public.organizations (
        organization_name,
        status
      ) VALUES (
        org_name,
        'active'
      ) RETURNING uuid INTO org_uuid;

      IF org_uuid IS NULL THEN
        RAISE EXCEPTION 'Organization creation returned NULL UUID';
      END IF;

      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Organization created successfully with UUID: % for %', org_uuid, error_context;

    EXCEPTION
      WHEN unique_violation THEN
        RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Organization name "%s" already exists for %: % (SQLSTATE: %)',
          org_name, error_context, SQLERRM, SQLSTATE;
        -- Try with a unique suffix
        org_name := org_name || ' (' || NEW.id::text || ')';
        RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Retrying with unique organization name: "%s"', org_name;

        BEGIN
          INSERT INTO public.organizations (
            organization_name,
            status
          ) VALUES (
            org_name,
            'active'
          ) RETURNING uuid INTO org_uuid;

          RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Organization created with unique name, UUID: % for %', org_uuid, error_context;
        EXCEPTION WHEN OTHERS THEN
          RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Failed to create organization even with unique name for %: % (SQLSTATE: %)',
            error_context, SQLERRM, SQLSTATE;
          RETURN NEW; -- Exit gracefully, don't create profile without organization
        END;

      WHEN OTHERS THEN
        RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Unexpected error during organization creation for %: % (SQLSTATE: %)',
          error_context, SQLERRM, SQLSTATE;
        RETURN NEW; -- Exit gracefully, don't create profile without organization
    END;

    -- Create user profile with comprehensive error handling
    operation_step := 'profile_creation';
    BEGIN
      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Attempting to create user profile for %', error_context;

      INSERT INTO public.profiles (
        user_id,
        organization_uuid,
        first_name,
        last_name,
        email,
        phone_number,
        user_type,
        user_status,
        is_email_verified,
        is_mobile_verified
      ) VALUES (
        NEW.id,
        org_uuid,
        first_name,
        last_name,
        NEW.email,
        NULLIF(phone_number, ''), -- Convert empty string to NULL
        'admin',
        'active',
        TRUE,
        FALSE
      );

      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: User profile created successfully for %', error_context;

    EXCEPTION
      WHEN unique_violation THEN
        RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: User profile already exists for %: % (SQLSTATE: %)',
          error_context, SQLERRM, SQLSTATE;
        -- Check if it's the user_id constraint
        IF SQLSTATE = '23505' AND SQLERRM LIKE '%user_id%' THEN
          RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Profile already exists for user, skipping profile creation for %', error_context;
        ELSE
          RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Unexpected unique violation during profile creation for %: %',
            error_context, SQLERRM;
        END IF;

      WHEN foreign_key_violation THEN
        RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Foreign key violation during profile creation for %: % (SQLSTATE: %)',
          error_context, SQLERRM, SQLSTATE;
        -- This could happen if the organization was not created properly

      WHEN OTHERS THEN
        RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Unexpected error during profile creation for %: % (SQLSTATE: %)',
          error_context, SQLERRM, SQLSTATE;
    END;

    RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Successfully completed processing for %', error_context;

  ELSE
    -- Log when trigger runs but email is not being confirmed
    IF OLD.email_confirmed_at IS NOT NULL THEN
      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Email already confirmed for %', error_context;
    ELSE
      RAISE LOG 'EMAIL_VERIFICATION_TRIGGER: Email not being confirmed for % (OLD: %, NEW: %)',
        error_context, OLD.email_confirmed_at, NEW.email_confirmed_at;
    END IF;
  END IF;

  RETURN NEW;

EXCEPTION WHEN OTHERS THEN
  -- Catch any other errors that might occur at the top level
  RAISE WARNING 'EMAIL_VERIFICATION_TRIGGER: Unexpected top-level error during % for %: % (SQLSTATE: %)',
    operation_step, error_context, SQLERRM, SQLSTATE;
  -- Always return NEW to ensure the email verification process continues
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists to avoid conflicts
DROP TRIGGER IF EXISTS on_email_verification ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_email_confirmed ON auth.users;

-- Create a trigger on the auth.users table to call the handle_email_verification function
CREATE TRIGGER on_auth_user_email_confirmed
AFTER UPDATE ON auth.users
FOR EACH ROW
EXECUTE FUNCTION handle_email_verification();

-- Add comment to explain the trigger
COMMENT ON FUNCTION handle_email_verification() IS 'Automatically creates an organization and user profile when a user verifies their email. Features comprehensive error handling, detailed logging with EMAIL_VERIFICATION_TRIGGER prefix, graceful failure recovery, and ensures email verification process continues even if organization/profile creation fails. Handles unique violations, foreign key violations, and provides detailed context for debugging.';