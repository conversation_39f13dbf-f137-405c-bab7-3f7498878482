-- Create enum type for organization status
CREATE TYPE organization_status AS ENUM ('active', 'inactive');

-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
    uuid UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_name TEXT NOT NULL,
    status organization_status NOT NULL DEFAULT 'inactive',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on organization_name for faster lookups
CREATE INDEX IF NOT EXISTS idx_organizations_name ON organizations(organization_name);

-- Create index on status for filtering
CREATE INDEX IF NOT EXISTS idx_organizations_status ON organizations(status);


-- Create a trigger to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_organizations_updated_at
BEFORE UPDATE ON organizations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Allow service role to manage all organizations
CREATE POLICY "Service role can manage all organizations"
    ON public.organizations
    FOR ALL
    USING (auth.role() = 'service_role');

-- Allow anon role to create organizations through the create_user_with_profile function
CREATE POLICY "Allow anon to create organizations through function"
    ON public.organizations
    FOR INSERT
    WITH CHECK (true);

-- Note: Policies that reference profiles table are created in the profiles migration
-- to avoid circular dependencies

-- Add comments to the table and columns
COMMENT ON TABLE organizations IS 'Organizations that users belong to';
COMMENT ON COLUMN organizations.uuid IS 'Primary key for the organization';
COMMENT ON COLUMN organizations.organization_name IS 'Name of the organization';
COMMENT ON COLUMN organizations.status IS 'Organization status: active or inactive';
