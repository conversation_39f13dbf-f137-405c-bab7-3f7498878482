-- Add email and phone_number columns to organizations table
ALTER TABLE organizations
ADD COLUMN email TEXT,
ADD COLUMN phone_number TEXT;

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_organizations_email ON organizations(email);
CREATE INDEX IF NOT EXISTS idx_organizations_phone_number ON organizations(phone_number);

-- Add comments to the new columns
COMMENT ON COLUMN organizations.email IS 'Organization email address';
COMMENT ON COLUMN organizations.phone_number IS 'Organization phone number';